package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.PetApplicableDTO;
import com.moego.server.grooming.dto.ob.SelectedPetServiceDTO;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.service.ob.OBBusinessStaffService;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OBStaffTest {

    @InjectMocks
    private OBBusinessStaffService service;

    @Mock
    private MoeGroomingServiceMapper groomingServiceMapper;

    @Mock
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Mock
    private GroomingServiceService groomingServiceService;

    private Method buildApplicableMap;

    private static final int STAFF_A = 1;
    private static final int STAFF_B = 2;
    private static final int STAFF_C = 3;

    private static final int SERVICE_101 = 101;
    private static final int SERVICE_102 = 102;
    private static final int SERVICE_103 = 103;
    private static final int SERVICE_104 = 104;

    private static final int ADDON_201 = 201;

    // serviceId -> staffId list
    private static final Map<Integer, List<Integer>> serviceToStaffMap = Maps.newHashMap();

    static {
        serviceToStaffMap.put(SERVICE_101, Arrays.asList(STAFF_A, STAFF_B));
        serviceToStaffMap.put(SERVICE_102, Arrays.asList(STAFF_A));
        serviceToStaffMap.put(SERVICE_103, Arrays.asList(STAFF_C));
        serviceToStaffMap.put(SERVICE_104, Arrays.asList(STAFF_A, STAFF_B, STAFF_C));
        serviceToStaffMap.put(ADDON_201, Arrays.asList(STAFF_A, STAFF_B, STAFF_C));
    }

    private void initPrivateMethod() throws Exception {
        buildApplicableMap =
                OBBusinessStaffService.class.getDeclaredMethod("buildApplicableMap", List.class, List.class, Map.class);
        buildApplicableMap.setAccessible(true);
    }

    @BeforeEach
    public void init() throws Exception {
        // initMock();
        // initPrivateMethod();
    }

    @Test
    public void testBuildApplicableMap() throws Exception {
        initPrivateMethod();

        List<Integer> allStaffIdList = Lists.newArrayList(STAFF_A, STAFF_B, STAFF_C);

        List<SelectedPetServiceDTO> selectedPetServiceList = Lists.newArrayList(
                newPetServiceDTO(1, SERVICE_101, ADDON_201), newPetServiceDTO(2, SERVICE_102, ADDON_201));

        // staffId -> list of applicable for each pet
        Map<Integer, List<PetApplicableDTO>> result = (Map<Integer, List<PetApplicableDTO>>)
                buildApplicableMap.invoke(service, allStaffIdList, selectedPetServiceList, serviceToStaffMap);

        assertThat(result.size()).isEqualTo(allStaffIdList.size());

        // check staff_A (both are applicable)
        List<PetApplicableDTO> petApplicableDTOList = result.get(STAFF_A);
        assertThat(petApplicableDTOList).isNotNull();
        assertThat(petApplicableDTOList.size()).isEqualTo(selectedPetServiceList.size());

        assertThat(petApplicableDTOList.get(0).getPetId())
                .isEqualTo(selectedPetServiceList.get(0).getPetDataDTO().getPetId());
        assertThat(petApplicableDTOList.get(0).getApplicable()).isTrue();
        assertThat(petApplicableDTOList.get(1).getPetId())
                .isEqualTo(selectedPetServiceList.get(1).getPetDataDTO().getPetId());
        assertThat(petApplicableDTOList.get(1).getApplicable()).isTrue();

        // check staff_B (only one applicable)
        petApplicableDTOList = result.get(STAFF_B);
        assertThat(petApplicableDTOList).isNotNull();
        assertThat(petApplicableDTOList.size()).isEqualTo(selectedPetServiceList.size());

        assertThat(petApplicableDTOList.get(0).getPetId())
                .isEqualTo(selectedPetServiceList.get(0).getPetDataDTO().getPetId());
        assertThat(petApplicableDTOList.get(0).getApplicable()).isTrue();
        assertThat(petApplicableDTOList.get(1).getPetId())
                .isEqualTo(selectedPetServiceList.get(1).getPetDataDTO().getPetId());
        assertThat(petApplicableDTOList.get(1).getApplicable()).isFalse();

        // check staff_C (both are not applicable)
        petApplicableDTOList = result.get(STAFF_C);
        assertThat(petApplicableDTOList).isNotNull();
        assertThat(petApplicableDTOList.size()).isEqualTo(selectedPetServiceList.size());

        assertThat(petApplicableDTOList.get(0).getPetId())
                .isEqualTo(selectedPetServiceList.get(0).getPetDataDTO().getPetId());
        assertThat(petApplicableDTOList.get(0).getApplicable()).isFalse();
        assertThat(petApplicableDTOList.get(1).getPetId())
                .isEqualTo(selectedPetServiceList.get(1).getPetDataDTO().getPetId());
        assertThat(petApplicableDTOList.get(1).getApplicable()).isFalse();
    }

    private void initMock() {
        // mock groomingServiceMapper.getServicesByServiceIds
        List<MoeGroomingService> serviceList = new ArrayList<>();
        serviceList.add(newService(SERVICE_101, false, false));
        serviceList.add(newService(SERVICE_102, false, false));
        serviceList.add(newService(SERVICE_103, false, false));
        serviceList.add(newService(SERVICE_104, true, false));
        serviceList.add(newService(ADDON_201, true, true));
        lenient()
                .doReturn(serviceList)
                .when(companyGroomingServiceQueryService)
                .groomingServiceSelectByBusinessIdServiceIds(any(), any());

        // mock groomingServiceService.getServiceIdToStaffMap
        Map<Integer, List<Integer>> serviceToStaffMap = Maps.newHashMap();
        serviceToStaffMap.put(SERVICE_101, Arrays.asList(STAFF_A, STAFF_B));
        serviceToStaffMap.put(SERVICE_102, Arrays.asList(STAFF_A));
        serviceToStaffMap.put(SERVICE_103, Arrays.asList(STAFF_C));
        serviceToStaffMap.put(SERVICE_104, Arrays.asList(STAFF_A, STAFF_B, STAFF_C));
        lenient().doReturn(serviceToStaffMap).when(groomingServiceService).getServiceIdToStaffMap(any());
    }

    @Test
    public void testGetServiceToStaffMap() {
        initMock();

        List<Integer> allStaffIdList = Lists.newArrayList(STAFF_A, STAFF_B, STAFF_C);
        List<Integer> allServiceIdList =
                Lists.newArrayList(SERVICE_101, SERVICE_102, SERVICE_103, SERVICE_104, ADDON_201);

        // serviceId -> staffId list
        Map<Integer, List<Integer>> map = service.getServiceToStaffMap(1, allServiceIdList, allStaffIdList);

        assertThat(map.size()).isEqualTo(allServiceIdList.size());
        assertThat(map.keySet()).containsAll(allServiceIdList);
        for (Integer serviceId : allServiceIdList) {
            assertThat(map.get(serviceId)).isEqualTo(serviceToStaffMap.get(serviceId));
        }
    }

    private SelectedPetServiceDTO newPetServiceDTO(Integer petId, Integer... serviceIds) {
        SelectedPetServiceDTO dto = new SelectedPetServiceDTO();
        OBPetDataDTO obPetDataDTO = new OBPetDataDTO();
        obPetDataDTO.setPetId(petId);
        dto.setPetDataDTO(obPetDataDTO);
        dto.setServiceIdList(Arrays.asList(serviceIds));
        return dto;
    }

    private MoeGroomingService newService(Integer serviceId, boolean isAllStaff, boolean isAddon) {
        MoeGroomingService service = new MoeGroomingService();
        service.setId(serviceId);
        service.setIsAllStaff(isAllStaff ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
        service.setType(isAddon ? ServiceEnum.TYPE_ADD_ONS : ServiceEnum.TYPE_SERVICE);
        return service;
    }
}
