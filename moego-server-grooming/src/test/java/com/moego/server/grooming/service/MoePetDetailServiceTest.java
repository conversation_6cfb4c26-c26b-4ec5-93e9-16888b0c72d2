package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.moego.common.enums.ScopeModifyTypeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.lib.common.exception.BizException;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import com.moego.server.grooming.dto.GroomingPetServiceDTO;
import com.moego.server.grooming.dto.GroomingServiceOperationDTO;
import com.moego.server.grooming.mapperbean.EvaluationServiceDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.params.appointment.OperationParams;
import com.moego.server.grooming.params.appointment.PetParams;
import com.moego.server.grooming.params.appointment.ServiceAndOperationParams;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.CollectionUtils;

@ExtendWith(MockitoExtension.class)
class MoePetDetailServiceTest {

    @Mock
    MoeGroomingCustomerServicesService customerService;

    @Mock
    GroomingServiceOperationService groomingServiceOperationService;

    @Mock
    GroomingServiceService groomingServiceService;

    @Mock
    IBusinessStaffClient iBusinessStaffClient;

    @InjectMocks
    MoePetDetailService service;

    @Test
    void getServiceTimeAndPrice() {
        int businessId = 1;
        int customerId = 10;
        Integer petOneId = 100;
        Integer petTwoId = 200;
        Integer serviceOneId = 1000;
        Integer serviceTwoId = 2000;
        List<Pair<Integer, MoeGroomingService>> petServices = List.of(
                Pair.of(petOneId, new MoeGroomingService() {
                    {
                        setId(serviceOneId);
                        setDuration(10000);
                        setPrice(new BigDecimal("100.01"));
                    }
                }),
                Pair.of(petTwoId, new MoeGroomingService() {
                    {
                        setId(serviceTwoId);
                        setDuration(20000);
                        setPrice(new BigDecimal("200.02"));
                    }
                }));
        when(customerService.getCustomerServiceList(
                        businessId, List.of(petOneId, petTwoId), List.of(serviceOneId, serviceTwoId)))
                .thenReturn(List.of(
                        new MoeGroomingCustomerServices() {
                            {
                                setPetId(petOneId);
                                setServiceId(serviceOneId);
                                setServiceTime(30000);
                                setSaveType(ServiceEnum.SAVE_TYPE_TIME);
                            }
                        },
                        new MoeGroomingCustomerServices() {
                            {
                                setPetId(petOneId);
                                setServiceId(serviceOneId);
                                setServiceFee(new BigDecimal("300.03"));
                                setSaveType(ServiceEnum.SAVE_TYPE_PRICE);
                            }
                        }));
        Map<Pair<Integer, Integer>, Pair<Integer, BigDecimal>> result =
                service.getServiceTimeAndPrice(businessId, petServices);
        assertEquals(2, result.size());
        assertEquals(result.get(Pair.of(petOneId, serviceOneId)), Pair.of(30000, new BigDecimal("300.03")));
        assertEquals(result.get(Pair.of(petTwoId, serviceTwoId)), Pair.of(20000, new BigDecimal("200.02")));
    }

    @Test
    void buildPetDetailList() {
        Integer businessId = 1;
        Integer petId_1 = 100;
        Integer serviceId_1_1 = 1100;
        Integer serviceId_1_2 = 1200;
        Integer petId_2 = 200;
        Integer serviceId_2_1 = 2100;
        Integer petId_3 = 300;
        Integer serviceId_3_1 = 3100;
        Integer staffId = 10000;

        MoePetDetailService spyService = Mockito.spy(service);
        List<PetParams> petParams = List.of(
                new PetParams(
                        petId_1,
                        List.of(
                                ServiceAndOperationParams.builder()
                                        .serviceId(serviceId_1_1)
                                        .build(),
                                ServiceAndOperationParams.builder()
                                        .serviceId(serviceId_1_2)
                                        .build()),
                        null),
                new PetParams(
                        petId_2,
                        List.of(ServiceAndOperationParams.builder()
                                .serviceId(serviceId_2_1)
                                .build()),
                        null),
                new PetParams(
                        petId_3,
                        List.of(new ServiceAndOperationParams(
                                serviceId_3_1,
                                333,
                                ScopeModifyTypeEnum.DO_NOT_SAVE,
                                new BigDecimal("333.33"),
                                ScopeModifyTypeEnum.THIS_FUTURE,
                                1,
                                staffId,
                                true,
                                1,
                                null,
                                null,
                                null,
                                null,
                                null,
                                List.of(new OperationParams(
                                        100,
                                        "op1",
                                        new BigDecimal("100.01"),
                                        new BigDecimal("100.01"),
                                        staffId.longValue())))),
                        null));
        when(groomingServiceService.getServiceMap(any(), any())).thenReturn(new HashMap<>());
        assertThrows(
                BizException.class,
                () -> spyService.buildPetDetailList(petParams, businessId, "2024-01-01", 100, true));

        when(groomingServiceService.getServiceMap(any(), any()))
                .thenReturn(Map.of(
                        serviceId_1_1,
                        new MoeGroomingService() {
                            {
                                setId(serviceId_1_1);
                                setType((byte) 1);
                            }
                        },
                        serviceId_1_2,
                        new MoeGroomingService() {
                            {
                                setId(serviceId_1_2);
                                setType((byte) 2);
                            }
                        },
                        serviceId_2_1,
                        new MoeGroomingService() {
                            {
                                setId(serviceId_2_1);
                                setType((byte) 1);
                            }
                        },
                        serviceId_3_1,
                        new MoeGroomingService() {
                            {
                                setId(serviceId_3_1);
                                setType((byte) 1);
                            }
                        }));
        doReturn(Map.of(
                        Pair.of(petId_1, serviceId_1_1), Pair.of(110, new BigDecimal("100.01")),
                        Pair.of(petId_1, serviceId_1_2), Pair.of(120, new BigDecimal("120.01")),
                        Pair.of(petId_2, serviceId_2_1), Pair.of(210, new BigDecimal("200.02")),
                        Pair.of(petId_3, serviceId_3_1), Pair.of(310, new BigDecimal("300.02"))))
                .when(spyService)
                .getServiceTimeAndPrice(any(), any());

        List<MoeGroomingPetDetail> result =
                spyService.buildPetDetailList(petParams, businessId, "2024-01-01", 100, true);
        assertEquals(4, result.size());
        assertEquals(petId_1, result.get(0).getPetId());
        assertEquals(serviceId_1_1, result.get(0).getServiceId());
        assertEquals(1, result.get(0).getServiceType());
        assertEquals(100, result.get(0).getStartTime());
        assertEquals(210, result.get(0).getEndTime());
        assertEquals("2024-01-01", result.get(0).getStartDate());
        assertEquals("2024-01-01", result.get(0).getEndDate());

        assertEquals(petId_1, result.get(1).getPetId());
        assertEquals(serviceId_1_2, result.get(1).getServiceId());
        assertEquals(2, result.get(1).getServiceType());
        assertEquals(210, result.get(1).getStartTime());
        assertEquals(330, result.get(1).getEndTime());
        assertEquals("2024-01-01", result.get(0).getStartDate());
        assertEquals("2024-01-01", result.get(0).getEndDate());

        assertEquals(petId_2, result.get(2).getPetId());
        assertEquals(serviceId_2_1, result.get(2).getServiceId());
        assertEquals(1, result.get(2).getServiceType());
        assertEquals(100, result.get(2).getStartTime());
        assertEquals(310, result.get(2).getEndTime());
        assertEquals("2024-01-01", result.get(0).getStartDate());
        assertEquals("2024-01-01", result.get(0).getEndDate());

        assertEquals(petId_3, result.get(3).getPetId());
        assertEquals(serviceId_3_1, result.get(3).getServiceId());
        assertEquals(1, result.get(3).getServiceType());
        assertEquals(100, result.get(3).getStartTime());
        assertEquals(433, result.get(3).getEndTime());
        assertEquals(
                ScopeModifyTypeEnum.DO_NOT_SAVE.getScopeType(), result.get(3).getScopeTypeTime());
        assertEquals(new BigDecimal("333.33"), result.get(3).getServicePrice());
        assertEquals(
                ScopeModifyTypeEnum.THIS_FUTURE.getScopeType(), result.get(3).getScopeTypePrice());
        assertEquals(staffId, result.get(3).getStaffId());
        assertEquals(1, result.get(3).getWorkMode());
        assertTrue(result.get(3).getEnableOperation());
        assertEquals("2024-01-01", result.get(0).getStartDate());
        assertEquals("2024-01-01", result.get(0).getEndDate());

        result = spyService.buildPetDetailList(petParams, businessId, "2024-01-01", 100, false);
        assertEquals(4, result.size());
        assertEquals(100, result.get(0).getStartTime());
        assertEquals(210, result.get(0).getEndTime());

        assertEquals(210, result.get(1).getStartTime());
        assertEquals(330, result.get(1).getEndTime());

        assertEquals(330, result.get(2).getStartTime());
        assertEquals(540, result.get(2).getEndTime());

        assertEquals(540, result.get(3).getStartTime());
        assertEquals(873, result.get(3).getEndTime());
        assertEquals("2024-01-01", result.get(0).getStartDate());
        assertEquals("2024-01-01", result.get(0).getEndDate());
    }

    @Test
    void getPetServiceDTOs() {
        Integer petId_1 = 10;
        Integer petId_2 = 20;
        Integer petServiceId_1_1 = 110;
        Integer petServiceId_1_2 = 120;
        Integer petServiceId_2_1 = 210;
        Integer petServiceId_2_2 = 220;
        Integer staffId_1_1 = 1100;
        Integer staffId_1_2 = 1200;
        Integer staffId_2_1 = 2100;

        Map<Integer, List<MoeGroomingPetDetail>> petServiceMap = Map.of(
                petId_1,
                        List.of(
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_1);
                                        setId(petServiceId_1_1);
                                        setStaffId(staffId_1_1);
                                        setEnableOperation(true);
                                    }
                                },
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_1);
                                        setId(petServiceId_1_2);
                                        setStaffId(staffId_1_2);
                                        setEnableOperation(true);
                                    }
                                }),
                petId_2,
                        List.of(
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_2);
                                        setId(petServiceId_2_1);
                                        setStaffId(staffId_2_1);
                                        setEnableOperation(true);
                                    }
                                },
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_2);
                                        setId(petServiceId_2_2);
                                        setEnableOperation(false);
                                    }
                                }));
        MoeGroomingService groomingService = new MoeGroomingService() {
            {
                setId(0);
            }
        };
        when(groomingServiceService.getServiceMap(any(), any())).thenReturn(Map.of(0, groomingService));
        Map<Integer, List<GroomingServiceOperationDTO>> operationMap = Map.of(
                petServiceId_1_1, List.of(new GroomingServiceOperationDTO(), new GroomingServiceOperationDTO()),
                petServiceId_1_2, List.of(new GroomingServiceOperationDTO()),
                petServiceId_2_1, List.of(new GroomingServiceOperationDTO()));
        when(groomingServiceOperationService.getOperationMapByGroomingServiceIdList(any(), any()))
                .thenReturn(operationMap);
        when(iBusinessStaffClient.getStaffList(any()))
                .thenReturn(List.of(
                        new MoeStaffDto() {
                            {
                                setId(staffId_1_1);
                            }
                        },
                        new MoeStaffDto() {
                            {
                                setId(staffId_1_2);
                            }
                        },
                        new MoeStaffDto() {
                            {
                                setId(staffId_2_1);
                            }
                        }));
        Map<Integer, List<GroomingPetServiceDTO>> result = service.getPetServiceDTOs(100, petServiceMap);
        assertEquals(2, result.size());
        assertEquals(2, result.get(petId_1).size());
        assertEquals(2, result.get(petId_1).get(0).getOperationList().size());
        assertEquals(staffId_1_1, result.get(petId_1).get(0).getStaffId());
        assertEquals(1, result.get(petId_1).get(1).getOperationList().size());
        assertEquals(staffId_1_2, result.get(petId_1).get(1).getStaffId());
        assertEquals(2, result.get(petId_2).size());
        assertEquals(1, result.get(petId_2).get(0).getOperationList().size());
        assertEquals(staffId_2_1, result.get(petId_2).get(0).getStaffId());
        assertTrue(CollectionUtils.isEmpty(result.get(petId_2).get(1).getOperationList()));
    }

    @Test
    void makeGroomingPetServiceDTO() {
        Integer petServiceId = 100;
        Integer staffId = 10;
        Integer serviceId = 1000;
        MoeGroomingPetDetail moeGroomingPetDetail = new MoeGroomingPetDetail() {
            {
                setId(petServiceId);
                setStaffId(staffId);
                setServiceId(serviceId);
                setEnableOperation(false);
            }
        };
        MoeGroomingService groomingService = new MoeGroomingService() {
            {
                setId(serviceId);
            }
        };
        GroomingPetServiceDTO result = service.makeGroomingPetServiceDTO(
                moeGroomingPetDetail, null, Map.of(serviceId, groomingService), new HashMap<>());
        assertEquals(petServiceId, result.getPetDetailId());

        Map<Integer, MoeStaffDto> staffDtoMap = Map.of(staffId, new MoeStaffDto() {
            {
                setLastName("last");
                setFirstName("first");
            }
        });
        result = service.makeGroomingPetServiceDTO(
                moeGroomingPetDetail, null, Map.of(serviceId, groomingService), staffDtoMap);
        assertEquals("last", result.getStaffLastName());
        assertEquals("first", result.getStaffFirstName());

        Map<Integer, List<GroomingServiceOperationDTO>> operationMap =
                Map.of(petServiceId, List.of(new GroomingServiceOperationDTO()));
        moeGroomingPetDetail.setEnableOperation(true);
        result = service.makeGroomingPetServiceDTO(
                moeGroomingPetDetail, operationMap, Map.of(serviceId, groomingService), staffDtoMap);
        assertEquals(1, result.getOperationList().size());
    }

    @Test
    void syncNewestTimeAndPrice() {
        Integer petId_1 = 10;
        Integer petId_2 = 20;
        Integer petServiceId_1_1 = 110;
        Integer petServiceId_1_2 = 120;
        Integer petServiceId_2_1 = 210;
        Integer petServiceId_2_2 = 220;
        Map<Integer, MoeGroomingService> serviceMap = Map.of(
                petServiceId_1_1,
                        new MoeGroomingService() {
                            {
                                setId(petServiceId_1_1);
                                setDuration(110);
                                setPrice(new BigDecimal("110.01"));
                            }
                        },
                petServiceId_1_2,
                        new MoeGroomingService() {
                            {
                                setId(petServiceId_1_2);
                                setDuration(120);
                                setPrice(new BigDecimal("120.01"));
                            }
                        },
                petServiceId_2_1,
                        new MoeGroomingService() {
                            {
                                setId(petServiceId_2_1);
                                setDuration(210);
                                setPrice(new BigDecimal("210.01"));
                            }
                        },
                petServiceId_2_2,
                        new MoeGroomingService() {
                            {
                                setId(petServiceId_2_2);
                                setDuration(220);
                                setPrice(new BigDecimal("220.01"));
                            }
                        });
        Map<Pair<Integer, Integer>, BigDecimal> scopeTypePriceMap =
                Map.of(Pair.of(petId_2, petServiceId_2_1), new BigDecimal("215.01"));
        Map<Pair<Integer, Integer>, Integer> scopeTypeTimeMap = Map.of(Pair.of(petId_2, petServiceId_2_1), 215);

        Map<Integer, List<MoeGroomingPetDetail>> petDetails = Map.of(
                petId_1,
                        new ArrayList<>(List.of(
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_1);
                                        setServiceId(petServiceId_1_1);
                                        setServiceTime(110);
                                        setStartTime(10L);
                                        setEndTime(120L);
                                        setServicePrice(new BigDecimal("110.01"));
                                    }
                                },
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_1);
                                        setServiceId(petServiceId_1_2);
                                        setServiceTime(120);
                                        setStartTime(120L);
                                        setEndTime(240L);
                                        setServicePrice(new BigDecimal("120.01"));
                                    }
                                })),
                petId_2,
                        new ArrayList<>(List.of(
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_2);
                                        setServiceId(petServiceId_2_1);
                                        setServiceTime(100);
                                        setStartTime(10L);
                                        setEndTime(110L);
                                        setServicePrice(new BigDecimal("100.01"));
                                    }
                                },
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_2);
                                        setServiceId(petServiceId_2_2);
                                        setServiceTime(220);
                                        setStartTime(110L);
                                        setEndTime(430L);
                                        setServicePrice(new BigDecimal("220.01"));
                                    }
                                })));
        service.syncNewestTimeAndPrice(petDetails, serviceMap, scopeTypePriceMap, scopeTypeTimeMap);
        assertEquals(10, petDetails.get(petId_1).get(0).getStartTime());
        assertEquals(120, petDetails.get(petId_1).get(0).getEndTime());
        assertEquals(110, petDetails.get(petId_1).get(0).getServiceTime());
        assertEquals(0, petDetails.get(petId_1).get(0).getServicePrice().compareTo(new BigDecimal("110.01")));
        assertEquals(120, petDetails.get(petId_1).get(1).getStartTime());
        assertEquals(240, petDetails.get(petId_1).get(1).getEndTime());
        assertEquals(120, petDetails.get(petId_1).get(1).getServiceTime());
        assertEquals(0, petDetails.get(petId_1).get(1).getServicePrice().compareTo(new BigDecimal("120.01")));

        assertEquals(10, petDetails.get(petId_2).get(0).getStartTime());
        assertEquals(225, petDetails.get(petId_2).get(0).getEndTime());
        assertEquals(215, petDetails.get(petId_2).get(0).getServiceTime());
        assertEquals(0, petDetails.get(petId_2).get(0).getServicePrice().compareTo(new BigDecimal("215.01")));
        assertEquals(ServiceOverrideType.CLIENT, petDetails.get(petId_2).get(0).getPriceOverrideType());
        assertEquals(225, petDetails.get(petId_2).get(1).getStartTime());
        assertEquals(445, petDetails.get(petId_2).get(1).getEndTime());
        assertEquals(220, petDetails.get(petId_2).get(1).getServiceTime());
        assertEquals(0, petDetails.get(petId_2).get(1).getServicePrice().compareTo(new BigDecimal("220.01")));
    }

    @Test
    void isAllPetsStartAtSameTime() {
        List<GroomingPetInfoDetailDTO> petDetailList = List.of(
                new GroomingPetInfoDetailDTO() {
                    {
                        setPetId(1);
                    }
                },
                new GroomingPetInfoDetailDTO() {
                    {
                        setPetId(2);
                        setGroomingPetServiceDTOS(List.of(new GroomingPetServiceDTO() {
                            {
                                setStartTime(100L);
                            }
                        }));
                    }
                },
                new GroomingPetInfoDetailDTO() {
                    {
                        setPetId(3);
                        setGroomingPetServiceDTOS(List.of(
                                new GroomingPetServiceDTO() {
                                    {
                                        setStartTime(100L);
                                    }
                                },
                                new GroomingPetServiceDTO() {
                                    {
                                        setStartTime(200L);
                                    }
                                }));
                    }
                });
        assertTrue(service.isAllPetsStartAtSameTime(petDetailList));
        petDetailList.get(2).getGroomingPetServiceDTOS().get(0).setStartTime(110L);
        assertFalse(service.isAllPetsStartAtSameTime(petDetailList));
    }

    @Test
    void isAllPetsStartAtSameTimeV2() {
        Map<Integer, List<MoeGroomingPetDetail>> petDetails = Map.of(
                1, List.of(),
                2,
                        List.of(new MoeGroomingPetDetail() {
                            {
                                setStartTime(100L);
                            }
                        }),
                3,
                        List.of(
                                new MoeGroomingPetDetail() {
                                    {
                                        setStartTime(100L);
                                    }
                                },
                                new MoeGroomingPetDetail() {
                                    {
                                        setStartTime(200L);
                                    }
                                }));
        assertTrue(service.isAllPetsStartAtSameTimeV2(petDetails));
        petDetails.get(3).get(0).setStartTime(110L);
        assertFalse(service.isAllPetsStartAtSameTimeV2(petDetails));
    }

    @Test
    void reBuildPetDetail() {
        Integer petId_1 = 10;
        Integer petId_2 = 20;
        Map<Integer, List<MoeGroomingPetDetail>> petDetailMap = Map.of(
                petId_1,
                        List.of(
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_1);
                                        setStartTime(100L);
                                        setEndTime(200L);
                                        setServiceTime(100);
                                    }
                                },
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_1);
                                        setStartTime(200L);
                                        setEndTime(300L);
                                        setServiceTime(100);
                                    }
                                }),
                petId_2,
                        List.of(
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_2);
                                        setStartTime(100L);
                                        setEndTime(150L);
                                        setServiceTime(50);
                                    }
                                },
                                new MoeGroomingPetDetail() {
                                    {
                                        setPetId(petId_2);
                                        setStartTime(150L);
                                        setEndTime(200L);
                                        setServiceTime(50);
                                    }
                                }));
        List<MoeGroomingPetDetail> result = service.reBuildPetDetail(petDetailMap, "", 200, true);
        Map<Integer, List<MoeGroomingPetDetail>> resultMap =
                result.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId));
        assertEquals(2, resultMap.size());
        assertEquals(2, resultMap.get(petId_1).size());
        assertEquals(200, resultMap.get(petId_1).get(0).getStartTime());
        assertEquals(300, resultMap.get(petId_1).get(0).getEndTime());
        assertEquals(300, resultMap.get(petId_1).get(1).getStartTime());
        assertEquals(400, resultMap.get(petId_1).get(1).getEndTime());
        assertEquals(2, resultMap.get(petId_2).size());
        assertEquals(200, resultMap.get(petId_2).get(0).getStartTime());
        assertEquals(250, resultMap.get(petId_2).get(0).getEndTime());
        assertEquals(250, resultMap.get(petId_2).get(1).getStartTime());
        assertEquals(300, resultMap.get(petId_2).get(1).getEndTime());

        result = service.reBuildPetDetail(petDetailMap, "", 200, false);
        resultMap = result.stream().collect(Collectors.groupingBy(MoeGroomingPetDetail::getPetId));
        assertEquals(2, resultMap.size());
        assertEquals(2, resultMap.get(petId_1).size());
        assertEquals(2, resultMap.get(petId_2).size());
    }

    @Test
    void diffGroomingWithSinglePetMultiPet() {
        Integer petId = 10;
        Pair<List<Integer>, List<Integer>> result = service.diffGroomingWithSinglePetMultiPet(Map.of(), petId);
        assertTrue(result.getKey().isEmpty());
        assertTrue(result.getValue().isEmpty());
        Map<Integer, com.moego.lib.utils.model.Pair<List<MoeGroomingPetDetail>, List<EvaluationServiceDetail>>>
                petDetailMap = Map.of(
                        1,
                                com.moego.lib.utils.model.Pair.of(
                                        List.of(
                                                new MoeGroomingPetDetail() {
                                                    {
                                                        setGroomingId(1);
                                                        setPetId(petId);
                                                    }
                                                },
                                                new MoeGroomingPetDetail() {
                                                    {
                                                        setGroomingId(1);
                                                        setPetId(20);
                                                    }
                                                }),
                                        List.of()),
                        2,
                                com.moego.lib.utils.model.Pair.of(
                                        List.of(new MoeGroomingPetDetail() {
                                            {
                                                setGroomingId(2);
                                                setPetId(petId);
                                            }
                                        }),
                                        List.of()),
                        3,
                                com.moego.lib.utils.model.Pair.of(
                                        List.of(
                                                new MoeGroomingPetDetail() {
                                                    {
                                                        setGroomingId(3);
                                                        setPetId(30);
                                                    }
                                                },
                                                new MoeGroomingPetDetail() {
                                                    {
                                                        setGroomingId(3);
                                                        setPetId(petId);
                                                    }
                                                }),
                                        List.of()),
                        4,
                                com.moego.lib.utils.model.Pair.of(
                                        List.of(new MoeGroomingPetDetail() {
                                            {
                                                setGroomingId(4);
                                                setPetId(30);
                                            }
                                        }),
                                        List.of()));
        result = service.diffGroomingWithSinglePetMultiPet(petDetailMap, petId);
        assertEquals(1, result.getKey().size());
        assertEquals(2, result.getValue().size());
    }
}
