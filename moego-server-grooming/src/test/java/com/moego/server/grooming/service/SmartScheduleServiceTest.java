package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.google.common.collect.ImmutableList;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.service.utils.SmartScheduleUtil;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SmartScheduleServiceTest {

    @Test
    public void buildFreeTimeSlots_Return_OnFullDay_WithoutExistingService() {
        TimeSlot staffHour = TimeSlot.builder().start(100).end(900).build();
        List<TimeSlot> result = SmartScheduleService.buildFreeTimeSlots(ImmutableList.of(), staffHour);
        List<TimeSlot> expected = Arrays.asList(TimeSlot.builder()
                .start(100)
                .end(900)
                .beforeApptId(-1)
                .beforeServiceId(-1)
                .afterApptId(-1)
                .afterServiceId(-1)
                .build());
        assertEquals(expected, result);
        List<TimeRangeDto> obTimes = new ArrayList<>();
        obTimes.add(new TimeRangeDto(80, 500));
        obTimes.add(new TimeRangeDto(600, 800));
        obTimes.add(new TimeRangeDto(850, 900));
        List<TimeSlot> newResult = SmartScheduleUtil.filterNonWorkingTime(result, obTimes);
        System.out.println(newResult);
        // 100-500, 600-800,860-900
    }

    @Test
    public void buildFreeTimeSlots_Return_2Slots_BeforeAndAfterSingleService() {
        TimeSlot staffHour = TimeSlot.builder().start(100).end(900).build();
        MoeGroomingPetDetail petDetail = new MoeGroomingPetDetail();
        petDetail.setStartTime(300L);
        petDetail.setEndTime(500L);
        petDetail.setServiceId(111);
        petDetail.setGroomingId(222);

        List<TimeSlot> result = SmartScheduleService.buildFreeTimeSlots(ImmutableList.of(petDetail), staffHour);
        List<TimeSlot> expected = Arrays.asList(
                TimeSlot.builder()
                        .start(100)
                        .end(300)
                        .beforeApptId(-1)
                        .beforeServiceId(-1)
                        .afterApptId(222)
                        .afterServiceId(111)
                        .build(),
                TimeSlot.builder()
                        .start(500)
                        .end(900)
                        .beforeApptId(222)
                        .beforeServiceId(111)
                        .afterApptId(-1)
                        .afterServiceId(-1)
                        .build());

        assertEquals(expected, result);
    }

    @Test
    public void buildFreeTimeSlots_Return_5Slots_With4ServiceGiven() {
        TimeSlot staffHour = TimeSlot.builder().start(100).end(900).build();
        MoeGroomingPetDetail s1 = new MoeGroomingPetDetail();
        s1.setStartTime(200L);
        s1.setEndTime(250L);
        s1.setServiceId(111);
        s1.setGroomingId(221);

        MoeGroomingPetDetail s2 = new MoeGroomingPetDetail();
        s2.setStartTime(300L);
        s2.setEndTime(350L);
        s2.setServiceId(112);
        s2.setGroomingId(222);

        MoeGroomingPetDetail s3 = new MoeGroomingPetDetail();
        s3.setStartTime(400L);
        s3.setEndTime(450L);
        s3.setServiceId(113);
        s3.setGroomingId(223);

        MoeGroomingPetDetail s4 = new MoeGroomingPetDetail();
        s4.setStartTime(500L);
        s4.setEndTime(550L);
        s4.setServiceId(114);
        s4.setGroomingId(224);

        List<TimeSlot> result = SmartScheduleService.buildFreeTimeSlots(ImmutableList.of(s1, s2, s3, s4), staffHour);
        List<TimeSlot> expected = Arrays.asList(
                TimeSlot.builder()
                        .start(100)
                        .end(200)
                        .beforeServiceId(-1)
                        .beforeApptId(-1)
                        .afterServiceId(111)
                        .afterApptId(221)
                        .build(),
                TimeSlot.builder()
                        .start(250)
                        .end(300)
                        .beforeServiceId(111)
                        .beforeApptId(221)
                        .afterServiceId(112)
                        .afterApptId(222)
                        .build(),
                TimeSlot.builder()
                        .start(350)
                        .end(400)
                        .beforeServiceId(112)
                        .beforeApptId(222)
                        .afterServiceId(113)
                        .afterApptId(223)
                        .build(),
                TimeSlot.builder()
                        .start(450)
                        .end(500)
                        .beforeServiceId(113)
                        .beforeApptId(223)
                        .afterServiceId(114)
                        .afterApptId(224)
                        .build(),
                TimeSlot.builder()
                        .start(550)
                        .end(900)
                        .beforeServiceId(114)
                        .beforeApptId(224)
                        .afterServiceId(-1)
                        .afterApptId(-1)
                        .build());

        assertEquals(expected, result);
    }

    @Test
    public void buildFreeTimeSlots_Return_OneSlotLess_IfLastServiceEndByStaffHour() {
        TimeSlot staffHour = TimeSlot.builder().start(100).end(900).build();
        MoeGroomingPetDetail s1 = new MoeGroomingPetDetail();
        s1.setStartTime(200L);
        s1.setEndTime(250L);
        s1.setServiceId(111);
        s1.setGroomingId(221);

        MoeGroomingPetDetail s3 = new MoeGroomingPetDetail();
        s3.setStartTime(300L);
        s3.setEndTime(900L);
        s3.setServiceId(112);
        s3.setGroomingId(222);

        List<TimeSlot> result = SmartScheduleService.buildFreeTimeSlots(ImmutableList.of(s1, s3), staffHour);
        List<TimeSlot> expected = Arrays.asList(
                TimeSlot.builder()
                        .start(100)
                        .end(200)
                        .beforeApptId(-1)
                        .beforeServiceId(-1)
                        .afterServiceId(111)
                        .afterApptId(221)
                        .build(),
                TimeSlot.builder()
                        .start(250)
                        .end(300)
                        .beforeServiceId(111)
                        .beforeApptId(221)
                        .afterServiceId(112)
                        .afterApptId(222)
                        .build());

        assertEquals(expected, result);
    }

    @Test
    public void buildFreeTimeSlots_Return_Appts_WithOverlapService() {
        TimeSlot staffHour = TimeSlot.builder().start(100).end(900).build();
        MoeGroomingPetDetail s1 = new MoeGroomingPetDetail();
        s1.setStartTime(200L);
        s1.setEndTime(250L);
        s1.setServiceId(111);
        s1.setGroomingId(221);

        MoeGroomingPetDetail s2 = new MoeGroomingPetDetail();
        s2.setStartTime(300L);
        s2.setEndTime(350L);
        s2.setServiceId(112);
        s2.setGroomingId(222);

        MoeGroomingPetDetail s3 = new MoeGroomingPetDetail();
        s3.setStartTime(400L);
        s3.setEndTime(450L);
        s3.setServiceId(113);
        s3.setGroomingId(223);

        MoeGroomingPetDetail s4 = new MoeGroomingPetDetail();
        s4.setStartTime(330L);
        s4.setEndTime(550L);
        s4.setServiceId(114);
        s4.setGroomingId(224);

        List<TimeSlot> result = SmartScheduleService.buildFreeTimeSlots(ImmutableList.of(s1, s2, s4, s3), staffHour);
        List<TimeSlot> expected = Arrays.asList(
                TimeSlot.builder()
                        .start(100)
                        .end(200)
                        .beforeServiceId(-1)
                        .beforeApptId(-1)
                        .afterServiceId(111)
                        .afterApptId(221)
                        .build(),
                TimeSlot.builder()
                        .start(250)
                        .end(300)
                        .beforeServiceId(111)
                        .beforeApptId(221)
                        .afterServiceId(112)
                        .afterApptId(222)
                        .build(),
                TimeSlot.builder()
                        .start(550)
                        .end(900)
                        .beforeServiceId(114)
                        .beforeApptId(224)
                        .afterServiceId(-1)
                        .afterApptId(-1)
                        .build());

        assertEquals(expected, result);
    }

    @Test
    public void testFilterOutOb() {
        List<TimeSlot> result = Arrays.asList(
                TimeSlot.builder()
                        .start(100)
                        .end(200)
                        .beforeServiceId(-1)
                        .beforeApptId(-1)
                        .afterServiceId(111)
                        .afterApptId(221)
                        .build(),
                TimeSlot.builder()
                        .start(250)
                        .end(300)
                        .beforeServiceId(111)
                        .beforeApptId(221)
                        .afterServiceId(112)
                        .afterApptId(222)
                        .build(),
                TimeSlot.builder()
                        .start(550)
                        .end(900)
                        .beforeServiceId(114)
                        .beforeApptId(224)
                        .afterServiceId(-1)
                        .afterApptId(-1)
                        .build());

        List<TimeRangeDto> obTimes = new ArrayList<>();
        obTimes.add(new TimeRangeDto(80, 150));
        obTimes.add(new TimeRangeDto(280, 500));
        obTimes.add(new TimeRangeDto(600, 800));
        obTimes.add(new TimeRangeDto(850, 900));
        List<TimeSlot> newResult = SmartScheduleUtil.filterNonWorkingTime(result, obTimes);
        System.out.println(newResult);
        List<TimeSlot> expected = Arrays.asList(
                TimeSlot.builder()
                        .start(100)
                        .end(150)
                        .beforeServiceId(-1)
                        .beforeApptId(-1)
                        .afterServiceId(111)
                        .afterApptId(221)
                        .build(),
                TimeSlot.builder()
                        .start(280)
                        .end(300)
                        .beforeServiceId(111)
                        .beforeApptId(221)
                        .afterServiceId(112)
                        .afterApptId(222)
                        .build(),
                TimeSlot.builder()
                        .start(600)
                        .end(800)
                        .beforeServiceId(114)
                        .beforeApptId(224)
                        .afterServiceId(-1)
                        .afterApptId(-1)
                        .build(),
                TimeSlot.builder()
                        .start(850)
                        .end(900)
                        .beforeServiceId(114)
                        .beforeApptId(224)
                        .afterServiceId(-1)
                        .afterApptId(-1)
                        .build());

        assertEquals(expected, newResult);
    }

    @Test
    public void testFilterDaysByPreference() {
        SmartScheduleService smartScheduleService = new SmartScheduleService();

        // 1.preferDay包含：周日到周一
        Map<String, List<LocalDate>> testDatesMap1 = getTestDatesMap();
        Map<String, List<LocalDate>> expectedDateMap1 = getTestDatesMap();
        Integer[] preferDay1 = new Integer[] {0, 1, 2, 3, 4, 5, 6};
        smartScheduleService.filterDaysByPreference(testDatesMap1, preferDay1);
        Assertions.assertEquals(expectedDateMap1, testDatesMap1);

        // 2.preferDay不包含需要筛选的日期：周日、周一、周二
        Integer[] preferDay2 = new Integer[] {0, 1, 2};
        Map<String, List<LocalDate>> testDatesMap2 = getTestDatesMap();
        Map<String, List<LocalDate>> expectedDateMap2 = new HashMap<>();
        expectedDateMap2.put("2022-06-10", Collections.emptyList());
        expectedDateMap2.put("2022-06-17", Collections.emptyList());
        expectedDateMap2.put("2022-06-24", Collections.emptyList());
        smartScheduleService.filterDaysByPreference(testDatesMap2, preferDay2);
        Assertions.assertEquals(expectedDateMap2, testDatesMap2);

        // 3.preferDay部分包含需要筛选的日期：周四、周一、周二
        Integer[] preferDay3 = new Integer[] {4, 1, 2};
        Map<String, List<LocalDate>> testDatesMap3 = getTestDatesMap();
        Map<String, List<LocalDate>> expectedDateMap3 = new HashMap<>();
        expectedDateMap3.put("2022-06-10", Collections.singletonList(LocalDate.parse("2022-06-09")));
        expectedDateMap3.put("2022-06-17", Collections.singletonList(LocalDate.parse("2022-06-16")));
        expectedDateMap3.put("2022-06-24", Collections.singletonList(LocalDate.parse("2022-06-23")));
        smartScheduleService.filterDaysByPreference(testDatesMap3, preferDay3);
        Assertions.assertEquals(expectedDateMap3, testDatesMap3);
    }

    public Map<String, List<LocalDate>> getTestDatesMap() {
        // 筛选日期范围：周四、周五、周六
        HashMap<String, List<LocalDate>> map = new HashMap<>();
        map.put(
                "2022-06-10",
                new ArrayList<>(Arrays.asList(
                        LocalDate.parse("2022-06-10"), LocalDate.parse("2022-06-09"), LocalDate.parse("2022-06-11"))));
        map.put(
                "2022-06-17",
                new ArrayList<>(Arrays.asList(
                        LocalDate.parse("2022-06-17"), LocalDate.parse("2022-06-16"), LocalDate.parse("2022-06-18"))));
        map.put(
                "2022-06-24",
                new ArrayList<>(Arrays.asList(
                        LocalDate.parse("2022-06-24"), LocalDate.parse("2022-06-23"), LocalDate.parse("2022-06-25"))));
        return map;
    }

    @Test
    public void testFilterTimeByPreference() {
        SmartScheduleService smartScheduleService = new SmartScheduleService();
        // List<TimeSlot> timeslots, Integer[] preferTime, Integer serviceDuration

        // 1.timeslot和preferTime没有交集1
        List<TimeSlot> testSlots1 = getTestTimeslots();
        List<TimeSlot> expectedSlots1 = Collections.emptyList();
        Integer[] preferTime1 = new Integer[] {850, 950}; // 14:10 - 15:50
        smartScheduleService.filterTimeByPreference(testSlots1, preferTime1, 60);
        Assertions.assertEquals(expectedSlots1, testSlots1);

        // 2.preferTime包含所有timeslot时间范围
        List<TimeSlot> testSlots2 = getTestTimeslots();
        List<TimeSlot> expectedSlots2 = getTestTimeslots();
        Integer[] preferTime2 = new Integer[] {0, 1435}; // 00:00 - 23:55
        smartScheduleService.filterTimeByPreference(testSlots2, preferTime2, 60);
        Assertions.assertEquals(expectedSlots2, testSlots2);

        // 3.preferTime包含部分timeslot的startTime
        List<TimeSlot> testSlots3 = getTestTimeslots();
        List<TimeSlot> expectedSlots3 = new ArrayList<>();
        TimeSlot slot1 = new TimeSlot(); // 8:00 - 10:00
        slot1.setStart(480);
        slot1.setEnd(600);
        TimeSlot slot2 = new TimeSlot(); // 12:00 - 14:00
        slot2.setStart(720);
        slot2.setEnd(840);
        expectedSlots3.add(slot1);
        expectedSlots3.add(slot2);

        Integer[] preferTime3 = new Integer[] {420, 780}; // 07:00 - 13:00
        smartScheduleService.filterTimeByPreference(testSlots3, preferTime3, 60);
        Assertions.assertEquals(expectedSlots3, testSlots3);

        // 4.preferTime不包含timeslot的startTime，但与timeslot有交集，duration满足
        List<TimeSlot> testSlots4 = getTestTimeslots();
        List<TimeSlot> expectedSlots4 = new ArrayList<>();
        TimeSlot slot3 = new TimeSlot(); // 12:30 - 14:00 这个slot会被缩小范围，开始时间被设置为preferTime
        slot3.setStart(750);
        slot3.setEnd(840);
        expectedSlots4.add(slot3);

        Integer[] preferTime4 = new Integer[] {750, 900}; // 12:30 - 15:00
        smartScheduleService.filterTimeByPreference(testSlots4, preferTime4, 60);
        Assertions.assertEquals(expectedSlots4, testSlots4);

        // 5.preferTime不包含timeslot的startTime，但与timeslot有交集，duration不满足
        List<TimeSlot> testSlots5 = getTestTimeslots();
        List<TimeSlot> expectedSlots5 = Collections.emptyList();
        Integer[] preferTime5 = new Integer[] {810, 900}; // 13:30 - 15:00
        smartScheduleService.filterTimeByPreference(testSlots5, preferTime5, 60);
        Assertions.assertEquals(expectedSlots5, testSlots5);
    }

    public List<TimeSlot> getTestTimeslots() {
        List<TimeSlot> timeSlots = new ArrayList<>();
        TimeSlot slot1 = new TimeSlot(); // 8:00 - 10:00
        slot1.setStart(480);
        slot1.setEnd(600);
        TimeSlot slot2 = new TimeSlot(); // 12:00 - 14:00
        slot2.setStart(720);
        slot2.setEnd(840);
        TimeSlot slot3 = new TimeSlot(); // 16:00 - 18:00
        slot3.setStart(960);
        slot3.setEnd(1080);

        timeSlots.add(slot1);
        timeSlots.add(slot2);
        timeSlots.add(slot3);
        return timeSlots;
    }
}
