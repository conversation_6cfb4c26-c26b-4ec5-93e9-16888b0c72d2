package com.moego.server.grooming.utils;

import com.moego.server.grooming.service.intuit.utils.StringUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class QBStringUtilTest {

    @Test
    void TestReplaceInvalidString_Tabs() {
        String s = "\tab\t\tcd";
        String replaced = StringUtil.replaceInvalidString(s);
        Assertions.assertEquals("_ab__cd", replaced);
    }

    @Test
    void TestReplaceInvalidString_Newline() {
        String s = "\nab\ncd\n";
        String replaced = StringUtil.replaceInvalidString(s);
        Assertions.assertEquals("_ab_cd_", replaced);
    }

    @Test
    void TestReplaceInvalidString_Colon() {
        String s = "ab:cd:ef:";
        String replaced = StringUtil.replaceInvalidString(s);
        Assertions.assertEquals("ab_cd_ef_", replaced);
    }

    @Test
    void TestReplaceInvalidString() {
        String s = "ab:cd\tef\n";
        String replaced = StringUtil.replaceInvalidString(s);
        Assertions.assertEquals("ab_cd_ef_", replaced);
    }

    @Test
    void TestReplaceInvalidString_valid() {
        String s = "ab%s&ad*(&";
        String replaced = StringUtil.replaceInvalidString(s);
        Assertions.assertEquals(s, replaced);
    }

    @Test
    void TestReplaceInvalidString_Null() {
        String replaced = StringUtil.replaceInvalidString(null);
        Assertions.assertNull(replaced);
    }
}
