alter table moe_business_message_control add column available_emails int not null default 0 comment 'count of available emails';
alter table moe_business_message_control add column used_emails int not null default 0 comment 'count of used emails';
alter table moe_business_message_control add column locked_emails int not null default 0 comment 'count of locked emails';

CREATE TABLE `moe_marketing_email_detail`
(
  `id` bigint unsigned not null auto_increment,
  `business_id` bigint unsigned not null default 0,
  `staff_id` bigint unsigned not null default 0,
  `subject` varchar(128) not null default '' comment 'email subject',
  `content` text comment 'email content',
  `sender_name` varchar(128) not null default '' comment 'email sender name',
  `sender_email` varchar(128) not null default '' comment 'email sender email',
  `client_filter` text comment 'client filter',
  `recipient_count` int not null default 0 comment 'count of recipients',
  `delivered_count` int not null default 0 comment 'count of delivered',
  `opened_count` int not null default 0 comment 'count of opened',
  `clicked_count` int not null default 0 comment 'count of clicked',
  `replied_count` int not null default 0 comment 'count of replied',
  `status` int not null default 0 comment '1 - sent, 2 - schedule, 3 - draft',
  `sent_at` bigint not null default 0 comment 'send time, second timestamp' ,
  `attachments` text comment 'attachments, json string',
  `create_time` bigint not null default 0 comment 'record created time',
  `update_time` bigint not null default 0 comment 'record latest updated time',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_bid_sid` (`business_id`, `staff_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

CREATE TABLE `moe_message_recipient`
(
  `id`              bigint unsigned   not null auto_increment,
  `email_id`     bigint not null default 0 comment 'moe_marketing_email_detail.id',
  `business_id` bigint unsigned not null default 0,
  `mandrill_message_id` varchar(64) not null default '' comment 'message id from mandrill',
  `customer_id` bigint unsigned not null default 0,
  `recipient_name` varchar(128) not null default '',
  `recipient_email` varchar(128) not null default '',
  `send_status` int not null default 0 comment '1 - waiting, 2 - success, 3 - failed, 4 - schedule',
  `reject_reason` varchar(64) not null default '' comment 'reason for the email rejected',
  `delivered` tinyint(1) not null default 0 comment 'whether the delivery is successful',
  `opened` tinyint(1) not null default 0 comment 'whether the recipient opened the email',
  `clicked` tinyint(1) not null default 0 comment 'whether the recipient clicked the email',
  `replied` tinyint(1) not null default 0 comment 'whether the recipient replied the email',
  `replied_content` text comment 'the reply content from the recipient',
  `is_reply_read` tinyint(1) not null default 0 comment 'whether the reply is read',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_eid` (`email_id`) USING BTREE,
  KEY `idx_mmid` (`mandrill_message_id`) USING BTREE
  KEY `idx_eid_name` (`email_id`, `recipient_name`) USING BTREE,
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

CREATE TABLE `moe_marketing_email_template`
(
  `id` bigint unsigned not null auto_increment,
  `name` varchar(128) not null default '' comment 'template name',
  `subject` varchar(128) not null default '' comment 'template subject',
  `description` varchar(256) not null default '' comment 'template description',
  `content` text comment 'template content',
  `image_url` varchar(256) not null default '' comment 'template cover image url',
  `client_filter` text comment 'client filter',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;
