create table if not exists moe_payment.mm_stripe_dispute_fee
(
  id                int auto_increment primary key,
  business_id       int          default 0                 not null,
  charge_id         varchar(100) default ''                not null,
  dispute_id        varchar(100) default ''                not null,
  amount            bigint                                 not null,
  status            varchar(20)  default ''                not null comment '#StripeDisputeFeeEnum',
  reason            varchar(100) default ''                not null comment 'charge reason',
  need_refund       bool  default true                     not null comment '是否需要退款',
  created_at        timestamp    default CURRENT_TIMESTAMP not null,
  updated_at        timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
  );

CREATE UNIQUE INDEX IDX_DISPUTE_ID on moe_payment.mm_stripe_dispute_fee(dispute_id);
