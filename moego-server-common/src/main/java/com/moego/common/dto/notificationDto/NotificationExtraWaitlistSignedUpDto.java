package com.moego.common.dto.notificationDto;

import com.moego.common.enums.ServiceItemEnum;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NotificationExtraWaitlistSignedUpDto {
    private Long waitlistId;
    private Long customerId;
    private String firstName;
    private String lastName;
    private List<ServiceItemEnum> serviceItemType;
    private String startDate;
    private String endDate;
}
