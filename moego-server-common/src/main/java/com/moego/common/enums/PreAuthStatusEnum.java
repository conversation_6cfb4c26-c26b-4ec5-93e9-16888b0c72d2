package com.moego.common.enums;

public enum PreAuthStatusEnum {
    WAITING(1, "Waiting"),
    SUCCEED(2, "Succeed"),
    FAILED(3, "Failed"),
    CLOSE(4, "Close");

    public final Integer code;
    private final String desc;

    PreAuthStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String valueOf(Byte code) {
        if (code == null) {
            return "";
        }
        Integer c = Integer.valueOf(code);
        for (PreAuthStatusEnum value : PreAuthStatusEnum.values()) {
            if (value.getCode().equals(c)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public static PreAuthStatusEnum getType(int code) {
        for (PreAuthStatusEnum value : PreAuthStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("No matching type for [" + code + "]");
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
