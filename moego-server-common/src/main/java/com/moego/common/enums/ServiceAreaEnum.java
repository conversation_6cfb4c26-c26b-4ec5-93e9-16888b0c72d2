package com.moego.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum ServiceAreaEnum {
    UNKNOWN((byte) 0),
    POLYGON((byte) 1),
    ZIPCODE((byte) 2);

    private final Byte value;

    @JsonValue
    public Byte getValue() {
        return value;
    }

    public static ServiceAreaEnum fromValue(Byte value) {
        for (ServiceAreaEnum status : ServiceAreaEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
