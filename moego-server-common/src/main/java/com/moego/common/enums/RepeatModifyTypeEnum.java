package com.moego.common.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum RepeatModifyTypeEnum {
    ONLY_THIS(1),
    THIS_AND_FOLLOWING(2),
    ALL(3);

    private final Integer repeatType;

    @JsonValue
    public Integer getRepeatType() {
        return repeatType;
    }

    RepeatModifyTypeEnum(Integer repeatType) {
        this.repeatType = repeatType;
    }

    public static RepeatModifyTypeEnum getRepeatModifyTypeEnum(Integer repeatType) {
        for (RepeatModifyTypeEnum repeatModifyTypeEnum : RepeatModifyTypeEnum.values()) {
            if (repeatModifyTypeEnum.getRepeatType().equals(repeatType)) {
                return repeatModifyTypeEnum;
            }
        }
        return null;
    }
}
