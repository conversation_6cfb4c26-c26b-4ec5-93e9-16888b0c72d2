package com.moego.common.utils;

import com.moego.common.constant.Dictionary;
import com.moego.common.dto.CurrencyDto;
import com.moego.common.dto.LabelTypeDto;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TypeUtil {

    public static final String DATE_FORMAT_KEY_PREFIX = "date_format_";

    public static final String[] COUNTRIES = {
        Dictionary.COUNTRY_UNITED_STATES, Dictionary.COUNTRY_UNITED_KINGDOM, Dictionary.COUNTRY_AUSTRALIA,
    };

    // 下面list  数据库记录type，下发字段时，需要转换成label内容给前端

    @SuppressFBWarnings("MS_MUTABLE_COLLECTION_PKGPROTECT")
    public static final Map<String, String> MAP = new HashMap<String, String>() {
        {
            this.put("time_format_1", Dictionary.TIME_FORMAT_1);
            this.put("time_format_2", Dictionary.TIME_FORMAT_2);

            this.put("united_weight_1", Dictionary.UNITED_WEIGHT_1);
            this.put("united_weight_2", Dictionary.UNITED_WEIGHT_2);

            this.put("united_distance_1", Dictionary.UNITED_DISTANCE_1);
            this.put("united_distance_2", Dictionary.UNITED_DISTANCE_2);

            this.put("date_format_1", Dictionary.DATE_FORMAT_1);
            this.put("date_format_2", Dictionary.DATE_FORMAT_2);
            this.put("date_format_3", Dictionary.DATE_FORMAT_3);
            this.put("date_format_4", Dictionary.DATE_FORMAT_4);
            this.put("date_format_5", Dictionary.DATE_FORMAT_5);
            this.put("date_format_6", Dictionary.DATE_FORMAT_6);
            this.put("date_format_7", Dictionary.DATE_FORMAT_7);
            this.put("number_format_1", Dictionary.NUMBER_FORMAT_1);
            this.put("number_format_2", Dictionary.NUMBER_FORMAT_2);
            this.put("number_format_3", Dictionary.NUMBER_FORMAT_3);
            this.put("number_format_4", Dictionary.NUMBER_FORMAT_4);
            //            this.put("number_format_5", Dictionary.NUMBER_FORMAT_5);

            this.put("currency_symbol_usd", Dictionary.CURRENCY_SYMBOL_USD);
            this.put("currency_symbol_eur", Dictionary.CURRENCY_SYMBOL_EUR);
            this.put("currency_symbol_gbp", Dictionary.CURRENCY_SYMBOL_GBP);
            this.put("currency_symbol_aud", Dictionary.CURRENCY_SYMBOL_AUD);
            this.put("currency_symbol_brl", Dictionary.CURRENCY_SYMBOL_BRL);
            this.put("currency_symbol_cad", Dictionary.CURRENCY_SYMBOL_CAD);
            this.put("currency_symbol_nzd", Dictionary.CURRENCY_SYMBOL_NZD);
            this.put("currency_symbol_zar", Dictionary.CURRENCY_SYMBOL_ZAR);

            this.put("week_start_type_1", Dictionary.WEEK_START_MONDAY);
            this.put("week_start_type_2", Dictionary.WEEK_START_SUNDAY);
        }
    };

    /**
     * 获取business日期格式类型
     *
     * @param dateType
     * @param replaceStandard 是否替换为标准日期格式
     * @return
     */
    public static String getDateFormat(int dateType, boolean replaceStandard) {
        String dateFormat = TypeUtil.MAP.get(DATE_FORMAT_KEY_PREFIX + dateType);
        return replaceStandard ? dateFormat.replace("DD", "dd").replace("YYYY", "yyyy") : dateFormat;
    }

    public static String getDateTimeFormatByType(int dateType, int timeType) {
        return (TypeUtil.MAP
                        .get(DATE_FORMAT_KEY_PREFIX + dateType)
                        .replace("DD", "dd")
                        .replace("YYYY", "yyyy") + " "
                + (timeType == 1 ? "HH:mm" : "hh:mm a"));
    }

    public static final List<LabelTypeDto> TIME_FORMAT_LIST = new ArrayList<LabelTypeDto>() {
        {
            this.add(new LabelTypeDto(1, Dictionary.TIME_FORMAT_1));
            this.add(new LabelTypeDto(2, Dictionary.TIME_FORMAT_2));
        }
    };
    public static final List<LabelTypeDto> UNIT_OF_WEIGHT_LIST = new ArrayList<LabelTypeDto>() {
        {
            this.add(new LabelTypeDto(Dictionary.UNITED_WEIGHT_POUNDS_TYPE, Dictionary.UNITED_WEIGHT_1));
            this.add(new LabelTypeDto(Dictionary.UNITED_WEIGHT_KILOGRAM_TYPE, Dictionary.UNITED_WEIGHT_2));
        }
    };
    public static final List<LabelTypeDto> UNIT_OF_DISTANCE_LIST = new ArrayList<LabelTypeDto>() {
        {
            this.add(new LabelTypeDto(Dictionary.UNITED_DISTANCE_TYPE_1, Dictionary.UNITED_DISTANCE_1));
            this.add(new LabelTypeDto(Dictionary.UNITED_DISTANCE_TYPE_2, Dictionary.UNITED_DISTANCE_2));
        }
    };
    public static final List<LabelTypeDto> DATE_FORMAT_LIST = new ArrayList<LabelTypeDto>() {
        {
            this.add(new LabelTypeDto(1, Dictionary.DATE_FORMAT_1));
            this.add(new LabelTypeDto(2, Dictionary.DATE_FORMAT_2));
            this.add(new LabelTypeDto(3, Dictionary.DATE_FORMAT_3));
            this.add(new LabelTypeDto(4, Dictionary.DATE_FORMAT_4));
            this.add(new LabelTypeDto(5, Dictionary.DATE_FORMAT_5));
            this.add(new LabelTypeDto(6, Dictionary.DATE_FORMAT_6));
            this.add(new LabelTypeDto(7, Dictionary.DATE_FORMAT_7));
        }
    };

    public static final List<LabelTypeDto> NUMBER_FORMAT_LIST = new ArrayList<LabelTypeDto>() {
        {
            this.add(new LabelTypeDto(1, Dictionary.NUMBER_FORMAT_1));
            this.add(new LabelTypeDto(2, Dictionary.NUMBER_FORMAT_2));
            this.add(new LabelTypeDto(3, Dictionary.NUMBER_FORMAT_3));
            this.add(new LabelTypeDto(4, Dictionary.NUMBER_FORMAT_4));
        }
    };

    public static final List<CurrencyDto> CURRENCY_LIST = new ArrayList<CurrencyDto>() {
        {
            this.add(new CurrencyDto(Dictionary.CURRENCY_SYMBOL_USD, Dictionary.CURRENCY_CODE_USD));
            this.add(new CurrencyDto(Dictionary.CURRENCY_SYMBOL_EUR, Dictionary.CURRENCY_CODE_EUR));
            this.add(new CurrencyDto(Dictionary.CURRENCY_SYMBOL_GBP, Dictionary.CURRENCY_CODE_GBP));
            this.add(new CurrencyDto(Dictionary.CURRENCY_SYMBOL_AUD, Dictionary.CURRENCY_CODE_AUD));
            this.add(new CurrencyDto(Dictionary.CURRENCY_SYMBOL_BRL, Dictionary.CURRENCY_CODE_BRL));
            this.add(new CurrencyDto(Dictionary.CURRENCY_SYMBOL_CAD, Dictionary.CURRENCY_CODE_CAD));
            this.add(new CurrencyDto(Dictionary.CURRENCY_SYMBOL_NZD, Dictionary.CURRENCY_CODE_NZD));
            this.add(new CurrencyDto(Dictionary.CURRENCY_SYMBOL_ZAR, Dictionary.CURRENCY_CODE_ZAR));
        }
    };

    public static final List<LabelTypeDto> CALENDAR_FORMAT_LIST = new ArrayList<LabelTypeDto>() {
        {
            this.add(new LabelTypeDto(1, Dictionary.WEEK_START_MONDAY));
            this.add(new LabelTypeDto(2, Dictionary.WEEK_START_SUNDAY));
        }
    };
}
