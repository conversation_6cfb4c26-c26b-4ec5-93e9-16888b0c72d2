// @since 2023-07-05 19:43:15
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/ai_assistant/v1/business_conversation_admin.proto

package aiassistantapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/ai_assistant/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// describe system summaries params
type DescribeSystemSummariesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DescribeSystemSummariesParams) Reset() {
	*x = DescribeSystemSummariesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSystemSummariesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSystemSummariesParams) ProtoMessage() {}

func (x *DescribeSystemSummariesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSystemSummariesParams.ProtoReflect.Descriptor instead.
func (*DescribeSystemSummariesParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP(), []int{0}
}

// describe system summaries result
type DescribeSystemSummariesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business count
	BusinessCount int32 `protobuf:"varint,1,opt,name=business_count,json=businessCount,proto3" json:"business_count,omitempty"`
	// conversation count
	ConversationCount int32 `protobuf:"varint,2,opt,name=conversation_count,json=conversationCount,proto3" json:"conversation_count,omitempty"`
	// nonempty conversation count
	NonemptyConversationCount int32 `protobuf:"varint,10,opt,name=nonempty_conversation_count,json=nonemptyConversationCount,proto3" json:"nonempty_conversation_count,omitempty"`
	// adopted conversation count
	AdoptedConversationCount int32 `protobuf:"varint,3,opt,name=adopted_conversation_count,json=adoptedConversationCount,proto3" json:"adopted_conversation_count,omitempty"`
	// question count
	QuestionCount int32 `protobuf:"varint,4,opt,name=question_count,json=questionCount,proto3" json:"question_count,omitempty"`
	// total cost
	TotalCost float64 `protobuf:"fixed64,5,opt,name=total_cost,json=totalCost,proto3" json:"total_cost,omitempty"`
	// total input token
	TotalInputToken int32 `protobuf:"varint,6,opt,name=total_input_token,json=totalInputToken,proto3" json:"total_input_token,omitempty"`
	// total output token
	TotalOutputToken int32 `protobuf:"varint,7,opt,name=total_output_token,json=totalOutputToken,proto3" json:"total_output_token,omitempty"`
	// total token
	TotalToken int32 `protobuf:"varint,8,opt,name=total_token,json=totalToken,proto3" json:"total_token,omitempty"`
}

func (x *DescribeSystemSummariesResult) Reset() {
	*x = DescribeSystemSummariesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSystemSummariesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSystemSummariesResult) ProtoMessage() {}

func (x *DescribeSystemSummariesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSystemSummariesResult.ProtoReflect.Descriptor instead.
func (*DescribeSystemSummariesResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP(), []int{1}
}

func (x *DescribeSystemSummariesResult) GetBusinessCount() int32 {
	if x != nil {
		return x.BusinessCount
	}
	return 0
}

func (x *DescribeSystemSummariesResult) GetConversationCount() int32 {
	if x != nil {
		return x.ConversationCount
	}
	return 0
}

func (x *DescribeSystemSummariesResult) GetNonemptyConversationCount() int32 {
	if x != nil {
		return x.NonemptyConversationCount
	}
	return 0
}

func (x *DescribeSystemSummariesResult) GetAdoptedConversationCount() int32 {
	if x != nil {
		return x.AdoptedConversationCount
	}
	return 0
}

func (x *DescribeSystemSummariesResult) GetQuestionCount() int32 {
	if x != nil {
		return x.QuestionCount
	}
	return 0
}

func (x *DescribeSystemSummariesResult) GetTotalCost() float64 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *DescribeSystemSummariesResult) GetTotalInputToken() int32 {
	if x != nil {
		return x.TotalInputToken
	}
	return 0
}

func (x *DescribeSystemSummariesResult) GetTotalOutputToken() int32 {
	if x != nil {
		return x.TotalOutputToken
	}
	return 0
}

func (x *DescribeSystemSummariesResult) GetTotalToken() int32 {
	if x != nil {
		return x.TotalToken
	}
	return 0
}

// describe business conversation summaries params
type DescribeBusinessConversationSummariesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// order by, field name should be in BusinessConversationSummaryModel
	OrderBy *v2.OrderBy `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
}

func (x *DescribeBusinessConversationSummariesParams) Reset() {
	*x = DescribeBusinessConversationSummariesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBusinessConversationSummariesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBusinessConversationSummariesParams) ProtoMessage() {}

func (x *DescribeBusinessConversationSummariesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBusinessConversationSummariesParams.ProtoReflect.Descriptor instead.
func (*DescribeBusinessConversationSummariesParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP(), []int{2}
}

func (x *DescribeBusinessConversationSummariesParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *DescribeBusinessConversationSummariesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *DescribeBusinessConversationSummariesParams) GetOrderBy() *v2.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

// describe business conversation summaries result
type DescribeBusinessConversationSummariesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// summaries
	Summaries []*v1.BusinessConversationSummaryModel `protobuf:"bytes,1,rep,name=summaries,proto3" json:"summaries,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// business map, key is business id
	//
	// Deprecated: Do not use.
	DeprecatedBusinessMap map[int64]*structpb.Struct `protobuf:"bytes,3,rep,name=deprecated_business_map,json=deprecatedBusinessMap,proto3" json:"deprecated_business_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DescribeBusinessConversationSummariesResult) Reset() {
	*x = DescribeBusinessConversationSummariesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBusinessConversationSummariesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBusinessConversationSummariesResult) ProtoMessage() {}

func (x *DescribeBusinessConversationSummariesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBusinessConversationSummariesResult.ProtoReflect.Descriptor instead.
func (*DescribeBusinessConversationSummariesResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP(), []int{3}
}

func (x *DescribeBusinessConversationSummariesResult) GetSummaries() []*v1.BusinessConversationSummaryModel {
	if x != nil {
		return x.Summaries
	}
	return nil
}

func (x *DescribeBusinessConversationSummariesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// Deprecated: Do not use.
func (x *DescribeBusinessConversationSummariesResult) GetDeprecatedBusinessMap() map[int64]*structpb.Struct {
	if x != nil {
		return x.DeprecatedBusinessMap
	}
	return nil
}

// describe business conversations params
type DescribeBusinessConversationsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId *int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeBusinessConversationsParams) Reset() {
	*x = DescribeBusinessConversationsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBusinessConversationsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBusinessConversationsParams) ProtoMessage() {}

func (x *DescribeBusinessConversationsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBusinessConversationsParams.ProtoReflect.Descriptor instead.
func (*DescribeBusinessConversationsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP(), []int{4}
}

func (x *DescribeBusinessConversationsParams) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *DescribeBusinessConversationsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// describe business conversations result
type DescribeBusinessConversationsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversations
	Conversations []*v1.BusinessConversationModel `protobuf:"bytes,1,rep,name=conversations,proto3" json:"conversations,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// business map, key is business id
	//
	// Deprecated: Do not use.
	DeprecatedBusinessMap map[int64]*structpb.Struct `protobuf:"bytes,3,rep,name=deprecated_business_map,json=deprecatedBusinessMap,proto3" json:"deprecated_business_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// staff map, key is staff id
	//
	// Deprecated: Do not use.
	DeprecatedStaffMap map[int64]*structpb.Struct `protobuf:"bytes,4,rep,name=deprecated_staff_map,json=deprecatedStaffMap,proto3" json:"deprecated_staff_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DescribeBusinessConversationsResult) Reset() {
	*x = DescribeBusinessConversationsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeBusinessConversationsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeBusinessConversationsResult) ProtoMessage() {}

func (x *DescribeBusinessConversationsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeBusinessConversationsResult.ProtoReflect.Descriptor instead.
func (*DescribeBusinessConversationsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP(), []int{5}
}

func (x *DescribeBusinessConversationsResult) GetConversations() []*v1.BusinessConversationModel {
	if x != nil {
		return x.Conversations
	}
	return nil
}

func (x *DescribeBusinessConversationsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// Deprecated: Do not use.
func (x *DescribeBusinessConversationsResult) GetDeprecatedBusinessMap() map[int64]*structpb.Struct {
	if x != nil {
		return x.DeprecatedBusinessMap
	}
	return nil
}

// Deprecated: Do not use.
func (x *DescribeBusinessConversationsResult) GetDeprecatedStaffMap() map[int64]*structpb.Struct {
	if x != nil {
		return x.DeprecatedStaffMap
	}
	return nil
}

// get conversation questions params
type GetConversationQuestionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conversation id
	ConversationId int64 `protobuf:"varint,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id,omitempty"`
}

func (x *GetConversationQuestionsParams) Reset() {
	*x = GetConversationQuestionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConversationQuestionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationQuestionsParams) ProtoMessage() {}

func (x *GetConversationQuestionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationQuestionsParams.ProtoReflect.Descriptor instead.
func (*GetConversationQuestionsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP(), []int{6}
}

func (x *GetConversationQuestionsParams) GetConversationId() int64 {
	if x != nil {
		return x.ConversationId
	}
	return 0
}

// get conversation questions result
type GetConversationQuestionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// questions
	Questions []*v1.BusinessConversationQuestionModel `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	// staff map, key is staff id
	//
	// Deprecated: Do not use.
	DeprecatedStaffMap map[int64]*structpb.Struct `protobuf:"bytes,4,rep,name=deprecated_staff_map,json=deprecatedStaffMap,proto3" json:"deprecated_staff_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetConversationQuestionsResult) Reset() {
	*x = GetConversationQuestionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConversationQuestionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConversationQuestionsResult) ProtoMessage() {}

func (x *GetConversationQuestionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConversationQuestionsResult.ProtoReflect.Descriptor instead.
func (*GetConversationQuestionsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP(), []int{7}
}

func (x *GetConversationQuestionsResult) GetQuestions() []*v1.BusinessConversationQuestionModel {
	if x != nil {
		return x.Questions
	}
	return nil
}

// Deprecated: Do not use.
func (x *GetConversationQuestionsResult) GetDeprecatedStaffMap() map[int64]*structpb.Struct {
	if x != nil {
		return x.DeprecatedStaffMap
	}
	return nil
}

var File_moego_admin_ai_assistant_v1_business_conversation_admin_proto protoreflect.FileDescriptor

var file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDesc = []byte{
	0x0a, 0x3d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f,
	0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1f,
	0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22,
	0xb4, 0x03, 0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x1b, 0x6e, 0x6f, 0x6e, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x6e, 0x6f,
	0x6e, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x1a, 0x61, 0x64, 0x6f, 0x70, 0x74,
	0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x18, 0x61, 0x64, 0x6f,
	0x70, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xec, 0x01, 0x0a, 0x2b, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65,
	0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x37, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x01, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x62, 0x79, 0x22, 0xd4, 0x03, 0x0a, 0x2b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x09, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x69, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x9f, 0x01, 0x0a, 0x17, 0x64, 0x65, 0x70, 0x72,
	0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x63, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x15, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x70, 0x1a, 0x61, 0x0a, 0x1a, 0x44, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9e, 0x01, 0x0a,
	0x23, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a,
	0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0xb6, 0x05,
	0x0a, 0x23, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5d, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x97, 0x01, 0x0a, 0x17, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x44, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x15, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d,
	0x61, 0x70, 0x12, 0x8e, 0x01, 0x0a, 0x14, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x58, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x12, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x4d, 0x61, 0x70, 0x1a, 0x61, 0x0a, 0x1a, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5e, 0x0a, 0x17, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x49, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0xeb, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x5d, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x89, 0x01, 0x0a, 0x14, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e,
	0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x64, 0x65, 0x70,
	0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x61, 0x70, 0x1a,
	0x5e, 0x0a, 0x17, 0x44, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2d, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32,
	0x9c, 0x05, 0x0a, 0x1b, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x91, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0xb3, 0x01, 0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x69, 0x65, 0x73, 0x12, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69,
	0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x9b, 0x01, 0x0a, 0x15, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x8a,
	0x01, 0x0a, 0x23, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x69, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x61, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x69, 0x5f, 0x61, 0x73,
	0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x69, 0x61, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescOnce sync.Once
	file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescData = file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDesc
)

func file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescData)
	})
	return file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDescData
}

var file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_goTypes = []interface{}{
	(*DescribeSystemSummariesParams)(nil),               // 0: moego.admin.ai_assistant.v1.DescribeSystemSummariesParams
	(*DescribeSystemSummariesResult)(nil),               // 1: moego.admin.ai_assistant.v1.DescribeSystemSummariesResult
	(*DescribeBusinessConversationSummariesParams)(nil), // 2: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesParams
	(*DescribeBusinessConversationSummariesResult)(nil), // 3: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesResult
	(*DescribeBusinessConversationsParams)(nil),         // 4: moego.admin.ai_assistant.v1.DescribeBusinessConversationsParams
	(*DescribeBusinessConversationsResult)(nil),         // 5: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult
	(*GetConversationQuestionsParams)(nil),              // 6: moego.admin.ai_assistant.v1.GetConversationQuestionsParams
	(*GetConversationQuestionsResult)(nil),              // 7: moego.admin.ai_assistant.v1.GetConversationQuestionsResult
	nil,                                                 // 8: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesResult.DeprecatedBusinessMapEntry
	nil,                                                 // 9: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.DeprecatedBusinessMapEntry
	nil,                                                 // 10: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.DeprecatedStaffMapEntry
	nil,                                                 // 11: moego.admin.ai_assistant.v1.GetConversationQuestionsResult.DeprecatedStaffMapEntry
	(*v2.PaginationRequest)(nil),                        // 12: moego.utils.v2.PaginationRequest
	(*v2.OrderBy)(nil),                                  // 13: moego.utils.v2.OrderBy
	(*v1.BusinessConversationSummaryModel)(nil),         // 14: moego.models.ai_assistant.v1.BusinessConversationSummaryModel
	(*v2.PaginationResponse)(nil),                       // 15: moego.utils.v2.PaginationResponse
	(*v1.BusinessConversationModel)(nil),                // 16: moego.models.ai_assistant.v1.BusinessConversationModel
	(*v1.BusinessConversationQuestionModel)(nil),        // 17: moego.models.ai_assistant.v1.BusinessConversationQuestionModel
	(*structpb.Struct)(nil),                             // 18: google.protobuf.Struct
}
var file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_depIdxs = []int32{
	12, // 0: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	13, // 1: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesParams.order_by:type_name -> moego.utils.v2.OrderBy
	14, // 2: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesResult.summaries:type_name -> moego.models.ai_assistant.v1.BusinessConversationSummaryModel
	15, // 3: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	8,  // 4: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesResult.deprecated_business_map:type_name -> moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesResult.DeprecatedBusinessMapEntry
	12, // 5: moego.admin.ai_assistant.v1.DescribeBusinessConversationsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	16, // 6: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.conversations:type_name -> moego.models.ai_assistant.v1.BusinessConversationModel
	15, // 7: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	9,  // 8: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.deprecated_business_map:type_name -> moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.DeprecatedBusinessMapEntry
	10, // 9: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.deprecated_staff_map:type_name -> moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.DeprecatedStaffMapEntry
	17, // 10: moego.admin.ai_assistant.v1.GetConversationQuestionsResult.questions:type_name -> moego.models.ai_assistant.v1.BusinessConversationQuestionModel
	11, // 11: moego.admin.ai_assistant.v1.GetConversationQuestionsResult.deprecated_staff_map:type_name -> moego.admin.ai_assistant.v1.GetConversationQuestionsResult.DeprecatedStaffMapEntry
	18, // 12: moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesResult.DeprecatedBusinessMapEntry.value:type_name -> google.protobuf.Struct
	18, // 13: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.DeprecatedBusinessMapEntry.value:type_name -> google.protobuf.Struct
	18, // 14: moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult.DeprecatedStaffMapEntry.value:type_name -> google.protobuf.Struct
	18, // 15: moego.admin.ai_assistant.v1.GetConversationQuestionsResult.DeprecatedStaffMapEntry.value:type_name -> google.protobuf.Struct
	0,  // 16: moego.admin.ai_assistant.v1.BusinessConversationService.DescribeSystemSummaries:input_type -> moego.admin.ai_assistant.v1.DescribeSystemSummariesParams
	2,  // 17: moego.admin.ai_assistant.v1.BusinessConversationService.DescribeConversationSummaries:input_type -> moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesParams
	4,  // 18: moego.admin.ai_assistant.v1.BusinessConversationService.DescribeConversations:input_type -> moego.admin.ai_assistant.v1.DescribeBusinessConversationsParams
	6,  // 19: moego.admin.ai_assistant.v1.BusinessConversationService.GetConversationQuestions:input_type -> moego.admin.ai_assistant.v1.GetConversationQuestionsParams
	1,  // 20: moego.admin.ai_assistant.v1.BusinessConversationService.DescribeSystemSummaries:output_type -> moego.admin.ai_assistant.v1.DescribeSystemSummariesResult
	3,  // 21: moego.admin.ai_assistant.v1.BusinessConversationService.DescribeConversationSummaries:output_type -> moego.admin.ai_assistant.v1.DescribeBusinessConversationSummariesResult
	5,  // 22: moego.admin.ai_assistant.v1.BusinessConversationService.DescribeConversations:output_type -> moego.admin.ai_assistant.v1.DescribeBusinessConversationsResult
	7,  // 23: moego.admin.ai_assistant.v1.BusinessConversationService.GetConversationQuestions:output_type -> moego.admin.ai_assistant.v1.GetConversationQuestionsResult
	20, // [20:24] is the sub-list for method output_type
	16, // [16:20] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_init() }
func file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_init() {
	if File_moego_admin_ai_assistant_v1_business_conversation_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSystemSummariesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSystemSummariesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBusinessConversationSummariesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBusinessConversationSummariesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBusinessConversationsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeBusinessConversationsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConversationQuestionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConversationQuestionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_ai_assistant_v1_business_conversation_admin_proto = out.File
	file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_rawDesc = nil
	file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_goTypes = nil
	file_moego_admin_ai_assistant_v1_business_conversation_admin_proto_depIdxs = nil
}
