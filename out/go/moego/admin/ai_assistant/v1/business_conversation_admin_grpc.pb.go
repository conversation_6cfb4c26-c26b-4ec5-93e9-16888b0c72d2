// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/ai_assistant/v1/business_conversation_admin.proto

package aiassistantapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessConversationServiceClient is the client API for BusinessConversationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessConversationServiceClient interface {
	// describe system summaries
	DescribeSystemSummaries(ctx context.Context, in *DescribeSystemSummariesParams, opts ...grpc.CallOption) (*DescribeSystemSummariesResult, error)
	// describe business conversation summaries
	DescribeConversationSummaries(ctx context.Context, in *DescribeBusinessConversationSummariesParams, opts ...grpc.CallOption) (*DescribeBusinessConversationSummariesResult, error)
	// describe business conversations
	DescribeConversations(ctx context.Context, in *DescribeBusinessConversationsParams, opts ...grpc.CallOption) (*DescribeBusinessConversationsResult, error)
	// get conversation questions
	GetConversationQuestions(ctx context.Context, in *GetConversationQuestionsParams, opts ...grpc.CallOption) (*GetConversationQuestionsResult, error)
}

type businessConversationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessConversationServiceClient(cc grpc.ClientConnInterface) BusinessConversationServiceClient {
	return &businessConversationServiceClient{cc}
}

func (c *businessConversationServiceClient) DescribeSystemSummaries(ctx context.Context, in *DescribeSystemSummariesParams, opts ...grpc.CallOption) (*DescribeSystemSummariesResult, error) {
	out := new(DescribeSystemSummariesResult)
	err := c.cc.Invoke(ctx, "/moego.admin.ai_assistant.v1.BusinessConversationService/DescribeSystemSummaries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) DescribeConversationSummaries(ctx context.Context, in *DescribeBusinessConversationSummariesParams, opts ...grpc.CallOption) (*DescribeBusinessConversationSummariesResult, error) {
	out := new(DescribeBusinessConversationSummariesResult)
	err := c.cc.Invoke(ctx, "/moego.admin.ai_assistant.v1.BusinessConversationService/DescribeConversationSummaries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) DescribeConversations(ctx context.Context, in *DescribeBusinessConversationsParams, opts ...grpc.CallOption) (*DescribeBusinessConversationsResult, error) {
	out := new(DescribeBusinessConversationsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.ai_assistant.v1.BusinessConversationService/DescribeConversations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessConversationServiceClient) GetConversationQuestions(ctx context.Context, in *GetConversationQuestionsParams, opts ...grpc.CallOption) (*GetConversationQuestionsResult, error) {
	out := new(GetConversationQuestionsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.ai_assistant.v1.BusinessConversationService/GetConversationQuestions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessConversationServiceServer is the server API for BusinessConversationService service.
// All implementations must embed UnimplementedBusinessConversationServiceServer
// for forward compatibility
type BusinessConversationServiceServer interface {
	// describe system summaries
	DescribeSystemSummaries(context.Context, *DescribeSystemSummariesParams) (*DescribeSystemSummariesResult, error)
	// describe business conversation summaries
	DescribeConversationSummaries(context.Context, *DescribeBusinessConversationSummariesParams) (*DescribeBusinessConversationSummariesResult, error)
	// describe business conversations
	DescribeConversations(context.Context, *DescribeBusinessConversationsParams) (*DescribeBusinessConversationsResult, error)
	// get conversation questions
	GetConversationQuestions(context.Context, *GetConversationQuestionsParams) (*GetConversationQuestionsResult, error)
	mustEmbedUnimplementedBusinessConversationServiceServer()
}

// UnimplementedBusinessConversationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessConversationServiceServer struct {
}

func (UnimplementedBusinessConversationServiceServer) DescribeSystemSummaries(context.Context, *DescribeSystemSummariesParams) (*DescribeSystemSummariesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeSystemSummaries not implemented")
}
func (UnimplementedBusinessConversationServiceServer) DescribeConversationSummaries(context.Context, *DescribeBusinessConversationSummariesParams) (*DescribeBusinessConversationSummariesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeConversationSummaries not implemented")
}
func (UnimplementedBusinessConversationServiceServer) DescribeConversations(context.Context, *DescribeBusinessConversationsParams) (*DescribeBusinessConversationsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeConversations not implemented")
}
func (UnimplementedBusinessConversationServiceServer) GetConversationQuestions(context.Context, *GetConversationQuestionsParams) (*GetConversationQuestionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConversationQuestions not implemented")
}
func (UnimplementedBusinessConversationServiceServer) mustEmbedUnimplementedBusinessConversationServiceServer() {
}

// UnsafeBusinessConversationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessConversationServiceServer will
// result in compilation errors.
type UnsafeBusinessConversationServiceServer interface {
	mustEmbedUnimplementedBusinessConversationServiceServer()
}

func RegisterBusinessConversationServiceServer(s grpc.ServiceRegistrar, srv BusinessConversationServiceServer) {
	s.RegisterService(&BusinessConversationService_ServiceDesc, srv)
}

func _BusinessConversationService_DescribeSystemSummaries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeSystemSummariesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).DescribeSystemSummaries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.ai_assistant.v1.BusinessConversationService/DescribeSystemSummaries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).DescribeSystemSummaries(ctx, req.(*DescribeSystemSummariesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_DescribeConversationSummaries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeBusinessConversationSummariesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).DescribeConversationSummaries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.ai_assistant.v1.BusinessConversationService/DescribeConversationSummaries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).DescribeConversationSummaries(ctx, req.(*DescribeBusinessConversationSummariesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_DescribeConversations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeBusinessConversationsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).DescribeConversations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.ai_assistant.v1.BusinessConversationService/DescribeConversations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).DescribeConversations(ctx, req.(*DescribeBusinessConversationsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessConversationService_GetConversationQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConversationQuestionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessConversationServiceServer).GetConversationQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.ai_assistant.v1.BusinessConversationService/GetConversationQuestions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessConversationServiceServer).GetConversationQuestions(ctx, req.(*GetConversationQuestionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessConversationService_ServiceDesc is the grpc.ServiceDesc for BusinessConversationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessConversationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.ai_assistant.v1.BusinessConversationService",
	HandlerType: (*BusinessConversationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DescribeSystemSummaries",
			Handler:    _BusinessConversationService_DescribeSystemSummaries_Handler,
		},
		{
			MethodName: "DescribeConversationSummaries",
			Handler:    _BusinessConversationService_DescribeConversationSummaries_Handler,
		},
		{
			MethodName: "DescribeConversations",
			Handler:    _BusinessConversationService_DescribeConversations_Handler,
		},
		{
			MethodName: "GetConversationQuestions",
			Handler:    _BusinessConversationService_GetConversationQuestions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/ai_assistant/v1/business_conversation_admin.proto",
}
