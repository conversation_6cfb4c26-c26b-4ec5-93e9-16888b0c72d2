// @since 2-24-1-12
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/pay_ops/v1/verification_admin.proto

package payopsapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SearchAccountParams is the params for search account
type SearchAccountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *SearchAccountParams) Reset() {
	*x = SearchAccountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAccountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAccountParams) ProtoMessage() {}

func (x *SearchAccountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAccountParams.ProtoReflect.Descriptor instead.
func (*SearchAccountParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{0}
}

func (x *SearchAccountParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// update verification info params
type UpdateAccountInfoParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// company name
	CompanyName *string `protobuf:"bytes,2,opt,name=company_name,json=companyName,proto3,oneof" json:"company_name,omitempty"`
	// business profile name
	BusinessProfileName *string `protobuf:"bytes,3,opt,name=business_profile_name,json=businessProfileName,proto3,oneof" json:"business_profile_name,omitempty"`
	// business profile email
	Email *string `protobuf:"bytes,4,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// business profile url
	BusinessProfileUrl *string `protobuf:"bytes,5,opt,name=business_profile_url,json=businessProfileUrl,proto3,oneof" json:"business_profile_url,omitempty"`
	// business profile support url
	BusinessProfileSupportUrl *string `protobuf:"bytes,6,opt,name=business_profile_support_url,json=businessProfileSupportUrl,proto3,oneof" json:"business_profile_support_url,omitempty"`
	// business profile address
	BusinessProfileAddress *string `protobuf:"bytes,7,opt,name=business_profile_address,json=businessProfileAddress,proto3,oneof" json:"business_profile_address,omitempty"`
	// company address city
	CompanyAddressCity *string `protobuf:"bytes,8,opt,name=company_address_city,json=companyAddressCity,proto3,oneof" json:"company_address_city,omitempty"`
	// company address country
	CompanyAddressCountry *string `protobuf:"bytes,9,opt,name=company_address_country,json=companyAddressCountry,proto3,oneof" json:"company_address_country,omitempty"`
	// company address line1
	CompanyAddressLine1 *string `protobuf:"bytes,10,opt,name=company_address_line1,json=companyAddressLine1,proto3,oneof" json:"company_address_line1,omitempty"`
	// company address line2
	CompanyAddressLine2 *string `protobuf:"bytes,11,opt,name=company_address_line2,json=companyAddressLine2,proto3,oneof" json:"company_address_line2,omitempty"`
	// company address postal code
	CompanyAddressPostalCode *string `protobuf:"bytes,12,opt,name=company_address_postal_code,json=companyAddressPostalCode,proto3,oneof" json:"company_address_postal_code,omitempty"`
	// company address state
	CompanyAddressState *string `protobuf:"bytes,13,opt,name=company_address_state,json=companyAddressState,proto3,oneof" json:"company_address_state,omitempty"`
	// company phone
	CompanyPhone *string `protobuf:"bytes,14,opt,name=company_phone,json=companyPhone,proto3,oneof" json:"company_phone,omitempty"`
	// business profile number
	BusinessProfileSupportPhone *string `protobuf:"bytes,15,opt,name=business_profile_support_phone,json=businessProfileSupportPhone,proto3,oneof" json:"business_profile_support_phone,omitempty"`
	// employer identification number, tax id
	TaxId *string `protobuf:"bytes,16,opt,name=tax_id,json=taxId,proto3,oneof" json:"tax_id,omitempty"`
	// industry
	Industry *string `protobuf:"bytes,17,opt,name=industry,proto3,oneof" json:"industry,omitempty"`
	// company verification document
	CompanyVerificationDocument *string `protobuf:"bytes,18,opt,name=company_verification_document,json=companyVerificationDocument,proto3,oneof" json:"company_verification_document,omitempty"`
	// business registration file
	SettingsPaymentStatementDescriptor *string `protobuf:"bytes,19,opt,name=settings_payment_statement_descriptor,json=settingsPaymentStatementDescriptor,proto3,oneof" json:"settings_payment_statement_descriptor,omitempty"`
	// statement descriptor prefix
	SettingsCardPaymentsStatementDescriptorPrefix *string `protobuf:"bytes,20,opt,name=settings_card_payments_statement_descriptor_prefix,json=settingsCardPaymentsStatementDescriptorPrefix,proto3,oneof" json:"settings_card_payments_statement_descriptor_prefix,omitempty"`
	// business type
	BusinessType *v1.StripeBusinessType `protobuf:"varint,21,opt,name=business_type,json=businessType,proto3,enum=moego.models.pay_ops.v1.StripeBusinessType,oneof" json:"business_type,omitempty"`
	// company struct
	CompanyStruct *v1.StripeCompanyStruct `protobuf:"varint,22,opt,name=company_struct,json=companyStruct,proto3,enum=moego.models.pay_ops.v1.StripeCompanyStruct,oneof" json:"company_struct,omitempty"`
}

func (x *UpdateAccountInfoParams) Reset() {
	*x = UpdateAccountInfoParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountInfoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountInfoParams) ProtoMessage() {}

func (x *UpdateAccountInfoParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountInfoParams.ProtoReflect.Descriptor instead.
func (*UpdateAccountInfoParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateAccountInfoParams) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyName() string {
	if x != nil && x.CompanyName != nil {
		return *x.CompanyName
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetBusinessProfileName() string {
	if x != nil && x.BusinessProfileName != nil {
		return *x.BusinessProfileName
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetBusinessProfileUrl() string {
	if x != nil && x.BusinessProfileUrl != nil {
		return *x.BusinessProfileUrl
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetBusinessProfileSupportUrl() string {
	if x != nil && x.BusinessProfileSupportUrl != nil {
		return *x.BusinessProfileSupportUrl
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetBusinessProfileAddress() string {
	if x != nil && x.BusinessProfileAddress != nil {
		return *x.BusinessProfileAddress
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyAddressCity() string {
	if x != nil && x.CompanyAddressCity != nil {
		return *x.CompanyAddressCity
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyAddressCountry() string {
	if x != nil && x.CompanyAddressCountry != nil {
		return *x.CompanyAddressCountry
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyAddressLine1() string {
	if x != nil && x.CompanyAddressLine1 != nil {
		return *x.CompanyAddressLine1
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyAddressLine2() string {
	if x != nil && x.CompanyAddressLine2 != nil {
		return *x.CompanyAddressLine2
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyAddressPostalCode() string {
	if x != nil && x.CompanyAddressPostalCode != nil {
		return *x.CompanyAddressPostalCode
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyAddressState() string {
	if x != nil && x.CompanyAddressState != nil {
		return *x.CompanyAddressState
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyPhone() string {
	if x != nil && x.CompanyPhone != nil {
		return *x.CompanyPhone
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetBusinessProfileSupportPhone() string {
	if x != nil && x.BusinessProfileSupportPhone != nil {
		return *x.BusinessProfileSupportPhone
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetTaxId() string {
	if x != nil && x.TaxId != nil {
		return *x.TaxId
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetIndustry() string {
	if x != nil && x.Industry != nil {
		return *x.Industry
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetCompanyVerificationDocument() string {
	if x != nil && x.CompanyVerificationDocument != nil {
		return *x.CompanyVerificationDocument
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetSettingsPaymentStatementDescriptor() string {
	if x != nil && x.SettingsPaymentStatementDescriptor != nil {
		return *x.SettingsPaymentStatementDescriptor
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetSettingsCardPaymentsStatementDescriptorPrefix() string {
	if x != nil && x.SettingsCardPaymentsStatementDescriptorPrefix != nil {
		return *x.SettingsCardPaymentsStatementDescriptorPrefix
	}
	return ""
}

func (x *UpdateAccountInfoParams) GetBusinessType() v1.StripeBusinessType {
	if x != nil && x.BusinessType != nil {
		return *x.BusinessType
	}
	return v1.StripeBusinessType(0)
}

func (x *UpdateAccountInfoParams) GetCompanyStruct() v1.StripeCompanyStruct {
	if x != nil && x.CompanyStruct != nil {
		return *x.CompanyStruct
	}
	return v1.StripeCompanyStruct(0)
}

// update account info result
type UpdateAccountInfoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *UpdateAccountInfoResult) Reset() {
	*x = UpdateAccountInfoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountInfoResult) ProtoMessage() {}

func (x *UpdateAccountInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountInfoResult.ProtoReflect.Descriptor instead.
func (*UpdateAccountInfoResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateAccountInfoResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// update account person
type UpdateAccountPersonParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first name
	FirstName *string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3,oneof" json:"first_name,omitempty"`
	// last name
	LastName *string `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3,oneof" json:"last_name,omitempty"`
	// email address
	EmailAddress *string `protobuf:"bytes,3,opt,name=email_address,json=emailAddress,proto3,oneof" json:"email_address,omitempty"`
	// maiden name
	MaidenName *string `protobuf:"bytes,4,opt,name=maiden_name,json=maidenName,proto3,oneof" json:"maiden_name,omitempty"`
	// job title
	JobTitle *string `protobuf:"bytes,5,opt,name=job_title,json=jobTitle,proto3,oneof" json:"job_title,omitempty"`
	// dob, date of birth
	DateOfBirth int64 `protobuf:"varint,6,opt,name=date_of_birth,json=dateOfBirth,proto3" json:"date_of_birth,omitempty"`
	// phone number
	Phone *string `protobuf:"bytes,7,opt,name=phone,proto3,oneof" json:"phone,omitempty"`
	// home address city
	HomeAddressCity *string `protobuf:"bytes,8,opt,name=home_address_city,json=homeAddressCity,proto3,oneof" json:"home_address_city,omitempty"`
	// company address country
	HomeAddressCountry *string `protobuf:"bytes,9,opt,name=home_address_country,json=homeAddressCountry,proto3,oneof" json:"home_address_country,omitempty"`
	// company address line1
	HomeAddressLine1 *string `protobuf:"bytes,10,opt,name=home_address_line1,json=homeAddressLine1,proto3,oneof" json:"home_address_line1,omitempty"`
	// company address line2
	HomeAddressLine2 *string `protobuf:"bytes,11,opt,name=home_address_line2,json=homeAddressLine2,proto3,oneof" json:"home_address_line2,omitempty"`
	// company address postal code
	HomeAddressPostalCode *string `protobuf:"bytes,12,opt,name=home_address_postal_code,json=homeAddressPostalCode,proto3,oneof" json:"home_address_postal_code,omitempty"`
	// company address state
	HomeAddressState *string `protobuf:"bytes,13,opt,name=home_address_state,json=homeAddressState,proto3,oneof" json:"home_address_state,omitempty"`
	// ssn last 4
	SsnLast4 *string `protobuf:"bytes,14,opt,name=ssn_last4,json=ssnLast4,proto3,oneof" json:"ssn_last4,omitempty"`
	// percent ownership of the business
	PercentOwnership *float64 `protobuf:"fixed64,15,opt,name=percent_ownership,json=percentOwnership,proto3,oneof" json:"percent_ownership,omitempty"`
	// identity document
	IdentityDocument *string `protobuf:"bytes,16,opt,name=identity_document,json=identityDocument,proto3,oneof" json:"identity_document,omitempty"`
	// account id
	AccountId string `protobuf:"bytes,17,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// person id
	PersonId string `protobuf:"bytes,18,opt,name=person_id,json=personId,proto3" json:"person_id,omitempty"`
}

func (x *UpdateAccountPersonParams) Reset() {
	*x = UpdateAccountPersonParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAccountPersonParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAccountPersonParams) ProtoMessage() {}

func (x *UpdateAccountPersonParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAccountPersonParams.ProtoReflect.Descriptor instead.
func (*UpdateAccountPersonParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateAccountPersonParams) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetLastName() string {
	if x != nil && x.LastName != nil {
		return *x.LastName
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetEmailAddress() string {
	if x != nil && x.EmailAddress != nil {
		return *x.EmailAddress
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetMaidenName() string {
	if x != nil && x.MaidenName != nil {
		return *x.MaidenName
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetJobTitle() string {
	if x != nil && x.JobTitle != nil {
		return *x.JobTitle
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetDateOfBirth() int64 {
	if x != nil {
		return x.DateOfBirth
	}
	return 0
}

func (x *UpdateAccountPersonParams) GetPhone() string {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetHomeAddressCity() string {
	if x != nil && x.HomeAddressCity != nil {
		return *x.HomeAddressCity
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetHomeAddressCountry() string {
	if x != nil && x.HomeAddressCountry != nil {
		return *x.HomeAddressCountry
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetHomeAddressLine1() string {
	if x != nil && x.HomeAddressLine1 != nil {
		return *x.HomeAddressLine1
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetHomeAddressLine2() string {
	if x != nil && x.HomeAddressLine2 != nil {
		return *x.HomeAddressLine2
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetHomeAddressPostalCode() string {
	if x != nil && x.HomeAddressPostalCode != nil {
		return *x.HomeAddressPostalCode
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetHomeAddressState() string {
	if x != nil && x.HomeAddressState != nil {
		return *x.HomeAddressState
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetSsnLast4() string {
	if x != nil && x.SsnLast4 != nil {
		return *x.SsnLast4
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetPercentOwnership() float64 {
	if x != nil && x.PercentOwnership != nil {
		return *x.PercentOwnership
	}
	return 0
}

func (x *UpdateAccountPersonParams) GetIdentityDocument() string {
	if x != nil && x.IdentityDocument != nil {
		return *x.IdentityDocument
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *UpdateAccountPersonParams) GetPersonId() string {
	if x != nil {
		return x.PersonId
	}
	return ""
}

// get stripe enums result
type GetStripeEnumResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe company struct list
	StripeCompanyStruct []string `protobuf:"bytes,1,rep,name=stripe_company_struct,json=stripeCompanyStruct,proto3" json:"stripe_company_struct,omitempty"`
	// stripe business type list
	StripeBusinessType []string `protobuf:"bytes,2,rep,name=stripe_business_type,json=stripeBusinessType,proto3" json:"stripe_business_type,omitempty"`
}

func (x *GetStripeEnumResult) Reset() {
	*x = GetStripeEnumResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStripeEnumResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStripeEnumResult) ProtoMessage() {}

func (x *GetStripeEnumResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStripeEnumResult.ProtoReflect.Descriptor instead.
func (*GetStripeEnumResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{4}
}

func (x *GetStripeEnumResult) GetStripeCompanyStruct() []string {
	if x != nil {
		return x.StripeCompanyStruct
	}
	return nil
}

func (x *GetStripeEnumResult) GetStripeBusinessType() []string {
	if x != nil {
		return x.StripeBusinessType
	}
	return nil
}

// set default external account params
type SetDefaultExternalAccountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe account id
	StripeAccountId string `protobuf:"bytes,1,opt,name=stripe_account_id,json=stripeAccountId,proto3" json:"stripe_account_id,omitempty"`
	// stripe bank account id
	StripeBankAccountId string `protobuf:"bytes,2,opt,name=stripe_bank_account_id,json=stripeBankAccountId,proto3" json:"stripe_bank_account_id,omitempty"`
}

func (x *SetDefaultExternalAccountParams) Reset() {
	*x = SetDefaultExternalAccountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDefaultExternalAccountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDefaultExternalAccountParams) ProtoMessage() {}

func (x *SetDefaultExternalAccountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDefaultExternalAccountParams.ProtoReflect.Descriptor instead.
func (*SetDefaultExternalAccountParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{5}
}

func (x *SetDefaultExternalAccountParams) GetStripeAccountId() string {
	if x != nil {
		return x.StripeAccountId
	}
	return ""
}

func (x *SetDefaultExternalAccountParams) GetStripeBankAccountId() string {
	if x != nil {
		return x.StripeBankAccountId
	}
	return ""
}

// set default external account result
type SetDefaultExternalAccountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe bank account id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SetDefaultExternalAccountResult) Reset() {
	*x = SetDefaultExternalAccountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetDefaultExternalAccountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDefaultExternalAccountResult) ProtoMessage() {}

func (x *SetDefaultExternalAccountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDefaultExternalAccountResult.ProtoReflect.Descriptor instead.
func (*SetDefaultExternalAccountResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{6}
}

func (x *SetDefaultExternalAccountResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// delete external account params
type DeleteExternalAccountParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe account id
	StripeAccountId string `protobuf:"bytes,1,opt,name=stripe_account_id,json=stripeAccountId,proto3" json:"stripe_account_id,omitempty"`
	// stripe bank account id
	StripeBankAccountId string `protobuf:"bytes,2,opt,name=stripe_bank_account_id,json=stripeBankAccountId,proto3" json:"stripe_bank_account_id,omitempty"`
}

func (x *DeleteExternalAccountParams) Reset() {
	*x = DeleteExternalAccountParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExternalAccountParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExternalAccountParams) ProtoMessage() {}

func (x *DeleteExternalAccountParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExternalAccountParams.ProtoReflect.Descriptor instead.
func (*DeleteExternalAccountParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteExternalAccountParams) GetStripeAccountId() string {
	if x != nil {
		return x.StripeAccountId
	}
	return ""
}

func (x *DeleteExternalAccountParams) GetStripeBankAccountId() string {
	if x != nil {
		return x.StripeBankAccountId
	}
	return ""
}

// delete external account result
type DeleteExternalAccountResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe bank account id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteExternalAccountResult) Reset() {
	*x = DeleteExternalAccountResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExternalAccountResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExternalAccountResult) ProtoMessage() {}

func (x *DeleteExternalAccountResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExternalAccountResult.ProtoReflect.Descriptor instead.
func (*DeleteExternalAccountResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteExternalAccountResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_moego_admin_pay_ops_v1_verification_admin_proto protoreflect.FileDescriptor

var file_moego_admin_pay_ops_v1_verification_admin_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x61,
	0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x22, 0x8e, 0x0f, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x30, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01,
	0x48, 0x00, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x41, 0x0a, 0x15, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x13, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x60, 0x01, 0x48, 0x02, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x14, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff,
	0x01, 0x48, 0x03, 0x52, 0x12, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x1c, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x04, 0x52, 0x19,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x3d, 0x0a, 0x18,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05,
	0x52, 0x16, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x14, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x12, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x69, 0x74, 0x79, 0x88,
	0x01, 0x01, 0x12, 0x3b, 0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x37, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08,
	0x52, 0x13, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x4c, 0x69, 0x6e, 0x65, 0x31, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65,
	0x32, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x13, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x88, 0x01,
	0x01, 0x12, 0x42, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x18, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x0b, 0x52, 0x13, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28,
	0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0c, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x50, 0x68, 0x6f, 0x6e, 0x65, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x1e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x0d, 0x52, 0x1b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x1a, 0x0a, 0x06, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x0e, 0x52, 0x05, 0x74, 0x61, 0x78, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f,
	0x0a, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x0f, 0x52, 0x08, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x47, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x48, 0x10, 0x52, 0x1b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x56, 0x0a, 0x25, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x48, 0x11, 0x52, 0x22, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x88, 0x01, 0x01,
	0x12, 0x6e, 0x0a, 0x32, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x5f,
	0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x48, 0x12, 0x52, 0x2d,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x88, 0x01, 0x01,
	0x12, 0x55, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x48, 0x13, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x58, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x14, 0x52,
	0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x88, 0x01,
	0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x42,
	0x1f, 0x0a, 0x1d, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x75, 0x72, 0x6c,
	0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x17, 0x0a,
	0x15, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x42, 0x18, 0x0a, 0x16,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x42, 0x1e, 0x0a, 0x1c, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x42, 0x21, 0x0a, 0x1f, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x69, 0x64,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x42, 0x20, 0x0a,
	0x1e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x42,
	0x28, 0x0a, 0x26, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x42, 0x35, 0x0a, 0x33, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x22, 0x29, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x22, 0xb5, 0x08, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22,
	0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0c, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x24,
	0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0a, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x08, 0x6a, 0x6f, 0x62, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f,
	0x66, 0x5f, 0x62, 0x69, 0x72, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x64,
	0x61, 0x74, 0x65, 0x4f, 0x66, 0x42, 0x69, 0x72, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x11, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x06, 0x52, 0x0f, 0x68, 0x6f, 0x6d, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43,
	0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x14, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x12, 0x68, 0x6f, 0x6d, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a,
	0x12, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x31, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x08, 0x52, 0x10, 0x68, 0x6f, 0x6d,
	0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x88, 0x01, 0x01,
	0x12, 0x31, 0x0a, 0x12, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x09, 0x52, 0x10,
	0x68, 0x6f, 0x6d, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32,
	0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x18, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0a, 0x52, 0x15, 0x68, 0x6f, 0x6d, 0x65, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x31, 0x0a, 0x12, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0b, 0x52,
	0x10, 0x68, 0x6f, 0x6d, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x73, 0x73, 0x6e, 0x5f, 0x6c, 0x61, 0x73, 0x74,
	0x34, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x0c, 0x52, 0x08, 0x73, 0x73, 0x6e, 0x4c, 0x61,
	0x73, 0x74, 0x34, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x0d, 0x52, 0x10, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x0e, 0x52, 0x10, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x49, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x69, 0x74, 0x79, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x15, 0x0a, 0x13,
	0x5f, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x31, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x42, 0x1b, 0x0a, 0x19, 0x5f, 0x68,
	0x6f, 0x6d, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x68, 0x6f, 0x6d, 0x65,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x73, 0x73, 0x6e, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x34, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x7b, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x32, 0x0a, 0x15, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13,
	0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x12, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x1f, 0x53, 0x65, 0x74, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x72,
	0x69, 0x70, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x16, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x42, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x1f, 0x53, 0x65,
	0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x94, 0x01,
	0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x35, 0x0a,
	0x11, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x32, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x16, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52,
	0x13, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x32, 0xdd, 0x05, 0x0a, 0x13, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6a, 0x0a, 0x13, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x75, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x79,
	0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x8d, 0x01, 0x0a, 0x19, 0x53, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x81, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79,
	0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x7b, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x57, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70,
	0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6f, 0x70, 0x73, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescOnce sync.Once
	file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescData = file_moego_admin_pay_ops_v1_verification_admin_proto_rawDesc
)

func file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescData)
	})
	return file_moego_admin_pay_ops_v1_verification_admin_proto_rawDescData
}

var file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_admin_pay_ops_v1_verification_admin_proto_goTypes = []interface{}{
	(*SearchAccountParams)(nil),             // 0: moego.admin.pay_ops.v1.SearchAccountParams
	(*UpdateAccountInfoParams)(nil),         // 1: moego.admin.pay_ops.v1.UpdateAccountInfoParams
	(*UpdateAccountInfoResult)(nil),         // 2: moego.admin.pay_ops.v1.UpdateAccountInfoResult
	(*UpdateAccountPersonParams)(nil),       // 3: moego.admin.pay_ops.v1.UpdateAccountPersonParams
	(*GetStripeEnumResult)(nil),             // 4: moego.admin.pay_ops.v1.GetStripeEnumResult
	(*SetDefaultExternalAccountParams)(nil), // 5: moego.admin.pay_ops.v1.SetDefaultExternalAccountParams
	(*SetDefaultExternalAccountResult)(nil), // 6: moego.admin.pay_ops.v1.SetDefaultExternalAccountResult
	(*DeleteExternalAccountParams)(nil),     // 7: moego.admin.pay_ops.v1.DeleteExternalAccountParams
	(*DeleteExternalAccountResult)(nil),     // 8: moego.admin.pay_ops.v1.DeleteExternalAccountResult
	(v1.StripeBusinessType)(0),              // 9: moego.models.pay_ops.v1.StripeBusinessType
	(v1.StripeCompanyStruct)(0),             // 10: moego.models.pay_ops.v1.StripeCompanyStruct
	(*emptypb.Empty)(nil),                   // 11: google.protobuf.Empty
	(*v1.StripeAccount)(nil),                // 12: moego.models.pay_ops.v1.StripeAccount
}
var file_moego_admin_pay_ops_v1_verification_admin_proto_depIdxs = []int32{
	9,  // 0: moego.admin.pay_ops.v1.UpdateAccountInfoParams.business_type:type_name -> moego.models.pay_ops.v1.StripeBusinessType
	10, // 1: moego.admin.pay_ops.v1.UpdateAccountInfoParams.company_struct:type_name -> moego.models.pay_ops.v1.StripeCompanyStruct
	0,  // 2: moego.admin.pay_ops.v1.VerificationService.SearchStripeAccount:input_type -> moego.admin.pay_ops.v1.SearchAccountParams
	1,  // 3: moego.admin.pay_ops.v1.VerificationService.UpdateAccountInfo:input_type -> moego.admin.pay_ops.v1.UpdateAccountInfoParams
	3,  // 4: moego.admin.pay_ops.v1.VerificationService.UpdateAccountPerson:input_type -> moego.admin.pay_ops.v1.UpdateAccountPersonParams
	11, // 5: moego.admin.pay_ops.v1.VerificationService.GetStripeEnum:input_type -> google.protobuf.Empty
	5,  // 6: moego.admin.pay_ops.v1.VerificationService.SetDefaultExternalAccount:input_type -> moego.admin.pay_ops.v1.SetDefaultExternalAccountParams
	7,  // 7: moego.admin.pay_ops.v1.VerificationService.DeleteExternalAccount:input_type -> moego.admin.pay_ops.v1.DeleteExternalAccountParams
	12, // 8: moego.admin.pay_ops.v1.VerificationService.SearchStripeAccount:output_type -> moego.models.pay_ops.v1.StripeAccount
	2,  // 9: moego.admin.pay_ops.v1.VerificationService.UpdateAccountInfo:output_type -> moego.admin.pay_ops.v1.UpdateAccountInfoResult
	2,  // 10: moego.admin.pay_ops.v1.VerificationService.UpdateAccountPerson:output_type -> moego.admin.pay_ops.v1.UpdateAccountInfoResult
	4,  // 11: moego.admin.pay_ops.v1.VerificationService.GetStripeEnum:output_type -> moego.admin.pay_ops.v1.GetStripeEnumResult
	6,  // 12: moego.admin.pay_ops.v1.VerificationService.SetDefaultExternalAccount:output_type -> moego.admin.pay_ops.v1.SetDefaultExternalAccountResult
	8,  // 13: moego.admin.pay_ops.v1.VerificationService.DeleteExternalAccount:output_type -> moego.admin.pay_ops.v1.DeleteExternalAccountResult
	8,  // [8:14] is the sub-list for method output_type
	2,  // [2:8] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_moego_admin_pay_ops_v1_verification_admin_proto_init() }
func file_moego_admin_pay_ops_v1_verification_admin_proto_init() {
	if File_moego_admin_pay_ops_v1_verification_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAccountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountInfoParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountInfoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAccountPersonParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStripeEnumResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDefaultExternalAccountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetDefaultExternalAccountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExternalAccountParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExternalAccountResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_pay_ops_v1_verification_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_pay_ops_v1_verification_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_pay_ops_v1_verification_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_pay_ops_v1_verification_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_pay_ops_v1_verification_admin_proto = out.File
	file_moego_admin_pay_ops_v1_verification_admin_proto_rawDesc = nil
	file_moego_admin_pay_ops_v1_verification_admin_proto_goTypes = nil
	file_moego_admin_pay_ops_v1_verification_admin_proto_depIdxs = nil
}
