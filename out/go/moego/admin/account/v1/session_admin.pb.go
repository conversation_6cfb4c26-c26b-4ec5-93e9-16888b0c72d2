// @since 2023-08-17 14:05:33
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/account/v1/session_admin.proto

package accountapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// describe sessions request
type DescribeSessionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*DescribeSessionsParams_Id
	//	*DescribeSessionsParams_AccountId
	Identifier isDescribeSessionsParams_Identifier `protobuf_oneof:"identifier"`
	// source
	Source *string `protobuf:"bytes,4,opt,name=source,proto3,oneof" json:"source,omitempty"`
	// only describe active sessions, deleted or expired sessions will be ignored
	OnlyActive *bool `protobuf:"varint,5,opt,name=only_active,json=onlyActive,proto3,oneof" json:"only_active,omitempty"`
	// impersonator type, optional, default is 0
	// - 0 is exclude all impersonator sessions
	// - 1 is include all impersonator sessions
	// - 2 is only query impersonator sessions
	ImpersonatorType *int32 `protobuf:"varint,6,opt,name=impersonator_type,json=impersonatorType,proto3,oneof" json:"impersonator_type,omitempty"`
	// order by, field name should be in SessionModel
	OrderBy *v2.OrderBy `protobuf:"bytes,14,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeSessionsParams) Reset() {
	*x = DescribeSessionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSessionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSessionsParams) ProtoMessage() {}

func (x *DescribeSessionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSessionsParams.ProtoReflect.Descriptor instead.
func (*DescribeSessionsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{0}
}

func (m *DescribeSessionsParams) GetIdentifier() isDescribeSessionsParams_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *DescribeSessionsParams) GetId() int64 {
	if x, ok := x.GetIdentifier().(*DescribeSessionsParams_Id); ok {
		return x.Id
	}
	return 0
}

func (x *DescribeSessionsParams) GetAccountId() int64 {
	if x, ok := x.GetIdentifier().(*DescribeSessionsParams_AccountId); ok {
		return x.AccountId
	}
	return 0
}

func (x *DescribeSessionsParams) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *DescribeSessionsParams) GetOnlyActive() bool {
	if x != nil && x.OnlyActive != nil {
		return *x.OnlyActive
	}
	return false
}

func (x *DescribeSessionsParams) GetImpersonatorType() int32 {
	if x != nil && x.ImpersonatorType != nil {
		return *x.ImpersonatorType
	}
	return 0
}

func (x *DescribeSessionsParams) GetOrderBy() *v2.OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *DescribeSessionsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type isDescribeSessionsParams_Identifier interface {
	isDescribeSessionsParams_Identifier()
}

type DescribeSessionsParams_Id struct {
	// session id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3,oneof"`
}

type DescribeSessionsParams_AccountId struct {
	// account id
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3,oneof"`
}

func (*DescribeSessionsParams_Id) isDescribeSessionsParams_Identifier() {}

func (*DescribeSessionsParams_AccountId) isDescribeSessionsParams_Identifier() {}

// describe sessions result
type DescribeSessionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sessions
	Sessions []*v1.SessionModel `protobuf:"bytes,1,rep,name=sessions,proto3" json:"sessions,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeSessionsResult) Reset() {
	*x = DescribeSessionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeSessionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeSessionsResult) ProtoMessage() {}

func (x *DescribeSessionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeSessionsResult.ProtoReflect.Descriptor instead.
func (*DescribeSessionsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{1}
}

func (x *DescribeSessionsResult) GetSessions() []*v1.SessionModel {
	if x != nil {
		return x.Sessions
	}
	return nil
}

func (x *DescribeSessionsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// batch delete sessions params
type BatchDeleteSessionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *BatchDeleteSessionsParams) Reset() {
	*x = BatchDeleteSessionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteSessionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteSessionsParams) ProtoMessage() {}

func (x *BatchDeleteSessionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteSessionsParams.ProtoReflect.Descriptor instead.
func (*BatchDeleteSessionsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{2}
}

func (x *BatchDeleteSessionsParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// batch delete sessions result
type BatchDeleteSessionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchDeleteSessionsResult) Reset() {
	*x = BatchDeleteSessionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDeleteSessionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteSessionsResult) ProtoMessage() {}

func (x *BatchDeleteSessionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteSessionsResult.ProtoReflect.Descriptor instead.
func (*BatchDeleteSessionsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{3}
}

// batch update sessions params
type BatchUpdateSessionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// max age, in units of seconds
	MaxAge *int64 `protobuf:"varint,2,opt,name=max_age,json=maxAge,proto3,oneof" json:"max_age,omitempty"`
}

func (x *BatchUpdateSessionsParams) Reset() {
	*x = BatchUpdateSessionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateSessionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateSessionsParams) ProtoMessage() {}

func (x *BatchUpdateSessionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateSessionsParams.ProtoReflect.Descriptor instead.
func (*BatchUpdateSessionsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{4}
}

func (x *BatchUpdateSessionsParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *BatchUpdateSessionsParams) GetMaxAge() int64 {
	if x != nil && x.MaxAge != nil {
		return *x.MaxAge
	}
	return 0
}

// batch update sessions result
type BatchUpdateSessionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchUpdateSessionsResult) Reset() {
	*x = BatchUpdateSessionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateSessionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateSessionsResult) ProtoMessage() {}

func (x *BatchUpdateSessionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateSessionsResult.ProtoReflect.Descriptor instead.
func (*BatchUpdateSessionsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{5}
}

// create session archive task params
type CreateSessionArchiveTaskParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start id
	StartId int64 `protobuf:"varint,1,opt,name=start_id,json=startId,proto3" json:"start_id,omitempty"`
	// end id
	EndId int64 `protobuf:"varint,2,opt,name=end_id,json=endId,proto3" json:"end_id,omitempty"`
	// step
	Step int32 `protobuf:"varint,3,opt,name=step,proto3" json:"step,omitempty"`
	// max date
	MaxDate *date.Date `protobuf:"bytes,4,opt,name=max_date,json=maxDate,proto3" json:"max_date,omitempty"`
}

func (x *CreateSessionArchiveTaskParams) Reset() {
	*x = CreateSessionArchiveTaskParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionArchiveTaskParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionArchiveTaskParams) ProtoMessage() {}

func (x *CreateSessionArchiveTaskParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionArchiveTaskParams.ProtoReflect.Descriptor instead.
func (*CreateSessionArchiveTaskParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{6}
}

func (x *CreateSessionArchiveTaskParams) GetStartId() int64 {
	if x != nil {
		return x.StartId
	}
	return 0
}

func (x *CreateSessionArchiveTaskParams) GetEndId() int64 {
	if x != nil {
		return x.EndId
	}
	return 0
}

func (x *CreateSessionArchiveTaskParams) GetStep() int32 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (x *CreateSessionArchiveTaskParams) GetMaxDate() *date.Date {
	if x != nil {
		return x.MaxDate
	}
	return nil
}

// create session archive task result
type CreateSessionArchiveTaskResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// task id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateSessionArchiveTaskResult) Reset() {
	*x = CreateSessionArchiveTaskResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSessionArchiveTaskResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSessionArchiveTaskResult) ProtoMessage() {}

func (x *CreateSessionArchiveTaskResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSessionArchiveTaskResult.ProtoReflect.Descriptor instead.
func (*CreateSessionArchiveTaskResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{7}
}

func (x *CreateSessionArchiveTaskResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// update session archive task status params
type UpdateSessionArchiveTaskStatusParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// task id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateSessionArchiveTaskStatusParams) Reset() {
	*x = UpdateSessionArchiveTaskStatusParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSessionArchiveTaskStatusParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSessionArchiveTaskStatusParams) ProtoMessage() {}

func (x *UpdateSessionArchiveTaskStatusParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSessionArchiveTaskStatusParams.ProtoReflect.Descriptor instead.
func (*UpdateSessionArchiveTaskStatusParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateSessionArchiveTaskStatusParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSessionArchiveTaskStatusParams) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// update session archive task status result
type UpdateSessionArchiveTaskStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateSessionArchiveTaskStatusResult) Reset() {
	*x = UpdateSessionArchiveTaskStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSessionArchiveTaskStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSessionArchiveTaskStatusResult) ProtoMessage() {}

func (x *UpdateSessionArchiveTaskStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_account_v1_session_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSessionArchiveTaskStatusResult.ProtoReflect.Descriptor instead.
func (*UpdateSessionArchiveTaskStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_account_v1_session_admin_proto_rawDescGZIP(), []int{9}
}

var File_moego_admin_account_v1_session_admin_proto protoreflect.FileDescriptor

var file_moego_admin_account_v1_session_admin_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbf, 0x03, 0x0a, 0x16, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x52, 0x08, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x52, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x03, 0x6d, 0x69, 0x73, 0x48, 0x01, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x79,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x69, 0x6d, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x03, 0x52, 0x10, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x37, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x04, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x22, 0x9f, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x41, 0x0a, 0x08, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x40, 0x0a, 0x19, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xe8,
	0x07, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x1b, 0x0a, 0x19,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x73, 0x0a, 0x19, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xe8, 0x07,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x07, 0x6d,
	0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x41, 0x67, 0x65, 0x88,
	0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x67, 0x65, 0x22, 0x1b,
	0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xb9, 0x01, 0x0a, 0x1e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63,
	0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22,
	0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x06, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x05, 0x65, 0x6e, 0x64,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x04, 0x73, 0x74, 0x65, 0x70, 0x12,
	0x36, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x6d, 0x61, 0x78, 0x44, 0x61, 0x74, 0x65, 0x22, 0x30, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4e, 0x0a, 0x24, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x26, 0x0a, 0x24, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x32, 0xaa, 0x05, 0x0a, 0x0e, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x72, 0x0a, 0x10, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x8a, 0x01, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72,
	0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x9c, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x7c,
	0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_account_v1_session_admin_proto_rawDescOnce sync.Once
	file_moego_admin_account_v1_session_admin_proto_rawDescData = file_moego_admin_account_v1_session_admin_proto_rawDesc
)

func file_moego_admin_account_v1_session_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_account_v1_session_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_account_v1_session_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_account_v1_session_admin_proto_rawDescData)
	})
	return file_moego_admin_account_v1_session_admin_proto_rawDescData
}

var file_moego_admin_account_v1_session_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_admin_account_v1_session_admin_proto_goTypes = []interface{}{
	(*DescribeSessionsParams)(nil),               // 0: moego.admin.account.v1.DescribeSessionsParams
	(*DescribeSessionsResult)(nil),               // 1: moego.admin.account.v1.DescribeSessionsResult
	(*BatchDeleteSessionsParams)(nil),            // 2: moego.admin.account.v1.BatchDeleteSessionsParams
	(*BatchDeleteSessionsResult)(nil),            // 3: moego.admin.account.v1.BatchDeleteSessionsResult
	(*BatchUpdateSessionsParams)(nil),            // 4: moego.admin.account.v1.BatchUpdateSessionsParams
	(*BatchUpdateSessionsResult)(nil),            // 5: moego.admin.account.v1.BatchUpdateSessionsResult
	(*CreateSessionArchiveTaskParams)(nil),       // 6: moego.admin.account.v1.CreateSessionArchiveTaskParams
	(*CreateSessionArchiveTaskResult)(nil),       // 7: moego.admin.account.v1.CreateSessionArchiveTaskResult
	(*UpdateSessionArchiveTaskStatusParams)(nil), // 8: moego.admin.account.v1.UpdateSessionArchiveTaskStatusParams
	(*UpdateSessionArchiveTaskStatusResult)(nil), // 9: moego.admin.account.v1.UpdateSessionArchiveTaskStatusResult
	(*v2.OrderBy)(nil),                           // 10: moego.utils.v2.OrderBy
	(*v2.PaginationRequest)(nil),                 // 11: moego.utils.v2.PaginationRequest
	(*v1.SessionModel)(nil),                      // 12: moego.models.account.v1.SessionModel
	(*v2.PaginationResponse)(nil),                // 13: moego.utils.v2.PaginationResponse
	(*date.Date)(nil),                            // 14: google.type.Date
}
var file_moego_admin_account_v1_session_admin_proto_depIdxs = []int32{
	10, // 0: moego.admin.account.v1.DescribeSessionsParams.order_by:type_name -> moego.utils.v2.OrderBy
	11, // 1: moego.admin.account.v1.DescribeSessionsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	12, // 2: moego.admin.account.v1.DescribeSessionsResult.sessions:type_name -> moego.models.account.v1.SessionModel
	13, // 3: moego.admin.account.v1.DescribeSessionsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	14, // 4: moego.admin.account.v1.CreateSessionArchiveTaskParams.max_date:type_name -> google.type.Date
	0,  // 5: moego.admin.account.v1.SessionService.DescribeSessions:input_type -> moego.admin.account.v1.DescribeSessionsParams
	2,  // 6: moego.admin.account.v1.SessionService.BatchDeleteSessions:input_type -> moego.admin.account.v1.BatchDeleteSessionsParams
	4,  // 7: moego.admin.account.v1.SessionService.BatchUpdateSessions:input_type -> moego.admin.account.v1.BatchUpdateSessionsParams
	6,  // 8: moego.admin.account.v1.SessionService.CreateSessionArchiveTask:input_type -> moego.admin.account.v1.CreateSessionArchiveTaskParams
	8,  // 9: moego.admin.account.v1.SessionService.UpdateSessionArchiveTaskStatus:input_type -> moego.admin.account.v1.UpdateSessionArchiveTaskStatusParams
	1,  // 10: moego.admin.account.v1.SessionService.DescribeSessions:output_type -> moego.admin.account.v1.DescribeSessionsResult
	3,  // 11: moego.admin.account.v1.SessionService.BatchDeleteSessions:output_type -> moego.admin.account.v1.BatchDeleteSessionsResult
	5,  // 12: moego.admin.account.v1.SessionService.BatchUpdateSessions:output_type -> moego.admin.account.v1.BatchUpdateSessionsResult
	7,  // 13: moego.admin.account.v1.SessionService.CreateSessionArchiveTask:output_type -> moego.admin.account.v1.CreateSessionArchiveTaskResult
	9,  // 14: moego.admin.account.v1.SessionService.UpdateSessionArchiveTaskStatus:output_type -> moego.admin.account.v1.UpdateSessionArchiveTaskStatusResult
	10, // [10:15] is the sub-list for method output_type
	5,  // [5:10] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_moego_admin_account_v1_session_admin_proto_init() }
func file_moego_admin_account_v1_session_admin_proto_init() {
	if File_moego_admin_account_v1_session_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_account_v1_session_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSessionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeSessionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteSessionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDeleteSessionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateSessionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateSessionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionArchiveTaskParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSessionArchiveTaskResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSessionArchiveTaskStatusParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_account_v1_session_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSessionArchiveTaskStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_account_v1_session_admin_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*DescribeSessionsParams_Id)(nil),
		(*DescribeSessionsParams_AccountId)(nil),
	}
	file_moego_admin_account_v1_session_admin_proto_msgTypes[4].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_account_v1_session_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_account_v1_session_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_account_v1_session_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_account_v1_session_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_account_v1_session_admin_proto = out.File
	file_moego_admin_account_v1_session_admin_proto_rawDesc = nil
	file_moego_admin_account_v1_session_admin_proto_goTypes = nil
	file_moego_admin_account_v1_session_admin_proto_depIdxs = nil
}
