// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/account/v1/account_admin.proto

package accountapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountServiceClient is the client API for AccountService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountServiceClient interface {
	// get login token
	CreateLoginToken(ctx context.Context, in *CreateLoginTokenParams, opts ...grpc.CallOption) (*CreateLoginTokenResult, error)
	// list account impersonate logs
	ListAccountImpersonateLogs(ctx context.Context, in *ListAccountImpersonateLogsParams, opts ...grpc.CallOption) (*ListAccountImpersonateLogsResult, error)
	// search account without pagination
	// if has no permission, will match email extract
	SearchAccount(ctx context.Context, in *SearchAccountParams, opts ...grpc.CallOption) (*SearchAccountResult, error)
	// describe accounts
	DescribeAccounts(ctx context.Context, in *DescribeAccountsParams, opts ...grpc.CallOption) (*DescribeAccountsResult, error)
	// describe account with companies
	DescribeAccountWithCompanies(ctx context.Context, in *DescribeAccountWithCompaniesParams, opts ...grpc.CallOption) (*DescribeAccountWithCompaniesResult, error)
	// change password
	ChangePassword(ctx context.Context, in *ChangePasswordParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// change email
	ChangeEmail(ctx context.Context, in *ChangeEmailParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// freeze account
	FreezeAccount(ctx context.Context, in *FreezeAccountParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// unfreeze account
	UnfreezeAccount(ctx context.Context, in *UnfreezeAccountParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// delete account
	DeleteAccount(ctx context.Context, in *DeleteAccountParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// recover account
	RecoverAccount(ctx context.Context, in *RecoverAccountParams, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type accountServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountServiceClient(cc grpc.ClientConnInterface) AccountServiceClient {
	return &accountServiceClient{cc}
}

func (c *accountServiceClient) CreateLoginToken(ctx context.Context, in *CreateLoginTokenParams, opts ...grpc.CallOption) (*CreateLoginTokenResult, error) {
	out := new(CreateLoginTokenResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/CreateLoginToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) ListAccountImpersonateLogs(ctx context.Context, in *ListAccountImpersonateLogsParams, opts ...grpc.CallOption) (*ListAccountImpersonateLogsResult, error) {
	out := new(ListAccountImpersonateLogsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/ListAccountImpersonateLogs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) SearchAccount(ctx context.Context, in *SearchAccountParams, opts ...grpc.CallOption) (*SearchAccountResult, error) {
	out := new(SearchAccountResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/SearchAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) DescribeAccounts(ctx context.Context, in *DescribeAccountsParams, opts ...grpc.CallOption) (*DescribeAccountsResult, error) {
	out := new(DescribeAccountsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/DescribeAccounts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) DescribeAccountWithCompanies(ctx context.Context, in *DescribeAccountWithCompaniesParams, opts ...grpc.CallOption) (*DescribeAccountWithCompaniesResult, error) {
	out := new(DescribeAccountWithCompaniesResult)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/DescribeAccountWithCompanies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) ChangePassword(ctx context.Context, in *ChangePasswordParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/ChangePassword", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) ChangeEmail(ctx context.Context, in *ChangeEmailParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/ChangeEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) FreezeAccount(ctx context.Context, in *FreezeAccountParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/FreezeAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) UnfreezeAccount(ctx context.Context, in *UnfreezeAccountParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/UnfreezeAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) DeleteAccount(ctx context.Context, in *DeleteAccountParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/DeleteAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) RecoverAccount(ctx context.Context, in *RecoverAccountParams, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.account.v1.AccountService/RecoverAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountServiceServer is the server API for AccountService service.
// All implementations must embed UnimplementedAccountServiceServer
// for forward compatibility
type AccountServiceServer interface {
	// get login token
	CreateLoginToken(context.Context, *CreateLoginTokenParams) (*CreateLoginTokenResult, error)
	// list account impersonate logs
	ListAccountImpersonateLogs(context.Context, *ListAccountImpersonateLogsParams) (*ListAccountImpersonateLogsResult, error)
	// search account without pagination
	// if has no permission, will match email extract
	SearchAccount(context.Context, *SearchAccountParams) (*SearchAccountResult, error)
	// describe accounts
	DescribeAccounts(context.Context, *DescribeAccountsParams) (*DescribeAccountsResult, error)
	// describe account with companies
	DescribeAccountWithCompanies(context.Context, *DescribeAccountWithCompaniesParams) (*DescribeAccountWithCompaniesResult, error)
	// change password
	ChangePassword(context.Context, *ChangePasswordParams) (*emptypb.Empty, error)
	// change email
	ChangeEmail(context.Context, *ChangeEmailParams) (*emptypb.Empty, error)
	// freeze account
	FreezeAccount(context.Context, *FreezeAccountParams) (*emptypb.Empty, error)
	// unfreeze account
	UnfreezeAccount(context.Context, *UnfreezeAccountParams) (*emptypb.Empty, error)
	// delete account
	DeleteAccount(context.Context, *DeleteAccountParams) (*emptypb.Empty, error)
	// recover account
	RecoverAccount(context.Context, *RecoverAccountParams) (*emptypb.Empty, error)
	mustEmbedUnimplementedAccountServiceServer()
}

// UnimplementedAccountServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountServiceServer struct {
}

func (UnimplementedAccountServiceServer) CreateLoginToken(context.Context, *CreateLoginTokenParams) (*CreateLoginTokenResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLoginToken not implemented")
}
func (UnimplementedAccountServiceServer) ListAccountImpersonateLogs(context.Context, *ListAccountImpersonateLogsParams) (*ListAccountImpersonateLogsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccountImpersonateLogs not implemented")
}
func (UnimplementedAccountServiceServer) SearchAccount(context.Context, *SearchAccountParams) (*SearchAccountResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAccount not implemented")
}
func (UnimplementedAccountServiceServer) DescribeAccounts(context.Context, *DescribeAccountsParams) (*DescribeAccountsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeAccounts not implemented")
}
func (UnimplementedAccountServiceServer) DescribeAccountWithCompanies(context.Context, *DescribeAccountWithCompaniesParams) (*DescribeAccountWithCompaniesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeAccountWithCompanies not implemented")
}
func (UnimplementedAccountServiceServer) ChangePassword(context.Context, *ChangePasswordParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePassword not implemented")
}
func (UnimplementedAccountServiceServer) ChangeEmail(context.Context, *ChangeEmailParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeEmail not implemented")
}
func (UnimplementedAccountServiceServer) FreezeAccount(context.Context, *FreezeAccountParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FreezeAccount not implemented")
}
func (UnimplementedAccountServiceServer) UnfreezeAccount(context.Context, *UnfreezeAccountParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnfreezeAccount not implemented")
}
func (UnimplementedAccountServiceServer) DeleteAccount(context.Context, *DeleteAccountParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAccount not implemented")
}
func (UnimplementedAccountServiceServer) RecoverAccount(context.Context, *RecoverAccountParams) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverAccount not implemented")
}
func (UnimplementedAccountServiceServer) mustEmbedUnimplementedAccountServiceServer() {}

// UnsafeAccountServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountServiceServer will
// result in compilation errors.
type UnsafeAccountServiceServer interface {
	mustEmbedUnimplementedAccountServiceServer()
}

func RegisterAccountServiceServer(s grpc.ServiceRegistrar, srv AccountServiceServer) {
	s.RegisterService(&AccountService_ServiceDesc, srv)
}

func _AccountService_CreateLoginToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLoginTokenParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).CreateLoginToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/CreateLoginToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).CreateLoginToken(ctx, req.(*CreateLoginTokenParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_ListAccountImpersonateLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccountImpersonateLogsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).ListAccountImpersonateLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/ListAccountImpersonateLogs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).ListAccountImpersonateLogs(ctx, req.(*ListAccountImpersonateLogsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_SearchAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAccountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).SearchAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/SearchAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).SearchAccount(ctx, req.(*SearchAccountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_DescribeAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeAccountsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).DescribeAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/DescribeAccounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).DescribeAccounts(ctx, req.(*DescribeAccountsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_DescribeAccountWithCompanies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeAccountWithCompaniesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).DescribeAccountWithCompanies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/DescribeAccountWithCompanies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).DescribeAccountWithCompanies(ctx, req.(*DescribeAccountWithCompaniesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_ChangePassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangePasswordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).ChangePassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/ChangePassword",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).ChangePassword(ctx, req.(*ChangePasswordParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_ChangeEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeEmailParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).ChangeEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/ChangeEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).ChangeEmail(ctx, req.(*ChangeEmailParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_FreezeAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeAccountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).FreezeAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/FreezeAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).FreezeAccount(ctx, req.(*FreezeAccountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_UnfreezeAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfreezeAccountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).UnfreezeAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/UnfreezeAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).UnfreezeAccount(ctx, req.(*UnfreezeAccountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_DeleteAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAccountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).DeleteAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/DeleteAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).DeleteAccount(ctx, req.(*DeleteAccountParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_RecoverAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverAccountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).RecoverAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.account.v1.AccountService/RecoverAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).RecoverAccount(ctx, req.(*RecoverAccountParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountService_ServiceDesc is the grpc.ServiceDesc for AccountService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.account.v1.AccountService",
	HandlerType: (*AccountServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLoginToken",
			Handler:    _AccountService_CreateLoginToken_Handler,
		},
		{
			MethodName: "ListAccountImpersonateLogs",
			Handler:    _AccountService_ListAccountImpersonateLogs_Handler,
		},
		{
			MethodName: "SearchAccount",
			Handler:    _AccountService_SearchAccount_Handler,
		},
		{
			MethodName: "DescribeAccounts",
			Handler:    _AccountService_DescribeAccounts_Handler,
		},
		{
			MethodName: "DescribeAccountWithCompanies",
			Handler:    _AccountService_DescribeAccountWithCompanies_Handler,
		},
		{
			MethodName: "ChangePassword",
			Handler:    _AccountService_ChangePassword_Handler,
		},
		{
			MethodName: "ChangeEmail",
			Handler:    _AccountService_ChangeEmail_Handler,
		},
		{
			MethodName: "FreezeAccount",
			Handler:    _AccountService_FreezeAccount_Handler,
		},
		{
			MethodName: "UnfreezeAccount",
			Handler:    _AccountService_UnfreezeAccount_Handler,
		},
		{
			MethodName: "DeleteAccount",
			Handler:    _AccountService_DeleteAccount_Handler,
		},
		{
			MethodName: "RecoverAccount",
			Handler:    _AccountService_RecoverAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/account/v1/account_admin.proto",
}
