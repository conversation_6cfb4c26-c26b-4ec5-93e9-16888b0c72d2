// @since 2-23-10-07
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/coupon/v1/coupon_admin.proto

package couponapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/coupon/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create new coupon params
type CreateNewCouponParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// valid month for the coupon [1 to 100]
	ValidMonth int64 `protobuf:"varint,1,opt,name=valid_month,json=validMonth,proto3" json:"valid_month,omitempty"`
	// custom name for the coupon
	CustomName string `protobuf:"bytes,2,opt,name=custom_name,json=customName,proto3" json:"custom_name,omitempty"`
	// discount percentage, an integer from 1 to 100
	// default value is 50, it can be omitted in the front-end
	PercentOff int32 `protobuf:"varint,3,opt,name=percent_off,json=percentOff,proto3" json:"percent_off,omitempty"`
	// category of coupon
	// used to distinguish which business the coupon belongs to
	BusinessCategory v1.CouponBusinessCategory `protobuf:"varint,4,opt,name=business_category,json=businessCategory,proto3,enum=moego.models.coupon.v1.CouponBusinessCategory" json:"business_category,omitempty"`
}

func (x *CreateNewCouponParams) Reset() {
	*x = CreateNewCouponParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNewCouponParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNewCouponParams) ProtoMessage() {}

func (x *CreateNewCouponParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNewCouponParams.ProtoReflect.Descriptor instead.
func (*CreateNewCouponParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{0}
}

func (x *CreateNewCouponParams) GetValidMonth() int64 {
	if x != nil {
		return x.ValidMonth
	}
	return 0
}

func (x *CreateNewCouponParams) GetCustomName() string {
	if x != nil {
		return x.CustomName
	}
	return ""
}

func (x *CreateNewCouponParams) GetPercentOff() int32 {
	if x != nil {
		return x.PercentOff
	}
	return 0
}

func (x *CreateNewCouponParams) GetBusinessCategory() v1.CouponBusinessCategory {
	if x != nil {
		return x.BusinessCategory
	}
	return v1.CouponBusinessCategory(0)
}

// create new coupon result
type CreateNewCouponResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe coupon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateNewCouponResult) Reset() {
	*x = CreateNewCouponResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateNewCouponResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNewCouponResult) ProtoMessage() {}

func (x *CreateNewCouponResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNewCouponResult.ProtoReflect.Descriptor instead.
func (*CreateNewCouponResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{1}
}

func (x *CreateNewCouponResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// create new coupon params
type CreateCouponParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies how long the discount will be in effect
	ValidMonth *int64 `protobuf:"varint,1,opt,name=valid_month,json=validMonth,proto3,oneof" json:"valid_month,omitempty"`
	// name for the coupon,can't be null
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// discount percentage, an integer from 1 to 100
	// default value is 50, it can be omitted in the front-end
	PercentOff *int32 `protobuf:"varint,3,opt,name=percent_off,json=percentOff,proto3,oneof" json:"percent_off,omitempty"`
	// category of coupon
	// used to distinguish which business the coupon belongs to
	BusinessCategory v1.CouponBusinessCategory `protobuf:"varint,4,opt,name=business_category,json=businessCategory,proto3,enum=moego.models.coupon.v1.CouponBusinessCategory" json:"business_category,omitempty"`
	// discount amount
	DiscountAmount *float64 `protobuf:"fixed64,5,opt,name=discount_amount,json=discountAmount,proto3,oneof" json:"discount_amount,omitempty"`
	// Specifies how long the discount will be in effect if used on a subscription. Defaults to once
	Duration string `protobuf:"bytes,6,opt,name=duration,proto3" json:"duration,omitempty"`
	// Limit the date range when customers can redeem this coupon
	RedeemBy *string `protobuf:"bytes,7,opt,name=redeem_by,json=redeemBy,proto3,oneof" json:"redeem_by,omitempty"`
	// Limit the total number of times this coupon can be redeemed
	MaxRedeem *int32 `protobuf:"varint,8,opt,name=max_redeem,json=maxRedeem,proto3,oneof" json:"max_redeem,omitempty"`
}

func (x *CreateCouponParams) Reset() {
	*x = CreateCouponParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCouponParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCouponParams) ProtoMessage() {}

func (x *CreateCouponParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCouponParams.ProtoReflect.Descriptor instead.
func (*CreateCouponParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{2}
}

func (x *CreateCouponParams) GetValidMonth() int64 {
	if x != nil && x.ValidMonth != nil {
		return *x.ValidMonth
	}
	return 0
}

func (x *CreateCouponParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateCouponParams) GetPercentOff() int32 {
	if x != nil && x.PercentOff != nil {
		return *x.PercentOff
	}
	return 0
}

func (x *CreateCouponParams) GetBusinessCategory() v1.CouponBusinessCategory {
	if x != nil {
		return x.BusinessCategory
	}
	return v1.CouponBusinessCategory(0)
}

func (x *CreateCouponParams) GetDiscountAmount() float64 {
	if x != nil && x.DiscountAmount != nil {
		return *x.DiscountAmount
	}
	return 0
}

func (x *CreateCouponParams) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

func (x *CreateCouponParams) GetRedeemBy() string {
	if x != nil && x.RedeemBy != nil {
		return *x.RedeemBy
	}
	return ""
}

func (x *CreateCouponParams) GetMaxRedeem() int32 {
	if x != nil && x.MaxRedeem != nil {
		return *x.MaxRedeem
	}
	return 0
}

// create new coupon result
type CreateCouponResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// stripe coupon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateCouponResult) Reset() {
	*x = CreateCouponResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCouponResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCouponResult) ProtoMessage() {}

func (x *CreateCouponResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCouponResult.ProtoReflect.Descriptor instead.
func (*CreateCouponResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{3}
}

func (x *CreateCouponResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// query coupon list params
type QueryCouponListParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Title of the coupon, supports fuzzy search
	Code *string `protobuf:"bytes,1,opt,name=code,proto3,oneof" json:"code,omitempty"`
	// Lower limit of the discount percentage, range 0 to 100
	PercentOffLeft *float64 `protobuf:"fixed64,2,opt,name=percent_off_left,json=percentOffLeft,proto3,oneof" json:"percent_off_left,omitempty"`
	// Upper limit of the discount percentage, range 0 to 100
	PercentOffRight *float64 `protobuf:"fixed64,3,opt,name=percent_off_right,json=percentOffRight,proto3,oneof" json:"percent_off_right,omitempty"`
	// 1 - valid, 0 - invalid; if null, all will be queried
	Valid *int32 `protobuf:"varint,4,opt,name=valid,proto3,oneof" json:"valid,omitempty"`
	// 0 - unassigned, 1 - assigned, 2 - used
	Status *int32 `protobuf:"varint,5,opt,name=status,proto3,oneof" json:"status,omitempty"`
	// Validity period in months, can be specified from 1 to 100
	ValidMonth *int32 `protobuf:"varint,6,opt,name=valid_month,json=validMonth,proto3,oneof" json:"valid_month,omitempty"`
	// filter by business category
	BusinessCategory *v1.CouponBusinessCategory `protobuf:"varint,7,opt,name=business_category,json=businessCategory,proto3,enum=moego.models.coupon.v1.CouponBusinessCategory,oneof" json:"business_category,omitempty"`
	// stripe coupon id
	StripeCouponId *string `protobuf:"bytes,8,opt,name=stripe_coupon_id,json=stripeCouponId,proto3,oneof" json:"stripe_coupon_id,omitempty"`
	// pagination params
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *QueryCouponListParams) Reset() {
	*x = QueryCouponListParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCouponListParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCouponListParams) ProtoMessage() {}

func (x *QueryCouponListParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCouponListParams.ProtoReflect.Descriptor instead.
func (*QueryCouponListParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{4}
}

func (x *QueryCouponListParams) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *QueryCouponListParams) GetPercentOffLeft() float64 {
	if x != nil && x.PercentOffLeft != nil {
		return *x.PercentOffLeft
	}
	return 0
}

func (x *QueryCouponListParams) GetPercentOffRight() float64 {
	if x != nil && x.PercentOffRight != nil {
		return *x.PercentOffRight
	}
	return 0
}

func (x *QueryCouponListParams) GetValid() int32 {
	if x != nil && x.Valid != nil {
		return *x.Valid
	}
	return 0
}

func (x *QueryCouponListParams) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

func (x *QueryCouponListParams) GetValidMonth() int32 {
	if x != nil && x.ValidMonth != nil {
		return *x.ValidMonth
	}
	return 0
}

func (x *QueryCouponListParams) GetBusinessCategory() v1.CouponBusinessCategory {
	if x != nil && x.BusinessCategory != nil {
		return *x.BusinessCategory
	}
	return v1.CouponBusinessCategory(0)
}

func (x *QueryCouponListParams) GetStripeCouponId() string {
	if x != nil && x.StripeCouponId != nil {
		return *x.StripeCouponId
	}
	return ""
}

func (x *QueryCouponListParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// query coupon list result
type QueryCouponListResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// coupon list
	Coupons []*v1.CouponSimpleView `protobuf:"bytes,1,rep,name=coupons,proto3" json:"coupons,omitempty"`
	// business category map
	BusinessCategoryMap map[int32]string `protobuf:"bytes,2,rep,name=business_category_map,json=businessCategoryMap,proto3" json:"business_category_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// pagination result
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *QueryCouponListResult) Reset() {
	*x = QueryCouponListResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCouponListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCouponListResult) ProtoMessage() {}

func (x *QueryCouponListResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCouponListResult.ProtoReflect.Descriptor instead.
func (*QueryCouponListResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{5}
}

func (x *QueryCouponListResult) GetCoupons() []*v1.CouponSimpleView {
	if x != nil {
		return x.Coupons
	}
	return nil
}

func (x *QueryCouponListResult) GetBusinessCategoryMap() map[int32]string {
	if x != nil {
		return x.BusinessCategoryMap
	}
	return nil
}

func (x *QueryCouponListResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// query coupon params
type QueryCouponParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// coupon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *QueryCouponParams) Reset() {
	*x = QueryCouponParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryCouponParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCouponParams) ProtoMessage() {}

func (x *QueryCouponParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCouponParams.ProtoReflect.Descriptor instead.
func (*QueryCouponParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{6}
}

func (x *QueryCouponParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// update coupon params
type UpdateCouponParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// status, Delete
	Status *int32 `protobuf:"varint,2,opt,name=status,proto3,oneof" json:"status,omitempty"`
}

func (x *UpdateCouponParams) Reset() {
	*x = UpdateCouponParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCouponParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCouponParams) ProtoMessage() {}

func (x *UpdateCouponParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCouponParams.ProtoReflect.Descriptor instead.
func (*UpdateCouponParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateCouponParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCouponParams) GetStatus() int32 {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return 0
}

// search coupon by term params
type SearchCouponByTermParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the search term
	Term string `protobuf:"bytes,1,opt,name=term,proto3" json:"term,omitempty"`
}

func (x *SearchCouponByTermParams) Reset() {
	*x = SearchCouponByTermParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCouponByTermParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCouponByTermParams) ProtoMessage() {}

func (x *SearchCouponByTermParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCouponByTermParams.ProtoReflect.Descriptor instead.
func (*SearchCouponByTermParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{8}
}

func (x *SearchCouponByTermParams) GetTerm() string {
	if x != nil {
		return x.Term
	}
	return ""
}

// search coupon result
type SearchCouponByTermResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// options
	Options []*v2.Option `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
}

func (x *SearchCouponByTermResult) Reset() {
	*x = SearchCouponByTermResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCouponByTermResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCouponByTermResult) ProtoMessage() {}

func (x *SearchCouponByTermResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCouponByTermResult.ProtoReflect.Descriptor instead.
func (*SearchCouponByTermResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{9}
}

func (x *SearchCouponByTermResult) GetOptions() []*v2.Option {
	if x != nil {
		return x.Options
	}
	return nil
}

// deleted coupon params
type DeleteCouponParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// coupon id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteCouponParams) Reset() {
	*x = DeleteCouponParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCouponParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCouponParams) ProtoMessage() {}

func (x *DeleteCouponParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCouponParams.ProtoReflect.Descriptor instead.
func (*DeleteCouponParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteCouponParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_moego_admin_coupon_v1_coupon_admin_proto protoreflect.FileDescriptor

var file_moego_admin_coupon_v1_coupon_admin_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x63, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xed, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x77, 0x43, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2a, 0x0a, 0x0b, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x22, 0x04, 0x18, 0x64, 0x28, 0x01, 0x52, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x1a, 0x04, 0x18, 0x64, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x4f,
	0x66, 0x66, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x10, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0x27, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xea, 0x03, 0x0a, 0x12, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x2f, 0x0a, 0x0b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x18, 0x64, 0x28, 0x00, 0x48,
	0x00, 0x52, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x88, 0x01, 0x01,
	0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x2f, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x01, 0x48,
	0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x88, 0x01, 0x01,
	0x12, 0x67, 0x0a, 0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x2c, 0x0a, 0x0f, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x48, 0x02, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x25, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x01, 0x18, 0x32, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x09, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x03, 0x52, 0x08, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x42, 0x79, 0x88, 0x01, 0x01,
	0x12, 0x22, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x5f, 0x6f, 0x66, 0x66, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x72, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x5f, 0x62, 0x79, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x72,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x22, 0x24, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x99, 0x05, 0x0a, 0x15,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x46,
	0x0a, 0x10, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x6c, 0x65,
	0x66, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x19,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x48, 0x01, 0x52, 0x0e, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x4c,
	0x65, 0x66, 0x74, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x01, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x12, 0x12, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59,
	0x40, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x02, 0x52, 0x0f, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x66, 0x66, 0x52, 0x69, 0x67, 0x68, 0x74, 0x88, 0x01, 0x01,
	0x12, 0x24, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x01, 0x28, 0x00, 0x48, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x02, 0x28,
	0x00, 0x48, 0x04, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2f,
	0x0a, 0x0b, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x01, 0x48, 0x05,
	0x52, 0x0a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x88, 0x01, 0x01, 0x12,
	0x60, 0x0a, 0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x48, 0x06, 0x52, 0x10, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x88, 0x01,
	0x01, 0x12, 0x2d, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x0e, 0x73,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x13, 0x0a, 0x11,
	0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x5f, 0x6c, 0x65, 0x66,
	0x74, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x66,
	0x66, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x22, 0xe2, 0x02, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x42, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x63, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x73, 0x12, 0x79, 0x0a, 0x15, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x61, 0x70,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x46, 0x0a, 0x18, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x23, 0x0a, 0x11,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x4c, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x37, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x42,
	0x79, 0x54, 0x65, 0x72, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x04, 0x74,
	0x65, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x64, 0x52, 0x04, 0x74, 0x65, 0x72, 0x6d, 0x22, 0x4c, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x30, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x24, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x32, 0xee, 0x05, 0x0a,
	0x0d, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x72,
	0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4e, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65,
	0x77, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x03, 0x88,
	0x02, 0x01, 0x12, 0x64, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5c, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x12, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x5e, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x76, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x42, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x42, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x42, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5e, 0x0a,
	0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x12, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42, 0x79, 0x0a,
	0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_coupon_v1_coupon_admin_proto_rawDescOnce sync.Once
	file_moego_admin_coupon_v1_coupon_admin_proto_rawDescData = file_moego_admin_coupon_v1_coupon_admin_proto_rawDesc
)

func file_moego_admin_coupon_v1_coupon_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_coupon_v1_coupon_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_coupon_v1_coupon_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_coupon_v1_coupon_admin_proto_rawDescData)
	})
	return file_moego_admin_coupon_v1_coupon_admin_proto_rawDescData
}

var file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_admin_coupon_v1_coupon_admin_proto_goTypes = []interface{}{
	(*CreateNewCouponParams)(nil),    // 0: moego.admin.coupon.v1.CreateNewCouponParams
	(*CreateNewCouponResult)(nil),    // 1: moego.admin.coupon.v1.CreateNewCouponResult
	(*CreateCouponParams)(nil),       // 2: moego.admin.coupon.v1.CreateCouponParams
	(*CreateCouponResult)(nil),       // 3: moego.admin.coupon.v1.CreateCouponResult
	(*QueryCouponListParams)(nil),    // 4: moego.admin.coupon.v1.QueryCouponListParams
	(*QueryCouponListResult)(nil),    // 5: moego.admin.coupon.v1.QueryCouponListResult
	(*QueryCouponParams)(nil),        // 6: moego.admin.coupon.v1.QueryCouponParams
	(*UpdateCouponParams)(nil),       // 7: moego.admin.coupon.v1.UpdateCouponParams
	(*SearchCouponByTermParams)(nil), // 8: moego.admin.coupon.v1.SearchCouponByTermParams
	(*SearchCouponByTermResult)(nil), // 9: moego.admin.coupon.v1.SearchCouponByTermResult
	(*DeleteCouponParams)(nil),       // 10: moego.admin.coupon.v1.DeleteCouponParams
	nil,                              // 11: moego.admin.coupon.v1.QueryCouponListResult.BusinessCategoryMapEntry
	(v1.CouponBusinessCategory)(0),   // 12: moego.models.coupon.v1.CouponBusinessCategory
	(*v2.PaginationRequest)(nil),     // 13: moego.utils.v2.PaginationRequest
	(*v1.CouponSimpleView)(nil),      // 14: moego.models.coupon.v1.CouponSimpleView
	(*v2.PaginationResponse)(nil),    // 15: moego.utils.v2.PaginationResponse
	(*v2.Option)(nil),                // 16: moego.utils.v2.Option
	(*v1.CouponModel)(nil),           // 17: moego.models.coupon.v1.CouponModel
}
var file_moego_admin_coupon_v1_coupon_admin_proto_depIdxs = []int32{
	12, // 0: moego.admin.coupon.v1.CreateNewCouponParams.business_category:type_name -> moego.models.coupon.v1.CouponBusinessCategory
	12, // 1: moego.admin.coupon.v1.CreateCouponParams.business_category:type_name -> moego.models.coupon.v1.CouponBusinessCategory
	12, // 2: moego.admin.coupon.v1.QueryCouponListParams.business_category:type_name -> moego.models.coupon.v1.CouponBusinessCategory
	13, // 3: moego.admin.coupon.v1.QueryCouponListParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	14, // 4: moego.admin.coupon.v1.QueryCouponListResult.coupons:type_name -> moego.models.coupon.v1.CouponSimpleView
	11, // 5: moego.admin.coupon.v1.QueryCouponListResult.business_category_map:type_name -> moego.admin.coupon.v1.QueryCouponListResult.BusinessCategoryMapEntry
	15, // 6: moego.admin.coupon.v1.QueryCouponListResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	16, // 7: moego.admin.coupon.v1.SearchCouponByTermResult.options:type_name -> moego.utils.v2.Option
	0,  // 8: moego.admin.coupon.v1.CouponService.CreateNewCoupon:input_type -> moego.admin.coupon.v1.CreateNewCouponParams
	2,  // 9: moego.admin.coupon.v1.CouponService.CreateCoupon:input_type -> moego.admin.coupon.v1.CreateCouponParams
	4,  // 10: moego.admin.coupon.v1.CouponService.QueryCouponList:input_type -> moego.admin.coupon.v1.QueryCouponListParams
	6,  // 11: moego.admin.coupon.v1.CouponService.QueryCoupon:input_type -> moego.admin.coupon.v1.QueryCouponParams
	7,  // 12: moego.admin.coupon.v1.CouponService.UpdateCoupon:input_type -> moego.admin.coupon.v1.UpdateCouponParams
	8,  // 13: moego.admin.coupon.v1.CouponService.SearchCouponByTerm:input_type -> moego.admin.coupon.v1.SearchCouponByTermParams
	10, // 14: moego.admin.coupon.v1.CouponService.DeleteCoupon:input_type -> moego.admin.coupon.v1.DeleteCouponParams
	1,  // 15: moego.admin.coupon.v1.CouponService.CreateNewCoupon:output_type -> moego.admin.coupon.v1.CreateNewCouponResult
	3,  // 16: moego.admin.coupon.v1.CouponService.CreateCoupon:output_type -> moego.admin.coupon.v1.CreateCouponResult
	5,  // 17: moego.admin.coupon.v1.CouponService.QueryCouponList:output_type -> moego.admin.coupon.v1.QueryCouponListResult
	17, // 18: moego.admin.coupon.v1.CouponService.QueryCoupon:output_type -> moego.models.coupon.v1.CouponModel
	17, // 19: moego.admin.coupon.v1.CouponService.UpdateCoupon:output_type -> moego.models.coupon.v1.CouponModel
	9,  // 20: moego.admin.coupon.v1.CouponService.SearchCouponByTerm:output_type -> moego.admin.coupon.v1.SearchCouponByTermResult
	17, // 21: moego.admin.coupon.v1.CouponService.DeleteCoupon:output_type -> moego.models.coupon.v1.CouponModel
	15, // [15:22] is the sub-list for method output_type
	8,  // [8:15] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_admin_coupon_v1_coupon_admin_proto_init() }
func file_moego_admin_coupon_v1_coupon_admin_proto_init() {
	if File_moego_admin_coupon_v1_coupon_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNewCouponParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateNewCouponResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCouponParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCouponResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCouponListParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCouponListResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryCouponParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCouponParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCouponByTermParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCouponByTermResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCouponParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes[7].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_coupon_v1_coupon_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_coupon_v1_coupon_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_coupon_v1_coupon_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_coupon_v1_coupon_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_coupon_v1_coupon_admin_proto = out.File
	file_moego_admin_coupon_v1_coupon_admin_proto_rawDesc = nil
	file_moego_admin_coupon_v1_coupon_admin_proto_goTypes = nil
	file_moego_admin_coupon_v1_coupon_admin_proto_depIdxs = nil
}
