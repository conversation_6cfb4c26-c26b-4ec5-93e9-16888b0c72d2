// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/account/v1/account_pet_api.proto

package accountapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountPetServiceClient is the client API for AccountPetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountPetServiceClient interface {
	// List account pets
	ListAccountPets(ctx context.Context, in *ListAccountPetsParams, opts ...grpc.CallOption) (*ListAccountPetsResult, error)
	// Create a new account pet
	CreateAccountPet(ctx context.Context, in *CreateAccountPetParams, opts ...grpc.CallOption) (*CreateAccountPetResult, error)
}

type accountPetServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountPetServiceClient(cc grpc.ClientConnInterface) AccountPetServiceClient {
	return &accountPetServiceClient{cc}
}

func (c *accountPetServiceClient) ListAccountPets(ctx context.Context, in *ListAccountPetsParams, opts ...grpc.CallOption) (*ListAccountPetsResult, error) {
	out := new(ListAccountPetsResult)
	err := c.cc.Invoke(ctx, "/moego.client.account.v1.AccountPetService/ListAccountPets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountPetServiceClient) CreateAccountPet(ctx context.Context, in *CreateAccountPetParams, opts ...grpc.CallOption) (*CreateAccountPetResult, error) {
	out := new(CreateAccountPetResult)
	err := c.cc.Invoke(ctx, "/moego.client.account.v1.AccountPetService/CreateAccountPet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountPetServiceServer is the server API for AccountPetService service.
// All implementations must embed UnimplementedAccountPetServiceServer
// for forward compatibility
type AccountPetServiceServer interface {
	// List account pets
	ListAccountPets(context.Context, *ListAccountPetsParams) (*ListAccountPetsResult, error)
	// Create a new account pet
	CreateAccountPet(context.Context, *CreateAccountPetParams) (*CreateAccountPetResult, error)
	mustEmbedUnimplementedAccountPetServiceServer()
}

// UnimplementedAccountPetServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountPetServiceServer struct {
}

func (UnimplementedAccountPetServiceServer) ListAccountPets(context.Context, *ListAccountPetsParams) (*ListAccountPetsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAccountPets not implemented")
}
func (UnimplementedAccountPetServiceServer) CreateAccountPet(context.Context, *CreateAccountPetParams) (*CreateAccountPetResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccountPet not implemented")
}
func (UnimplementedAccountPetServiceServer) mustEmbedUnimplementedAccountPetServiceServer() {}

// UnsafeAccountPetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountPetServiceServer will
// result in compilation errors.
type UnsafeAccountPetServiceServer interface {
	mustEmbedUnimplementedAccountPetServiceServer()
}

func RegisterAccountPetServiceServer(s grpc.ServiceRegistrar, srv AccountPetServiceServer) {
	s.RegisterService(&AccountPetService_ServiceDesc, srv)
}

func _AccountPetService_ListAccountPets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAccountPetsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountPetServiceServer).ListAccountPets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.account.v1.AccountPetService/ListAccountPets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountPetServiceServer).ListAccountPets(ctx, req.(*ListAccountPetsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountPetService_CreateAccountPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountPetParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountPetServiceServer).CreateAccountPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.account.v1.AccountPetService/CreateAccountPet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountPetServiceServer).CreateAccountPet(ctx, req.(*CreateAccountPetParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountPetService_ServiceDesc is the grpc.ServiceDesc for AccountPetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountPetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.account.v1.AccountPetService",
	HandlerType: (*AccountPetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAccountPets",
			Handler:    _AccountPetService_ListAccountPets_Handler,
		},
		{
			MethodName: "CreateAccountPet",
			Handler:    _AccountPetService_CreateAccountPet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/account/v1/account_pet_api.proto",
}
