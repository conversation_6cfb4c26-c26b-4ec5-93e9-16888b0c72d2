// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/notification/v1/app_push_api.proto

package notificationapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Refresh device params
type RefreshDeviceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// push token
	PushToken string `protobuf:"bytes,1,opt,name=push_token,json=pushToken,proto3" json:"push_token,omitempty"`
	// device type
	DeviceType v1.DeviceType `protobuf:"varint,2,opt,name=device_type,json=deviceType,proto3,enum=moego.models.notification.v1.DeviceType" json:"device_type,omitempty"`
}

func (x *RefreshDeviceParams) Reset() {
	*x = RefreshDeviceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_notification_v1_app_push_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshDeviceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshDeviceParams) ProtoMessage() {}

func (x *RefreshDeviceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_notification_v1_app_push_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshDeviceParams.ProtoReflect.Descriptor instead.
func (*RefreshDeviceParams) Descriptor() ([]byte, []int) {
	return file_moego_client_notification_v1_app_push_api_proto_rawDescGZIP(), []int{0}
}

func (x *RefreshDeviceParams) GetPushToken() string {
	if x != nil {
		return x.PushToken
	}
	return ""
}

func (x *RefreshDeviceParams) GetDeviceType() v1.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return v1.DeviceType(0)
}

// Refresh device result
type RefreshDeviceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is online, true if the device is registered successfully, false if the device is offline successfully
	IsOnline bool `protobuf:"varint,1,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
}

func (x *RefreshDeviceResult) Reset() {
	*x = RefreshDeviceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_notification_v1_app_push_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshDeviceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshDeviceResult) ProtoMessage() {}

func (x *RefreshDeviceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_notification_v1_app_push_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshDeviceResult.ProtoReflect.Descriptor instead.
func (*RefreshDeviceResult) Descriptor() ([]byte, []int) {
	return file_moego_client_notification_v1_app_push_api_proto_rawDescGZIP(), []int{1}
}

func (x *RefreshDeviceResult) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

var File_moego_client_notification_v1_app_push_api_proto protoreflect.FileDescriptor

var file_moego_client_notification_v1_app_push_api_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a,
	0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x93, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x0a, 0x70, 0x75, 0x73, 0x68, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x09, 0x70, 0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x53, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x32, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x87, 0x01, 0x0a, 0x0e, 0x41, 0x70,
	0x70, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x75, 0x0a, 0x0d,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x8d, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x3b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70,
	0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_notification_v1_app_push_api_proto_rawDescOnce sync.Once
	file_moego_client_notification_v1_app_push_api_proto_rawDescData = file_moego_client_notification_v1_app_push_api_proto_rawDesc
)

func file_moego_client_notification_v1_app_push_api_proto_rawDescGZIP() []byte {
	file_moego_client_notification_v1_app_push_api_proto_rawDescOnce.Do(func() {
		file_moego_client_notification_v1_app_push_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_notification_v1_app_push_api_proto_rawDescData)
	})
	return file_moego_client_notification_v1_app_push_api_proto_rawDescData
}

var file_moego_client_notification_v1_app_push_api_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_client_notification_v1_app_push_api_proto_goTypes = []interface{}{
	(*RefreshDeviceParams)(nil), // 0: moego.client.notification.v1.RefreshDeviceParams
	(*RefreshDeviceResult)(nil), // 1: moego.client.notification.v1.RefreshDeviceResult
	(v1.DeviceType)(0),          // 2: moego.models.notification.v1.DeviceType
}
var file_moego_client_notification_v1_app_push_api_proto_depIdxs = []int32{
	2, // 0: moego.client.notification.v1.RefreshDeviceParams.device_type:type_name -> moego.models.notification.v1.DeviceType
	0, // 1: moego.client.notification.v1.AppPushService.RefreshDevice:input_type -> moego.client.notification.v1.RefreshDeviceParams
	1, // 2: moego.client.notification.v1.AppPushService.RefreshDevice:output_type -> moego.client.notification.v1.RefreshDeviceResult
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_client_notification_v1_app_push_api_proto_init() }
func file_moego_client_notification_v1_app_push_api_proto_init() {
	if File_moego_client_notification_v1_app_push_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_notification_v1_app_push_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshDeviceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_notification_v1_app_push_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshDeviceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_notification_v1_app_push_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_notification_v1_app_push_api_proto_goTypes,
		DependencyIndexes: file_moego_client_notification_v1_app_push_api_proto_depIdxs,
		MessageInfos:      file_moego_client_notification_v1_app_push_api_proto_msgTypes,
	}.Build()
	File_moego_client_notification_v1_app_push_api_proto = out.File
	file_moego_client_notification_v1_app_push_api_proto_rawDesc = nil
	file_moego_client_notification_v1_app_push_api_proto_goTypes = nil
	file_moego_client_notification_v1_app_push_api_proto_depIdxs = nil
}
