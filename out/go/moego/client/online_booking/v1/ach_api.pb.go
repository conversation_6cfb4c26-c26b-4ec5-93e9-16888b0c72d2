// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v1/ach_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create ACH setup intent params
type CreateACHSetupIntentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*CreateACHSetupIntentParams_Name
	//	*CreateACHSetupIntentParams_Domain
	Anonymous isCreateACHSetupIntentParams_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *CreateACHSetupIntentParams) Reset() {
	*x = CreateACHSetupIntentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateACHSetupIntentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateACHSetupIntentParams) ProtoMessage() {}

func (x *CreateACHSetupIntentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateACHSetupIntentParams.ProtoReflect.Descriptor instead.
func (*CreateACHSetupIntentParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP(), []int{0}
}

func (m *CreateACHSetupIntentParams) GetAnonymous() isCreateACHSetupIntentParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *CreateACHSetupIntentParams) GetName() string {
	if x, ok := x.GetAnonymous().(*CreateACHSetupIntentParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *CreateACHSetupIntentParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*CreateACHSetupIntentParams_Domain); ok {
		return x.Domain
	}
	return ""
}

type isCreateACHSetupIntentParams_Anonymous interface {
	isCreateACHSetupIntentParams_Anonymous()
}

type CreateACHSetupIntentParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type CreateACHSetupIntentParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*CreateACHSetupIntentParams_Name) isCreateACHSetupIntentParams_Anonymous() {}

func (*CreateACHSetupIntentParams_Domain) isCreateACHSetupIntentParams_Anonymous() {}

// create ACH setup intent result
type CreateACHSetupIntentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// setup intent id
	SetupIntentId string `protobuf:"bytes,1,opt,name=setup_intent_id,json=setupIntentId,proto3" json:"setup_intent_id,omitempty"`
	// client secret
	ClientSecret string `protobuf:"bytes,2,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
}

func (x *CreateACHSetupIntentResult) Reset() {
	*x = CreateACHSetupIntentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateACHSetupIntentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateACHSetupIntentResult) ProtoMessage() {}

func (x *CreateACHSetupIntentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateACHSetupIntentResult.ProtoReflect.Descriptor instead.
func (*CreateACHSetupIntentResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP(), []int{1}
}

func (x *CreateACHSetupIntentResult) GetSetupIntentId() string {
	if x != nil {
		return x.SetupIntentId
	}
	return ""
}

func (x *CreateACHSetupIntentResult) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

// add ACH on file params
type AddACHOnFileParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*AddACHOnFileParams_Name
	//	*AddACHOnFileParams_Domain
	Anonymous isAddACHOnFileParams_Anonymous `protobuf_oneof:"anonymous"`
	// payment method id
	PaymentMethodId string `protobuf:"bytes,3,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
}

func (x *AddACHOnFileParams) Reset() {
	*x = AddACHOnFileParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddACHOnFileParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddACHOnFileParams) ProtoMessage() {}

func (x *AddACHOnFileParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddACHOnFileParams.ProtoReflect.Descriptor instead.
func (*AddACHOnFileParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP(), []int{2}
}

func (m *AddACHOnFileParams) GetAnonymous() isAddACHOnFileParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *AddACHOnFileParams) GetName() string {
	if x, ok := x.GetAnonymous().(*AddACHOnFileParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *AddACHOnFileParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*AddACHOnFileParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *AddACHOnFileParams) GetPaymentMethodId() string {
	if x != nil {
		return x.PaymentMethodId
	}
	return ""
}

type isAddACHOnFileParams_Anonymous interface {
	isAddACHOnFileParams_Anonymous()
}

type AddACHOnFileParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type AddACHOnFileParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*AddACHOnFileParams_Name) isAddACHOnFileParams_Anonymous() {}

func (*AddACHOnFileParams_Domain) isAddACHOnFileParams_Anonymous() {}

// add ACH on file result
type AddACHOnFileResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ach view
	AchView *v1.ACHView `protobuf:"bytes,1,opt,name=ach_view,json=achView,proto3" json:"ach_view,omitempty"`
}

func (x *AddACHOnFileResult) Reset() {
	*x = AddACHOnFileResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddACHOnFileResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddACHOnFileResult) ProtoMessage() {}

func (x *AddACHOnFileResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddACHOnFileResult.ProtoReflect.Descriptor instead.
func (*AddACHOnFileResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP(), []int{3}
}

func (x *AddACHOnFileResult) GetAchView() *v1.ACHView {
	if x != nil {
		return x.AchView
	}
	return nil
}

// delete credit card params
type DeleteACHOnFileParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*DeleteACHOnFileParams_Name
	//	*DeleteACHOnFileParams_Domain
	Anonymous isDeleteACHOnFileParams_Anonymous `protobuf_oneof:"anonymous"`
	// payment method id
	PaymentMethodId string `protobuf:"bytes,3,opt,name=payment_method_id,json=paymentMethodId,proto3" json:"payment_method_id,omitempty"`
}

func (x *DeleteACHOnFileParams) Reset() {
	*x = DeleteACHOnFileParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteACHOnFileParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteACHOnFileParams) ProtoMessage() {}

func (x *DeleteACHOnFileParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteACHOnFileParams.ProtoReflect.Descriptor instead.
func (*DeleteACHOnFileParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP(), []int{4}
}

func (m *DeleteACHOnFileParams) GetAnonymous() isDeleteACHOnFileParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *DeleteACHOnFileParams) GetName() string {
	if x, ok := x.GetAnonymous().(*DeleteACHOnFileParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *DeleteACHOnFileParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*DeleteACHOnFileParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *DeleteACHOnFileParams) GetPaymentMethodId() string {
	if x != nil {
		return x.PaymentMethodId
	}
	return ""
}

type isDeleteACHOnFileParams_Anonymous interface {
	isDeleteACHOnFileParams_Anonymous()
}

type DeleteACHOnFileParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type DeleteACHOnFileParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*DeleteACHOnFileParams_Name) isDeleteACHOnFileParams_Anonymous() {}

func (*DeleteACHOnFileParams_Domain) isDeleteACHOnFileParams_Anonymous() {}

// delete credit card result
type DeleteACHOnFileResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteACHOnFileResult) Reset() {
	*x = DeleteACHOnFileResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteACHOnFileResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteACHOnFileResult) ProtoMessage() {}

func (x *DeleteACHOnFileResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteACHOnFileResult.ProtoReflect.Descriptor instead.
func (*DeleteACHOnFileResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP(), []int{5}
}

// list ACH on files params
type ListACHOnFilesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*ListACHOnFilesParams_Name
	//	*ListACHOnFilesParams_Domain
	Anonymous isListACHOnFilesParams_Anonymous `protobuf_oneof:"anonymous"`
}

func (x *ListACHOnFilesParams) Reset() {
	*x = ListACHOnFilesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListACHOnFilesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListACHOnFilesParams) ProtoMessage() {}

func (x *ListACHOnFilesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListACHOnFilesParams.ProtoReflect.Descriptor instead.
func (*ListACHOnFilesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP(), []int{6}
}

func (m *ListACHOnFilesParams) GetAnonymous() isListACHOnFilesParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *ListACHOnFilesParams) GetName() string {
	if x, ok := x.GetAnonymous().(*ListACHOnFilesParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *ListACHOnFilesParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*ListACHOnFilesParams_Domain); ok {
		return x.Domain
	}
	return ""
}

type isListACHOnFilesParams_Anonymous interface {
	isListACHOnFilesParams_Anonymous()
}

type ListACHOnFilesParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type ListACHOnFilesParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*ListACHOnFilesParams_Name) isListACHOnFilesParams_Anonymous() {}

func (*ListACHOnFilesParams_Domain) isListACHOnFilesParams_Anonymous() {}

// list ACH on files result
type ListACHOnFilesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ach view list
	AchViews []*v1.ACHView `protobuf:"bytes,1,rep,name=ach_views,json=achViews,proto3" json:"ach_views,omitempty"`
}

func (x *ListACHOnFilesResult) Reset() {
	*x = ListACHOnFilesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListACHOnFilesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListACHOnFilesResult) ProtoMessage() {}

func (x *ListACHOnFilesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_ach_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListACHOnFilesResult.ProtoReflect.Descriptor instead.
func (*ListACHOnFilesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP(), []int{7}
}

func (x *ListACHOnFilesResult) GetAchViews() []*v1.ACHView {
	if x != nil {
		return x.AchViews
	}
	return nil
}

var File_moego_client_online_booking_v1_ach_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v1_ach_api_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x63, 0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x68, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x5e, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x43, 0x48, 0x53, 0x65,
	0x74, 0x75, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42,
	0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x22, 0x69, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x43, 0x48, 0x53, 0x65,
	0x74, 0x75, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x26, 0x0a, 0x0f, 0x73, 0x65, 0x74, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x74, 0x75, 0x70, 0x49,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x8b, 0x01, 0x0a,
	0x12, 0x41, 0x64, 0x64, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x33, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x42, 0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e,
	0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x51, 0x0a, 0x12, 0x41, 0x64,
	0x64, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x3b, 0x0a, 0x08, 0x61, 0x63, 0x68, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x48,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x61, 0x63, 0x68, 0x56, 0x69, 0x65, 0x77, 0x22, 0x8e, 0x01,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x33, 0x0a, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x64, 0x42, 0x10, 0x0a, 0x09,
	0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x17,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x58, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x42,
	0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x22, 0x55, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x09, 0x61, 0x63, 0x68,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x48, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08,
	0x61, 0x63, 0x68, 0x56, 0x69, 0x65, 0x77, 0x73, 0x32, 0x94, 0x04, 0x0a, 0x0a, 0x41, 0x43, 0x48,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x41, 0x43,
	0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x43, 0x48, 0x4f,
	0x6e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x7f, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69,
	0x6c, 0x65, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46,
	0x69, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x7c, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43,
	0x48, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8e,
	0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x43, 0x48, 0x53, 0x65, 0x74, 0x75,
	0x70, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x43, 0x48, 0x53, 0x65, 0x74, 0x75, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x43, 0x48, 0x53, 0x65,
	0x74, 0x75, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42,
	0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x66, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x61,
	0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v1_ach_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v1_ach_api_proto_rawDescData = file_moego_client_online_booking_v1_ach_api_proto_rawDesc
)

func file_moego_client_online_booking_v1_ach_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v1_ach_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v1_ach_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v1_ach_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v1_ach_api_proto_rawDescData
}

var file_moego_client_online_booking_v1_ach_api_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_client_online_booking_v1_ach_api_proto_goTypes = []interface{}{
	(*CreateACHSetupIntentParams)(nil), // 0: moego.client.online_booking.v1.CreateACHSetupIntentParams
	(*CreateACHSetupIntentResult)(nil), // 1: moego.client.online_booking.v1.CreateACHSetupIntentResult
	(*AddACHOnFileParams)(nil),         // 2: moego.client.online_booking.v1.AddACHOnFileParams
	(*AddACHOnFileResult)(nil),         // 3: moego.client.online_booking.v1.AddACHOnFileResult
	(*DeleteACHOnFileParams)(nil),      // 4: moego.client.online_booking.v1.DeleteACHOnFileParams
	(*DeleteACHOnFileResult)(nil),      // 5: moego.client.online_booking.v1.DeleteACHOnFileResult
	(*ListACHOnFilesParams)(nil),       // 6: moego.client.online_booking.v1.ListACHOnFilesParams
	(*ListACHOnFilesResult)(nil),       // 7: moego.client.online_booking.v1.ListACHOnFilesResult
	(*v1.ACHView)(nil),                 // 8: moego.models.payment.v1.ACHView
}
var file_moego_client_online_booking_v1_ach_api_proto_depIdxs = []int32{
	8, // 0: moego.client.online_booking.v1.AddACHOnFileResult.ach_view:type_name -> moego.models.payment.v1.ACHView
	8, // 1: moego.client.online_booking.v1.ListACHOnFilesResult.ach_views:type_name -> moego.models.payment.v1.ACHView
	2, // 2: moego.client.online_booking.v1.ACHService.AddACHOnFile:input_type -> moego.client.online_booking.v1.AddACHOnFileParams
	4, // 3: moego.client.online_booking.v1.ACHService.DeleteACHOnFile:input_type -> moego.client.online_booking.v1.DeleteACHOnFileParams
	6, // 4: moego.client.online_booking.v1.ACHService.ListACHOnFiles:input_type -> moego.client.online_booking.v1.ListACHOnFilesParams
	0, // 5: moego.client.online_booking.v1.ACHService.CreateACHSetupIntent:input_type -> moego.client.online_booking.v1.CreateACHSetupIntentParams
	3, // 6: moego.client.online_booking.v1.ACHService.AddACHOnFile:output_type -> moego.client.online_booking.v1.AddACHOnFileResult
	5, // 7: moego.client.online_booking.v1.ACHService.DeleteACHOnFile:output_type -> moego.client.online_booking.v1.DeleteACHOnFileResult
	7, // 8: moego.client.online_booking.v1.ACHService.ListACHOnFiles:output_type -> moego.client.online_booking.v1.ListACHOnFilesResult
	1, // 9: moego.client.online_booking.v1.ACHService.CreateACHSetupIntent:output_type -> moego.client.online_booking.v1.CreateACHSetupIntentResult
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v1_ach_api_proto_init() }
func file_moego_client_online_booking_v1_ach_api_proto_init() {
	if File_moego_client_online_booking_v1_ach_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v1_ach_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateACHSetupIntentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ach_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateACHSetupIntentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ach_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddACHOnFileParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ach_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddACHOnFileResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ach_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteACHOnFileParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ach_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteACHOnFileResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ach_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListACHOnFilesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_ach_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListACHOnFilesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v1_ach_api_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CreateACHSetupIntentParams_Name)(nil),
		(*CreateACHSetupIntentParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_ach_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*AddACHOnFileParams_Name)(nil),
		(*AddACHOnFileParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_ach_api_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*DeleteACHOnFileParams_Name)(nil),
		(*DeleteACHOnFileParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_ach_api_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*ListACHOnFilesParams_Name)(nil),
		(*ListACHOnFilesParams_Domain)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v1_ach_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v1_ach_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v1_ach_api_proto_depIdxs,
		MessageInfos:      file_moego_client_online_booking_v1_ach_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v1_ach_api_proto = out.File
	file_moego_client_online_booking_v1_ach_api_proto_rawDesc = nil
	file_moego_client_online_booking_v1_ach_api_proto_goTypes = nil
	file_moego_client_online_booking_v1_ach_api_proto_depIdxs = nil
}
