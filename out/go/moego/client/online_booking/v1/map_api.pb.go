// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/online_booking/v1/map_api.proto

package onlinebookingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetCountriesRequest
type GetCountriesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the name of the country, which can be a short name, official name or local name.
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// the code of the country, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code.
	Code *string `protobuf:"bytes,2,opt,name=code,proto3,oneof" json:"code,omitempty"`
	// whether the country is independent
	Independent *bool `protobuf:"varint,3,opt,name=independent,proto3,oneof" json:"independent,omitempty"`
}

func (x *GetCountriesParams) Reset() {
	*x = GetCountriesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCountriesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCountriesParams) ProtoMessage() {}

func (x *GetCountriesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCountriesParams.ProtoReflect.Descriptor instead.
func (*GetCountriesParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_map_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetCountriesParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *GetCountriesParams) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *GetCountriesParams) GetIndependent() bool {
	if x != nil && x.Independent != nil {
		return *x.Independent
	}
	return false
}

// GetCountriesResponse
type GetCountriesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// country list
	Countries []*v1.CountryModel `protobuf:"bytes,1,rep,name=countries,proto3" json:"countries,omitempty"`
}

func (x *GetCountriesResult) Reset() {
	*x = GetCountriesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCountriesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCountriesResult) ProtoMessage() {}

func (x *GetCountriesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCountriesResult.ProtoReflect.Descriptor instead.
func (*GetCountriesResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_map_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetCountriesResult) GetCountries() []*v1.CountryModel {
	if x != nil {
		return x.Countries
	}
	return nil
}

// GetAddressRequest
type GetAddressParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// condition_type
	//
	// Types that are assignable to ConditionType:
	//
	//	*GetAddressParams_AddressSource
	//	*GetAddressParams_Coordinate
	//	*GetAddressParams_Condition
	ConditionType isGetAddressParams_ConditionType `protobuf_oneof:"condition_type"`
	// language code
	LanguageCode *string `protobuf:"bytes,6,opt,name=language_code,json=languageCode,proto3,oneof" json:"language_code,omitempty"`
}

func (x *GetAddressParams) Reset() {
	*x = GetAddressParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressParams) ProtoMessage() {}

func (x *GetAddressParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressParams.ProtoReflect.Descriptor instead.
func (*GetAddressParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_map_api_proto_rawDescGZIP(), []int{2}
}

func (m *GetAddressParams) GetConditionType() isGetAddressParams_ConditionType {
	if m != nil {
		return m.ConditionType
	}
	return nil
}

func (x *GetAddressParams) GetAddressSource() *v1.AddressSource {
	if x, ok := x.GetConditionType().(*GetAddressParams_AddressSource); ok {
		return x.AddressSource
	}
	return nil
}

func (x *GetAddressParams) GetCoordinate() *latlng.LatLng {
	if x, ok := x.GetConditionType().(*GetAddressParams_Coordinate); ok {
		return x.Coordinate
	}
	return nil
}

func (x *GetAddressParams) GetCondition() *GetAddressParams_CombinedConditions {
	if x, ok := x.GetConditionType().(*GetAddressParams_Condition); ok {
		return x.Condition
	}
	return nil
}

func (x *GetAddressParams) GetLanguageCode() string {
	if x != nil && x.LanguageCode != nil {
		return *x.LanguageCode
	}
	return ""
}

type isGetAddressParams_ConditionType interface {
	isGetAddressParams_ConditionType()
}

type GetAddressParams_AddressSource struct {
	// address source
	AddressSource *v1.AddressSource `protobuf:"bytes,1,opt,name=address_source,json=addressSource,proto3,oneof"`
}

type GetAddressParams_Coordinate struct {
	// specify the coordinate of address
	Coordinate *latlng.LatLng `protobuf:"bytes,2,opt,name=coordinate,proto3,oneof"`
}

type GetAddressParams_Condition struct {
	// combined conditions for querying address
	// Note: that the API itself does not check the accuracy of the conditions.
	// It will only return an address that meets all conditions,
	// which means that the accuracy of the address is guaranteed by the caller.
	Condition *GetAddressParams_CombinedConditions `protobuf:"bytes,3,opt,name=condition,proto3,oneof"`
}

func (*GetAddressParams_AddressSource) isGetAddressParams_ConditionType() {}

func (*GetAddressParams_Coordinate) isGetAddressParams_ConditionType() {}

func (*GetAddressParams_Condition) isGetAddressParams_ConditionType() {}

// GetAddressResponse
type GetAddressResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the address
	Address *v1.AddressModel `protobuf:"bytes,1,opt,name=address,proto3,oneof" json:"address,omitempty"`
}

func (x *GetAddressResult) Reset() {
	*x = GetAddressResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressResult) ProtoMessage() {}

func (x *GetAddressResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressResult.ProtoReflect.Descriptor instead.
func (*GetAddressResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_map_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetAddressResult) GetAddress() *v1.AddressModel {
	if x != nil {
		return x.Address
	}
	return nil
}

// AutocompleteAddressParams
type AutocompleteAddressParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// online booking name and domain
	//
	// Types that are assignable to Anonymous:
	//
	//	*AutocompleteAddressParams_Name
	//	*AutocompleteAddressParams_Domain
	Anonymous isAutocompleteAddressParams_Anonymous `protobuf_oneof:"anonymous"`
	// The text string on which to search.
	Term string `protobuf:"bytes,3,opt,name=term,proto3" json:"term,omitempty"`
	// The origin point from which to calculate geodesic distance to the destination (returned as distance).
	// If this value is omitted, geodesic distance will not be returned.
	Origin *latlng.LatLng `protobuf:"bytes,4,opt,name=origin,proto3,oneof" json:"origin,omitempty"`
	// A list of countries to limit the search. Each country element can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
	// If no element is specified, there is no restriction on countries, which is equivalent to searching worldwide.
	// Note: country element is not case sensitive, but an exact match is required.
	Countries []string `protobuf:"bytes,5,rep,name=countries,proto3" json:"countries,omitempty"`
	// the state of the address.
	// you must specify `countries` and must contain exactly one element when specifying this field.
	State *string `protobuf:"bytes,6,opt,name=state,proto3,oneof" json:"state,omitempty"`
	// the county of the address.
	// you must specify `countries` and must contain exactly one element when specifying this field.
	County *string `protobuf:"bytes,7,opt,name=county,proto3,oneof" json:"county,omitempty"`
	// the district of the address.
	// you must specify `countries` and must contain exactly one element when specifying this field.
	District *string `protobuf:"bytes,8,opt,name=district,proto3,oneof" json:"district,omitempty"`
	// the city of the address.
	// you must specify `countries` and must contain exactly one element when specifying this field.
	City *string `protobuf:"bytes,9,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// the zip/postal code of the address.
	// you must specify `countries` and must contain exactly one element when specifying this field.
	PostalCode *string `protobuf:"bytes,10,opt,name=postal_code,json=postalCode,proto3,oneof" json:"postal_code,omitempty"`
	// Included primary Place type (for example, "restaurant" or "gas_station"), or only (regions), or only (cities).
	// Supported types please see: https://developers.google.com/maps/documentation/places/web-service/place-types
	// A Place is only returned if its primary type is included in this list.
	// Up to 5 values can be specified.
	// If no types are specified, all Place types are returned.
	AddressTypes []string `protobuf:"bytes,11,rep,name=address_types,json=addressTypes,proto3" json:"address_types,omitempty"`
	// Bias results to a specified location.
	// At most one of `location_bias` or `location_restriction` should be set.
	LocationBias *v1.LocationBias `protobuf:"bytes,12,opt,name=location_bias,json=locationBias,proto3,oneof" json:"location_bias,omitempty"`
	// Restrict results to a specified location.
	// At most one of `location_bias` or `location_restriction` should be set.
	LocationRestriction *v1.LocationRestriction `protobuf:"bytes,13,opt,name=location_restriction,json=locationRestriction,proto3,oneof" json:"location_restriction,omitempty"`
	// language code
	LanguageCode *string `protobuf:"bytes,15,opt,name=language_code,json=languageCode,proto3,oneof" json:"language_code,omitempty"`
	// Specify the maximum number of results to return, default 5
	MaxResults *int32 `protobuf:"varint,16,opt,name=max_results,json=maxResults,proto3,oneof" json:"max_results,omitempty"`
}

func (x *AutocompleteAddressParams) Reset() {
	*x = AutocompleteAddressParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutocompleteAddressParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutocompleteAddressParams) ProtoMessage() {}

func (x *AutocompleteAddressParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutocompleteAddressParams.ProtoReflect.Descriptor instead.
func (*AutocompleteAddressParams) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_map_api_proto_rawDescGZIP(), []int{4}
}

func (m *AutocompleteAddressParams) GetAnonymous() isAutocompleteAddressParams_Anonymous {
	if m != nil {
		return m.Anonymous
	}
	return nil
}

func (x *AutocompleteAddressParams) GetName() string {
	if x, ok := x.GetAnonymous().(*AutocompleteAddressParams_Name); ok {
		return x.Name
	}
	return ""
}

func (x *AutocompleteAddressParams) GetDomain() string {
	if x, ok := x.GetAnonymous().(*AutocompleteAddressParams_Domain); ok {
		return x.Domain
	}
	return ""
}

func (x *AutocompleteAddressParams) GetTerm() string {
	if x != nil {
		return x.Term
	}
	return ""
}

func (x *AutocompleteAddressParams) GetOrigin() *latlng.LatLng {
	if x != nil {
		return x.Origin
	}
	return nil
}

func (x *AutocompleteAddressParams) GetCountries() []string {
	if x != nil {
		return x.Countries
	}
	return nil
}

func (x *AutocompleteAddressParams) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *AutocompleteAddressParams) GetCounty() string {
	if x != nil && x.County != nil {
		return *x.County
	}
	return ""
}

func (x *AutocompleteAddressParams) GetDistrict() string {
	if x != nil && x.District != nil {
		return *x.District
	}
	return ""
}

func (x *AutocompleteAddressParams) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *AutocompleteAddressParams) GetPostalCode() string {
	if x != nil && x.PostalCode != nil {
		return *x.PostalCode
	}
	return ""
}

func (x *AutocompleteAddressParams) GetAddressTypes() []string {
	if x != nil {
		return x.AddressTypes
	}
	return nil
}

func (x *AutocompleteAddressParams) GetLocationBias() *v1.LocationBias {
	if x != nil {
		return x.LocationBias
	}
	return nil
}

func (x *AutocompleteAddressParams) GetLocationRestriction() *v1.LocationRestriction {
	if x != nil {
		return x.LocationRestriction
	}
	return nil
}

func (x *AutocompleteAddressParams) GetLanguageCode() string {
	if x != nil && x.LanguageCode != nil {
		return *x.LanguageCode
	}
	return ""
}

func (x *AutocompleteAddressParams) GetMaxResults() int32 {
	if x != nil && x.MaxResults != nil {
		return *x.MaxResults
	}
	return 0
}

type isAutocompleteAddressParams_Anonymous interface {
	isAutocompleteAddressParams_Anonymous()
}

type AutocompleteAddressParams_Name struct {
	// Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
	Name string `protobuf:"bytes,1,opt,name=name,proto3,oneof"`
}

type AutocompleteAddressParams_Domain struct {
	// Customized URL domain, demo URL: crazycutepetspa.moego.online
	Domain string `protobuf:"bytes,2,opt,name=domain,proto3,oneof"`
}

func (*AutocompleteAddressParams_Name) isAutocompleteAddressParams_Anonymous() {}

func (*AutocompleteAddressParams_Domain) isAutocompleteAddressParams_Anonymous() {}

// AutocompleteAddressResult
type AutocompleteAddressResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Contains a list of suggestions, ordered in descending order of relevance.
	Suggestions []*v1.SuggestedAddress `protobuf:"bytes,1,rep,name=suggestions,proto3" json:"suggestions,omitempty"`
}

func (x *AutocompleteAddressResult) Reset() {
	*x = AutocompleteAddressResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutocompleteAddressResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutocompleteAddressResult) ProtoMessage() {}

func (x *AutocompleteAddressResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutocompleteAddressResult.ProtoReflect.Descriptor instead.
func (*AutocompleteAddressResult) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_map_api_proto_rawDescGZIP(), []int{5}
}

func (x *AutocompleteAddressResult) GetSuggestions() []*v1.SuggestedAddress {
	if x != nil {
		return x.Suggestions
	}
	return nil
}

// CombinedConditions
type GetAddressParams_CombinedConditions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the country of the address, which can be an ISO 3166-1 alpha-2/alpha-3/numeric/ccTLD code or country name.
	// Note: country is not case sensitive, but an exact match is required.
	Country string `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	// the part of the address
	Address *string `protobuf:"bytes,2,opt,name=address,proto3,oneof" json:"address,omitempty"`
	// the zip/postal code of the address
	PostalCode *string `protobuf:"bytes,3,opt,name=postal_code,json=postalCode,proto3,oneof" json:"postal_code,omitempty"`
	// the label of the address
	Label *string `protobuf:"bytes,4,opt,name=label,proto3,oneof" json:"label,omitempty"`
	// the bounds of the address
	Bounds *v1.Bounds `protobuf:"bytes,5,opt,name=bounds,proto3,oneof" json:"bounds,omitempty"`
}

func (x *GetAddressParams_CombinedConditions) Reset() {
	*x = GetAddressParams_CombinedConditions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressParams_CombinedConditions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressParams_CombinedConditions) ProtoMessage() {}

func (x *GetAddressParams_CombinedConditions) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_online_booking_v1_map_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressParams_CombinedConditions.ProtoReflect.Descriptor instead.
func (*GetAddressParams_CombinedConditions) Descriptor() ([]byte, []int) {
	return file_moego_client_online_booking_v1_map_api_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetAddressParams_CombinedConditions) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *GetAddressParams_CombinedConditions) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

func (x *GetAddressParams_CombinedConditions) GetPostalCode() string {
	if x != nil && x.PostalCode != nil {
		return *x.PostalCode
	}
	return ""
}

func (x *GetAddressParams_CombinedConditions) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *GetAddressParams_CombinedConditions) GetBounds() *v1.Bounds {
	if x != nil {
		return x.Bounds
	}
	return nil
}

var File_moego_client_online_booking_v1_map_api_proto protoreflect.FileDescriptor

var file_moego_client_online_booking_v1_map_api_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6d, 0x61, 0x70, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x18,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61,
	0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8f, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x25, 0x0a, 0x0b, 0x69, 0x6e, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x0b, 0x69, 0x6e, 0x64, 0x65, 0x70, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x74, 0x22, 0x55, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x3f, 0x0a, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x22, 0xc5, 0x04, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4b, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x48, 0x00, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x35, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x0a, 0x63,
	0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x63, 0x0a, 0x09, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x43,
	0x6f, 0x6d, 0x62, 0x69, 0x6e, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28,
	0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x1a, 0xf9, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6d,
	0x62, 0x69, 0x6e, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52,
	0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19,
	0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52,
	0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x06, 0x62, 0x6f, 0x75,
	0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x48, 0x03, 0x52, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73,
	0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42,
	0x08, 0x0a, 0x06, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x62, 0x6f,
	0x75, 0x6e, 0x64, 0x73, 0x42, 0x10, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x60, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x48, 0x00, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0a,
	0x0a, 0x08, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x97, 0x07, 0x0a, 0x19, 0x41,
	0x75, 0x74, 0x6f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x1e, 0x0a, 0x04, 0x74, 0x65, 0x72, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18,
	0x80, 0x02, 0x52, 0x04, 0x74, 0x65, 0x72, 0x6d, 0x12, 0x30, 0x0a, 0x06, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x48, 0x01, 0x52,
	0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x09, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x13, 0xfa,
	0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0x10, 0x18, 0x01, 0x22, 0x07, 0x72, 0x05, 0x10, 0x02, 0x18,
	0x80, 0x01, 0x52, 0x09, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x25, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x01, 0x48, 0x02, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x06, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x01,
	0x48, 0x03, 0x52, 0x06, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a,
	0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0x80, 0x01, 0x48, 0x04, 0x52, 0x08, 0x64,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x04, 0x63, 0x69,
	0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10,
	0x01, 0x18, 0x80, 0x01, 0x48, 0x05, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x2f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x20, 0x48,
	0x06, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x38, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10,
	0x05, 0x18, 0x01, 0x22, 0x07, 0x72, 0x05, 0x10, 0x02, 0x18, 0x80, 0x01, 0x52, 0x0c, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x69, 0x61, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x69, 0x61, 0x73, 0x48, 0x07, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x69, 0x61, 0x73, 0x88, 0x01, 0x01, 0x12, 0x60, 0x0a, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x08, 0x52, 0x13, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x74, 0x72,
	0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x0d, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x02, 0x18, 0x08, 0x48, 0x09, 0x52, 0x0c, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f,
	0x0a, 0x0b, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x0a, 0x28, 0x01, 0x48, 0x0a,
	0x52, 0x0a, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x88, 0x01, 0x01, 0x42,
	0x10, 0x0a, 0x09, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x12, 0x03, 0xf8, 0x42,
	0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x42, 0x08, 0x0a, 0x06,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x79, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x69, 0x61, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x22, 0x64, 0x0a, 0x19, 0x41, 0x75, 0x74, 0x6f, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x47, 0x0a, 0x0b, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0b, 0x73,
	0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x32, 0x84, 0x03, 0x0a, 0x0a, 0x4d,
	0x61, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x70, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x13, 0x41, 0x75, 0x74, 0x6f, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74,
	0x6f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x92, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x66,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_online_booking_v1_map_api_proto_rawDescOnce sync.Once
	file_moego_client_online_booking_v1_map_api_proto_rawDescData = file_moego_client_online_booking_v1_map_api_proto_rawDesc
)

func file_moego_client_online_booking_v1_map_api_proto_rawDescGZIP() []byte {
	file_moego_client_online_booking_v1_map_api_proto_rawDescOnce.Do(func() {
		file_moego_client_online_booking_v1_map_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_online_booking_v1_map_api_proto_rawDescData)
	})
	return file_moego_client_online_booking_v1_map_api_proto_rawDescData
}

var file_moego_client_online_booking_v1_map_api_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_client_online_booking_v1_map_api_proto_goTypes = []interface{}{
	(*GetCountriesParams)(nil),                  // 0: moego.client.online_booking.v1.GetCountriesParams
	(*GetCountriesResult)(nil),                  // 1: moego.client.online_booking.v1.GetCountriesResult
	(*GetAddressParams)(nil),                    // 2: moego.client.online_booking.v1.GetAddressParams
	(*GetAddressResult)(nil),                    // 3: moego.client.online_booking.v1.GetAddressResult
	(*AutocompleteAddressParams)(nil),           // 4: moego.client.online_booking.v1.AutocompleteAddressParams
	(*AutocompleteAddressResult)(nil),           // 5: moego.client.online_booking.v1.AutocompleteAddressResult
	(*GetAddressParams_CombinedConditions)(nil), // 6: moego.client.online_booking.v1.GetAddressParams.CombinedConditions
	(*v1.CountryModel)(nil),                     // 7: moego.models.map.v1.CountryModel
	(*v1.AddressSource)(nil),                    // 8: moego.models.map.v1.AddressSource
	(*latlng.LatLng)(nil),                       // 9: google.type.LatLng
	(*v1.AddressModel)(nil),                     // 10: moego.models.map.v1.AddressModel
	(*v1.LocationBias)(nil),                     // 11: moego.models.map.v1.LocationBias
	(*v1.LocationRestriction)(nil),              // 12: moego.models.map.v1.LocationRestriction
	(*v1.SuggestedAddress)(nil),                 // 13: moego.models.map.v1.SuggestedAddress
	(*v1.Bounds)(nil),                           // 14: moego.models.map.v1.Bounds
}
var file_moego_client_online_booking_v1_map_api_proto_depIdxs = []int32{
	7,  // 0: moego.client.online_booking.v1.GetCountriesResult.countries:type_name -> moego.models.map.v1.CountryModel
	8,  // 1: moego.client.online_booking.v1.GetAddressParams.address_source:type_name -> moego.models.map.v1.AddressSource
	9,  // 2: moego.client.online_booking.v1.GetAddressParams.coordinate:type_name -> google.type.LatLng
	6,  // 3: moego.client.online_booking.v1.GetAddressParams.condition:type_name -> moego.client.online_booking.v1.GetAddressParams.CombinedConditions
	10, // 4: moego.client.online_booking.v1.GetAddressResult.address:type_name -> moego.models.map.v1.AddressModel
	9,  // 5: moego.client.online_booking.v1.AutocompleteAddressParams.origin:type_name -> google.type.LatLng
	11, // 6: moego.client.online_booking.v1.AutocompleteAddressParams.location_bias:type_name -> moego.models.map.v1.LocationBias
	12, // 7: moego.client.online_booking.v1.AutocompleteAddressParams.location_restriction:type_name -> moego.models.map.v1.LocationRestriction
	13, // 8: moego.client.online_booking.v1.AutocompleteAddressResult.suggestions:type_name -> moego.models.map.v1.SuggestedAddress
	14, // 9: moego.client.online_booking.v1.GetAddressParams.CombinedConditions.bounds:type_name -> moego.models.map.v1.Bounds
	0,  // 10: moego.client.online_booking.v1.MapService.GetCountries:input_type -> moego.client.online_booking.v1.GetCountriesParams
	2,  // 11: moego.client.online_booking.v1.MapService.GetAddress:input_type -> moego.client.online_booking.v1.GetAddressParams
	4,  // 12: moego.client.online_booking.v1.MapService.AutocompleteAddress:input_type -> moego.client.online_booking.v1.AutocompleteAddressParams
	1,  // 13: moego.client.online_booking.v1.MapService.GetCountries:output_type -> moego.client.online_booking.v1.GetCountriesResult
	3,  // 14: moego.client.online_booking.v1.MapService.GetAddress:output_type -> moego.client.online_booking.v1.GetAddressResult
	5,  // 15: moego.client.online_booking.v1.MapService.AutocompleteAddress:output_type -> moego.client.online_booking.v1.AutocompleteAddressResult
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_moego_client_online_booking_v1_map_api_proto_init() }
func file_moego_client_online_booking_v1_map_api_proto_init() {
	if File_moego_client_online_booking_v1_map_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_online_booking_v1_map_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCountriesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_map_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCountriesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_map_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_map_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_map_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutocompleteAddressParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_map_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AutocompleteAddressResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_online_booking_v1_map_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressParams_CombinedConditions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_online_booking_v1_map_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_map_api_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetAddressParams_AddressSource)(nil),
		(*GetAddressParams_Coordinate)(nil),
		(*GetAddressParams_Condition)(nil),
	}
	file_moego_client_online_booking_v1_map_api_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_client_online_booking_v1_map_api_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*AutocompleteAddressParams_Name)(nil),
		(*AutocompleteAddressParams_Domain)(nil),
	}
	file_moego_client_online_booking_v1_map_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_online_booking_v1_map_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_online_booking_v1_map_api_proto_goTypes,
		DependencyIndexes: file_moego_client_online_booking_v1_map_api_proto_depIdxs,
		MessageInfos:      file_moego_client_online_booking_v1_map_api_proto_msgTypes,
	}.Build()
	File_moego_client_online_booking_v1_map_api_proto = out.File
	file_moego_client_online_booking_v1_map_api_proto_rawDesc = nil
	file_moego_client_online_booking_v1_map_api_proto_goTypes = nil
	file_moego_client_online_booking_v1_map_api_proto_depIdxs = nil
}
