// @since 2024-07-07 15:01:03
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/ws/v1/ws_models.proto

package wspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the connection group type
type GroupType int32

const (
	// unspecified
	GroupType_GROUP_TYPE_UNSPECIFIED GroupType = 0
	// to connection, only one connection for a connection id
	GroupType_CONNECTION GroupType = 1
	// to account
	GroupType_ACCOUNT GroupType = 2
	// to staff
	GroupType_STAFF GroupType = 3
	// to business
	GroupType_BUSINESS GroupType = 4
	// to company
	GroupType_COMPANY GroupType = 5
	// to enterprise
	GroupType_ENTERPRISE GroupType = 6
)

// Enum value maps for GroupType.
var (
	GroupType_name = map[int32]string{
		0: "GROUP_TYPE_UNSPECIFIED",
		1: "CONNECTION",
		2: "ACCOUNT",
		3: "STAFF",
		4: "BUSINESS",
		5: "COMPANY",
		6: "ENTERPRISE",
	}
	GroupType_value = map[string]int32{
		"GROUP_TYPE_UNSPECIFIED": 0,
		"CONNECTION":             1,
		"ACCOUNT":                2,
		"STAFF":                  3,
		"BUSINESS":               4,
		"COMPANY":                5,
		"ENTERPRISE":             6,
	}
)

func (x GroupType) Enum() *GroupType {
	p := new(GroupType)
	*p = x
	return p
}

func (x GroupType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_ws_v1_ws_models_proto_enumTypes[0].Descriptor()
}

func (GroupType) Type() protoreflect.EnumType {
	return &file_moego_models_ws_v1_ws_models_proto_enumTypes[0]
}

func (x GroupType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupType.Descriptor instead.
func (GroupType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_ws_v1_ws_models_proto_rawDescGZIP(), []int{0}
}

// the ws client model
type ConnectionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// account id
	AccountId *int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// staff id
	StaffId *int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// company id
	CompanyId *int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// enterprise id
	EnterpriseId *int64 `protobuf:"varint,6,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// user agent
	UserAgent string `protobuf:"bytes,7,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	// ip
	Ip string `protobuf:"bytes,8,opt,name=ip,proto3" json:"ip,omitempty"`
	// push count
	PushCount int64 `protobuf:"varint,9,opt,name=push_count,json=pushCount,proto3" json:"push_count,omitempty"`
	// create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *ConnectionModel) Reset() {
	*x = ConnectionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_ws_v1_ws_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionModel) ProtoMessage() {}

func (x *ConnectionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_ws_v1_ws_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionModel.ProtoReflect.Descriptor instead.
func (*ConnectionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_ws_v1_ws_models_proto_rawDescGZIP(), []int{0}
}

func (x *ConnectionModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConnectionModel) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *ConnectionModel) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *ConnectionModel) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *ConnectionModel) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *ConnectionModel) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *ConnectionModel) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *ConnectionModel) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ConnectionModel) GetPushCount() int64 {
	if x != nil {
		return x.PushCount
	}
	return 0
}

func (x *ConnectionModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *ConnectionModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_moego_models_ws_v1_ws_models_proto protoreflect.FileDescriptor

var file_moego_models_ws_v1_ws_models_proto_rawDesc = []byte{
	0x0a, 0x22, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x77,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x77, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xea, 0x03, 0x0a, 0x0f, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x04, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x75, 0x73, 0x68, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x2a, 0x7a, 0x0a, 0x09, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0e, 0x0a, 0x0a, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12,
	0x0b, 0x0a, 0x07, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05,
	0x53, 0x54, 0x41, 0x46, 0x46, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x55, 0x53, 0x49, 0x4e,
	0x45, 0x53, 0x53, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59,
	0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45,
	0x10, 0x06, 0x42, 0x6c, 0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x77, 0x73, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x4c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x77, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x77, 0x73, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_ws_v1_ws_models_proto_rawDescOnce sync.Once
	file_moego_models_ws_v1_ws_models_proto_rawDescData = file_moego_models_ws_v1_ws_models_proto_rawDesc
)

func file_moego_models_ws_v1_ws_models_proto_rawDescGZIP() []byte {
	file_moego_models_ws_v1_ws_models_proto_rawDescOnce.Do(func() {
		file_moego_models_ws_v1_ws_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_ws_v1_ws_models_proto_rawDescData)
	})
	return file_moego_models_ws_v1_ws_models_proto_rawDescData
}

var file_moego_models_ws_v1_ws_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_ws_v1_ws_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_ws_v1_ws_models_proto_goTypes = []interface{}{
	(GroupType)(0),                // 0: moego.models.ws.v1.GroupType
	(*ConnectionModel)(nil),       // 1: moego.models.ws.v1.ConnectionModel
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_moego_models_ws_v1_ws_models_proto_depIdxs = []int32{
	2, // 0: moego.models.ws.v1.ConnectionModel.created_at:type_name -> google.protobuf.Timestamp
	2, // 1: moego.models.ws.v1.ConnectionModel.updated_at:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_ws_v1_ws_models_proto_init() }
func file_moego_models_ws_v1_ws_models_proto_init() {
	if File_moego_models_ws_v1_ws_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_ws_v1_ws_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_ws_v1_ws_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_ws_v1_ws_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_ws_v1_ws_models_proto_goTypes,
		DependencyIndexes: file_moego_models_ws_v1_ws_models_proto_depIdxs,
		EnumInfos:         file_moego_models_ws_v1_ws_models_proto_enumTypes,
		MessageInfos:      file_moego_models_ws_v1_ws_models_proto_msgTypes,
	}.Build()
	File_moego_models_ws_v1_ws_models_proto = out.File
	file_moego_models_ws_v1_ws_models_proto_rawDesc = nil
	file_moego_models_ws_v1_ws_models_proto_goTypes = nil
	file_moego_models_ws_v1_ws_models_proto_depIdxs = nil
}
