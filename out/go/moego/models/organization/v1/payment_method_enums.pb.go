// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/payment_method_enums.proto

package organizationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PaymentMethod status
type PaymentMethodStatus int32

const (
	// Payment method status unspecified
	PaymentMethodStatus_PAYMENT_METHOD_STATUS_UNSPECIFIED PaymentMethodStatus = 0
	// Payment method status normal
	PaymentMethodStatus_PAYMENT_METHOD_STATUS_NORMAL PaymentMethodStatus = 1
	// Payment method status deleted
	PaymentMethodStatus_PAYMENT_METHOD_STATUS_DELETED PaymentMethodStatus = 2
)

// Enum value maps for PaymentMethodStatus.
var (
	PaymentMethodStatus_name = map[int32]string{
		0: "PAYMENT_METHOD_STATUS_UNSPECIFIED",
		1: "PAYMENT_METHOD_STATUS_NORMAL",
		2: "PAYMENT_METHOD_STATUS_DELETED",
	}
	PaymentMethodStatus_value = map[string]int32{
		"PAYMENT_METHOD_STATUS_UNSPECIFIED": 0,
		"PAYMENT_METHOD_STATUS_NORMAL":      1,
		"PAYMENT_METHOD_STATUS_DELETED":     2,
	}
)

func (x PaymentMethodStatus) Enum() *PaymentMethodStatus {
	p := new(PaymentMethodStatus)
	*p = x
	return p
}

func (x PaymentMethodStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMethodStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_payment_method_enums_proto_enumTypes[0].Descriptor()
}

func (PaymentMethodStatus) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_payment_method_enums_proto_enumTypes[0]
}

func (x PaymentMethodStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMethodStatus.Descriptor instead.
func (PaymentMethodStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_payment_method_enums_proto_rawDescGZIP(), []int{0}
}

// PaymentMethod type
type PaymentMethodType int32

const (
	// Unspecified payment method type
	PaymentMethodType_PAYMENT_METHOD_TYPE_UNSPECIFIED PaymentMethodType = 0
	// Default payment method, which cannot be deleted
	PaymentMethodType_DEFAULT PaymentMethodType = 1
	// Customized payment method, created by user, can be deleted
	PaymentMethodType_CUSTOMIZED PaymentMethodType = 2
)

// Enum value maps for PaymentMethodType.
var (
	PaymentMethodType_name = map[int32]string{
		0: "PAYMENT_METHOD_TYPE_UNSPECIFIED",
		1: "DEFAULT",
		2: "CUSTOMIZED",
	}
	PaymentMethodType_value = map[string]int32{
		"PAYMENT_METHOD_TYPE_UNSPECIFIED": 0,
		"DEFAULT":                         1,
		"CUSTOMIZED":                      2,
	}
)

func (x PaymentMethodType) Enum() *PaymentMethodType {
	p := new(PaymentMethodType)
	*p = x
	return p
}

func (x PaymentMethodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMethodType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_payment_method_enums_proto_enumTypes[1].Descriptor()
}

func (PaymentMethodType) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_payment_method_enums_proto_enumTypes[1]
}

func (x PaymentMethodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMethodType.Descriptor instead.
func (PaymentMethodType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_payment_method_enums_proto_rawDescGZIP(), []int{1}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// PaymentMethod inactive status
type PaymentMethodInactive int32

const (
	// Payment method is active
	PaymentMethodInactive_PAYMENT_METHOD_ACTIVE PaymentMethodInactive = 0
	// Payment method is inactive
	PaymentMethodInactive_PAYMENT_METHOD_INACTIVE PaymentMethodInactive = 1
)

// Enum value maps for PaymentMethodInactive.
var (
	PaymentMethodInactive_name = map[int32]string{
		0: "PAYMENT_METHOD_ACTIVE",
		1: "PAYMENT_METHOD_INACTIVE",
	}
	PaymentMethodInactive_value = map[string]int32{
		"PAYMENT_METHOD_ACTIVE":   0,
		"PAYMENT_METHOD_INACTIVE": 1,
	}
)

func (x PaymentMethodInactive) Enum() *PaymentMethodInactive {
	p := new(PaymentMethodInactive)
	*p = x
	return p
}

func (x PaymentMethodInactive) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMethodInactive) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_organization_v1_payment_method_enums_proto_enumTypes[2].Descriptor()
}

func (PaymentMethodInactive) Type() protoreflect.EnumType {
	return &file_moego_models_organization_v1_payment_method_enums_proto_enumTypes[2]
}

func (x PaymentMethodInactive) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMethodInactive.Descriptor instead.
func (PaymentMethodInactive) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_payment_method_enums_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_organization_v1_payment_method_enums_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_payment_method_enums_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2a, 0x81, 0x01, 0x0a, 0x13, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x25, 0x0a, 0x21, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f,
	0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x55, 0x0a, 0x11, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x23, 0x0a, 0x1f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48,
	0x4f, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54,
	0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45, 0x44,
	0x10, 0x02, 0x2a, 0x4f, 0x0a, 0x15, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x45, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x45, 0x10, 0x01, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_payment_method_enums_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_payment_method_enums_proto_rawDescData = file_moego_models_organization_v1_payment_method_enums_proto_rawDesc
)

func file_moego_models_organization_v1_payment_method_enums_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_payment_method_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_payment_method_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_payment_method_enums_proto_rawDescData)
	})
	return file_moego_models_organization_v1_payment_method_enums_proto_rawDescData
}

var file_moego_models_organization_v1_payment_method_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_organization_v1_payment_method_enums_proto_goTypes = []interface{}{
	(PaymentMethodStatus)(0),   // 0: moego.models.organization.v1.PaymentMethodStatus
	(PaymentMethodType)(0),     // 1: moego.models.organization.v1.PaymentMethodType
	(PaymentMethodInactive)(0), // 2: moego.models.organization.v1.PaymentMethodInactive
}
var file_moego_models_organization_v1_payment_method_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_payment_method_enums_proto_init() }
func file_moego_models_organization_v1_payment_method_enums_proto_init() {
	if File_moego_models_organization_v1_payment_method_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_payment_method_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_payment_method_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_payment_method_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_organization_v1_payment_method_enums_proto_enumTypes,
	}.Build()
	File_moego_models_organization_v1_payment_method_enums_proto = out.File
	file_moego_models_organization_v1_payment_method_enums_proto_rawDesc = nil
	file_moego_models_organization_v1_payment_method_enums_proto_goTypes = nil
	file_moego_models_organization_v1_payment_method_enums_proto_depIdxs = nil
}
