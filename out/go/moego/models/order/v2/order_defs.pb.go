// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/order/v2/order_defs.proto

package orderpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 支付层所需要的参数
type PayDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 支付类型, 在合单支付只会是 standard
	PaymentType v2.PaymentModel_PaymentType `protobuf:"varint,1,opt,name=payment_type,json=paymentType,proto3,enum=moego.models.payment.v2.PaymentModel_PaymentType" json:"payment_type,omitempty"`
	// 支付方式
	PaymentMethodType v2.PaymentMethod_MethodType `protobuf:"varint,2,opt,name=payment_method_type,json=paymentMethodType,proto3,enum=moego.models.payment.v2.PaymentMethod_MethodType" json:"payment_method_type,omitempty"`
	// 支付凭证
	PaymentMethodDetail *v2.PaymentMethod_Detail `protobuf:"bytes,3,opt,name=payment_method_detail,json=paymentMethodDetail,proto3" json:"payment_method_detail,omitempty"`
	// 是否添加cv fee,不传的时候后端判断
	AddConvenienceFee *bool `protobuf:"varint,4,opt,name=add_convenience_fee,json=addConvenienceFee,proto3,oneof" json:"add_convenience_fee,omitempty"`
	// paid by, 一般是 customer name
	Payer string `protobuf:"bytes,5,opt,name=payer,proto3" json:"payer,omitempty"`
	// payment description
	PaymentDescription string `protobuf:"bytes,6,opt,name=payment_description,json=paymentDescription,proto3" json:"payment_description,omitempty"`
	// module, e.g. grooming
	Module string `protobuf:"bytes,7,opt,name=module,proto3" json:"module,omitempty"`
}

func (x *PayDef) Reset() {
	*x = PayDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_order_v2_order_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayDef) ProtoMessage() {}

func (x *PayDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_order_v2_order_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayDef.ProtoReflect.Descriptor instead.
func (*PayDef) Descriptor() ([]byte, []int) {
	return file_moego_models_order_v2_order_defs_proto_rawDescGZIP(), []int{0}
}

func (x *PayDef) GetPaymentType() v2.PaymentModel_PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return v2.PaymentModel_PaymentType(0)
}

func (x *PayDef) GetPaymentMethodType() v2.PaymentMethod_MethodType {
	if x != nil {
		return x.PaymentMethodType
	}
	return v2.PaymentMethod_MethodType(0)
}

func (x *PayDef) GetPaymentMethodDetail() *v2.PaymentMethod_Detail {
	if x != nil {
		return x.PaymentMethodDetail
	}
	return nil
}

func (x *PayDef) GetAddConvenienceFee() bool {
	if x != nil && x.AddConvenienceFee != nil {
		return *x.AddConvenienceFee
	}
	return false
}

func (x *PayDef) GetPayer() string {
	if x != nil {
		return x.Payer
	}
	return ""
}

func (x *PayDef) GetPaymentDescription() string {
	if x != nil {
		return x.PaymentDescription
	}
	return ""
}

func (x *PayDef) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

var File_moego_models_order_v2_order_defs_proto protoreflect.FileDescriptor

var file_moego_models_order_v2_order_defs_proto_rawDesc = []byte{
	0x0a, 0x26, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x32, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x32, 0x1a,
	0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd0, 0x03,
	0x0a, 0x06, 0x50, 0x61, 0x79, 0x44, 0x65, 0x66, 0x12, 0x54, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x61,
	0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x61, 0x0a, 0x15, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x2e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x0a, 0x13, 0x61, 0x64, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x76,
	0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x11, 0x61, 0x64, 0x64, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65,
	0x6e, 0x63, 0x65, 0x46, 0x65, 0x65, 0x88, 0x01, 0x01, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x61, 0x79,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x12,
	0x2f, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x61, 0x64, 0x64,
	0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x65, 0x65,
	0x42, 0x75, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x32, 0x50, 0x01, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x32, 0x3b,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_order_v2_order_defs_proto_rawDescOnce sync.Once
	file_moego_models_order_v2_order_defs_proto_rawDescData = file_moego_models_order_v2_order_defs_proto_rawDesc
)

func file_moego_models_order_v2_order_defs_proto_rawDescGZIP() []byte {
	file_moego_models_order_v2_order_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_order_v2_order_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_order_v2_order_defs_proto_rawDescData)
	})
	return file_moego_models_order_v2_order_defs_proto_rawDescData
}

var file_moego_models_order_v2_order_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_order_v2_order_defs_proto_goTypes = []interface{}{
	(*PayDef)(nil),                   // 0: moego.models.order.v2.PayDef
	(v2.PaymentModel_PaymentType)(0), // 1: moego.models.payment.v2.PaymentModel.PaymentType
	(v2.PaymentMethod_MethodType)(0), // 2: moego.models.payment.v2.PaymentMethod.MethodType
	(*v2.PaymentMethod_Detail)(nil),  // 3: moego.models.payment.v2.PaymentMethod.Detail
}
var file_moego_models_order_v2_order_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.order.v2.PayDef.payment_type:type_name -> moego.models.payment.v2.PaymentModel.PaymentType
	2, // 1: moego.models.order.v2.PayDef.payment_method_type:type_name -> moego.models.payment.v2.PaymentMethod.MethodType
	3, // 2: moego.models.order.v2.PayDef.payment_method_detail:type_name -> moego.models.payment.v2.PaymentMethod.Detail
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_order_v2_order_defs_proto_init() }
func file_moego_models_order_v2_order_defs_proto_init() {
	if File_moego_models_order_v2_order_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_order_v2_order_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_order_v2_order_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_order_v2_order_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_order_v2_order_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_order_v2_order_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_order_v2_order_defs_proto_msgTypes,
	}.Build()
	File_moego_models_order_v2_order_defs_proto = out.File
	file_moego_models_order_v2_order_defs_proto_rawDesc = nil
	file_moego_models_order_v2_order_defs_proto_goTypes = nil
	file_moego_models_order_v2_order_defs_proto_depIdxs = nil
}
