// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/map/v1/map_models.proto

package mappb

import (
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Bounds
type Bounds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The northeast corner of the bounding box.
	Northeast *latlng.LatLng `protobuf:"bytes,1,opt,name=northeast,proto3" json:"northeast,omitempty"`
	// The southwest corner of the bounding box.
	Southwest *latlng.LatLng `protobuf:"bytes,2,opt,name=southwest,proto3" json:"southwest,omitempty"`
}

func (x *Bounds) Reset() {
	*x = Bounds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bounds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bounds) ProtoMessage() {}

func (x *Bounds) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bounds.ProtoReflect.Descriptor instead.
func (*Bounds) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{0}
}

func (x *Bounds) GetNortheast() *latlng.LatLng {
	if x != nil {
		return x.Northeast
	}
	return nil
}

func (x *Bounds) GetSouthwest() *latlng.LatLng {
	if x != nil {
		return x.Southwest
	}
	return nil
}

// Polygon
type Polygon struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// points
	Points []*latlng.LatLng `protobuf:"bytes,1,rep,name=points,proto3" json:"points,omitempty"`
}

func (x *Polygon) Reset() {
	*x = Polygon{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Polygon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Polygon) ProtoMessage() {}

func (x *Polygon) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Polygon.ProtoReflect.Descriptor instead.
func (*Polygon) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{1}
}

func (x *Polygon) GetPoints() []*latlng.LatLng {
	if x != nil {
		return x.Points
	}
	return nil
}

// Circle
type Circle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// center of circle
	Center *latlng.LatLng `protobuf:"bytes,1,opt,name=center,proto3" json:"center,omitempty"`
	// radius of circle
	Radius float64 `protobuf:"fixed64,2,opt,name=radius,proto3" json:"radius,omitempty"`
}

func (x *Circle) Reset() {
	*x = Circle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Circle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Circle) ProtoMessage() {}

func (x *Circle) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Circle.ProtoReflect.Descriptor instead.
func (*Circle) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{2}
}

func (x *Circle) GetCenter() *latlng.LatLng {
	if x != nil {
		return x.Center
	}
	return nil
}

func (x *Circle) GetRadius() float64 {
	if x != nil {
		return x.Radius
	}
	return 0
}

// LocationBias
type LocationBias struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location bias
	//
	// Types that are assignable to LocationBias:
	//
	//	*LocationBias_Circle
	//	*LocationBias_Viewport
	LocationBias isLocationBias_LocationBias `protobuf_oneof:"location_bias"`
}

func (x *LocationBias) Reset() {
	*x = LocationBias{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationBias) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationBias) ProtoMessage() {}

func (x *LocationBias) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationBias.ProtoReflect.Descriptor instead.
func (*LocationBias) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{3}
}

func (m *LocationBias) GetLocationBias() isLocationBias_LocationBias {
	if m != nil {
		return m.LocationBias
	}
	return nil
}

func (x *LocationBias) GetCircle() *Circle {
	if x, ok := x.GetLocationBias().(*LocationBias_Circle); ok {
		return x.Circle
	}
	return nil
}

func (x *LocationBias) GetViewport() *Bounds {
	if x, ok := x.GetLocationBias().(*LocationBias_Viewport); ok {
		return x.Viewport
	}
	return nil
}

type isLocationBias_LocationBias interface {
	isLocationBias_LocationBias()
}

type LocationBias_Circle struct {
	// A circle defined by a center point and radius.
	Circle *Circle `protobuf:"bytes,1,opt,name=circle,proto3,oneof"`
}

type LocationBias_Viewport struct {
	// A viewport defined by a northeast and a southwest corner.
	Viewport *Bounds `protobuf:"bytes,2,opt,name=viewport,proto3,oneof"`
}

func (*LocationBias_Circle) isLocationBias_LocationBias() {}

func (*LocationBias_Viewport) isLocationBias_LocationBias() {}

// LocationRestriction
type LocationRestriction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// location restriction
	//
	// Types that are assignable to LocationRestriction:
	//
	//	*LocationRestriction_Circle
	//	*LocationRestriction_Viewport
	LocationRestriction isLocationRestriction_LocationRestriction `protobuf_oneof:"location_restriction"`
}

func (x *LocationRestriction) Reset() {
	*x = LocationRestriction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LocationRestriction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocationRestriction) ProtoMessage() {}

func (x *LocationRestriction) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocationRestriction.ProtoReflect.Descriptor instead.
func (*LocationRestriction) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{4}
}

func (m *LocationRestriction) GetLocationRestriction() isLocationRestriction_LocationRestriction {
	if m != nil {
		return m.LocationRestriction
	}
	return nil
}

func (x *LocationRestriction) GetCircle() *Circle {
	if x, ok := x.GetLocationRestriction().(*LocationRestriction_Circle); ok {
		return x.Circle
	}
	return nil
}

func (x *LocationRestriction) GetViewport() *Bounds {
	if x, ok := x.GetLocationRestriction().(*LocationRestriction_Viewport); ok {
		return x.Viewport
	}
	return nil
}

type isLocationRestriction_LocationRestriction interface {
	isLocationRestriction_LocationRestriction()
}

type LocationRestriction_Circle struct {
	// A circle defined by a center point and radius.
	Circle *Circle `protobuf:"bytes,1,opt,name=circle,proto3,oneof"`
}

type LocationRestriction_Viewport struct {
	// A viewport defined by a northeast and a southwest corner.
	Viewport *Bounds `protobuf:"bytes,2,opt,name=viewport,proto3,oneof"`
}

func (*LocationRestriction_Circle) isLocationRestriction_LocationRestriction() {}

func (*LocationRestriction_Viewport) isLocationRestriction_LocationRestriction() {}

// SuggestedAddress
type SuggestedAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address source
	AddressSource *AddressSource `protobuf:"bytes,1,opt,name=address_source,json=addressSource,proto3" json:"address_source,omitempty"`
	// formatted address text
	FormattedAddress string `protobuf:"bytes,2,opt,name=formatted_address,json=formattedAddress,proto3" json:"formatted_address,omitempty"`
	// country name
	Country string `protobuf:"bytes,3,opt,name=country,proto3" json:"country,omitempty"`
	// the additional information of the address, for example:
	//
	//	county: "Santa Clara County"
	//	state: "California"
	//	city: "San Jose"
	//	url: "/get/NDg5YmQ5NzY5Zjk0YmI5IDUxMTQ3MTI1"
	//	distance: 1248.79
	//	plusCode: "7MJCH93V+7F"
	//	labels: ["school", "restaurant", "museum", ...]
	Additional *structpb.Struct `protobuf:"bytes,4,opt,name=additional,proto3,oneof" json:"additional,omitempty"`
}

func (x *SuggestedAddress) Reset() {
	*x = SuggestedAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SuggestedAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuggestedAddress) ProtoMessage() {}

func (x *SuggestedAddress) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuggestedAddress.ProtoReflect.Descriptor instead.
func (*SuggestedAddress) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{5}
}

func (x *SuggestedAddress) GetAddressSource() *AddressSource {
	if x != nil {
		return x.AddressSource
	}
	return nil
}

func (x *SuggestedAddress) GetFormattedAddress() string {
	if x != nil {
		return x.FormattedAddress
	}
	return ""
}

func (x *SuggestedAddress) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *SuggestedAddress) GetAdditional() *structpb.Struct {
	if x != nil {
		return x.Additional
	}
	return nil
}

// RegionModel
type RegionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id in MoeGo
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// parent id in MoeGo
	ParentId *int64 `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3,oneof" json:"parent_id,omitempty"`
	// code of region, for country, this is ISO-3166-1 numeric code
	Code *string `protobuf:"bytes,3,opt,name=code,proto3,oneof" json:"code,omitempty"`
	// code type, e.g. ISO-3166-1/numeric, ISO-3166-2, local_code
	CodeType *string `protobuf:"bytes,4,opt,name=code_type,json=codeType,proto3,oneof" json:"code_type,omitempty"`
	// ISO-3166-1 alpha-2 code
	Country string `protobuf:"bytes,5,opt,name=country,proto3" json:"country,omitempty"`
	// the short name of the region
	ShortName string `protobuf:"bytes,6,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty"`
	// the long name of the region
	LongName string `protobuf:"bytes,7,opt,name=long_name,json=longName,proto3" json:"long_name,omitempty"`
	// the formatted name of the region
	FormattedRegion string `protobuf:"bytes,8,opt,name=formatted_region,json=formattedRegion,proto3" json:"formatted_region,omitempty"`
	// the formatted local text of the region
	FormattedLocal string `protobuf:"bytes,9,opt,name=formatted_local,json=formattedLocal,proto3" json:"formatted_local,omitempty"`
	// zip/postal code
	PostalCode *string `protobuf:"bytes,10,opt,name=postal_code,json=postalCode,proto3,oneof" json:"postal_code,omitempty"`
	// the level of the region
	Level RegionLevel `protobuf:"varint,11,opt,name=level,proto3,enum=moego.models.map.v1.RegionLevel" json:"level,omitempty"`
	// the coordinate of the region
	Coordinate *latlng.LatLng `protobuf:"bytes,12,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
	// boundary area of the region
	Bounds *Bounds `protobuf:"bytes,13,opt,name=bounds,proto3" json:"bounds,omitempty"`
	// the labels of the region, e.g. province, county, municipality etc.
	Labels []string `protobuf:"bytes,14,rep,name=labels,proto3" json:"labels,omitempty"`
	// the additional information of the region, etc. ISO-3166 code
	Additional *structpb.Struct `protobuf:"bytes,15,opt,name=additional,proto3,oneof" json:"additional,omitempty"`
	// the status of the region
	Status RegionStatus `protobuf:"varint,16,opt,name=status,proto3,enum=moego.models.map.v1.RegionStatus" json:"status,omitempty"`
	// code of parent
	ParentCode *string `protobuf:"bytes,17,opt,name=parent_code,json=parentCode,proto3,oneof" json:"parent_code,omitempty"`
	// name of parent
	ParentName *string `protobuf:"bytes,18,opt,name=parent_name,json=parentName,proto3,oneof" json:"parent_name,omitempty"`
	// level of parent
	ParentLevel *RegionLevel `protobuf:"varint,19,opt,name=parent_level,json=parentLevel,proto3,enum=moego.models.map.v1.RegionLevel,oneof" json:"parent_level,omitempty"`
	// polygon points used to display the region
	Polygons []*Polygon `protobuf:"bytes,30,rep,name=polygons,proto3" json:"polygons,omitempty"`
	// the created time of the region data
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the updated time of the region data
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,32,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *RegionModel) Reset() {
	*x = RegionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionModel) ProtoMessage() {}

func (x *RegionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionModel.ProtoReflect.Descriptor instead.
func (*RegionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{6}
}

func (x *RegionModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegionModel) GetParentId() int64 {
	if x != nil && x.ParentId != nil {
		return *x.ParentId
	}
	return 0
}

func (x *RegionModel) GetCode() string {
	if x != nil && x.Code != nil {
		return *x.Code
	}
	return ""
}

func (x *RegionModel) GetCodeType() string {
	if x != nil && x.CodeType != nil {
		return *x.CodeType
	}
	return ""
}

func (x *RegionModel) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *RegionModel) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *RegionModel) GetLongName() string {
	if x != nil {
		return x.LongName
	}
	return ""
}

func (x *RegionModel) GetFormattedRegion() string {
	if x != nil {
		return x.FormattedRegion
	}
	return ""
}

func (x *RegionModel) GetFormattedLocal() string {
	if x != nil {
		return x.FormattedLocal
	}
	return ""
}

func (x *RegionModel) GetPostalCode() string {
	if x != nil && x.PostalCode != nil {
		return *x.PostalCode
	}
	return ""
}

func (x *RegionModel) GetLevel() RegionLevel {
	if x != nil {
		return x.Level
	}
	return RegionLevel_REGION_LEVEL_UNSPECIFIED
}

func (x *RegionModel) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *RegionModel) GetBounds() *Bounds {
	if x != nil {
		return x.Bounds
	}
	return nil
}

func (x *RegionModel) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *RegionModel) GetAdditional() *structpb.Struct {
	if x != nil {
		return x.Additional
	}
	return nil
}

func (x *RegionModel) GetStatus() RegionStatus {
	if x != nil {
		return x.Status
	}
	return RegionStatus_REGION_STATUS_UNSPECIFIED
}

func (x *RegionModel) GetParentCode() string {
	if x != nil && x.ParentCode != nil {
		return *x.ParentCode
	}
	return ""
}

func (x *RegionModel) GetParentName() string {
	if x != nil && x.ParentName != nil {
		return *x.ParentName
	}
	return ""
}

func (x *RegionModel) GetParentLevel() RegionLevel {
	if x != nil && x.ParentLevel != nil {
		return *x.ParentLevel
	}
	return RegionLevel_REGION_LEVEL_UNSPECIFIED
}

func (x *RegionModel) GetPolygons() []*Polygon {
	if x != nil {
		return x.Polygons
	}
	return nil
}

func (x *RegionModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RegionModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// AddressSource
// Note: the source id and type must be returned by map service
type AddressSource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the id of the address
	SourceId string `protobuf:"bytes,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// the source of the address, e.g. GOOGLE_MAP, PLUS_CODE, BING_MAP, OSM, BAIDU_MAP, AMAP, MOEGO etc.
	SourceType string `protobuf:"bytes,2,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
}

func (x *AddressSource) Reset() {
	*x = AddressSource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressSource) ProtoMessage() {}

func (x *AddressSource) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressSource.ProtoReflect.Descriptor instead.
func (*AddressSource) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{7}
}

func (x *AddressSource) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *AddressSource) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

// AddressModel
type AddressModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// formatted address text
	FormattedAddress string `protobuf:"bytes,2,opt,name=formatted_address,json=formattedAddress,proto3" json:"formatted_address,omitempty"`
	// formatted local text
	FormattedLocal string `protobuf:"bytes,3,opt,name=formatted_local,json=formattedLocal,proto3" json:"formatted_local,omitempty"`
	// the coordinates of the address
	Coordinate *latlng.LatLng `protobuf:"bytes,4,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
	// boundary area of the address
	Bounds *Bounds `protobuf:"bytes,5,opt,name=bounds,proto3" json:"bounds,omitempty"`
	// the country where this address is located, ISO-3166-1 alpha-2 code
	Country string `protobuf:"bytes,6,opt,name=country,proto3" json:"country,omitempty"`
	// the name of region level1 for the address
	Level1 *string `protobuf:"bytes,7,opt,name=level1,proto3,oneof" json:"level1,omitempty"`
	// the name of region level2 for the address
	Level2 *string `protobuf:"bytes,8,opt,name=level2,proto3,oneof" json:"level2,omitempty"`
	// the name of region level3 for the address
	Level3 *string `protobuf:"bytes,9,opt,name=level3,proto3,oneof" json:"level3,omitempty"`
	// zip/postal code
	PostalCode *string `protobuf:"bytes,10,opt,name=postal_code,json=postalCode,proto3,oneof" json:"postal_code,omitempty"`
	// the labels of the address, e.g. park, school, hospital, etc.
	Labels []string `protobuf:"bytes,11,rep,name=labels,proto3" json:"labels,omitempty"`
	// the additional information of the address, for example:
	//
	//	state: "California"
	//	county: "Santa Clara County"
	//	city: "San Jose"
	Additional *structpb.Struct `protobuf:"bytes,12,opt,name=additional,proto3,oneof" json:"additional,omitempty"`
	// source id
	SourceId *string `protobuf:"bytes,13,opt,name=source_id,json=sourceId,proto3,oneof" json:"source_id,omitempty"`
	// source type
	SourceType *string `protobuf:"bytes,14,opt,name=source_type,json=sourceType,proto3,oneof" json:"source_type,omitempty"`
	// the created time of the address data
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the updated time of the address data
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,32,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *AddressModel) Reset() {
	*x = AddressModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressModel) ProtoMessage() {}

func (x *AddressModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressModel.ProtoReflect.Descriptor instead.
func (*AddressModel) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{8}
}

func (x *AddressModel) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *AddressModel) GetFormattedAddress() string {
	if x != nil {
		return x.FormattedAddress
	}
	return ""
}

func (x *AddressModel) GetFormattedLocal() string {
	if x != nil {
		return x.FormattedLocal
	}
	return ""
}

func (x *AddressModel) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *AddressModel) GetBounds() *Bounds {
	if x != nil {
		return x.Bounds
	}
	return nil
}

func (x *AddressModel) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *AddressModel) GetLevel1() string {
	if x != nil && x.Level1 != nil {
		return *x.Level1
	}
	return ""
}

func (x *AddressModel) GetLevel2() string {
	if x != nil && x.Level2 != nil {
		return *x.Level2
	}
	return ""
}

func (x *AddressModel) GetLevel3() string {
	if x != nil && x.Level3 != nil {
		return *x.Level3
	}
	return ""
}

func (x *AddressModel) GetPostalCode() string {
	if x != nil && x.PostalCode != nil {
		return *x.PostalCode
	}
	return ""
}

func (x *AddressModel) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *AddressModel) GetAdditional() *structpb.Struct {
	if x != nil {
		return x.Additional
	}
	return nil
}

func (x *AddressModel) GetSourceId() string {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return ""
}

func (x *AddressModel) GetSourceType() string {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return ""
}

func (x *AddressModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AddressModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// PostalCodeRegion
type PostalCodeRegion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the country where this address is located, ISO-3166-1 alpha-2 code
	Country string `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	// zip/postal code
	PostalCode string `protobuf:"bytes,3,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	// formatted address text
	FormattedAddress string `protobuf:"bytes,4,opt,name=formatted_address,json=formattedAddress,proto3" json:"formatted_address,omitempty"`
	// formatted local text
	FormattedLocal string `protobuf:"bytes,5,opt,name=formatted_local,json=formattedLocal,proto3" json:"formatted_local,omitempty"`
	// the coordinates of the address
	Coordinate *latlng.LatLng `protobuf:"bytes,6,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
	// boundary area of the address
	Bounds *Bounds `protobuf:"bytes,7,opt,name=bounds,proto3" json:"bounds,omitempty"`
	// the name of region level1 for the address
	Level1 *string `protobuf:"bytes,8,opt,name=level1,proto3,oneof" json:"level1,omitempty"`
	// the name of region level2 for the address
	Level2 *string `protobuf:"bytes,9,opt,name=level2,proto3,oneof" json:"level2,omitempty"`
	// the name of region level3 for the address
	Level3 *string `protobuf:"bytes,10,opt,name=level3,proto3,oneof" json:"level3,omitempty"`
	// the labels of the address, e.g. park, school, hospital, etc.
	Labels []string `protobuf:"bytes,11,rep,name=labels,proto3" json:"labels,omitempty"`
	// the additional information of the address, for example:
	//
	//	state: "California"
	//	county: "Santa Clara County"
	//	city: "San Jose"
	Additional *structpb.Struct `protobuf:"bytes,12,opt,name=additional,proto3,oneof" json:"additional,omitempty"`
	// source id
	SourceId *string `protobuf:"bytes,13,opt,name=source_id,json=sourceId,proto3,oneof" json:"source_id,omitempty"`
	// source type
	SourceType *string `protobuf:"bytes,14,opt,name=source_type,json=sourceType,proto3,oneof" json:"source_type,omitempty"`
	// the created time of the address data
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the updated time of the address data
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,32,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *PostalCodeRegion) Reset() {
	*x = PostalCodeRegion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostalCodeRegion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostalCodeRegion) ProtoMessage() {}

func (x *PostalCodeRegion) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostalCodeRegion.ProtoReflect.Descriptor instead.
func (*PostalCodeRegion) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{9}
}

func (x *PostalCodeRegion) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PostalCodeRegion) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *PostalCodeRegion) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *PostalCodeRegion) GetFormattedAddress() string {
	if x != nil {
		return x.FormattedAddress
	}
	return ""
}

func (x *PostalCodeRegion) GetFormattedLocal() string {
	if x != nil {
		return x.FormattedLocal
	}
	return ""
}

func (x *PostalCodeRegion) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *PostalCodeRegion) GetBounds() *Bounds {
	if x != nil {
		return x.Bounds
	}
	return nil
}

func (x *PostalCodeRegion) GetLevel1() string {
	if x != nil && x.Level1 != nil {
		return *x.Level1
	}
	return ""
}

func (x *PostalCodeRegion) GetLevel2() string {
	if x != nil && x.Level2 != nil {
		return *x.Level2
	}
	return ""
}

func (x *PostalCodeRegion) GetLevel3() string {
	if x != nil && x.Level3 != nil {
		return *x.Level3
	}
	return ""
}

func (x *PostalCodeRegion) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *PostalCodeRegion) GetAdditional() *structpb.Struct {
	if x != nil {
		return x.Additional
	}
	return nil
}

func (x *PostalCodeRegion) GetSourceId() string {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return ""
}

func (x *PostalCodeRegion) GetSourceType() string {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return ""
}

func (x *PostalCodeRegion) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PostalCodeRegion) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Country
type CountryModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the country
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// official name of the country
	OfficialName string `protobuf:"bytes,2,opt,name=official_name,json=officialName,proto3" json:"official_name,omitempty"`
	// local name of the country
	LocalName string `protobuf:"bytes,3,opt,name=local_name,json=localName,proto3" json:"local_name,omitempty"`
	// whether the country is independent
	Independent bool `protobuf:"varint,4,opt,name=independent,proto3" json:"independent,omitempty"`
	// ISO-3166-1 numeric code
	NumericCode string `protobuf:"bytes,5,opt,name=numeric_code,json=numericCode,proto3" json:"numeric_code,omitempty"`
	// ISO-3166-1 alpha-2 code
	Alpha2Code string `protobuf:"bytes,6,opt,name=alpha2_code,json=alpha2Code,proto3" json:"alpha2_code,omitempty"`
	// ISO-3166-1 alpha-3 code
	Alpha3Code string `protobuf:"bytes,7,opt,name=alpha3_code,json=alpha3Code,proto3" json:"alpha3_code,omitempty"`
	// capital of country
	Capital *string `protobuf:"bytes,8,opt,name=capital,proto3,oneof" json:"capital,omitempty"`
	// the continent where the country is located
	Region *string `protobuf:"bytes,9,opt,name=region,proto3,oneof" json:"region,omitempty"`
	// the continent code where the country is located
	RegionCode *string `protobuf:"bytes,10,opt,name=region_code,json=regionCode,proto3,oneof" json:"region_code,omitempty"`
	// official language code
	LanguageCode *string `protobuf:"bytes,11,opt,name=language_code,json=languageCode,proto3,oneof" json:"language_code,omitempty"`
	// ISO-3166-1 ccTLD
	CcTld []string `protobuf:"bytes,12,rep,name=cc_tld,json=ccTld,proto3" json:"cc_tld,omitempty"`
	// calling code of country
	CallingCodes []string `protobuf:"bytes,13,rep,name=calling_codes,json=callingCodes,proto3" json:"calling_codes,omitempty"`
	// timezones of country, ISO-8601 offset format, e.g. UTC+08:00
	// see: https://en.wikipedia.org/wiki/ISO_8601#Time_zone_designators
	// Note: this field can contain one or two values.
	// If there is only one value, it indicates that this country has only this time zone.
	// If there are two values, it expresses the minimum and maximum time zones.
	Timezones []string `protobuf:"bytes,14,rep,name=timezones,proto3" json:"timezones,omitempty"`
	// ISO-4217 currency code
	Currencies []*CountryModel_Currency `protobuf:"bytes,15,rep,name=currencies,proto3" json:"currencies,omitempty"`
	// some territories that are sovereignty under the jurisdiction of this country, ISO-3166-1/alpha-2 code list
	// For example:
	//
	//	for United States: Guam, Puerto Rico, American Samoa, Northern Mariana Islands, U.S. Virgin Islands
	//	for United Kingdom: Anguilla, Bermuda, British Virgin Islands, Cayman Islands ...
	//	for China: Hong Kong, Macao, Taiwan
	Territories []string `protobuf:"bytes,16,rep,name=territories,proto3" json:"territories,omitempty"`
	// specify whether postal codes are supported
	SupportPostalCode bool `protobuf:"varint,17,opt,name=support_postal_code,json=supportPostalCode,proto3" json:"support_postal_code,omitempty"`
}

func (x *CountryModel) Reset() {
	*x = CountryModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountryModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountryModel) ProtoMessage() {}

func (x *CountryModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountryModel.ProtoReflect.Descriptor instead.
func (*CountryModel) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{10}
}

func (x *CountryModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CountryModel) GetOfficialName() string {
	if x != nil {
		return x.OfficialName
	}
	return ""
}

func (x *CountryModel) GetLocalName() string {
	if x != nil {
		return x.LocalName
	}
	return ""
}

func (x *CountryModel) GetIndependent() bool {
	if x != nil {
		return x.Independent
	}
	return false
}

func (x *CountryModel) GetNumericCode() string {
	if x != nil {
		return x.NumericCode
	}
	return ""
}

func (x *CountryModel) GetAlpha2Code() string {
	if x != nil {
		return x.Alpha2Code
	}
	return ""
}

func (x *CountryModel) GetAlpha3Code() string {
	if x != nil {
		return x.Alpha3Code
	}
	return ""
}

func (x *CountryModel) GetCapital() string {
	if x != nil && x.Capital != nil {
		return *x.Capital
	}
	return ""
}

func (x *CountryModel) GetRegion() string {
	if x != nil && x.Region != nil {
		return *x.Region
	}
	return ""
}

func (x *CountryModel) GetRegionCode() string {
	if x != nil && x.RegionCode != nil {
		return *x.RegionCode
	}
	return ""
}

func (x *CountryModel) GetLanguageCode() string {
	if x != nil && x.LanguageCode != nil {
		return *x.LanguageCode
	}
	return ""
}

func (x *CountryModel) GetCcTld() []string {
	if x != nil {
		return x.CcTld
	}
	return nil
}

func (x *CountryModel) GetCallingCodes() []string {
	if x != nil {
		return x.CallingCodes
	}
	return nil
}

func (x *CountryModel) GetTimezones() []string {
	if x != nil {
		return x.Timezones
	}
	return nil
}

func (x *CountryModel) GetCurrencies() []*CountryModel_Currency {
	if x != nil {
		return x.Currencies
	}
	return nil
}

func (x *CountryModel) GetTerritories() []string {
	if x != nil {
		return x.Territories
	}
	return nil
}

func (x *CountryModel) GetSupportPostalCode() bool {
	if x != nil {
		return x.SupportPostalCode
	}
	return false
}

// models carrying postal addresses
// optimized based on fields in AddressModel
type PostalAddressModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// formatted address
	FormattedAddress string `protobuf:"bytes,2,opt,name=formatted_address,json=formattedAddress,proto3" json:"formatted_address,omitempty"`
	// local
	FormattedLocal string `protobuf:"bytes,3,opt,name=formatted_local,json=formattedLocal,proto3" json:"formatted_local,omitempty"`
	// lat lng
	LatLng *latlng.LatLng `protobuf:"bytes,4,opt,name=lat_lng,json=latLng,proto3" json:"lat_lng,omitempty"`
	// bounds
	Bounds *Bounds `protobuf:"bytes,5,opt,name=bounds,proto3" json:"bounds,omitempty"`
	// address
	PostalAddress *postaladdress.PostalAddress `protobuf:"bytes,6,opt,name=postal_address,json=postalAddress,proto3" json:"postal_address,omitempty"`
	// labels
	Labels []string `protobuf:"bytes,11,rep,name=labels,proto3" json:"labels,omitempty"`
	// additional
	Additional *structpb.Struct `protobuf:"bytes,12,opt,name=additional,proto3,oneof" json:"additional,omitempty"`
	// source id
	SourceId *string `protobuf:"bytes,13,opt,name=source_id,json=sourceId,proto3,oneof" json:"source_id,omitempty"`
	// source type
	SourceType *string `protobuf:"bytes,14,opt,name=source_type,json=sourceType,proto3,oneof" json:"source_type,omitempty"` // reserved 15 to 30;
	// created time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,31,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,32,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *PostalAddressModel) Reset() {
	*x = PostalAddressModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostalAddressModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostalAddressModel) ProtoMessage() {}

func (x *PostalAddressModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostalAddressModel.ProtoReflect.Descriptor instead.
func (*PostalAddressModel) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{11}
}

func (x *PostalAddressModel) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *PostalAddressModel) GetFormattedAddress() string {
	if x != nil {
		return x.FormattedAddress
	}
	return ""
}

func (x *PostalAddressModel) GetFormattedLocal() string {
	if x != nil {
		return x.FormattedLocal
	}
	return ""
}

func (x *PostalAddressModel) GetLatLng() *latlng.LatLng {
	if x != nil {
		return x.LatLng
	}
	return nil
}

func (x *PostalAddressModel) GetBounds() *Bounds {
	if x != nil {
		return x.Bounds
	}
	return nil
}

func (x *PostalAddressModel) GetPostalAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.PostalAddress
	}
	return nil
}

func (x *PostalAddressModel) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *PostalAddressModel) GetAdditional() *structpb.Struct {
	if x != nil {
		return x.Additional
	}
	return nil
}

func (x *PostalAddressModel) GetSourceId() string {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return ""
}

func (x *PostalAddressModel) GetSourceType() string {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return ""
}

func (x *PostalAddressModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PostalAddressModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Currency
type CountryModel_Currency struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ISO-4217 currency code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// ISO-4217 number code
	Number string `protobuf:"bytes,2,opt,name=number,proto3" json:"number,omitempty"`
	// the name of the currency
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// the symbol of the currency
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
}

func (x *CountryModel_Currency) Reset() {
	*x = CountryModel_Currency{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_map_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountryModel_Currency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountryModel_Currency) ProtoMessage() {}

func (x *CountryModel_Currency) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_map_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountryModel_Currency.ProtoReflect.Descriptor instead.
func (*CountryModel_Currency) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_map_models_proto_rawDescGZIP(), []int{10, 0}
}

func (x *CountryModel_Currency) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CountryModel_Currency) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *CountryModel_Currency) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CountryModel_Currency) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

var File_moego_models_map_v1_map_models_proto protoreflect.FileDescriptor

var file_moego_models_map_v1_map_models_proto_rawDesc = []byte{
	0x0a, 0x24, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x61, 0x70, 0x5f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6e, 0x0a, 0x06, 0x42,
	0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x09, 0x6e, 0x6f, 0x72, 0x74, 0x68, 0x65, 0x61,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x09, 0x6e,
	0x6f, 0x72, 0x74, 0x68, 0x65, 0x61, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x74,
	0x68, 0x77, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67,
	0x52, 0x09, 0x73, 0x6f, 0x75, 0x74, 0x68, 0x77, 0x65, 0x73, 0x74, 0x22, 0x36, 0x0a, 0x07, 0x50,
	0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x06, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x22, 0x4d, 0x0a, 0x06, 0x43, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x12, 0x2b, 0x0a,
	0x06, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c,
	0x6e, 0x67, 0x52, 0x06, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x72, 0x61, 0x64, 0x69,
	0x75, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x69, 0x61, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x63, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x6c, 0x65,
	0x48, 0x00, 0x52, 0x06, 0x63, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x76, 0x69,
	0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x48, 0x00, 0x52, 0x08, 0x76, 0x69, 0x65,
	0x77, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x62, 0x69, 0x61, 0x73, 0x22, 0x9f, 0x01, 0x0a, 0x13, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35,
	0x0a, 0x06, 0x63, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x06, 0x63,
	0x69, 0x72, 0x63, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x75, 0x6e, 0x64, 0x73, 0x48, 0x00, 0x52, 0x08, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74,
	0x42, 0x16, 0x0a, 0x14, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xf1, 0x01, 0x0a, 0x10, 0x53, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x49, 0x0a,
	0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x3c, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x00, 0x52, 0x0a,
	0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x22, 0xb8, 0x08, 0x0a,
	0x0b, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x09,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x00, 0x52, 0x08, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x17,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x08, 0x63, 0x6f,
	0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x74, 0x65, 0x64, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x4c, 0x6f,
	0x63, 0x61, 0x6c, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x36, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0a, 0x63, 0x6f, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x75,
	0x6e, 0x64, 0x73, 0x52, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x48, 0x04, 0x52, 0x0a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x88, 0x01,
	0x01, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0b,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x05, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x48, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x48, 0x07, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x88,
	0x01, 0x01, 0x12, 0x38, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x73, 0x18, 0x1e,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x6c, 0x79, 0x67,
	0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x79, 0x67, 0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x4d, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xf3, 0x05, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x11,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74,
	0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63,
	0x61, 0x6c, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0a, 0x63, 0x6f, 0x6f,
	0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x75, 0x6e, 0x64, 0x73, 0x52, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31,
	0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x88, 0x01, 0x01,
	0x12, 0x1b, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x03, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a,
	0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x04, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x05, 0x52, 0x0a, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x08,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x07, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x32, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xd6, 0x05, 0x0a,
	0x10, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x70,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x11,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74,
	0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63,
	0x61, 0x6c, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x0a, 0x63, 0x6f, 0x6f,
	0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f,
	0x75, 0x6e, 0x64, 0x73, 0x52, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x1b, 0x0a, 0x06,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x32, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33,
	0x88, 0x01, 0x01, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x61,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x03, 0x52, 0x0a, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x08,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x05, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x31, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x42, 0x09, 0x0a,
	0x07, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x8e, 0x06, 0x0a, 0x0c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x66,
	0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x69, 0x6e, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x6e, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x74,
	0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x32, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x32,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x33, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x33, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x07, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61,
	0x6c, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x24, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03,
	0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x63, 0x5f, 0x74, 0x6c, 0x64, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x63, 0x54, 0x6c, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x0a, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x0a, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x65, 0x72, 0x72, 0x69,
	0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x65,
	0x72, 0x72, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x50,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x62, 0x0a, 0x08, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xed, 0x04, 0x0a, 0x12, 0x50, 0x6f, 0x73, 0x74, 0x61,
	0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x13, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x2b, 0x0a, 0x11, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x27, 0x0a, 0x0f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x74, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x12, 0x2c, 0x0a, 0x07, 0x6c, 0x61, 0x74, 0x5f,
	0x6c, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x06,
	0x6c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x12, 0x33, 0x0a, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x75,
	0x6e, 0x64, 0x73, 0x52, 0x06, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x70,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x0d, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x6f, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x61, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76,
	0x31, 0x3b, 0x6d, 0x61, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_map_v1_map_models_proto_rawDescOnce sync.Once
	file_moego_models_map_v1_map_models_proto_rawDescData = file_moego_models_map_v1_map_models_proto_rawDesc
)

func file_moego_models_map_v1_map_models_proto_rawDescGZIP() []byte {
	file_moego_models_map_v1_map_models_proto_rawDescOnce.Do(func() {
		file_moego_models_map_v1_map_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_map_v1_map_models_proto_rawDescData)
	})
	return file_moego_models_map_v1_map_models_proto_rawDescData
}

var file_moego_models_map_v1_map_models_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_models_map_v1_map_models_proto_goTypes = []interface{}{
	(*Bounds)(nil),                      // 0: moego.models.map.v1.Bounds
	(*Polygon)(nil),                     // 1: moego.models.map.v1.Polygon
	(*Circle)(nil),                      // 2: moego.models.map.v1.Circle
	(*LocationBias)(nil),                // 3: moego.models.map.v1.LocationBias
	(*LocationRestriction)(nil),         // 4: moego.models.map.v1.LocationRestriction
	(*SuggestedAddress)(nil),            // 5: moego.models.map.v1.SuggestedAddress
	(*RegionModel)(nil),                 // 6: moego.models.map.v1.RegionModel
	(*AddressSource)(nil),               // 7: moego.models.map.v1.AddressSource
	(*AddressModel)(nil),                // 8: moego.models.map.v1.AddressModel
	(*PostalCodeRegion)(nil),            // 9: moego.models.map.v1.PostalCodeRegion
	(*CountryModel)(nil),                // 10: moego.models.map.v1.CountryModel
	(*PostalAddressModel)(nil),          // 11: moego.models.map.v1.PostalAddressModel
	(*CountryModel_Currency)(nil),       // 12: moego.models.map.v1.CountryModel.Currency
	(*latlng.LatLng)(nil),               // 13: google.type.LatLng
	(*structpb.Struct)(nil),             // 14: google.protobuf.Struct
	(RegionLevel)(0),                    // 15: moego.models.map.v1.RegionLevel
	(RegionStatus)(0),                   // 16: moego.models.map.v1.RegionStatus
	(*timestamppb.Timestamp)(nil),       // 17: google.protobuf.Timestamp
	(*postaladdress.PostalAddress)(nil), // 18: google.type.PostalAddress
}
var file_moego_models_map_v1_map_models_proto_depIdxs = []int32{
	13, // 0: moego.models.map.v1.Bounds.northeast:type_name -> google.type.LatLng
	13, // 1: moego.models.map.v1.Bounds.southwest:type_name -> google.type.LatLng
	13, // 2: moego.models.map.v1.Polygon.points:type_name -> google.type.LatLng
	13, // 3: moego.models.map.v1.Circle.center:type_name -> google.type.LatLng
	2,  // 4: moego.models.map.v1.LocationBias.circle:type_name -> moego.models.map.v1.Circle
	0,  // 5: moego.models.map.v1.LocationBias.viewport:type_name -> moego.models.map.v1.Bounds
	2,  // 6: moego.models.map.v1.LocationRestriction.circle:type_name -> moego.models.map.v1.Circle
	0,  // 7: moego.models.map.v1.LocationRestriction.viewport:type_name -> moego.models.map.v1.Bounds
	7,  // 8: moego.models.map.v1.SuggestedAddress.address_source:type_name -> moego.models.map.v1.AddressSource
	14, // 9: moego.models.map.v1.SuggestedAddress.additional:type_name -> google.protobuf.Struct
	15, // 10: moego.models.map.v1.RegionModel.level:type_name -> moego.models.map.v1.RegionLevel
	13, // 11: moego.models.map.v1.RegionModel.coordinate:type_name -> google.type.LatLng
	0,  // 12: moego.models.map.v1.RegionModel.bounds:type_name -> moego.models.map.v1.Bounds
	14, // 13: moego.models.map.v1.RegionModel.additional:type_name -> google.protobuf.Struct
	16, // 14: moego.models.map.v1.RegionModel.status:type_name -> moego.models.map.v1.RegionStatus
	15, // 15: moego.models.map.v1.RegionModel.parent_level:type_name -> moego.models.map.v1.RegionLevel
	1,  // 16: moego.models.map.v1.RegionModel.polygons:type_name -> moego.models.map.v1.Polygon
	17, // 17: moego.models.map.v1.RegionModel.created_at:type_name -> google.protobuf.Timestamp
	17, // 18: moego.models.map.v1.RegionModel.updated_at:type_name -> google.protobuf.Timestamp
	13, // 19: moego.models.map.v1.AddressModel.coordinate:type_name -> google.type.LatLng
	0,  // 20: moego.models.map.v1.AddressModel.bounds:type_name -> moego.models.map.v1.Bounds
	14, // 21: moego.models.map.v1.AddressModel.additional:type_name -> google.protobuf.Struct
	17, // 22: moego.models.map.v1.AddressModel.created_at:type_name -> google.protobuf.Timestamp
	17, // 23: moego.models.map.v1.AddressModel.updated_at:type_name -> google.protobuf.Timestamp
	13, // 24: moego.models.map.v1.PostalCodeRegion.coordinate:type_name -> google.type.LatLng
	0,  // 25: moego.models.map.v1.PostalCodeRegion.bounds:type_name -> moego.models.map.v1.Bounds
	14, // 26: moego.models.map.v1.PostalCodeRegion.additional:type_name -> google.protobuf.Struct
	17, // 27: moego.models.map.v1.PostalCodeRegion.created_at:type_name -> google.protobuf.Timestamp
	17, // 28: moego.models.map.v1.PostalCodeRegion.updated_at:type_name -> google.protobuf.Timestamp
	12, // 29: moego.models.map.v1.CountryModel.currencies:type_name -> moego.models.map.v1.CountryModel.Currency
	13, // 30: moego.models.map.v1.PostalAddressModel.lat_lng:type_name -> google.type.LatLng
	0,  // 31: moego.models.map.v1.PostalAddressModel.bounds:type_name -> moego.models.map.v1.Bounds
	18, // 32: moego.models.map.v1.PostalAddressModel.postal_address:type_name -> google.type.PostalAddress
	14, // 33: moego.models.map.v1.PostalAddressModel.additional:type_name -> google.protobuf.Struct
	17, // 34: moego.models.map.v1.PostalAddressModel.created_at:type_name -> google.protobuf.Timestamp
	17, // 35: moego.models.map.v1.PostalAddressModel.updated_at:type_name -> google.protobuf.Timestamp
	36, // [36:36] is the sub-list for method output_type
	36, // [36:36] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_moego_models_map_v1_map_models_proto_init() }
func file_moego_models_map_v1_map_models_proto_init() {
	if File_moego_models_map_v1_map_models_proto != nil {
		return
	}
	file_moego_models_map_v1_map_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_map_v1_map_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bounds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Polygon); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Circle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationBias); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LocationRestriction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SuggestedAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressSource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostalCodeRegion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountryModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostalAddressModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_map_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountryModel_Currency); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_map_v1_map_models_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*LocationBias_Circle)(nil),
		(*LocationBias_Viewport)(nil),
	}
	file_moego_models_map_v1_map_models_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*LocationRestriction_Circle)(nil),
		(*LocationRestriction_Viewport)(nil),
	}
	file_moego_models_map_v1_map_models_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_map_models_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_map_models_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_map_models_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_map_models_proto_msgTypes[10].OneofWrappers = []interface{}{}
	file_moego_models_map_v1_map_models_proto_msgTypes[11].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_map_v1_map_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_map_v1_map_models_proto_goTypes,
		DependencyIndexes: file_moego_models_map_v1_map_models_proto_depIdxs,
		MessageInfos:      file_moego_models_map_v1_map_models_proto_msgTypes,
	}.Build()
	File_moego_models_map_v1_map_models_proto = out.File
	file_moego_models_map_v1_map_models_proto_rawDesc = nil
	file_moego_models_map_v1_map_models_proto_goTypes = nil
	file_moego_models_map_v1_map_models_proto_depIdxs = nil
}
