// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/agreement/v1/agreement_record_models.proto

package agreementpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AgreementRecordModel
// created according to AgreementModel, used for customer signing.
type AgreementRecordModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement record id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// agreement record uuid
	Uuid string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// agreement id
	AgreementId *int64 `protobuf:"varint,3,opt,name=agreement_id,json=agreementId,proto3,oneof" json:"agreement_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// associated target id
	TargetId *int64 `protobuf:"varint,6,opt,name=target_id,json=targetId,proto3,oneof" json:"target_id,omitempty"`
	// status: normal, deleted
	Status v1.Status `protobuf:"varint,7,opt,name=status,proto3,enum=moego.utils.v1.Status" json:"status,omitempty"`
	// associated services type, see the enum definition in ServiceType
	ServiceTypes int32 `protobuf:"varint,8,opt,name=service_types,json=serviceTypes,proto3" json:"service_types,omitempty"`
	// signed status: unsigned, signed
	SignedStatus SignedStatus `protobuf:"varint,9,opt,name=signed_status,json=signedStatus,proto3,enum=moego.models.agreement.v1.SignedStatus" json:"signed_status,omitempty"`
	// signed type, see definition in SignedType
	SignedType *SignedType `protobuf:"varint,10,opt,name=signed_type,json=signedType,proto3,enum=moego.models.agreement.v1.SignedType,oneof" json:"signed_type,omitempty"`
	// source type, see definition in SourceType
	SourceType SourceType `protobuf:"varint,11,opt,name=source_type,json=sourceType,proto3,enum=moego.models.agreement.v1.SourceType" json:"source_type,omitempty"`
	// agreement record link
	AgreementLink string `protobuf:"bytes,12,opt,name=agreement_link,json=agreementLink,proto3" json:"agreement_link,omitempty"`
	// agreement title
	AgreementTitle string `protobuf:"bytes,13,opt,name=agreement_title,json=agreementTitle,proto3" json:"agreement_title,omitempty"`
	// agreement content
	AgreementContent *string `protobuf:"bytes,14,opt,name=agreement_content,json=agreementContent,proto3,oneof" json:"agreement_content,omitempty"`
	// uploaded files: json array
	UploadFiles []string `protobuf:"bytes,15,rep,name=upload_files,json=uploadFiles,proto3" json:"upload_files,omitempty"`
	// customer signature
	Signature *string `protobuf:"bytes,16,opt,name=signature,proto3,oneof" json:"signature,omitempty"`
	// signed time: milliseconds
	SignedTime *int64 `protobuf:"varint,17,opt,name=signed_time,json=signedTime,proto3,oneof" json:"signed_time,omitempty"`
	// create time: milliseconds
	CreateTime int64 `protobuf:"varint,18,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time: milliseconds
	UpdateTime int64 `protobuf:"varint,19,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,20,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// inputs
	Inputs []string `protobuf:"bytes,21,rep,name=inputs,proto3" json:"inputs,omitempty"`
}

func (x *AgreementRecordModel) Reset() {
	*x = AgreementRecordModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgreementRecordModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgreementRecordModel) ProtoMessage() {}

func (x *AgreementRecordModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgreementRecordModel.ProtoReflect.Descriptor instead.
func (*AgreementRecordModel) Descriptor() ([]byte, []int) {
	return file_moego_models_agreement_v1_agreement_record_models_proto_rawDescGZIP(), []int{0}
}

func (x *AgreementRecordModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AgreementRecordModel) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *AgreementRecordModel) GetAgreementId() int64 {
	if x != nil && x.AgreementId != nil {
		return *x.AgreementId
	}
	return 0
}

func (x *AgreementRecordModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AgreementRecordModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AgreementRecordModel) GetTargetId() int64 {
	if x != nil && x.TargetId != nil {
		return *x.TargetId
	}
	return 0
}

func (x *AgreementRecordModel) GetStatus() v1.Status {
	if x != nil {
		return x.Status
	}
	return v1.Status(0)
}

func (x *AgreementRecordModel) GetServiceTypes() int32 {
	if x != nil {
		return x.ServiceTypes
	}
	return 0
}

func (x *AgreementRecordModel) GetSignedStatus() SignedStatus {
	if x != nil {
		return x.SignedStatus
	}
	return SignedStatus_SIGNED_STATUS_UNSPECIFIED
}

func (x *AgreementRecordModel) GetSignedType() SignedType {
	if x != nil && x.SignedType != nil {
		return *x.SignedType
	}
	return SignedType_SIGNED_TYPE_UNSPECIFIED
}

func (x *AgreementRecordModel) GetSourceType() SourceType {
	if x != nil {
		return x.SourceType
	}
	return SourceType_SOURCE_TYPE_UNSPECIFIED
}

func (x *AgreementRecordModel) GetAgreementLink() string {
	if x != nil {
		return x.AgreementLink
	}
	return ""
}

func (x *AgreementRecordModel) GetAgreementTitle() string {
	if x != nil {
		return x.AgreementTitle
	}
	return ""
}

func (x *AgreementRecordModel) GetAgreementContent() string {
	if x != nil && x.AgreementContent != nil {
		return *x.AgreementContent
	}
	return ""
}

func (x *AgreementRecordModel) GetUploadFiles() []string {
	if x != nil {
		return x.UploadFiles
	}
	return nil
}

func (x *AgreementRecordModel) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

func (x *AgreementRecordModel) GetSignedTime() int64 {
	if x != nil && x.SignedTime != nil {
		return *x.SignedTime
	}
	return 0
}

func (x *AgreementRecordModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *AgreementRecordModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *AgreementRecordModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AgreementRecordModel) GetInputs() []string {
	if x != nil {
		return x.Inputs
	}
	return nil
}

// simplified version of AgreementRecordModel for return list
type AgreementRecordSimpleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement record id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// agreement record uuid
	Uuid string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// agreement id
	AgreementId *int64 `protobuf:"varint,3,opt,name=agreement_id,json=agreementId,proto3,oneof" json:"agreement_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,5,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// associated target id
	TargetId *int64 `protobuf:"varint,6,opt,name=target_id,json=targetId,proto3,oneof" json:"target_id,omitempty"`
	// status: normal, deleted
	Status v1.Status `protobuf:"varint,7,opt,name=status,proto3,enum=moego.utils.v1.Status" json:"status,omitempty"`
	// associated services type, see the enum definition in ServiceType
	ServiceTypes int32 `protobuf:"varint,8,opt,name=service_types,json=serviceTypes,proto3" json:"service_types,omitempty"`
	// signed status: unsigned, signed
	SignedStatus SignedStatus `protobuf:"varint,9,opt,name=signed_status,json=signedStatus,proto3,enum=moego.models.agreement.v1.SignedStatus" json:"signed_status,omitempty"`
	// signed type, see definition in SignedType
	SignedType *SignedType `protobuf:"varint,10,opt,name=signed_type,json=signedType,proto3,enum=moego.models.agreement.v1.SignedType,oneof" json:"signed_type,omitempty"`
	// source type, see definition in SourceType
	SourceType SourceType `protobuf:"varint,11,opt,name=source_type,json=sourceType,proto3,enum=moego.models.agreement.v1.SourceType" json:"source_type,omitempty"`
	// agreement record link
	AgreementLink string `protobuf:"bytes,12,opt,name=agreement_link,json=agreementLink,proto3" json:"agreement_link,omitempty"`
	// agreement title
	AgreementTitle string `protobuf:"bytes,13,opt,name=agreement_title,json=agreementTitle,proto3" json:"agreement_title,omitempty"`
	// signed time: milliseconds
	SignedTime *int64 `protobuf:"varint,14,opt,name=signed_time,json=signedTime,proto3,oneof" json:"signed_time,omitempty"`
	// create time: milliseconds
	CreateTime int64 `protobuf:"varint,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time: milliseconds
	UpdateTime int64 `protobuf:"varint,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// customer signature
	Signature *string `protobuf:"bytes,17,opt,name=signature,proto3,oneof" json:"signature,omitempty"`
	// agreement content
	AgreementContent *string `protobuf:"bytes,18,opt,name=agreement_content,json=agreementContent,proto3,oneof" json:"agreement_content,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,19,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// inputs
	Inputs []string `protobuf:"bytes,20,rep,name=inputs,proto3" json:"inputs,omitempty"`
}

func (x *AgreementRecordSimpleView) Reset() {
	*x = AgreementRecordSimpleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgreementRecordSimpleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgreementRecordSimpleView) ProtoMessage() {}

func (x *AgreementRecordSimpleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgreementRecordSimpleView.ProtoReflect.Descriptor instead.
func (*AgreementRecordSimpleView) Descriptor() ([]byte, []int) {
	return file_moego_models_agreement_v1_agreement_record_models_proto_rawDescGZIP(), []int{1}
}

func (x *AgreementRecordSimpleView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *AgreementRecordSimpleView) GetAgreementId() int64 {
	if x != nil && x.AgreementId != nil {
		return *x.AgreementId
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetTargetId() int64 {
	if x != nil && x.TargetId != nil {
		return *x.TargetId
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetStatus() v1.Status {
	if x != nil {
		return x.Status
	}
	return v1.Status(0)
}

func (x *AgreementRecordSimpleView) GetServiceTypes() int32 {
	if x != nil {
		return x.ServiceTypes
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetSignedStatus() SignedStatus {
	if x != nil {
		return x.SignedStatus
	}
	return SignedStatus_SIGNED_STATUS_UNSPECIFIED
}

func (x *AgreementRecordSimpleView) GetSignedType() SignedType {
	if x != nil && x.SignedType != nil {
		return *x.SignedType
	}
	return SignedType_SIGNED_TYPE_UNSPECIFIED
}

func (x *AgreementRecordSimpleView) GetSourceType() SourceType {
	if x != nil {
		return x.SourceType
	}
	return SourceType_SOURCE_TYPE_UNSPECIFIED
}

func (x *AgreementRecordSimpleView) GetAgreementLink() string {
	if x != nil {
		return x.AgreementLink
	}
	return ""
}

func (x *AgreementRecordSimpleView) GetAgreementTitle() string {
	if x != nil {
		return x.AgreementTitle
	}
	return ""
}

func (x *AgreementRecordSimpleView) GetSignedTime() int64 {
	if x != nil && x.SignedTime != nil {
		return *x.SignedTime
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetSignature() string {
	if x != nil && x.Signature != nil {
		return *x.Signature
	}
	return ""
}

func (x *AgreementRecordSimpleView) GetAgreementContent() string {
	if x != nil && x.AgreementContent != nil {
		return *x.AgreementContent
	}
	return ""
}

func (x *AgreementRecordSimpleView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AgreementRecordSimpleView) GetInputs() []string {
	if x != nil {
		return x.Inputs
	}
	return nil
}

// agreement list with recent signed record
type AgreementWithRecentRecordsView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// agreement id
	AgreementId int64 `protobuf:"varint,2,opt,name=agreement_id,json=agreementId,proto3" json:"agreement_id,omitempty"`
	// agreement title
	AgreementTitle string `protobuf:"bytes,3,opt,name=agreement_title,json=agreementTitle,proto3" json:"agreement_title,omitempty"`
	// recent signed time
	RecentSignedTime int64 `protobuf:"varint,4,opt,name=recent_signed_time,json=recentSignedTime,proto3" json:"recent_signed_time,omitempty"`
	// agreement record view list
	AgreementRecordSimpleView []*AgreementRecordSimpleView `protobuf:"bytes,5,rep,name=agreement_record_simple_view,json=agreementRecordSimpleView,proto3" json:"agreement_record_simple_view,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *AgreementWithRecentRecordsView) Reset() {
	*x = AgreementWithRecentRecordsView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgreementWithRecentRecordsView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgreementWithRecentRecordsView) ProtoMessage() {}

func (x *AgreementWithRecentRecordsView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgreementWithRecentRecordsView.ProtoReflect.Descriptor instead.
func (*AgreementWithRecentRecordsView) Descriptor() ([]byte, []int) {
	return file_moego_models_agreement_v1_agreement_record_models_proto_rawDescGZIP(), []int{2}
}

func (x *AgreementWithRecentRecordsView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AgreementWithRecentRecordsView) GetAgreementId() int64 {
	if x != nil {
		return x.AgreementId
	}
	return 0
}

func (x *AgreementWithRecentRecordsView) GetAgreementTitle() string {
	if x != nil {
		return x.AgreementTitle
	}
	return ""
}

func (x *AgreementWithRecentRecordsView) GetRecentSignedTime() int64 {
	if x != nil {
		return x.RecentSignedTime
	}
	return 0
}

func (x *AgreementWithRecentRecordsView) GetAgreementRecordSimpleView() []*AgreementRecordSimpleView {
	if x != nil {
		return x.AgreementRecordSimpleView
	}
	return nil
}

func (x *AgreementWithRecentRecordsView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// AgreementWithRecentRecordsView list
type AgreementWithRecentRecordsViewList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement list with recent signed record
	Values []*AgreementWithRecentRecordsView `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *AgreementWithRecentRecordsViewList) Reset() {
	*x = AgreementWithRecentRecordsViewList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgreementWithRecentRecordsViewList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgreementWithRecentRecordsViewList) ProtoMessage() {}

func (x *AgreementWithRecentRecordsViewList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgreementWithRecentRecordsViewList.ProtoReflect.Descriptor instead.
func (*AgreementWithRecentRecordsViewList) Descriptor() ([]byte, []int) {
	return file_moego_models_agreement_v1_agreement_record_models_proto_rawDescGZIP(), []int{3}
}

func (x *AgreementWithRecentRecordsViewList) GetValues() []*AgreementWithRecentRecordsView {
	if x != nil {
		return x.Values
	}
	return nil
}

// unsigned agreement record list
type UnsignedAgreementRecordListView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement record view list
	AgreementRecordSimpleView []*AgreementRecordSimpleView `protobuf:"bytes,1,rep,name=agreement_record_simple_view,json=agreementRecordSimpleView,proto3" json:"agreement_record_simple_view,omitempty"`
}

func (x *UnsignedAgreementRecordListView) Reset() {
	*x = UnsignedAgreementRecordListView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnsignedAgreementRecordListView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsignedAgreementRecordListView) ProtoMessage() {}

func (x *UnsignedAgreementRecordListView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsignedAgreementRecordListView.ProtoReflect.Descriptor instead.
func (*UnsignedAgreementRecordListView) Descriptor() ([]byte, []int) {
	return file_moego_models_agreement_v1_agreement_record_models_proto_rawDescGZIP(), []int{4}
}

func (x *UnsignedAgreementRecordListView) GetAgreementRecordSimpleView() []*AgreementRecordSimpleView {
	if x != nil {
		return x.AgreementRecordSimpleView
	}
	return nil
}

var File_moego_models_agreement_v1_agreement_record_models_proto protoreflect.FileDescriptor

var file_moego_models_agreement_v1_agreement_record_models_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc8, 0x07, 0x0a, 0x14,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0c, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x0b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x02, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x11,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x10, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x21,
	0x0a, 0x0c, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x73, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x48, 0x05, 0x52, 0x0a, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xaa, 0x07, 0x0a, 0x19, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0c, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x0b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x48, 0x02, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x6e, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0b,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x03, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x05, 0x52, 0x10, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x14, 0x0a,
	0x12, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x22, 0xd1, 0x02, 0x0a, 0x1e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x73, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x10, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x75, 0x0a, 0x1c, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f, 0x76, 0x69, 0x65,
	0x77, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x19, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x77, 0x0a, 0x22, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x56, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x51, 0x0a,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x22, 0x98, 0x01, 0x0a, 0x1f, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x75, 0x0a, 0x1c, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f,
	0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x19, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x42, 0x81, 0x01, 0x0a, 0x21,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x3b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_agreement_v1_agreement_record_models_proto_rawDescOnce sync.Once
	file_moego_models_agreement_v1_agreement_record_models_proto_rawDescData = file_moego_models_agreement_v1_agreement_record_models_proto_rawDesc
)

func file_moego_models_agreement_v1_agreement_record_models_proto_rawDescGZIP() []byte {
	file_moego_models_agreement_v1_agreement_record_models_proto_rawDescOnce.Do(func() {
		file_moego_models_agreement_v1_agreement_record_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_agreement_v1_agreement_record_models_proto_rawDescData)
	})
	return file_moego_models_agreement_v1_agreement_record_models_proto_rawDescData
}

var file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_agreement_v1_agreement_record_models_proto_goTypes = []interface{}{
	(*AgreementRecordModel)(nil),               // 0: moego.models.agreement.v1.AgreementRecordModel
	(*AgreementRecordSimpleView)(nil),          // 1: moego.models.agreement.v1.AgreementRecordSimpleView
	(*AgreementWithRecentRecordsView)(nil),     // 2: moego.models.agreement.v1.AgreementWithRecentRecordsView
	(*AgreementWithRecentRecordsViewList)(nil), // 3: moego.models.agreement.v1.AgreementWithRecentRecordsViewList
	(*UnsignedAgreementRecordListView)(nil),    // 4: moego.models.agreement.v1.UnsignedAgreementRecordListView
	(v1.Status)(0),                             // 5: moego.utils.v1.Status
	(SignedStatus)(0),                          // 6: moego.models.agreement.v1.SignedStatus
	(SignedType)(0),                            // 7: moego.models.agreement.v1.SignedType
	(SourceType)(0),                            // 8: moego.models.agreement.v1.SourceType
}
var file_moego_models_agreement_v1_agreement_record_models_proto_depIdxs = []int32{
	5,  // 0: moego.models.agreement.v1.AgreementRecordModel.status:type_name -> moego.utils.v1.Status
	6,  // 1: moego.models.agreement.v1.AgreementRecordModel.signed_status:type_name -> moego.models.agreement.v1.SignedStatus
	7,  // 2: moego.models.agreement.v1.AgreementRecordModel.signed_type:type_name -> moego.models.agreement.v1.SignedType
	8,  // 3: moego.models.agreement.v1.AgreementRecordModel.source_type:type_name -> moego.models.agreement.v1.SourceType
	5,  // 4: moego.models.agreement.v1.AgreementRecordSimpleView.status:type_name -> moego.utils.v1.Status
	6,  // 5: moego.models.agreement.v1.AgreementRecordSimpleView.signed_status:type_name -> moego.models.agreement.v1.SignedStatus
	7,  // 6: moego.models.agreement.v1.AgreementRecordSimpleView.signed_type:type_name -> moego.models.agreement.v1.SignedType
	8,  // 7: moego.models.agreement.v1.AgreementRecordSimpleView.source_type:type_name -> moego.models.agreement.v1.SourceType
	1,  // 8: moego.models.agreement.v1.AgreementWithRecentRecordsView.agreement_record_simple_view:type_name -> moego.models.agreement.v1.AgreementRecordSimpleView
	2,  // 9: moego.models.agreement.v1.AgreementWithRecentRecordsViewList.values:type_name -> moego.models.agreement.v1.AgreementWithRecentRecordsView
	1,  // 10: moego.models.agreement.v1.UnsignedAgreementRecordListView.agreement_record_simple_view:type_name -> moego.models.agreement.v1.AgreementRecordSimpleView
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_models_agreement_v1_agreement_record_models_proto_init() }
func file_moego_models_agreement_v1_agreement_record_models_proto_init() {
	if File_moego_models_agreement_v1_agreement_record_models_proto != nil {
		return
	}
	file_moego_models_agreement_v1_agreement_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgreementRecordModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgreementRecordSimpleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgreementWithRecentRecordsView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgreementWithRecentRecordsViewList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnsignedAgreementRecordListView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_agreement_v1_agreement_record_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_agreement_v1_agreement_record_models_proto_goTypes,
		DependencyIndexes: file_moego_models_agreement_v1_agreement_record_models_proto_depIdxs,
		MessageInfos:      file_moego_models_agreement_v1_agreement_record_models_proto_msgTypes,
	}.Build()
	File_moego_models_agreement_v1_agreement_record_models_proto = out.File
	file_moego_models_agreement_v1_agreement_record_models_proto_rawDesc = nil
	file_moego_models_agreement_v1_agreement_record_models_proto_goTypes = nil
	file_moego_models_agreement_v1_agreement_record_models_proto_depIdxs = nil
}
