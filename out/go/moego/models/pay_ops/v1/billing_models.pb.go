// @since 2-23-11-21
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/pay_ops/v1/billing_models.proto

package payopspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// corresponding to the stripe_company_customer table in the mysql
type StripeCompanyCustomerModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int32 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id registered on stripe platform
	StripeCustomerId string `protobuf:"bytes,3,opt,name=stripe_customer_id,json=stripeCustomerId,proto3" json:"stripe_customer_id,omitempty"`
	// is the stripe platform parameter
	// an account or a customer
	Object string `protobuf:"bytes,4,opt,name=object,proto3" json:"object,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// currency type
	Currency string `protobuf:"bytes,7,opt,name=currency,proto3" json:"currency,omitempty"`
	// email
	Email string `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
	// indicates whether the object exists in real-time mode or test mode
	// true or false.
	LiveMode string `protobuf:"bytes,9,opt,name=live_mode,json=liveMode,proto3" json:"live_mode,omitempty"`
	// json transparent transmission parameters (array)
	Metadata string `protobuf:"bytes,10,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Status 1 - Normal 0 - Deleted
	Status int32 `protobuf:"varint,11,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *StripeCompanyCustomerModel) Reset() {
	*x = StripeCompanyCustomerModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeCompanyCustomerModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeCompanyCustomerModel) ProtoMessage() {}

func (x *StripeCompanyCustomerModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeCompanyCustomerModel.ProtoReflect.Descriptor instead.
func (*StripeCompanyCustomerModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{0}
}

func (x *StripeCompanyCustomerModel) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StripeCompanyCustomerModel) GetCompanyId() int32 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *StripeCompanyCustomerModel) GetStripeCustomerId() string {
	if x != nil {
		return x.StripeCustomerId
	}
	return ""
}

func (x *StripeCompanyCustomerModel) GetObject() string {
	if x != nil {
		return x.Object
	}
	return ""
}

func (x *StripeCompanyCustomerModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *StripeCompanyCustomerModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *StripeCompanyCustomerModel) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *StripeCompanyCustomerModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *StripeCompanyCustomerModel) GetLiveMode() string {
	if x != nil {
		return x.LiveMode
	}
	return ""
}

func (x *StripeCompanyCustomerModel) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *StripeCompanyCustomerModel) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// stripe company customer simple view
// used to display list data
type StripeCompanyCustomerSimpleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int32 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id registered on stripe platform
	StripeCustomerId string `protobuf:"bytes,3,opt,name=stripe_customer_id,json=stripeCustomerId,proto3" json:"stripe_customer_id,omitempty"`
	// is the stripe platform parameter
	// an account or a customer
	Object string `protobuf:"bytes,4,opt,name=object,proto3" json:"object,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// currency type
	Currency string `protobuf:"bytes,7,opt,name=currency,proto3" json:"currency,omitempty"`
	// email
	Email string `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *StripeCompanyCustomerSimpleView) Reset() {
	*x = StripeCompanyCustomerSimpleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeCompanyCustomerSimpleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeCompanyCustomerSimpleView) ProtoMessage() {}

func (x *StripeCompanyCustomerSimpleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeCompanyCustomerSimpleView.ProtoReflect.Descriptor instead.
func (*StripeCompanyCustomerSimpleView) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{1}
}

func (x *StripeCompanyCustomerSimpleView) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StripeCompanyCustomerSimpleView) GetCompanyId() int32 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *StripeCompanyCustomerSimpleView) GetStripeCustomerId() string {
	if x != nil {
		return x.StripeCustomerId
	}
	return ""
}

func (x *StripeCompanyCustomerSimpleView) GetObject() string {
	if x != nil {
		return x.Object
	}
	return ""
}

func (x *StripeCompanyCustomerSimpleView) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *StripeCompanyCustomerSimpleView) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *StripeCompanyCustomerSimpleView) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// stripe customer info
// The purpose of this model is to receive the information returned
// when calling the query balance interface provided by Stripe using the stripe_customer_id
type StripeCustomerInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id, registered on stripe platform
	CustomerId string `protobuf:"bytes,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// email, customer email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// invoice balance
	InvoiceBalance int64 `protobuf:"varint,4,opt,name=invoice_balance,json=invoiceBalance,proto3" json:"invoice_balance,omitempty"`
	// default source
	DefaultSource string `protobuf:"bytes,5,opt,name=default_source,json=defaultSource,proto3" json:"default_source,omitempty"`
	// created, create time
	Created int64 `protobuf:"varint,6,opt,name=created,proto3" json:"created,omitempty"`
	// description
	Description string `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	// meta
	Meta string `protobuf:"bytes,8,opt,name=meta,proto3" json:"meta,omitempty"`
	// balance list
	BalanceTransactions []*StripeBalanceTransactionModel `protobuf:"bytes,9,rep,name=balance_transactions,json=balanceTransactions,proto3" json:"balance_transactions,omitempty"`
}

func (x *StripeCustomerInfoModel) Reset() {
	*x = StripeCustomerInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeCustomerInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeCustomerInfoModel) ProtoMessage() {}

func (x *StripeCustomerInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeCustomerInfoModel.ProtoReflect.Descriptor instead.
func (*StripeCustomerInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{2}
}

func (x *StripeCustomerInfoModel) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *StripeCustomerInfoModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *StripeCustomerInfoModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StripeCustomerInfoModel) GetInvoiceBalance() int64 {
	if x != nil {
		return x.InvoiceBalance
	}
	return 0
}

func (x *StripeCustomerInfoModel) GetDefaultSource() string {
	if x != nil {
		return x.DefaultSource
	}
	return ""
}

func (x *StripeCustomerInfoModel) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *StripeCustomerInfoModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *StripeCustomerInfoModel) GetMeta() string {
	if x != nil {
		return x.Meta
	}
	return ""
}

func (x *StripeCustomerInfoModel) GetBalanceTransactions() []*StripeBalanceTransactionModel {
	if x != nil {
		return x.BalanceTransactions
	}
	return nil
}

// stripe balance transaction
type StripeBalanceTransactionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// amount
	Amount int64 `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// currency
	Currency string `protobuf:"bytes,3,opt,name=currency,proto3" json:"currency,omitempty"`
	// created, create time
	Created int64 `protobuf:"varint,4,opt,name=created,proto3" json:"created,omitempty"`
	// description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// invoice
	Invoice string `protobuf:"bytes,6,opt,name=invoice,proto3" json:"invoice,omitempty"`
	// type
	Type string `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`
	// ending balance
	EndingBalance int64 `protobuf:"varint,8,opt,name=ending_balance,json=endingBalance,proto3" json:"ending_balance,omitempty"`
}

func (x *StripeBalanceTransactionModel) Reset() {
	*x = StripeBalanceTransactionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeBalanceTransactionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeBalanceTransactionModel) ProtoMessage() {}

func (x *StripeBalanceTransactionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeBalanceTransactionModel.ProtoReflect.Descriptor instead.
func (*StripeBalanceTransactionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{3}
}

func (x *StripeBalanceTransactionModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StripeBalanceTransactionModel) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *StripeBalanceTransactionModel) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *StripeBalanceTransactionModel) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *StripeBalanceTransactionModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *StripeBalanceTransactionModel) GetInvoice() string {
	if x != nil {
		return x.Invoice
	}
	return ""
}

func (x *StripeBalanceTransactionModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *StripeBalanceTransactionModel) GetEndingBalance() int64 {
	if x != nil {
		return x.EndingBalance
	}
	return 0
}

// company permission state model
// corresponding to the stripe_company_permission_state table in the mysql
type CompanyPermissionStateModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int32 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// stripe subscriptions id
	StripeSubscriptionsId string `protobuf:"bytes,3,opt,name=stripe_subscriptions_id,json=stripeSubscriptionsId,proto3" json:"stripe_subscriptions_id,omitempty"`
	// level
	Level int32 `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	// begin date
	BeginDate int64 `protobuf:"varint,5,opt,name=begin_date,json=beginDate,proto3" json:"begin_date,omitempty"`
	// expire date
	ExpireDate int64 `protobuf:"varint,6,opt,name=expire_date,json=expireDate,proto3" json:"expire_date,omitempty"`
	// current plan id
	CurrentPlanId int32 `protobuf:"varint,7,opt,name=current_plan_id,json=currentPlanId,proto3" json:"current_plan_id,omitempty"`
	// auto renew
	AutoRenew int32 `protobuf:"varint,8,opt,name=auto_renew,json=autoRenew,proto3" json:"auto_renew,omitempty"`
	// next plan id
	NextPlanId int32 `protobuf:"varint,9,opt,name=next_plan_id,json=nextPlanId,proto3" json:"next_plan_id,omitempty"`
	// charge status
	ChargeStatus int32 `protobuf:"varint,10,opt,name=charge_status,json=chargeStatus,proto3" json:"charge_status,omitempty"`
	// charge message
	ChargeMsg string `protobuf:"bytes,11,opt,name=charge_msg,json=chargeMsg,proto3" json:"charge_msg,omitempty"`
	// package message quantity
	PackageMsgNum int32 `protobuf:"varint,12,opt,name=package_msg_num,json=packageMsgNum,proto3" json:"package_msg_num,omitempty"`
	// package message used quantity
	// 0 represents that the number of text messages has not increased by 50%,
	// 1: it has increased
	PackageMsgUsedNum int32 `protobuf:"varint,13,opt,name=package_msg_used_num,json=packageMsgUsedNum,proto3" json:"package_msg_used_num,omitempty"`
	// buy message remaining quantity
	BuyMsgRemainNum int32 `protobuf:"varint,14,opt,name=buy_msg_remain_num,json=buyMsgRemainNum,proto3" json:"buy_msg_remain_num,omitempty"`
	// business number
	BusinessNum int32 `protobuf:"varint,15,opt,name=business_num,json=businessNum,proto3" json:"business_num,omitempty"`
	// update time
	UpdateTime int64 `protobuf:"varint,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// create time
	CreateTime int64 `protobuf:"varint,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// charge failed time
	ChargeFailedTime int64 `protobuf:"varint,18,opt,name=charge_failed_time,json=chargeFailedTime,proto3" json:"charge_failed_time,omitempty"`
	// additional staff number
	AdditionalStaffNum int32 `protobuf:"varint,19,opt,name=additional_staff_num,json=additionalStaffNum,proto3" json:"additional_staff_num,omitempty"`
}

func (x *CompanyPermissionStateModel) Reset() {
	*x = CompanyPermissionStateModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyPermissionStateModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyPermissionStateModel) ProtoMessage() {}

func (x *CompanyPermissionStateModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyPermissionStateModel.ProtoReflect.Descriptor instead.
func (*CompanyPermissionStateModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{4}
}

func (x *CompanyPermissionStateModel) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetCompanyId() int32 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetStripeSubscriptionsId() string {
	if x != nil {
		return x.StripeSubscriptionsId
	}
	return ""
}

func (x *CompanyPermissionStateModel) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetBeginDate() int64 {
	if x != nil {
		return x.BeginDate
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetExpireDate() int64 {
	if x != nil {
		return x.ExpireDate
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetCurrentPlanId() int32 {
	if x != nil {
		return x.CurrentPlanId
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetAutoRenew() int32 {
	if x != nil {
		return x.AutoRenew
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetNextPlanId() int32 {
	if x != nil {
		return x.NextPlanId
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetChargeStatus() int32 {
	if x != nil {
		return x.ChargeStatus
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetChargeMsg() string {
	if x != nil {
		return x.ChargeMsg
	}
	return ""
}

func (x *CompanyPermissionStateModel) GetPackageMsgNum() int32 {
	if x != nil {
		return x.PackageMsgNum
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetPackageMsgUsedNum() int32 {
	if x != nil {
		return x.PackageMsgUsedNum
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetBuyMsgRemainNum() int32 {
	if x != nil {
		return x.BuyMsgRemainNum
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetBusinessNum() int32 {
	if x != nil {
		return x.BusinessNum
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetChargeFailedTime() int64 {
	if x != nil {
		return x.ChargeFailedTime
	}
	return 0
}

func (x *CompanyPermissionStateModel) GetAdditionalStaffNum() int32 {
	if x != nil {
		return x.AdditionalStaffNum
	}
	return 0
}

// subscription info by stripe
type StripeSubscriptionInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the subscription
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Timestamp for the end of the current period
	CurrentPeriodEnd int64 `protobuf:"varint,2,opt,name=current_period_end,json=currentPeriodEnd,proto3" json:"current_period_end,omitempty"`
	// Timestamp for the start of the current period
	CurrentPeriodStart int64 `protobuf:"varint,3,opt,name=current_period_start,json=currentPeriodStart,proto3" json:"current_period_start,omitempty"`
	// Indicates if the subscription will be cancelled at the end of the billing period
	CancelAtPeriodEnd bool `protobuf:"varint,4,opt,name=cancel_at_period_end,json=cancelAtPeriodEnd,proto3" json:"cancel_at_period_end,omitempty"`
	// Description of the subscription
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// Timestamp when the subscription was created
	Created int64 `protobuf:"varint,6,opt,name=created,proto3" json:"created,omitempty"`
	// Current status of the subscription
	Status string `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	// Timestamp when the subscription started
	StartDate int64 `protobuf:"varint,8,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// Metadata associated with the subscription
	Metadata string `protobuf:"bytes,9,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Information about the applied discount, if any
	StripeCouponInfoModel *StripeCouponInfoModel `protobuf:"bytes,10,opt,name=stripe_coupon_info_model,json=stripeCouponInfoModel,proto3" json:"stripe_coupon_info_model,omitempty"`
	// latest invoice, if any
	LatestInvoice *StripeInvoiceInfoModel `protobuf:"bytes,11,opt,name=latest_invoice,json=latestInvoice,proto3" json:"latest_invoice,omitempty"`
	// upcoming invoice, if any
	UpcomingInvoice *StripeInvoiceInfoModel `protobuf:"bytes,12,opt,name=upcoming_invoice,json=upcomingInvoice,proto3" json:"upcoming_invoice,omitempty"`
}

func (x *StripeSubscriptionInfoModel) Reset() {
	*x = StripeSubscriptionInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeSubscriptionInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeSubscriptionInfoModel) ProtoMessage() {}

func (x *StripeSubscriptionInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeSubscriptionInfoModel.ProtoReflect.Descriptor instead.
func (*StripeSubscriptionInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{5}
}

func (x *StripeSubscriptionInfoModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StripeSubscriptionInfoModel) GetCurrentPeriodEnd() int64 {
	if x != nil {
		return x.CurrentPeriodEnd
	}
	return 0
}

func (x *StripeSubscriptionInfoModel) GetCurrentPeriodStart() int64 {
	if x != nil {
		return x.CurrentPeriodStart
	}
	return 0
}

func (x *StripeSubscriptionInfoModel) GetCancelAtPeriodEnd() bool {
	if x != nil {
		return x.CancelAtPeriodEnd
	}
	return false
}

func (x *StripeSubscriptionInfoModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *StripeSubscriptionInfoModel) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *StripeSubscriptionInfoModel) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StripeSubscriptionInfoModel) GetStartDate() int64 {
	if x != nil {
		return x.StartDate
	}
	return 0
}

func (x *StripeSubscriptionInfoModel) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *StripeSubscriptionInfoModel) GetStripeCouponInfoModel() *StripeCouponInfoModel {
	if x != nil {
		return x.StripeCouponInfoModel
	}
	return nil
}

func (x *StripeSubscriptionInfoModel) GetLatestInvoice() *StripeInvoiceInfoModel {
	if x != nil {
		return x.LatestInvoice
	}
	return nil
}

func (x *StripeSubscriptionInfoModel) GetUpcomingInvoice() *StripeInvoiceInfoModel {
	if x != nil {
		return x.UpcomingInvoice
	}
	return nil
}

// stripe coupon info
type StripeCouponInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the coupon
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Name of the coupon
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// Timestamp when the coupon was created
	Created int64 `protobuf:"varint,3,opt,name=created,proto3" json:"created,omitempty"`
	// Duration type for which the coupon is valid (e.g., 'forever', 'once', 'repeating')
	Duration string `protobuf:"bytes,4,opt,name=duration,proto3" json:"duration,omitempty"`
	// Duration in months for which the coupon is valid, if applicable
	DurationInMonths int64 `protobuf:"varint,5,opt,name=duration_in_months,json=durationInMonths,proto3" json:"duration_in_months,omitempty"`
	// Indicates if the coupon is in live mode
	Livemode bool `protobuf:"varint,6,opt,name=livemode,proto3" json:"livemode,omitempty"`
	// Indicates if the coupon has been deleted
	Deleted bool `protobuf:"varint,7,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// Maximum number of redemptions allowed for the coupon
	MaxRedemptions int64 `protobuf:"varint,8,opt,name=max_redemptions,json=maxRedemptions,proto3" json:"max_redemptions,omitempty"`
	// Metadata associated with the coupon
	Metadata string `protobuf:"bytes,9,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Percentage discount offered by the coupon
	PercentOff float64 `protobuf:"fixed64,10,opt,name=percent_off,json=percentOff,proto3" json:"percent_off,omitempty"`
	// Timestamp when the coupon expires
	RedeemBy int64 `protobuf:"varint,11,opt,name=redeem_by,json=redeemBy,proto3" json:"redeem_by,omitempty"`
	// Number of times the coupon has been redeemed
	TimesRedeemed int64 `protobuf:"varint,12,opt,name=times_redeemed,json=timesRedeemed,proto3" json:"times_redeemed,omitempty"`
	// Indicates if the coupon is still valid
	Valid bool `protobuf:"varint,13,opt,name=valid,proto3" json:"valid,omitempty"`
	// Timestamp marking the start of coupon validity
	Start int64 `protobuf:"varint,14,opt,name=start,proto3" json:"start,omitempty"`
	// Timestamp marking the end of coupon validity
	End int64 `protobuf:"varint,15,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *StripeCouponInfoModel) Reset() {
	*x = StripeCouponInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeCouponInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeCouponInfoModel) ProtoMessage() {}

func (x *StripeCouponInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeCouponInfoModel.ProtoReflect.Descriptor instead.
func (*StripeCouponInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{6}
}

func (x *StripeCouponInfoModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StripeCouponInfoModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StripeCouponInfoModel) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *StripeCouponInfoModel) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

func (x *StripeCouponInfoModel) GetDurationInMonths() int64 {
	if x != nil {
		return x.DurationInMonths
	}
	return 0
}

func (x *StripeCouponInfoModel) GetLivemode() bool {
	if x != nil {
		return x.Livemode
	}
	return false
}

func (x *StripeCouponInfoModel) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *StripeCouponInfoModel) GetMaxRedemptions() int64 {
	if x != nil {
		return x.MaxRedemptions
	}
	return 0
}

func (x *StripeCouponInfoModel) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *StripeCouponInfoModel) GetPercentOff() float64 {
	if x != nil {
		return x.PercentOff
	}
	return 0
}

func (x *StripeCouponInfoModel) GetRedeemBy() int64 {
	if x != nil {
		return x.RedeemBy
	}
	return 0
}

func (x *StripeCouponInfoModel) GetTimesRedeemed() int64 {
	if x != nil {
		return x.TimesRedeemed
	}
	return 0
}

func (x *StripeCouponInfoModel) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *StripeCouponInfoModel) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *StripeCouponInfoModel) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

// Information about the invoice
type StripeInvoiceInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total amount due on the invoice
	AmountDue int64 `protobuf:"varint,1,opt,name=amount_due,json=amountDue,proto3" json:"amount_due,omitempty"`
	// creation timestamp of the invoice
	Created int64 `protobuf:"varint,2,opt,name=created,proto3" json:"created,omitempty"`
	// timestamp of the start of the billing period
	PeriodStart int64 `protobuf:"varint,3,opt,name=period_start,json=periodStart,proto3" json:"period_start,omitempty"`
	// timestamp of the end of the billing period
	PeriodEnd int64 `protobuf:"varint,4,opt,name=period_end,json=periodEnd,proto3" json:"period_end,omitempty"`
	// country associated with the account
	AccountCountry string `protobuf:"bytes,5,opt,name=account_country,json=accountCountry,proto3" json:"account_country,omitempty"`
	// billing reason description
	// see more at: https://stripe.com/docs/api/invoices/object#invoice_object-amount_due
	BillingReason string `protobuf:"bytes,6,opt,name=billing_reason,json=billingReason,proto3" json:"billing_reason,omitempty"`
	// total amount after discounts and taxes
	Total int64 `protobuf:"varint,7,opt,name=total,proto3" json:"total,omitempty"`
	// description of the invoice
	Description string `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	// hosted invoice URL
	HostedInvoiceUrl string `protobuf:"bytes,9,opt,name=hosted_invoice_url,json=hostedInvoiceUrl,proto3" json:"hosted_invoice_url,omitempty"`
	// payment intent associated with the invoice
	PaymentIntent string `protobuf:"bytes,10,opt,name=payment_intent,json=paymentIntent,proto3" json:"payment_intent,omitempty"`
}

func (x *StripeInvoiceInfoModel) Reset() {
	*x = StripeInvoiceInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeInvoiceInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeInvoiceInfoModel) ProtoMessage() {}

func (x *StripeInvoiceInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeInvoiceInfoModel.ProtoReflect.Descriptor instead.
func (*StripeInvoiceInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{7}
}

func (x *StripeInvoiceInfoModel) GetAmountDue() int64 {
	if x != nil {
		return x.AmountDue
	}
	return 0
}

func (x *StripeInvoiceInfoModel) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *StripeInvoiceInfoModel) GetPeriodStart() int64 {
	if x != nil {
		return x.PeriodStart
	}
	return 0
}

func (x *StripeInvoiceInfoModel) GetPeriodEnd() int64 {
	if x != nil {
		return x.PeriodEnd
	}
	return 0
}

func (x *StripeInvoiceInfoModel) GetAccountCountry() string {
	if x != nil {
		return x.AccountCountry
	}
	return ""
}

func (x *StripeInvoiceInfoModel) GetBillingReason() string {
	if x != nil {
		return x.BillingReason
	}
	return ""
}

func (x *StripeInvoiceInfoModel) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *StripeInvoiceInfoModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *StripeInvoiceInfoModel) GetHostedInvoiceUrl() string {
	if x != nil {
		return x.HostedInvoiceUrl
	}
	return ""
}

func (x *StripeInvoiceInfoModel) GetPaymentIntent() string {
	if x != nil {
		return x.PaymentIntent
	}
	return ""
}

// stripe card list
type StripeCardListInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// card brand
	Brand string `protobuf:"bytes,1,opt,name=brand,proto3" json:"brand,omitempty"`
	// card id
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// card country
	Country string `protobuf:"bytes,3,opt,name=country,proto3" json:"country,omitempty"`
	// card cvc
	CvcCheck string `protobuf:"bytes,4,opt,name=cvc_check,json=cvcCheck,proto3" json:"cvc_check,omitempty"`
	// card expire Month
	ExpMonth int64 `protobuf:"varint,5,opt,name=exp_month,json=expMonth,proto3" json:"exp_month,omitempty"`
	// card expire year
	ExpYear int64 `protobuf:"varint,6,opt,name=exp_year,json=expYear,proto3" json:"exp_year,omitempty"`
	// card defauld ?
	IsDefault bool `protobuf:"varint,7,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	// card is valid
	IsValid bool `protobuf:"varint,8,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	// card last 4
	Last4 string `protobuf:"bytes,9,opt,name=last4,proto3" json:"last4,omitempty"`
	// card customer name
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *StripeCardListInfoModel) Reset() {
	*x = StripeCardListInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StripeCardListInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StripeCardListInfoModel) ProtoMessage() {}

func (x *StripeCardListInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StripeCardListInfoModel.ProtoReflect.Descriptor instead.
func (*StripeCardListInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP(), []int{8}
}

func (x *StripeCardListInfoModel) GetBrand() string {
	if x != nil {
		return x.Brand
	}
	return ""
}

func (x *StripeCardListInfoModel) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *StripeCardListInfoModel) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *StripeCardListInfoModel) GetCvcCheck() string {
	if x != nil {
		return x.CvcCheck
	}
	return ""
}

func (x *StripeCardListInfoModel) GetExpMonth() int64 {
	if x != nil {
		return x.ExpMonth
	}
	return 0
}

func (x *StripeCardListInfoModel) GetExpYear() int64 {
	if x != nil {
		return x.ExpYear
	}
	return 0
}

func (x *StripeCardListInfoModel) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *StripeCardListInfoModel) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *StripeCardListInfoModel) GetLast4() string {
	if x != nil {
		return x.Last4
	}
	return ""
}

func (x *StripeCardListInfoModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_moego_models_pay_ops_v1_billing_models_proto protoreflect.FileDescriptor

var file_moego_models_pay_ops_v1_billing_models_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x22, 0xd6, 0x02, 0x0a, 0x1a, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x1b, 0x0a, 0x09, 0x6c, 0x69, 0x76, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6c, 0x69, 0x76, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0xe9, 0x01, 0x0a, 0x1f, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0xef, 0x02, 0x0a,
	0x17, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x69, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d,
	0x65, 0x74, 0x61, 0x12, 0x69, 0x0a, 0x14, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x13, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xf4,
	0x01, 0x0a, 0x1d, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x22, 0xd2, 0x05, 0x0a, 0x1b, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x17, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x5f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x73, 0x74, 0x72, 0x69, 0x70, 0x65, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4d, 0x73, 0x67, 0x12,
	0x26, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x4d, 0x73, 0x67, 0x4e, 0x75, 0x6d, 0x12, 0x2f, 0x0a, 0x14, 0x70, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4d, 0x73,
	0x67, 0x55, 0x73, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x12, 0x62, 0x75, 0x79, 0x5f,
	0x6d, 0x73, 0x67, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x62, 0x75, 0x79, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x6d, 0x61,
	0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4e, 0x75, 0x6d, 0x22, 0xea, 0x04, 0x0a, 0x1b, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x65, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x61, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x65,
	0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x41, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x67, 0x0a, 0x18, 0x73, 0x74,
	0x72, 0x69, 0x70, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f,
	0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x15, 0x73, 0x74,
	0x72, 0x69, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x56, 0x0a, 0x0e, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f,
	0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x49, 0x6e, 0x76, 0x6f,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0d, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x5a, 0x0a, 0x10, 0x75,
	0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x72, 0x69, 0x70, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x75, 0x70, 0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67,
	0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x22, 0xbd, 0x03, 0x0a, 0x15, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x69, 0x76,
	0x65, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6c, 0x69, 0x76,
	0x65, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x64,
	0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x5f,
	0x6f, 0x66, 0x66, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x4f, 0x66, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f,
	0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x42, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x5f, 0x72, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0xf0, 0x02, 0x0a, 0x16, 0x53, 0x74, 0x72, 0x69,
	0x70, 0x65, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x75, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x75,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x45, 0x6e, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x12, 0x68, 0x6f, 0x73, 0x74, 0x65, 0x64, 0x5f,
	0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x68, 0x6f, 0x73, 0x74, 0x65, 0x64, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x9b, 0x02, 0x0a, 0x17, 0x53,
	0x74, 0x72, 0x69, 0x70, 0x65, 0x43, 0x61, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x61, 0x72, 0x64, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x76, 0x63, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x76, 0x63, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x1b, 0x0a, 0x09,
	0x65, 0x78, 0x70, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x65, 0x78, 0x70, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x78, 0x70,
	0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x78, 0x70,
	0x59, 0x65, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x61, 0x73, 0x74, 0x34, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c,
	0x61, 0x73, 0x74, 0x34, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x7a, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x55, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x70, 0x61, 0x79, 0x5f, 0x6f, 0x70, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6f,
	0x70, 0x73, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_pay_ops_v1_billing_models_proto_rawDescOnce sync.Once
	file_moego_models_pay_ops_v1_billing_models_proto_rawDescData = file_moego_models_pay_ops_v1_billing_models_proto_rawDesc
)

func file_moego_models_pay_ops_v1_billing_models_proto_rawDescGZIP() []byte {
	file_moego_models_pay_ops_v1_billing_models_proto_rawDescOnce.Do(func() {
		file_moego_models_pay_ops_v1_billing_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_pay_ops_v1_billing_models_proto_rawDescData)
	})
	return file_moego_models_pay_ops_v1_billing_models_proto_rawDescData
}

var file_moego_models_pay_ops_v1_billing_models_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_models_pay_ops_v1_billing_models_proto_goTypes = []interface{}{
	(*StripeCompanyCustomerModel)(nil),      // 0: moego.models.pay_ops.v1.StripeCompanyCustomerModel
	(*StripeCompanyCustomerSimpleView)(nil), // 1: moego.models.pay_ops.v1.StripeCompanyCustomerSimpleView
	(*StripeCustomerInfoModel)(nil),         // 2: moego.models.pay_ops.v1.StripeCustomerInfoModel
	(*StripeBalanceTransactionModel)(nil),   // 3: moego.models.pay_ops.v1.StripeBalanceTransactionModel
	(*CompanyPermissionStateModel)(nil),     // 4: moego.models.pay_ops.v1.CompanyPermissionStateModel
	(*StripeSubscriptionInfoModel)(nil),     // 5: moego.models.pay_ops.v1.StripeSubscriptionInfoModel
	(*StripeCouponInfoModel)(nil),           // 6: moego.models.pay_ops.v1.StripeCouponInfoModel
	(*StripeInvoiceInfoModel)(nil),          // 7: moego.models.pay_ops.v1.StripeInvoiceInfoModel
	(*StripeCardListInfoModel)(nil),         // 8: moego.models.pay_ops.v1.StripeCardListInfoModel
}
var file_moego_models_pay_ops_v1_billing_models_proto_depIdxs = []int32{
	3, // 0: moego.models.pay_ops.v1.StripeCustomerInfoModel.balance_transactions:type_name -> moego.models.pay_ops.v1.StripeBalanceTransactionModel
	6, // 1: moego.models.pay_ops.v1.StripeSubscriptionInfoModel.stripe_coupon_info_model:type_name -> moego.models.pay_ops.v1.StripeCouponInfoModel
	7, // 2: moego.models.pay_ops.v1.StripeSubscriptionInfoModel.latest_invoice:type_name -> moego.models.pay_ops.v1.StripeInvoiceInfoModel
	7, // 3: moego.models.pay_ops.v1.StripeSubscriptionInfoModel.upcoming_invoice:type_name -> moego.models.pay_ops.v1.StripeInvoiceInfoModel
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_pay_ops_v1_billing_models_proto_init() }
func file_moego_models_pay_ops_v1_billing_models_proto_init() {
	if File_moego_models_pay_ops_v1_billing_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeCompanyCustomerModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeCompanyCustomerSimpleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeCustomerInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeBalanceTransactionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyPermissionStateModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeSubscriptionInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeCouponInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeInvoiceInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_pay_ops_v1_billing_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StripeCardListInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_pay_ops_v1_billing_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_pay_ops_v1_billing_models_proto_goTypes,
		DependencyIndexes: file_moego_models_pay_ops_v1_billing_models_proto_depIdxs,
		MessageInfos:      file_moego_models_pay_ops_v1_billing_models_proto_msgTypes,
	}.Build()
	File_moego_models_pay_ops_v1_billing_models_proto = out.File
	file_moego_models_pay_ops_v1_billing_models_proto_rawDesc = nil
	file_moego_models_pay_ops_v1_billing_models_proto_goTypes = nil
	file_moego_models_pay_ops_v1_billing_models_proto_depIdxs = nil
}
