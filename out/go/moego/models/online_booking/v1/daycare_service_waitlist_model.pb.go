// @gencoder.generated: api/moego/models/online_booking/v1/daycare_service_waitlist_model.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/daycare_service_waitlist_model.proto

package onlinebookingpb

import (
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// table: daycare_service_waitlist
// comment:
// indexes:
//
//	daycare_service_waitlist_pkey: (id)
type DaycareServiceWaitlistModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// booking request id
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// service detail id
	ServiceDetailId int64 `protobuf:"varint,3,opt,name=service_detail_id,json=serviceDetailId,proto3" json:"service_detail_id,omitempty"`
	// specific dates
	SpecificDates []*date.Date `protobuf:"bytes,4,rep,name=specific_dates,json=specificDates,proto3" json:"specific_dates,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *DaycareServiceWaitlistModel) Reset() {
	*x = DaycareServiceWaitlistModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareServiceWaitlistModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareServiceWaitlistModel) ProtoMessage() {}

func (x *DaycareServiceWaitlistModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareServiceWaitlistModel.ProtoReflect.Descriptor instead.
func (*DaycareServiceWaitlistModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDescGZIP(), []int{0}
}

func (x *DaycareServiceWaitlistModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DaycareServiceWaitlistModel) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *DaycareServiceWaitlistModel) GetServiceDetailId() int64 {
	if x != nil {
		return x.ServiceDetailId
	}
	return 0
}

func (x *DaycareServiceWaitlistModel) GetSpecificDates() []*date.Date {
	if x != nil {
		return x.SpecificDates
	}
	return nil
}

func (x *DaycareServiceWaitlistModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *DaycareServiceWaitlistModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_moego_models_online_booking_v1_daycare_service_waitlist_model_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x64, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7,
	0x02, 0x0a, 0x1b, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0e, 0x73, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x0d, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74,
	0x65, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDescData = file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDesc
)

func file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDescData
}

var file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_goTypes = []interface{}{
	(*DaycareServiceWaitlistModel)(nil), // 0: moego.models.online_booking.v1.DaycareServiceWaitlistModel
	(*date.Date)(nil),                   // 1: google.type.Date
	(*timestamppb.Timestamp)(nil),       // 2: google.protobuf.Timestamp
}
var file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_depIdxs = []int32{
	1, // 0: moego.models.online_booking.v1.DaycareServiceWaitlistModel.specific_dates:type_name -> google.type.Date
	2, // 1: moego.models.online_booking.v1.DaycareServiceWaitlistModel.created_at:type_name -> google.protobuf.Timestamp
	2, // 2: moego.models.online_booking.v1.DaycareServiceWaitlistModel.updated_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_init() }
func file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_init() {
	if File_moego_models_online_booking_v1_daycare_service_waitlist_model_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareServiceWaitlistModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_daycare_service_waitlist_model_proto = out.File
	file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_rawDesc = nil
	file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_goTypes = nil
	file_moego_models_online_booking_v1_daycare_service_waitlist_model_proto_depIdxs = nil
}
