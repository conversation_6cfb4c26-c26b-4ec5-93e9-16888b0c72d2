// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/ob_availability_setting_models.proto

package onlinebookingpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// grooming service availability for ob
type GroomingServiceAvailabilityModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accept customer type
	AcceptCustomerType AcceptCustomerType `protobuf:"varint,4,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType" json:"accept_customer_type,omitempty"`
	// availability type
	TimeAvailabilityType TimeAvailabilityType `protobuf:"varint,5,opt,name=time_availability_type,json=timeAvailabilityType,proto3,enum=moego.models.online_booking.v1.TimeAvailabilityType" json:"time_availability_type,omitempty"`
	// enable shift sync
	EnableShiftSync bool `protobuf:"varint,6,opt,name=enable_shift_sync,json=enableShiftSync,proto3" json:"enable_shift_sync,omitempty"`
}

func (x *GroomingServiceAvailabilityModel) Reset() {
	*x = GroomingServiceAvailabilityModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingServiceAvailabilityModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingServiceAvailabilityModel) ProtoMessage() {}

func (x *GroomingServiceAvailabilityModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingServiceAvailabilityModel.ProtoReflect.Descriptor instead.
func (*GroomingServiceAvailabilityModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP(), []int{0}
}

func (x *GroomingServiceAvailabilityModel) GetAcceptCustomerType() AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerType
	}
	return AcceptCustomerType_ACCEPT_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *GroomingServiceAvailabilityModel) GetTimeAvailabilityType() TimeAvailabilityType {
	if x != nil {
		return x.TimeAvailabilityType
	}
	return TimeAvailabilityType_TIME_AVAILABILITY_TYPE_UNSPECIFIED
}

func (x *GroomingServiceAvailabilityModel) GetEnableShiftSync() bool {
	if x != nil {
		return x.EnableShiftSync
	}
	return false
}

// boarding service availability for ob
type BoardingServiceAvailabilityModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accepted pet type
	AcceptedPetTypes []v1.PetType `protobuf:"varint,1,rep,packed,name=accepted_pet_types,json=acceptedPetTypes,proto3,enum=moego.models.customer.v1.PetType" json:"accepted_pet_types,omitempty"`
	// booking date range
	BookingDateRange *DateRangeDef `protobuf:"bytes,2,opt,name=booking_date_range,json=bookingDateRange,proto3" json:"booking_date_range,omitempty"`
	// pick up time range
	ArrivalPickUpTimeRange *ArrivalPickUpTimeDef `protobuf:"bytes,3,opt,name=arrival_pick_up_time_range,json=arrivalPickUpTimeRange,proto3" json:"arrival_pick_up_time_range,omitempty"`
	// accept customer type
	AcceptCustomerType AcceptCustomerType `protobuf:"varint,4,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType" json:"accept_customer_type,omitempty"`
	// lodging availability
	LodgingAvailability *LodgingAvailabilityDef `protobuf:"bytes,5,opt,name=lodging_availability,json=lodgingAvailability,proto3" json:"lodging_availability,omitempty"`
}

func (x *BoardingServiceAvailabilityModel) Reset() {
	*x = BoardingServiceAvailabilityModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingServiceAvailabilityModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingServiceAvailabilityModel) ProtoMessage() {}

func (x *BoardingServiceAvailabilityModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingServiceAvailabilityModel.ProtoReflect.Descriptor instead.
func (*BoardingServiceAvailabilityModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP(), []int{1}
}

func (x *BoardingServiceAvailabilityModel) GetAcceptedPetTypes() []v1.PetType {
	if x != nil {
		return x.AcceptedPetTypes
	}
	return nil
}

func (x *BoardingServiceAvailabilityModel) GetBookingDateRange() *DateRangeDef {
	if x != nil {
		return x.BookingDateRange
	}
	return nil
}

func (x *BoardingServiceAvailabilityModel) GetArrivalPickUpTimeRange() *ArrivalPickUpTimeDef {
	if x != nil {
		return x.ArrivalPickUpTimeRange
	}
	return nil
}

func (x *BoardingServiceAvailabilityModel) GetAcceptCustomerType() AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerType
	}
	return AcceptCustomerType_ACCEPT_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *BoardingServiceAvailabilityModel) GetLodgingAvailability() *LodgingAvailabilityDef {
	if x != nil {
		return x.LodgingAvailability
	}
	return nil
}

// daycare service availability for ob
type DaycareServiceAvailabilityModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accepted pet type
	AcceptedPetTypes []v1.PetType `protobuf:"varint,1,rep,packed,name=accepted_pet_types,json=acceptedPetTypes,proto3,enum=moego.models.customer.v1.PetType" json:"accepted_pet_types,omitempty"`
	// booking date range
	BookingDateRange *DateRangeDef `protobuf:"bytes,2,opt,name=booking_date_range,json=bookingDateRange,proto3" json:"booking_date_range,omitempty"`
	// pick up time range
	ArrivalPickUpTimeRange *ArrivalPickUpTimeDef `protobuf:"bytes,3,opt,name=arrival_pick_up_time_range,json=arrivalPickUpTimeRange,proto3" json:"arrival_pick_up_time_range,omitempty"`
	// accept customer type
	AcceptCustomerType AcceptCustomerType `protobuf:"varint,4,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType" json:"accept_customer_type,omitempty"`
	// lodging availability
	LodgingAvailability *LodgingAvailabilityDef `protobuf:"bytes,5,opt,name=lodging_availability,json=lodgingAvailability,proto3" json:"lodging_availability,omitempty"`
	// capacity overrides
	CapacityOverrides []*CapacityOverrideModel `protobuf:"bytes,6,rep,name=capacity_overrides,json=capacityOverrides,proto3" json:"capacity_overrides,omitempty"`
}

func (x *DaycareServiceAvailabilityModel) Reset() {
	*x = DaycareServiceAvailabilityModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareServiceAvailabilityModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareServiceAvailabilityModel) ProtoMessage() {}

func (x *DaycareServiceAvailabilityModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareServiceAvailabilityModel.ProtoReflect.Descriptor instead.
func (*DaycareServiceAvailabilityModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP(), []int{2}
}

func (x *DaycareServiceAvailabilityModel) GetAcceptedPetTypes() []v1.PetType {
	if x != nil {
		return x.AcceptedPetTypes
	}
	return nil
}

func (x *DaycareServiceAvailabilityModel) GetBookingDateRange() *DateRangeDef {
	if x != nil {
		return x.BookingDateRange
	}
	return nil
}

func (x *DaycareServiceAvailabilityModel) GetArrivalPickUpTimeRange() *ArrivalPickUpTimeDef {
	if x != nil {
		return x.ArrivalPickUpTimeRange
	}
	return nil
}

func (x *DaycareServiceAvailabilityModel) GetAcceptCustomerType() AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerType
	}
	return AcceptCustomerType_ACCEPT_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *DaycareServiceAvailabilityModel) GetLodgingAvailability() *LodgingAvailabilityDef {
	if x != nil {
		return x.LodgingAvailability
	}
	return nil
}

func (x *DaycareServiceAvailabilityModel) GetCapacityOverrides() []*CapacityOverrideModel {
	if x != nil {
		return x.CapacityOverrides
	}
	return nil
}

// dog walking service availability for ob
type DogWalkingServiceAvailabilityModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// only support dog pet type
	AcceptedPetTypes []v1.PetType `protobuf:"varint,1,rep,packed,name=accepted_pet_types,json=acceptedPetTypes,proto3,enum=moego.models.customer.v1.PetType" json:"accepted_pet_types,omitempty"`
	// booking date range
	BookingDateRange *DateRangeDef `protobuf:"bytes,2,opt,name=booking_date_range,json=bookingDateRange,proto3" json:"booking_date_range,omitempty"`
	// accept customer type
	AcceptCustomerType AcceptCustomerType `protobuf:"varint,4,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType" json:"accept_customer_type,omitempty"`
}

func (x *DogWalkingServiceAvailabilityModel) Reset() {
	*x = DogWalkingServiceAvailabilityModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DogWalkingServiceAvailabilityModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DogWalkingServiceAvailabilityModel) ProtoMessage() {}

func (x *DogWalkingServiceAvailabilityModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DogWalkingServiceAvailabilityModel.ProtoReflect.Descriptor instead.
func (*DogWalkingServiceAvailabilityModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP(), []int{3}
}

func (x *DogWalkingServiceAvailabilityModel) GetAcceptedPetTypes() []v1.PetType {
	if x != nil {
		return x.AcceptedPetTypes
	}
	return nil
}

func (x *DogWalkingServiceAvailabilityModel) GetBookingDateRange() *DateRangeDef {
	if x != nil {
		return x.BookingDateRange
	}
	return nil
}

func (x *DogWalkingServiceAvailabilityModel) GetAcceptCustomerType() AcceptCustomerType {
	if x != nil {
		return x.AcceptCustomerType
	}
	return AcceptCustomerType_ACCEPT_CUSTOMER_TYPE_UNSPECIFIED
}

// evaluation service availability for ob
type EvaluationServiceAvailabilityModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking date range
	BookingDateRange *DateRangeDef `protobuf:"bytes,2,opt,name=booking_date_range,json=bookingDateRange,proto3" json:"booking_date_range,omitempty"`
	// pick up time range
	ArrivalPickUpTimeRange *ArrivalPickUpTimeDef `protobuf:"bytes,3,opt,name=arrival_pick_up_time_range,json=arrivalPickUpTimeRange,proto3" json:"arrival_pick_up_time_range,omitempty"`
}

func (x *EvaluationServiceAvailabilityModel) Reset() {
	*x = EvaluationServiceAvailabilityModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationServiceAvailabilityModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationServiceAvailabilityModel) ProtoMessage() {}

func (x *EvaluationServiceAvailabilityModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationServiceAvailabilityModel.ProtoReflect.Descriptor instead.
func (*EvaluationServiceAvailabilityModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP(), []int{4}
}

func (x *EvaluationServiceAvailabilityModel) GetBookingDateRange() *DateRangeDef {
	if x != nil {
		return x.BookingDateRange
	}
	return nil
}

func (x *EvaluationServiceAvailabilityModel) GetArrivalPickUpTimeRange() *ArrivalPickUpTimeDef {
	if x != nil {
		return x.ArrivalPickUpTimeRange
	}
	return nil
}

// arrival/pick up time model
type ArrivalPickUpTimeOverrideModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// arrival time/pick up time
	Type TimeRangeType `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.online_booking.v1.TimeRangeType" json:"type,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// is available
	IsAvailable bool `protobuf:"varint,6,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	// arrival time range
	DayTimeRanges []*DayTimeRangeDef `protobuf:"bytes,7,rep,name=day_time_ranges,json=dayTimeRanges,proto3" json:"day_time_ranges,omitempty"`
	// is_active
	IsActive bool `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
}

func (x *ArrivalPickUpTimeOverrideModel) Reset() {
	*x = ArrivalPickUpTimeOverrideModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrivalPickUpTimeOverrideModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrivalPickUpTimeOverrideModel) ProtoMessage() {}

func (x *ArrivalPickUpTimeOverrideModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrivalPickUpTimeOverrideModel.ProtoReflect.Descriptor instead.
func (*ArrivalPickUpTimeOverrideModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP(), []int{5}
}

func (x *ArrivalPickUpTimeOverrideModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ArrivalPickUpTimeOverrideModel) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

func (x *ArrivalPickUpTimeOverrideModel) GetType() TimeRangeType {
	if x != nil {
		return x.Type
	}
	return TimeRangeType_TIME_RANGE_TYPE_UNSPECIFIED
}

func (x *ArrivalPickUpTimeOverrideModel) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ArrivalPickUpTimeOverrideModel) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ArrivalPickUpTimeOverrideModel) GetIsAvailable() bool {
	if x != nil {
		return x.IsAvailable
	}
	return false
}

func (x *ArrivalPickUpTimeOverrideModel) GetDayTimeRanges() []*DayTimeRangeDef {
	if x != nil {
		return x.DayTimeRanges
	}
	return nil
}

func (x *ArrivalPickUpTimeOverrideModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

// capacity override model
type CapacityOverrideModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// date ranges
	DateRanges []*CapacityOverrideModel_CapacityDateRange `protobuf:"bytes,2,rep,name=date_ranges,json=dateRanges,proto3" json:"date_ranges,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
	// unit type
	UnitType CapacityOverrideUnitType `protobuf:"varint,4,opt,name=unit_type,json=unitType,proto3,enum=moego.models.online_booking.v1.CapacityOverrideUnitType" json:"unit_type,omitempty"`
	// is_active
	IsActive bool `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
}

func (x *CapacityOverrideModel) Reset() {
	*x = CapacityOverrideModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CapacityOverrideModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapacityOverrideModel) ProtoMessage() {}

func (x *CapacityOverrideModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapacityOverrideModel.ProtoReflect.Descriptor instead.
func (*CapacityOverrideModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP(), []int{6}
}

func (x *CapacityOverrideModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CapacityOverrideModel) GetDateRanges() []*CapacityOverrideModel_CapacityDateRange {
	if x != nil {
		return x.DateRanges
	}
	return nil
}

func (x *CapacityOverrideModel) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *CapacityOverrideModel) GetUnitType() CapacityOverrideUnitType {
	if x != nil {
		return x.UnitType
	}
	return CapacityOverrideUnitType_CAPACITY_OVERRIDE_UNIT_TYPE_UNSPECIFIED
}

func (x *CapacityOverrideModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

// capacity date range
type CapacityOverrideModel_CapacityDateRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *CapacityOverrideModel_CapacityDateRange) Reset() {
	*x = CapacityOverrideModel_CapacityDateRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CapacityOverrideModel_CapacityDateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapacityOverrideModel_CapacityDateRange) ProtoMessage() {}

func (x *CapacityOverrideModel_CapacityDateRange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapacityOverrideModel_CapacityDateRange.ProtoReflect.Descriptor instead.
func (*CapacityOverrideModel_CapacityDateRange) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP(), []int{6, 0}
}

func (x *CapacityOverrideModel_CapacityDateRange) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CapacityOverrideModel_CapacityDateRange) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

var File_moego_models_online_booking_v1_ob_availability_setting_models_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x42, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x02, 0x0a, 0x20, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x64, 0x0a, 0x14, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x6a, 0x0a, 0x16, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x74, 0x69, 0x6d, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x69, 0x66, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x68,
	0x69, 0x66, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x22, 0x92, 0x04, 0x0a, 0x20, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x4f, 0x0a, 0x12,
	0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x5a, 0x0a,
	0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x70, 0x0a, 0x1a, 0x61, 0x72, 0x72,
	0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65,
	0x44, 0x65, 0x66, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b,
	0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x64, 0x0a, 0x14, 0x61,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x61,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x69, 0x0a, 0x14, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x52, 0x13, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0xf7, 0x04, 0x0a,
	0x1f, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x4f, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x10, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x5a, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x10, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x70, 0x0a,
	0x1a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70,
	0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x64, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x69, 0x0a, 0x14, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x52, 0x13, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x64, 0x0a, 0x12, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x11, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x22, 0xb7, 0x02, 0x0a, 0x22, 0x44, 0x6f, 0x67, 0x57, 0x61,
	0x6c, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x4f, 0x0a,
	0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x5a,
	0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x64, 0x0a, 0x14, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xf2, 0x01, 0x0a, 0x22, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x5a, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65,
	0x66, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x70, 0x0a, 0x1a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70,
	0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x52, 0x16, 0x61,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0xc3, 0x03, 0x0a, 0x1e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x41, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x57, 0x0a, 0x0f, 0x64, 0x61, 0x79, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x0d, 0x64, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0x96, 0x03, 0x0a, 0x15,
	0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x68, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x55, 0x0a, 0x09, 0x75,
	0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x1a,
	0x73, 0x0a, 0x11, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescData = file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDesc
)

func file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDescData
}

var file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_models_online_booking_v1_ob_availability_setting_models_proto_goTypes = []interface{}{
	(*GroomingServiceAvailabilityModel)(nil),        // 0: moego.models.online_booking.v1.GroomingServiceAvailabilityModel
	(*BoardingServiceAvailabilityModel)(nil),        // 1: moego.models.online_booking.v1.BoardingServiceAvailabilityModel
	(*DaycareServiceAvailabilityModel)(nil),         // 2: moego.models.online_booking.v1.DaycareServiceAvailabilityModel
	(*DogWalkingServiceAvailabilityModel)(nil),      // 3: moego.models.online_booking.v1.DogWalkingServiceAvailabilityModel
	(*EvaluationServiceAvailabilityModel)(nil),      // 4: moego.models.online_booking.v1.EvaluationServiceAvailabilityModel
	(*ArrivalPickUpTimeOverrideModel)(nil),          // 5: moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel
	(*CapacityOverrideModel)(nil),                   // 6: moego.models.online_booking.v1.CapacityOverrideModel
	(*CapacityOverrideModel_CapacityDateRange)(nil), // 7: moego.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange
	(AcceptCustomerType)(0),                         // 8: moego.models.online_booking.v1.AcceptCustomerType
	(TimeAvailabilityType)(0),                       // 9: moego.models.online_booking.v1.TimeAvailabilityType
	(v1.PetType)(0),                                 // 10: moego.models.customer.v1.PetType
	(*DateRangeDef)(nil),                            // 11: moego.models.online_booking.v1.DateRangeDef
	(*ArrivalPickUpTimeDef)(nil),                    // 12: moego.models.online_booking.v1.ArrivalPickUpTimeDef
	(*LodgingAvailabilityDef)(nil),                  // 13: moego.models.online_booking.v1.LodgingAvailabilityDef
	(v11.ServiceItemType)(0),                        // 14: moego.models.offering.v1.ServiceItemType
	(TimeRangeType)(0),                              // 15: moego.models.online_booking.v1.TimeRangeType
	(*date.Date)(nil),                               // 16: google.type.Date
	(*DayTimeRangeDef)(nil),                         // 17: moego.models.online_booking.v1.DayTimeRangeDef
	(CapacityOverrideUnitType)(0),                   // 18: moego.models.online_booking.v1.CapacityOverrideUnitType
}
var file_moego_models_online_booking_v1_ob_availability_setting_models_proto_depIdxs = []int32{
	8,  // 0: moego.models.online_booking.v1.GroomingServiceAvailabilityModel.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	9,  // 1: moego.models.online_booking.v1.GroomingServiceAvailabilityModel.time_availability_type:type_name -> moego.models.online_booking.v1.TimeAvailabilityType
	10, // 2: moego.models.online_booking.v1.BoardingServiceAvailabilityModel.accepted_pet_types:type_name -> moego.models.customer.v1.PetType
	11, // 3: moego.models.online_booking.v1.BoardingServiceAvailabilityModel.booking_date_range:type_name -> moego.models.online_booking.v1.DateRangeDef
	12, // 4: moego.models.online_booking.v1.BoardingServiceAvailabilityModel.arrival_pick_up_time_range:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeDef
	8,  // 5: moego.models.online_booking.v1.BoardingServiceAvailabilityModel.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	13, // 6: moego.models.online_booking.v1.BoardingServiceAvailabilityModel.lodging_availability:type_name -> moego.models.online_booking.v1.LodgingAvailabilityDef
	10, // 7: moego.models.online_booking.v1.DaycareServiceAvailabilityModel.accepted_pet_types:type_name -> moego.models.customer.v1.PetType
	11, // 8: moego.models.online_booking.v1.DaycareServiceAvailabilityModel.booking_date_range:type_name -> moego.models.online_booking.v1.DateRangeDef
	12, // 9: moego.models.online_booking.v1.DaycareServiceAvailabilityModel.arrival_pick_up_time_range:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeDef
	8,  // 10: moego.models.online_booking.v1.DaycareServiceAvailabilityModel.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	13, // 11: moego.models.online_booking.v1.DaycareServiceAvailabilityModel.lodging_availability:type_name -> moego.models.online_booking.v1.LodgingAvailabilityDef
	6,  // 12: moego.models.online_booking.v1.DaycareServiceAvailabilityModel.capacity_overrides:type_name -> moego.models.online_booking.v1.CapacityOverrideModel
	10, // 13: moego.models.online_booking.v1.DogWalkingServiceAvailabilityModel.accepted_pet_types:type_name -> moego.models.customer.v1.PetType
	11, // 14: moego.models.online_booking.v1.DogWalkingServiceAvailabilityModel.booking_date_range:type_name -> moego.models.online_booking.v1.DateRangeDef
	8,  // 15: moego.models.online_booking.v1.DogWalkingServiceAvailabilityModel.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	11, // 16: moego.models.online_booking.v1.EvaluationServiceAvailabilityModel.booking_date_range:type_name -> moego.models.online_booking.v1.DateRangeDef
	12, // 17: moego.models.online_booking.v1.EvaluationServiceAvailabilityModel.arrival_pick_up_time_range:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeDef
	14, // 18: moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	15, // 19: moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel.type:type_name -> moego.models.online_booking.v1.TimeRangeType
	16, // 20: moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel.start_date:type_name -> google.type.Date
	16, // 21: moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel.end_date:type_name -> google.type.Date
	17, // 22: moego.models.online_booking.v1.ArrivalPickUpTimeOverrideModel.day_time_ranges:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	7,  // 23: moego.models.online_booking.v1.CapacityOverrideModel.date_ranges:type_name -> moego.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange
	18, // 24: moego.models.online_booking.v1.CapacityOverrideModel.unit_type:type_name -> moego.models.online_booking.v1.CapacityOverrideUnitType
	16, // 25: moego.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange.start_date:type_name -> google.type.Date
	16, // 26: moego.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange.end_date:type_name -> google.type.Date
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_ob_availability_setting_models_proto_init() }
func file_moego_models_online_booking_v1_ob_availability_setting_models_proto_init() {
	if File_moego_models_online_booking_v1_ob_availability_setting_models_proto != nil {
		return
	}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_init()
	file_moego_models_online_booking_v1_ob_availability_setting_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingServiceAvailabilityModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingServiceAvailabilityModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareServiceAvailabilityModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DogWalkingServiceAvailabilityModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationServiceAvailabilityModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrivalPickUpTimeOverrideModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CapacityOverrideModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CapacityOverrideModel_CapacityDateRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_ob_availability_setting_models_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_ob_availability_setting_models_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_ob_availability_setting_models_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_ob_availability_setting_models_proto = out.File
	file_moego_models_online_booking_v1_ob_availability_setting_models_proto_rawDesc = nil
	file_moego_models_online_booking_v1_ob_availability_setting_models_proto_goTypes = nil
	file_moego_models_online_booking_v1_ob_availability_setting_models_proto_depIdxs = nil
}
