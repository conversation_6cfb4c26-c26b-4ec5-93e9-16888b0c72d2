// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/reporting/v2/attribute_model.proto

package reportingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Attribute type enum
type Attribute_Type int32

const (
	// Unspecified attribute type
	Attribute_TYPE_UNSPECIFIED Attribute_Type = 0
	// metric
	Attribute_METRIC Attribute_Type = 1
	// dimension
	Attribute_DIMENSION Attribute_Type = 2
)

// Enum value maps for Attribute_Type.
var (
	Attribute_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "METRIC",
		2: "DIMENSION",
	}
	Attribute_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"METRIC":           1,
		"DIMENSION":        2,
	}
)

func (x Attribute_Type) Enum() *Attribute_Type {
	p := new(Attribute_Type)
	*p = x
	return p
}

func (x Attribute_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Attribute_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_reporting_v2_attribute_model_proto_enumTypes[0].Descriptor()
}

func (Attribute_Type) Type() protoreflect.EnumType {
	return &file_moego_models_reporting_v2_attribute_model_proto_enumTypes[0]
}

func (x Attribute_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Attribute_Type.Descriptor instead.
func (Attribute_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_attribute_model_proto_rawDescGZIP(), []int{0, 0}
}

// Attribute model
type Attribute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Attribute type
	Type Attribute_Type `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.reporting.v2.Attribute_Type" json:"type,omitempty"`
	// label
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	// description/tooltips
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// field key
	FieldKey string `protobuf:"bytes,4,opt,name=field_key,json=fieldKey,proto3" json:"field_key,omitempty"`
	// field type
	FieldType Field_Type `protobuf:"varint,5,opt,name=field_type,json=fieldType,proto3,enum=moego.models.reporting.v2.Field_Type" json:"field_type,omitempty"`
	// Whether the field is sortable
	Sortable bool `protobuf:"varint,6,opt,name=sortable,proto3" json:"sortable,omitempty"`
	// Whether the field is removable
	Removable bool `protobuf:"varint,7,opt,name=removable,proto3" json:"removable,omitempty"`
	// Whether the field can be grouped by
	GroupByEnable bool `protobuf:"varint,8,opt,name=group_by_enable,json=groupByEnable,proto3" json:"group_by_enable,omitempty"`
	// Whether the field is movable
	Movable bool `protobuf:"varint,9,opt,name=movable,proto3" json:"movable,omitempty"`
	// Field trend type: BENEFIT, HARMFUL, NEUTRAL
	Trend Trend `protobuf:"varint,10,opt,name=trend,proto3,enum=moego.models.reporting.v2.Trend" json:"trend,omitempty"`
}

func (x *Attribute) Reset() {
	*x = Attribute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_reporting_v2_attribute_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attribute) ProtoMessage() {}

func (x *Attribute) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_reporting_v2_attribute_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attribute.ProtoReflect.Descriptor instead.
func (*Attribute) Descriptor() ([]byte, []int) {
	return file_moego_models_reporting_v2_attribute_model_proto_rawDescGZIP(), []int{0}
}

func (x *Attribute) GetType() Attribute_Type {
	if x != nil {
		return x.Type
	}
	return Attribute_TYPE_UNSPECIFIED
}

func (x *Attribute) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Attribute) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Attribute) GetFieldKey() string {
	if x != nil {
		return x.FieldKey
	}
	return ""
}

func (x *Attribute) GetFieldType() Field_Type {
	if x != nil {
		return x.FieldType
	}
	return Field_TYPE_UNSPECIFIED
}

func (x *Attribute) GetSortable() bool {
	if x != nil {
		return x.Sortable
	}
	return false
}

func (x *Attribute) GetRemovable() bool {
	if x != nil {
		return x.Removable
	}
	return false
}

func (x *Attribute) GetGroupByEnable() bool {
	if x != nil {
		return x.GroupByEnable
	}
	return false
}

func (x *Attribute) GetMovable() bool {
	if x != nil {
		return x.Movable
	}
	return false
}

func (x *Attribute) GetTrend() Trend {
	if x != nil {
		return x.Trend
	}
	return Trend_TREND_UNSPECIFIED
}

var File_moego_models_reporting_v2_attribute_model_proto protoreflect.FileDescriptor

var file_moego_models_reporting_v2_attribute_model_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x2c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe7, 0x03, 0x0a, 0x09, 0x41, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x44,
	0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x73, 0x6f, 0x72, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26,
	0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x76, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6d, 0x6f, 0x76, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x36, 0x0a, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x72, 0x65, 0x6e,
	0x64, 0x52, 0x05, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x22, 0x37, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x45, 0x54, 0x52, 0x49, 0x43,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x49, 0x4d, 0x45, 0x4e, 0x53, 0x49, 0x4f, 0x4e, 0x10,
	0x02, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x81, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_reporting_v2_attribute_model_proto_rawDescOnce sync.Once
	file_moego_models_reporting_v2_attribute_model_proto_rawDescData = file_moego_models_reporting_v2_attribute_model_proto_rawDesc
)

func file_moego_models_reporting_v2_attribute_model_proto_rawDescGZIP() []byte {
	file_moego_models_reporting_v2_attribute_model_proto_rawDescOnce.Do(func() {
		file_moego_models_reporting_v2_attribute_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_reporting_v2_attribute_model_proto_rawDescData)
	})
	return file_moego_models_reporting_v2_attribute_model_proto_rawDescData
}

var file_moego_models_reporting_v2_attribute_model_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_reporting_v2_attribute_model_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_reporting_v2_attribute_model_proto_goTypes = []interface{}{
	(Attribute_Type)(0), // 0: moego.models.reporting.v2.Attribute.Type
	(*Attribute)(nil),   // 1: moego.models.reporting.v2.Attribute
	(Field_Type)(0),     // 2: moego.models.reporting.v2.Field.Type
	(Trend)(0),          // 3: moego.models.reporting.v2.Trend
}
var file_moego_models_reporting_v2_attribute_model_proto_depIdxs = []int32{
	0, // 0: moego.models.reporting.v2.Attribute.type:type_name -> moego.models.reporting.v2.Attribute.Type
	2, // 1: moego.models.reporting.v2.Attribute.field_type:type_name -> moego.models.reporting.v2.Field.Type
	3, // 2: moego.models.reporting.v2.Attribute.trend:type_name -> moego.models.reporting.v2.Trend
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_reporting_v2_attribute_model_proto_init() }
func file_moego_models_reporting_v2_attribute_model_proto_init() {
	if File_moego_models_reporting_v2_attribute_model_proto != nil {
		return
	}
	file_moego_models_reporting_v2_common_model_proto_init()
	file_moego_models_reporting_v2_field_model_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_reporting_v2_attribute_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attribute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_reporting_v2_attribute_model_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_reporting_v2_attribute_model_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_reporting_v2_attribute_model_proto_goTypes,
		DependencyIndexes: file_moego_models_reporting_v2_attribute_model_proto_depIdxs,
		EnumInfos:         file_moego_models_reporting_v2_attribute_model_proto_enumTypes,
		MessageInfos:      file_moego_models_reporting_v2_attribute_model_proto_msgTypes,
	}.Build()
	File_moego_models_reporting_v2_attribute_model_proto = out.File
	file_moego_models_reporting_v2_attribute_model_proto_rawDesc = nil
	file_moego_models_reporting_v2_attribute_model_proto_goTypes = nil
	file_moego_models_reporting_v2_attribute_model_proto_depIdxs = nil
}
