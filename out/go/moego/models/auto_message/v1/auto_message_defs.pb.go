// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/auto_message/v1/auto_message_defs.proto

package automessagepb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// service type template def
type ServiceTypeTemplateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available service types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,1,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// email subject
	EmailSubject string `protobuf:"bytes,2,opt,name=email_subject,json=emailSubject,proto3" json:"email_subject,omitempty"`
	// email body
	EmailBody string `protobuf:"bytes,3,opt,name=email_body,json=emailBody,proto3" json:"email_body,omitempty"`
	// sms body
	SmsBody string `protobuf:"bytes,4,opt,name=sms_body,json=smsBody,proto3" json:"sms_body,omitempty"`
	// app body
	AppBody string `protobuf:"bytes,5,opt,name=app_body,json=appBody,proto3" json:"app_body,omitempty"`
}

func (x *ServiceTypeTemplateDef) Reset() {
	*x = ServiceTypeTemplateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceTypeTemplateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTypeTemplateDef) ProtoMessage() {}

func (x *ServiceTypeTemplateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTypeTemplateDef.ProtoReflect.Descriptor instead.
func (*ServiceTypeTemplateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceTypeTemplateDef) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ServiceTypeTemplateDef) GetEmailSubject() string {
	if x != nil {
		return x.EmailSubject
	}
	return ""
}

func (x *ServiceTypeTemplateDef) GetEmailBody() string {
	if x != nil {
		return x.EmailBody
	}
	return ""
}

func (x *ServiceTypeTemplateDef) GetSmsBody() string {
	if x != nil {
		return x.SmsBody
	}
	return ""
}

func (x *ServiceTypeTemplateDef) GetAppBody() string {
	if x != nil {
		return x.AppBody
	}
	return ""
}

// service type template def list
type ServiceTypeTemplateDefList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of values
	Values []*ServiceTypeTemplateDef `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *ServiceTypeTemplateDefList) Reset() {
	*x = ServiceTypeTemplateDefList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceTypeTemplateDefList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTypeTemplateDefList) ProtoMessage() {}

func (x *ServiceTypeTemplateDefList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTypeTemplateDefList.ProtoReflect.Descriptor instead.
func (*ServiceTypeTemplateDefList) Descriptor() ([]byte, []int) {
	return file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceTypeTemplateDefList) GetValues() []*ServiceTypeTemplateDef {
	if x != nil {
		return x.Values
	}
	return nil
}

// appointment template for service type config
type ServiceTypeConfigDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available service types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,1,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// sms template id
	SmsTemplateId int64 `protobuf:"varint,2,opt,name=sms_template_id,json=smsTemplateId,proto3" json:"sms_template_id,omitempty"`
	// email template id
	EmailTemplateId int64 `protobuf:"varint,3,opt,name=email_template_id,json=emailTemplateId,proto3" json:"email_template_id,omitempty"`
	// app template id
	AppTemplateId int64 `protobuf:"varint,4,opt,name=app_template_id,json=appTemplateId,proto3" json:"app_template_id,omitempty"`
}

func (x *ServiceTypeConfigDef) Reset() {
	*x = ServiceTypeConfigDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceTypeConfigDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTypeConfigDef) ProtoMessage() {}

func (x *ServiceTypeConfigDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTypeConfigDef.ProtoReflect.Descriptor instead.
func (*ServiceTypeConfigDef) Descriptor() ([]byte, []int) {
	return file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceTypeConfigDef) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ServiceTypeConfigDef) GetSmsTemplateId() int64 {
	if x != nil {
		return x.SmsTemplateId
	}
	return 0
}

func (x *ServiceTypeConfigDef) GetEmailTemplateId() int64 {
	if x != nil {
		return x.EmailTemplateId
	}
	return 0
}

func (x *ServiceTypeConfigDef) GetAppTemplateId() int64 {
	if x != nil {
		return x.AppTemplateId
	}
	return 0
}

// service type config def list
type ServiceTypeConfigDefList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of values
	Values []*ServiceTypeConfigDef `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *ServiceTypeConfigDefList) Reset() {
	*x = ServiceTypeConfigDefList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceTypeConfigDefList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceTypeConfigDefList) ProtoMessage() {}

func (x *ServiceTypeConfigDefList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceTypeConfigDefList.ProtoReflect.Descriptor instead.
func (*ServiceTypeConfigDefList) Descriptor() ([]byte, []int) {
	return file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescGZIP(), []int{3}
}

func (x *ServiceTypeConfigDefList) GetValues() []*ServiceTypeConfigDef {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_moego_models_auto_message_v1_auto_message_defs_proto protoreflect.FileDescriptor

var file_moego_models_auto_message_v1_auto_message_defs_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x2e, 0x76, 0x31, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xab, 0x02, 0x0a, 0x16, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x6c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x13, 0xfa, 0x42,
	0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20,
	0x00, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x0d, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0x80, 0x02, 0x52, 0x0c, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x75, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x62, 0x6f, 0x64, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80,
	0x08, 0x52, 0x09, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x24, 0x0a, 0x08,
	0x73, 0x6d, 0x73, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x04, 0x52, 0x07, 0x73, 0x6d, 0x73, 0x42, 0x6f,
	0x64, 0x79, 0x12, 0x24, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x04, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x42, 0x6f, 0x64, 0x79, 0x22, 0x74, 0x0a, 0x1a, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x9b,
	0x02, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x12, 0x6c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x13,
	0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x0f, 0x73, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x73, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x11, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0f, 0x61,
	0x70, 0x70, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x22, 0x70, 0x0a, 0x18,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x44, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x89,
	0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescOnce sync.Once
	file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescData = file_moego_models_auto_message_v1_auto_message_defs_proto_rawDesc
)

func file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescGZIP() []byte {
	file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescData)
	})
	return file_moego_models_auto_message_v1_auto_message_defs_proto_rawDescData
}

var file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_auto_message_v1_auto_message_defs_proto_goTypes = []interface{}{
	(*ServiceTypeTemplateDef)(nil),     // 0: moego.models.auto_message.v1.ServiceTypeTemplateDef
	(*ServiceTypeTemplateDefList)(nil), // 1: moego.models.auto_message.v1.ServiceTypeTemplateDefList
	(*ServiceTypeConfigDef)(nil),       // 2: moego.models.auto_message.v1.ServiceTypeConfigDef
	(*ServiceTypeConfigDefList)(nil),   // 3: moego.models.auto_message.v1.ServiceTypeConfigDefList
	(v1.ServiceItemType)(0),            // 4: moego.models.offering.v1.ServiceItemType
}
var file_moego_models_auto_message_v1_auto_message_defs_proto_depIdxs = []int32{
	4, // 0: moego.models.auto_message.v1.ServiceTypeTemplateDef.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	0, // 1: moego.models.auto_message.v1.ServiceTypeTemplateDefList.values:type_name -> moego.models.auto_message.v1.ServiceTypeTemplateDef
	4, // 2: moego.models.auto_message.v1.ServiceTypeConfigDef.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	2, // 3: moego.models.auto_message.v1.ServiceTypeConfigDefList.values:type_name -> moego.models.auto_message.v1.ServiceTypeConfigDef
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_auto_message_v1_auto_message_defs_proto_init() }
func file_moego_models_auto_message_v1_auto_message_defs_proto_init() {
	if File_moego_models_auto_message_v1_auto_message_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceTypeTemplateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceTypeTemplateDefList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceTypeConfigDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceTypeConfigDefList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_auto_message_v1_auto_message_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_auto_message_v1_auto_message_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_auto_message_v1_auto_message_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_auto_message_v1_auto_message_defs_proto_msgTypes,
	}.Build()
	File_moego_models_auto_message_v1_auto_message_defs_proto = out.File
	file_moego_models_auto_message_v1_auto_message_defs_proto_rawDesc = nil
	file_moego_models_auto_message_v1_auto_message_defs_proto_goTypes = nil
	file_moego_models_auto_message_v1_auto_message_defs_proto_depIdxs = nil
}
