// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/utils/v2/list.proto

package utilsV2

import (
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// A list of int64 values.
// used for:
// 1. As map values
// 2. Differentiate whether the frontend did not pass a value or passed an empty array.
type Int64List struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of values
	Values []int64 `protobuf:"varint,1,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (x *Int64List) Reset() {
	*x = Int64List{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_list_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int64List) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int64List) ProtoMessage() {}

func (x *Int64List) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_list_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int64List.ProtoReflect.Descriptor instead.
func (*Int64List) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_list_proto_rawDescGZIP(), []int{0}
}

func (x *Int64List) GetValues() []int64 {
	if x != nil {
		return x.Values
	}
	return nil
}

// Int32List represents a list of int32 values
type Int32List struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of values
	Values []int32 `protobuf:"varint,1,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (x *Int32List) Reset() {
	*x = Int32List{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_list_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Int32List) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Int32List) ProtoMessage() {}

func (x *Int32List) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_list_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Int32List.ProtoReflect.Descriptor instead.
func (*Int32List) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_list_proto_rawDescGZIP(), []int{1}
}

func (x *Int32List) GetValues() []int32 {
	if x != nil {
		return x.Values
	}
	return nil
}

// DateList represents a list of google.type.Date
type DateList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of values
	Dates []*date.Date `protobuf:"bytes,1,rep,name=dates,proto3" json:"dates,omitempty"`
}

func (x *DateList) Reset() {
	*x = DateList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_utils_v2_list_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateList) ProtoMessage() {}

func (x *DateList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_utils_v2_list_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateList.ProtoReflect.Descriptor instead.
func (*DateList) Descriptor() ([]byte, []int) {
	return file_moego_utils_v2_list_proto_rawDescGZIP(), []int{2}
}

func (x *DateList) GetDates() []*date.Date {
	if x != nil {
		return x.Dates
	}
	return nil
}

var File_moego_utils_v2_list_proto protoreflect.FileDescriptor

var file_moego_utils_v2_list_proto_rawDesc = []byte{
	0x0a, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x1a, 0x16, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x23, 0x0a, 0x09, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x23, 0x0a, 0x09, 0x49, 0x6e, 0x74, 0x33,
	0x32, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x33, 0x0a,
	0x08, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x05, 0x64, 0x61, 0x74,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x05, 0x64, 0x61, 0x74,
	0x65, 0x73, 0x42, 0x67, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x4b,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x3b, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x56, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_utils_v2_list_proto_rawDescOnce sync.Once
	file_moego_utils_v2_list_proto_rawDescData = file_moego_utils_v2_list_proto_rawDesc
)

func file_moego_utils_v2_list_proto_rawDescGZIP() []byte {
	file_moego_utils_v2_list_proto_rawDescOnce.Do(func() {
		file_moego_utils_v2_list_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_utils_v2_list_proto_rawDescData)
	})
	return file_moego_utils_v2_list_proto_rawDescData
}

var file_moego_utils_v2_list_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_utils_v2_list_proto_goTypes = []interface{}{
	(*Int64List)(nil), // 0: moego.utils.v2.Int64List
	(*Int32List)(nil), // 1: moego.utils.v2.Int32List
	(*DateList)(nil),  // 2: moego.utils.v2.DateList
	(*date.Date)(nil), // 3: google.type.Date
}
var file_moego_utils_v2_list_proto_depIdxs = []int32{
	3, // 0: moego.utils.v2.DateList.dates:type_name -> google.type.Date
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_utils_v2_list_proto_init() }
func file_moego_utils_v2_list_proto_init() {
	if File_moego_utils_v2_list_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_utils_v2_list_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int64List); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_list_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Int32List); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_utils_v2_list_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_utils_v2_list_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_utils_v2_list_proto_goTypes,
		DependencyIndexes: file_moego_utils_v2_list_proto_depIdxs,
		MessageInfos:      file_moego_utils_v2_list_proto_msgTypes,
	}.Build()
	File_moego_utils_v2_list_proto = out.File
	file_moego_utils_v2_list_proto_rawDesc = nil
	file_moego_utils_v2_list_proto_goTypes = nil
	file_moego_utils_v2_list_proto_depIdxs = nil
}
