// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/organization/v1/van_service.proto

package organizationsvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get van list request
type GetVanListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId string `protobuf:"bytes,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id(optional)
	BusinessId *string `protobuf:"bytes,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// pagination request(optional)
	Pagination *v1.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *GetVanListRequest) Reset() {
	*x = GetVanListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVanListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVanListRequest) ProtoMessage() {}

func (x *GetVanListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVanListRequest.ProtoReflect.Descriptor instead.
func (*GetVanListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetVanListRequest) GetCompanyId() string {
	if x != nil {
		return x.CompanyId
	}
	return ""
}

func (x *GetVanListRequest) GetBusinessId() string {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return ""
}

func (x *GetVanListRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get van list response
type GetVanListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v1.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// van list
	VanList []*v11.VanModel `protobuf:"bytes,2,rep,name=van_list,json=vanList,proto3" json:"van_list,omitempty"`
}

func (x *GetVanListResponse) Reset() {
	*x = GetVanListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVanListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVanListResponse) ProtoMessage() {}

func (x *GetVanListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVanListResponse.ProtoReflect.Descriptor instead.
func (*GetVanListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetVanListResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetVanListResponse) GetVanList() []*v11.VanModel {
	if x != nil {
		return x.VanList
	}
	return nil
}

// get van list by staff ids request
type GetVanListByStaffIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,2,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *GetVanListByStaffIdsRequest) Reset() {
	*x = GetVanListByStaffIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVanListByStaffIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVanListByStaffIdsRequest) ProtoMessage() {}

func (x *GetVanListByStaffIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVanListByStaffIdsRequest.ProtoReflect.Descriptor instead.
func (*GetVanListByStaffIdsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetVanListByStaffIdsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetVanListByStaffIdsRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// get van list by staff ids response
type GetVanListByStaffIdsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id to van
	StaffVanMap map[int64]*v11.VanModel `protobuf:"bytes,1,rep,name=staff_van_map,json=staffVanMap,proto3" json:"staff_van_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetVanListByStaffIdsResponse) Reset() {
	*x = GetVanListByStaffIdsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVanListByStaffIdsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVanListByStaffIdsResponse) ProtoMessage() {}

func (x *GetVanListByStaffIdsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVanListByStaffIdsResponse.ProtoReflect.Descriptor instead.
func (*GetVanListByStaffIdsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetVanListByStaffIdsResponse) GetStaffVanMap() map[int64]*v11.VanModel {
	if x != nil {
		return x.StaffVanMap
	}
	return nil
}

// get van list by multi company ids request
type GetVanListByMultiCompanyIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyIds []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *GetVanListByMultiCompanyIdRequest) Reset() {
	*x = GetVanListByMultiCompanyIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVanListByMultiCompanyIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVanListByMultiCompanyIdRequest) ProtoMessage() {}

func (x *GetVanListByMultiCompanyIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVanListByMultiCompanyIdRequest.ProtoReflect.Descriptor instead.
func (*GetVanListByMultiCompanyIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetVanListByMultiCompanyIdRequest) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// get van list by multi company ids response
type GetVanListByMultiCompanyIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id to van
	CompanyVanListMap map[int64]*v11.VanListModel `protobuf:"bytes,1,rep,name=company_van_list_map,json=companyVanListMap,proto3" json:"company_van_list_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetVanListByMultiCompanyIdResponse) Reset() {
	*x = GetVanListByMultiCompanyIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVanListByMultiCompanyIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVanListByMultiCompanyIdResponse) ProtoMessage() {}

func (x *GetVanListByMultiCompanyIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVanListByMultiCompanyIdResponse.ProtoReflect.Descriptor instead.
func (*GetVanListByMultiCompanyIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetVanListByMultiCompanyIdResponse) GetCompanyVanListMap() map[int64]*v11.VanListModel {
	if x != nil {
		return x.CompanyVanListMap
	}
	return nil
}

// get assigned van by staff ids request
type GetAssignedVanForStaffIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff ids
	StaffIds []int64 `protobuf:"varint,1,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
}

func (x *GetAssignedVanForStaffIdRequest) Reset() {
	*x = GetAssignedVanForStaffIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssignedVanForStaffIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssignedVanForStaffIdRequest) ProtoMessage() {}

func (x *GetAssignedVanForStaffIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssignedVanForStaffIdRequest.ProtoReflect.Descriptor instead.
func (*GetAssignedVanForStaffIdRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetAssignedVanForStaffIdRequest) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

// get assigned van by staff ids response
type GetAssignedVanForStaffIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id to van
	StaffVanMap map[int64]int64 `protobuf:"bytes,1,rep,name=staff_van_map,json=staffVanMap,proto3" json:"staff_van_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *GetAssignedVanForStaffIdResponse) Reset() {
	*x = GetAssignedVanForStaffIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssignedVanForStaffIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssignedVanForStaffIdResponse) ProtoMessage() {}

func (x *GetAssignedVanForStaffIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssignedVanForStaffIdResponse.ProtoReflect.Descriptor instead.
func (*GetAssignedVanForStaffIdResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetAssignedVanForStaffIdResponse) GetStaffVanMap() map[int64]int64 {
	if x != nil {
		return x.StaffVanMap
	}
	return nil
}

// force assign staff to van request
type ForceAssignStaffToVanRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// van id
	VanId *int64 `protobuf:"varint,2,opt,name=van_id,json=vanId,proto3,oneof" json:"van_id,omitempty"`
}

func (x *ForceAssignStaffToVanRequest) Reset() {
	*x = ForceAssignStaffToVanRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForceAssignStaffToVanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceAssignStaffToVanRequest) ProtoMessage() {}

func (x *ForceAssignStaffToVanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceAssignStaffToVanRequest.ProtoReflect.Descriptor instead.
func (*ForceAssignStaffToVanRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{8}
}

func (x *ForceAssignStaffToVanRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ForceAssignStaffToVanRequest) GetVanId() int64 {
	if x != nil && x.VanId != nil {
		return *x.VanId
	}
	return 0
}

// force assign staff to van response
type ForceAssignStaffToVanResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ForceAssignStaffToVanResponse) Reset() {
	*x = ForceAssignStaffToVanResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForceAssignStaffToVanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceAssignStaffToVanResponse) ProtoMessage() {}

func (x *ForceAssignStaffToVanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_organization_v1_van_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceAssignStaffToVanResponse.ProtoReflect.Descriptor instead.
func (*ForceAssignStaffToVanResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_organization_v1_van_service_proto_rawDescGZIP(), []int{9}
}

var File_moego_service_organization_v1_van_service_proto protoreflect.FileDescriptor

var file_moego_service_organization_v1_van_service_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x76, 0x61, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x76,
	0x61, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xbf, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x9b, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41,
	0x0a, 0x08, 0x76, 0x61, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x61, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x76, 0x61, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x74, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d,
	0x92, 0x01, 0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x56,
	0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x70, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x76, 0x61, 0x6e, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x4c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x56, 0x61, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x56, 0x61, 0x6e, 0x4d, 0x61, 0x70, 0x1a, 0x66, 0x0a, 0x10, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x56, 0x61, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x61, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x44, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0xa2, 0x02, 0x0a, 0x22, 0x47, 0x65, 0x74,
	0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x89, 0x01, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x76, 0x61, 0x6e, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x58,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x70, 0x1a, 0x70, 0x0a, 0x16, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x3e, 0x0a,
	0x1f, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6e, 0x46,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x22, 0xd8, 0x01,
	0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6e,
	0x46, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x74, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x76, 0x61, 0x6e, 0x5f,
	0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6e, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x56, 0x61, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x56, 0x61, 0x6e, 0x4d, 0x61, 0x70, 0x1a, 0x3e, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x56, 0x61, 0x6e, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x69, 0x0a, 0x1c, 0x46, 0x6f, 0x72, 0x63,
	0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x6f, 0x56, 0x61,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x06,
	0x76, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x05,
	0x76, 0x61, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x76, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x22, 0x1f, 0x0a, 0x1d, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x6f, 0x56, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x32, 0xe8, 0x05, 0x0a, 0x0a, 0x56, 0x61, 0x6e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8f, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x56, 0x61,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12,
	0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x56, 0x61, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x61, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6e, 0x46,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6e, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x64, 0x56, 0x61, 0x6e, 0x46, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x66, 0x66,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x15, 0x46,
	0x6f, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x54,
	0x6f, 0x56, 0x61, 0x6e, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x53, 0x74, 0x61, 0x66, 0x66, 0x54, 0x6f, 0x56, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x54, 0x6f, 0x56, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x8f, 0x01, 0x0a, 0x25, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x64, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_organization_v1_van_service_proto_rawDescOnce sync.Once
	file_moego_service_organization_v1_van_service_proto_rawDescData = file_moego_service_organization_v1_van_service_proto_rawDesc
)

func file_moego_service_organization_v1_van_service_proto_rawDescGZIP() []byte {
	file_moego_service_organization_v1_van_service_proto_rawDescOnce.Do(func() {
		file_moego_service_organization_v1_van_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_organization_v1_van_service_proto_rawDescData)
	})
	return file_moego_service_organization_v1_van_service_proto_rawDescData
}

var file_moego_service_organization_v1_van_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_service_organization_v1_van_service_proto_goTypes = []interface{}{
	(*GetVanListRequest)(nil),                  // 0: moego.service.organization.v1.GetVanListRequest
	(*GetVanListResponse)(nil),                 // 1: moego.service.organization.v1.GetVanListResponse
	(*GetVanListByStaffIdsRequest)(nil),        // 2: moego.service.organization.v1.GetVanListByStaffIdsRequest
	(*GetVanListByStaffIdsResponse)(nil),       // 3: moego.service.organization.v1.GetVanListByStaffIdsResponse
	(*GetVanListByMultiCompanyIdRequest)(nil),  // 4: moego.service.organization.v1.GetVanListByMultiCompanyIdRequest
	(*GetVanListByMultiCompanyIdResponse)(nil), // 5: moego.service.organization.v1.GetVanListByMultiCompanyIdResponse
	(*GetAssignedVanForStaffIdRequest)(nil),    // 6: moego.service.organization.v1.GetAssignedVanForStaffIdRequest
	(*GetAssignedVanForStaffIdResponse)(nil),   // 7: moego.service.organization.v1.GetAssignedVanForStaffIdResponse
	(*ForceAssignStaffToVanRequest)(nil),       // 8: moego.service.organization.v1.ForceAssignStaffToVanRequest
	(*ForceAssignStaffToVanResponse)(nil),      // 9: moego.service.organization.v1.ForceAssignStaffToVanResponse
	nil,                                        // 10: moego.service.organization.v1.GetVanListByStaffIdsResponse.StaffVanMapEntry
	nil,                                        // 11: moego.service.organization.v1.GetVanListByMultiCompanyIdResponse.CompanyVanListMapEntry
	nil,                                        // 12: moego.service.organization.v1.GetAssignedVanForStaffIdResponse.StaffVanMapEntry
	(*v1.PaginationRequest)(nil),               // 13: moego.utils.v1.PaginationRequest
	(*v1.PaginationResponse)(nil),              // 14: moego.utils.v1.PaginationResponse
	(*v11.VanModel)(nil),                       // 15: moego.models.organization.v1.VanModel
	(*v11.VanListModel)(nil),                   // 16: moego.models.organization.v1.VanListModel
}
var file_moego_service_organization_v1_van_service_proto_depIdxs = []int32{
	13, // 0: moego.service.organization.v1.GetVanListRequest.pagination:type_name -> moego.utils.v1.PaginationRequest
	14, // 1: moego.service.organization.v1.GetVanListResponse.pagination:type_name -> moego.utils.v1.PaginationResponse
	15, // 2: moego.service.organization.v1.GetVanListResponse.van_list:type_name -> moego.models.organization.v1.VanModel
	10, // 3: moego.service.organization.v1.GetVanListByStaffIdsResponse.staff_van_map:type_name -> moego.service.organization.v1.GetVanListByStaffIdsResponse.StaffVanMapEntry
	11, // 4: moego.service.organization.v1.GetVanListByMultiCompanyIdResponse.company_van_list_map:type_name -> moego.service.organization.v1.GetVanListByMultiCompanyIdResponse.CompanyVanListMapEntry
	12, // 5: moego.service.organization.v1.GetAssignedVanForStaffIdResponse.staff_van_map:type_name -> moego.service.organization.v1.GetAssignedVanForStaffIdResponse.StaffVanMapEntry
	15, // 6: moego.service.organization.v1.GetVanListByStaffIdsResponse.StaffVanMapEntry.value:type_name -> moego.models.organization.v1.VanModel
	16, // 7: moego.service.organization.v1.GetVanListByMultiCompanyIdResponse.CompanyVanListMapEntry.value:type_name -> moego.models.organization.v1.VanListModel
	0,  // 8: moego.service.organization.v1.VanService.GetVanList:input_type -> moego.service.organization.v1.GetVanListRequest
	2,  // 9: moego.service.organization.v1.VanService.GetVanListByStaffIds:input_type -> moego.service.organization.v1.GetVanListByStaffIdsRequest
	4,  // 10: moego.service.organization.v1.VanService.GetVanListByMultiCompanyId:input_type -> moego.service.organization.v1.GetVanListByMultiCompanyIdRequest
	6,  // 11: moego.service.organization.v1.VanService.GetAssignedVanForStaffId:input_type -> moego.service.organization.v1.GetAssignedVanForStaffIdRequest
	8,  // 12: moego.service.organization.v1.VanService.ForceAssignStaffToVan:input_type -> moego.service.organization.v1.ForceAssignStaffToVanRequest
	1,  // 13: moego.service.organization.v1.VanService.GetVanList:output_type -> moego.service.organization.v1.GetVanListResponse
	3,  // 14: moego.service.organization.v1.VanService.GetVanListByStaffIds:output_type -> moego.service.organization.v1.GetVanListByStaffIdsResponse
	5,  // 15: moego.service.organization.v1.VanService.GetVanListByMultiCompanyId:output_type -> moego.service.organization.v1.GetVanListByMultiCompanyIdResponse
	7,  // 16: moego.service.organization.v1.VanService.GetAssignedVanForStaffId:output_type -> moego.service.organization.v1.GetAssignedVanForStaffIdResponse
	9,  // 17: moego.service.organization.v1.VanService.ForceAssignStaffToVan:output_type -> moego.service.organization.v1.ForceAssignStaffToVanResponse
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_service_organization_v1_van_service_proto_init() }
func file_moego_service_organization_v1_van_service_proto_init() {
	if File_moego_service_organization_v1_van_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_organization_v1_van_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVanListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVanListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVanListByStaffIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVanListByStaffIdsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVanListByMultiCompanyIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVanListByMultiCompanyIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssignedVanForStaffIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssignedVanForStaffIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForceAssignStaffToVanRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_organization_v1_van_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForceAssignStaffToVanResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_organization_v1_van_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_organization_v1_van_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_organization_v1_van_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_organization_v1_van_service_proto_goTypes,
		DependencyIndexes: file_moego_service_organization_v1_van_service_proto_depIdxs,
		MessageInfos:      file_moego_service_organization_v1_van_service_proto_msgTypes,
	}.Build()
	File_moego_service_organization_v1_van_service_proto = out.File
	file_moego_service_organization_v1_van_service_proto_rawDesc = nil
	file_moego_service_organization_v1_van_service_proto_goTypes = nil
	file_moego_service_organization_v1_van_service_proto_depIdxs = nil
}
