// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/reporting/v2/reports_service.proto

package reportingsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	calendarperiod "google.golang.org/genproto/googleapis/type/calendarperiod"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The favorite action enum
type MarkReportFavoriteParams_Action int32

const (
	// Unspecified favorite actions
	MarkReportFavoriteParams_FAVORITE_ACTION_UNSPECIFIED MarkReportFavoriteParams_Action = 0
	// A favorite action to add a report to favorites
	MarkReportFavoriteParams_ADD MarkReportFavoriteParams_Action = 1
	// A favorite action to remove a report from favorites
	MarkReportFavoriteParams_REMOVE MarkReportFavoriteParams_Action = 2
)

// Enum value maps for MarkReportFavoriteParams_Action.
var (
	MarkReportFavoriteParams_Action_name = map[int32]string{
		0: "FAVORITE_ACTION_UNSPECIFIED",
		1: "ADD",
		2: "REMOVE",
	}
	MarkReportFavoriteParams_Action_value = map[string]int32{
		"FAVORITE_ACTION_UNSPECIFIED": 0,
		"ADD":                         1,
		"REMOVE":                      2,
	}
)

func (x MarkReportFavoriteParams_Action) Enum() *MarkReportFavoriteParams_Action {
	p := new(MarkReportFavoriteParams_Action)
	*p = x
	return p
}

func (x MarkReportFavoriteParams_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MarkReportFavoriteParams_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_reporting_v2_reports_service_proto_enumTypes[0].Descriptor()
}

func (MarkReportFavoriteParams_Action) Type() protoreflect.EnumType {
	return &file_moego_service_reporting_v2_reports_service_proto_enumTypes[0]
}

func (x MarkReportFavoriteParams_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MarkReportFavoriteParams_Action.Descriptor instead.
func (MarkReportFavoriteParams_Action) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{2, 0}
}

// QueryReportPagesParams
type QueryReportPagesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The tabs to query reports page
	Tabs []v2.ReportPage_Tab `protobuf:"varint,1,rep,packed,name=tabs,proto3,enum=moego.models.reporting.v2.ReportPage_Tab" json:"tabs,omitempty"`
	// token info: company id and staff id
	TokenInfo *v2.TokenInfo `protobuf:"bytes,2,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	// 区分不同的 reporting 场景
	ReportingType v2.ReportingScene `protobuf:"varint,3,opt,name=reporting_type,json=reportingType,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"reporting_type,omitempty"`
}

func (x *QueryReportPagesParams) Reset() {
	*x = QueryReportPagesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReportPagesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReportPagesParams) ProtoMessage() {}

func (x *QueryReportPagesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReportPagesParams.ProtoReflect.Descriptor instead.
func (*QueryReportPagesParams) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{0}
}

func (x *QueryReportPagesParams) GetTabs() []v2.ReportPage_Tab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

func (x *QueryReportPagesParams) GetTokenInfo() *v2.TokenInfo {
	if x != nil {
		return x.TokenInfo
	}
	return nil
}

func (x *QueryReportPagesParams) GetReportingType() v2.ReportingScene {
	if x != nil {
		return x.ReportingType
	}
	return v2.ReportingScene(0)
}

// QueryReportPagesResult
type QueryReportPagesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of report pages
	Pages []*v2.ReportPage `protobuf:"bytes,1,rep,name=pages,proto3" json:"pages,omitempty"`
	// Report data last synced time
	LastSyncedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_synced_time,json=lastSyncedTime,proto3" json:"last_synced_time,omitempty"`
}

func (x *QueryReportPagesResult) Reset() {
	*x = QueryReportPagesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReportPagesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReportPagesResult) ProtoMessage() {}

func (x *QueryReportPagesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReportPagesResult.ProtoReflect.Descriptor instead.
func (*QueryReportPagesResult) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{1}
}

func (x *QueryReportPagesResult) GetPages() []*v2.ReportPage {
	if x != nil {
		return x.Pages
	}
	return nil
}

func (x *QueryReportPagesResult) GetLastSyncedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncedTime
	}
	return nil
}

// MarkReportFavoriteParams
type MarkReportFavoriteParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report to mark as favorite
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The action to take
	Action MarkReportFavoriteParams_Action `protobuf:"varint,2,opt,name=action,proto3,enum=moego.service.reporting.v2.MarkReportFavoriteParams_Action" json:"action,omitempty"`
	// token info: company id and staff id
	TokenInfo *v2.TokenInfo `protobuf:"bytes,3,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	// 区分不同的 reporting 场景
	ReportingType v2.ReportingScene `protobuf:"varint,4,opt,name=reporting_type,json=reportingType,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"reporting_type,omitempty"`
}

func (x *MarkReportFavoriteParams) Reset() {
	*x = MarkReportFavoriteParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkReportFavoriteParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkReportFavoriteParams) ProtoMessage() {}

func (x *MarkReportFavoriteParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkReportFavoriteParams.ProtoReflect.Descriptor instead.
func (*MarkReportFavoriteParams) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{2}
}

func (x *MarkReportFavoriteParams) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *MarkReportFavoriteParams) GetAction() MarkReportFavoriteParams_Action {
	if x != nil {
		return x.Action
	}
	return MarkReportFavoriteParams_FAVORITE_ACTION_UNSPECIFIED
}

func (x *MarkReportFavoriteParams) GetTokenInfo() *v2.TokenInfo {
	if x != nil {
		return x.TokenInfo
	}
	return nil
}

func (x *MarkReportFavoriteParams) GetReportingType() v2.ReportingScene {
	if x != nil {
		return x.ReportingType
	}
	return v2.ReportingScene(0)
}

// MarkReportFavoriteResponse
type MarkReportFavoriteResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Mark result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *MarkReportFavoriteResult) Reset() {
	*x = MarkReportFavoriteResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarkReportFavoriteResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkReportFavoriteResult) ProtoMessage() {}

func (x *MarkReportFavoriteResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkReportFavoriteResult.ProtoReflect.Descriptor instead.
func (*MarkReportFavoriteResult) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{3}
}

func (x *MarkReportFavoriteResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// SaveReportCustomizeConfigRequest
type SaveReportCustomizeConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report to save customized configs
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// Customize configs to save
	CustomizedConfig *v2.TableCustomizedConfig `protobuf:"bytes,2,opt,name=customized_config,json=customizedConfig,proto3" json:"customized_config,omitempty"`
	// token info: company id and staff id
	TokenInfo *v2.TokenInfo `protobuf:"bytes,3,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	// 区分不同的 reporting 场景
	ReportingType v2.ReportingScene `protobuf:"varint,4,opt,name=reporting_type,json=reportingType,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"reporting_type,omitempty"`
}

func (x *SaveReportCustomizeConfigParams) Reset() {
	*x = SaveReportCustomizeConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveReportCustomizeConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveReportCustomizeConfigParams) ProtoMessage() {}

func (x *SaveReportCustomizeConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveReportCustomizeConfigParams.ProtoReflect.Descriptor instead.
func (*SaveReportCustomizeConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{4}
}

func (x *SaveReportCustomizeConfigParams) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *SaveReportCustomizeConfigParams) GetCustomizedConfig() *v2.TableCustomizedConfig {
	if x != nil {
		return x.CustomizedConfig
	}
	return nil
}

func (x *SaveReportCustomizeConfigParams) GetTokenInfo() *v2.TokenInfo {
	if x != nil {
		return x.TokenInfo
	}
	return nil
}

func (x *SaveReportCustomizeConfigParams) GetReportingType() v2.ReportingScene {
	if x != nil {
		return x.ReportingType
	}
	return v2.ReportingScene(0)
}

// QueryReportsMetaRequest
type QueryReportMetasParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report to query meta data, if empty, return all reports' meta data
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
	// token info: company id and staff id
	TokenInfo *v2.TokenInfo `protobuf:"bytes,2,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	// 区分不同的 reporting 场景
	ReportingType v2.ReportingScene `protobuf:"varint,3,opt,name=reporting_type,json=reportingType,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"reporting_type,omitempty"`
	// The tenant id
	TenantsIds []uint64 `protobuf:"varint,4,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
}

func (x *QueryReportMetasParams) Reset() {
	*x = QueryReportMetasParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReportMetasParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReportMetasParams) ProtoMessage() {}

func (x *QueryReportMetasParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReportMetasParams.ProtoReflect.Descriptor instead.
func (*QueryReportMetasParams) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{5}
}

func (x *QueryReportMetasParams) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

func (x *QueryReportMetasParams) GetTokenInfo() *v2.TokenInfo {
	if x != nil {
		return x.TokenInfo
	}
	return nil
}

func (x *QueryReportMetasParams) GetReportingType() v2.ReportingScene {
	if x != nil {
		return x.ReportingType
	}
	return v2.ReportingScene(0)
}

func (x *QueryReportMetasParams) GetTenantsIds() []uint64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

// QueryReportsMetaRequest
type QueryReportsMetasResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of report metadata
	ReportMetas []*v2.TableMeta `protobuf:"bytes,1,rep,name=report_metas,json=reportMetas,proto3" json:"report_metas,omitempty"`
}

func (x *QueryReportsMetasResult) Reset() {
	*x = QueryReportsMetasResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryReportsMetasResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryReportsMetasResult) ProtoMessage() {}

func (x *QueryReportsMetasResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryReportsMetasResult.ProtoReflect.Descriptor instead.
func (*QueryReportsMetasResult) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{6}
}

func (x *QueryReportsMetasResult) GetReportMetas() []*v2.TableMeta {
	if x != nil {
		return x.ReportMetas
	}
	return nil
}

// Describe a request to fetch report data
type FetchReportDataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// The business id
	BusinessIds []uint64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// current period
	CurrentPeriod *interval.Interval `protobuf:"bytes,3,opt,name=current_period,json=currentPeriod,proto3" json:"current_period,omitempty"`
	// previous period
	PreviousPeriod *interval.Interval `protobuf:"bytes,4,opt,name=previous_period,json=previousPeriod,proto3,oneof" json:"previous_period,omitempty"`
	// Filters
	Filters []*v2.FilterRequest `protobuf:"bytes,5,rep,name=filters,proto3" json:"filters,omitempty"`
	// The group by field key
	GroupByFieldKeys []string `protobuf:"bytes,6,rep,name=group_by_field_keys,json=groupByFieldKeys,proto3" json:"group_by_field_keys,omitempty"`
	// The pagination request
	Pagination *v21.PaginationRequest `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// The order by config
	OrderBys []*v21.OrderBy `protobuf:"bytes,8,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// token info: company id and staff id
	TokenInfo *v2.TokenInfo `protobuf:"bytes,9,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	// The group by period, could be day, week, month, year etc., default day.
	GroupByPeriod *calendarperiod.CalendarPeriod `protobuf:"varint,10,opt,name=group_by_period,json=groupByPeriod,proto3,enum=google.type.CalendarPeriod,oneof" json:"group_by_period,omitempty"`
	// The filter groups
	FilterGroups []*v2.FilterRequestGroup `protobuf:"bytes,11,rep,name=filter_groups,json=filterGroups,proto3" json:"filter_groups,omitempty"`
	// 区分不同的 reporting 场景
	ReportingType v2.ReportingScene `protobuf:"varint,12,opt,name=reporting_type,json=reportingType,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"reporting_type,omitempty"`
	// The tenant id
	TenantsIds []uint64 `protobuf:"varint,13,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
}

func (x *FetchReportDataParams) Reset() {
	*x = FetchReportDataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchReportDataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchReportDataParams) ProtoMessage() {}

func (x *FetchReportDataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchReportDataParams.ProtoReflect.Descriptor instead.
func (*FetchReportDataParams) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{7}
}

func (x *FetchReportDataParams) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *FetchReportDataParams) GetBusinessIds() []uint64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *FetchReportDataParams) GetCurrentPeriod() *interval.Interval {
	if x != nil {
		return x.CurrentPeriod
	}
	return nil
}

func (x *FetchReportDataParams) GetPreviousPeriod() *interval.Interval {
	if x != nil {
		return x.PreviousPeriod
	}
	return nil
}

func (x *FetchReportDataParams) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *FetchReportDataParams) GetGroupByFieldKeys() []string {
	if x != nil {
		return x.GroupByFieldKeys
	}
	return nil
}

func (x *FetchReportDataParams) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchReportDataParams) GetOrderBys() []*v21.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *FetchReportDataParams) GetTokenInfo() *v2.TokenInfo {
	if x != nil {
		return x.TokenInfo
	}
	return nil
}

func (x *FetchReportDataParams) GetGroupByPeriod() calendarperiod.CalendarPeriod {
	if x != nil && x.GroupByPeriod != nil {
		return *x.GroupByPeriod
	}
	return calendarperiod.CalendarPeriod(0)
}

func (x *FetchReportDataParams) GetFilterGroups() []*v2.FilterRequestGroup {
	if x != nil {
		return x.FilterGroups
	}
	return nil
}

func (x *FetchReportDataParams) GetReportingType() v2.ReportingScene {
	if x != nil {
		return x.ReportingType
	}
	return v2.ReportingScene(0)
}

func (x *FetchReportDataParams) GetTenantsIds() []uint64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

// Describe a response to fetch report data
type FetchReportDataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The report data
	TableData *v2.TableData `protobuf:"bytes,1,opt,name=table_data,json=tableData,proto3" json:"table_data,omitempty"`
	// The pagination response
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// Report data last synced time
	LastSyncedTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=last_synced_time,json=lastSyncedTime,proto3" json:"last_synced_time,omitempty"`
}

func (x *FetchReportDataResult) Reset() {
	*x = FetchReportDataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchReportDataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchReportDataResult) ProtoMessage() {}

func (x *FetchReportDataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchReportDataResult.ProtoReflect.Descriptor instead.
func (*FetchReportDataResult) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{8}
}

func (x *FetchReportDataResult) GetTableData() *v2.TableData {
	if x != nil {
		return x.TableData
	}
	return nil
}

func (x *FetchReportDataResult) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchReportDataResult) GetLastSyncedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncedTime
	}
	return nil
}

// ExportReportDataParams
type ExportReportDataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// Filters
	Filters []*v2.FilterRequest `protobuf:"bytes,2,rep,name=filters,proto3" json:"filters,omitempty"`
	// token info: company id and staff id
	TokenInfo *v2.TokenInfo `protobuf:"bytes,3,opt,name=token_info,json=tokenInfo,proto3" json:"token_info,omitempty"`
	// business ids
	BusinessIds []uint64 `protobuf:"varint,4,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// current period
	CurrentPeriod *interval.Interval `protobuf:"bytes,5,opt,name=current_period,json=currentPeriod,proto3" json:"current_period,omitempty"`
	// previous period
	PreviousPeriod *interval.Interval `protobuf:"bytes,6,opt,name=previous_period,json=previousPeriod,proto3,oneof" json:"previous_period,omitempty"`
	// group by field key
	GroupByFieldKeys []string `protobuf:"bytes,7,rep,name=group_by_field_keys,json=groupByFieldKeys,proto3" json:"group_by_field_keys,omitempty"`
	// order by params
	OrderBys []*v21.OrderBy `protobuf:"bytes,8,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// The group by period, could be day, week, month, year etc., default day.
	GroupByPeriod *calendarperiod.CalendarPeriod `protobuf:"varint,9,opt,name=group_by_period,json=groupByPeriod,proto3,enum=google.type.CalendarPeriod,oneof" json:"group_by_period,omitempty"`
	// 区分不同的 reporting 场景
	ReportingType v2.ReportingScene `protobuf:"varint,10,opt,name=reporting_type,json=reportingType,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"reporting_type,omitempty"`
	// tenant ids
	TenantsIds []uint64 `protobuf:"varint,11,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
	// filter groups
	FilterGroups []*v2.FilterRequestGroup `protobuf:"bytes,12,rep,name=filter_groups,json=filterGroups,proto3" json:"filter_groups,omitempty"`
	// is all tenants
	AllTenants *bool `protobuf:"varint,13,opt,name=all_tenants,json=allTenants,proto3,oneof" json:"all_tenants,omitempty"`
}

func (x *ExportReportDataParams) Reset() {
	*x = ExportReportDataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportReportDataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportReportDataParams) ProtoMessage() {}

func (x *ExportReportDataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportReportDataParams.ProtoReflect.Descriptor instead.
func (*ExportReportDataParams) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{9}
}

func (x *ExportReportDataParams) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *ExportReportDataParams) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ExportReportDataParams) GetTokenInfo() *v2.TokenInfo {
	if x != nil {
		return x.TokenInfo
	}
	return nil
}

func (x *ExportReportDataParams) GetBusinessIds() []uint64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ExportReportDataParams) GetCurrentPeriod() *interval.Interval {
	if x != nil {
		return x.CurrentPeriod
	}
	return nil
}

func (x *ExportReportDataParams) GetPreviousPeriod() *interval.Interval {
	if x != nil {
		return x.PreviousPeriod
	}
	return nil
}

func (x *ExportReportDataParams) GetGroupByFieldKeys() []string {
	if x != nil {
		return x.GroupByFieldKeys
	}
	return nil
}

func (x *ExportReportDataParams) GetOrderBys() []*v21.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ExportReportDataParams) GetGroupByPeriod() calendarperiod.CalendarPeriod {
	if x != nil && x.GroupByPeriod != nil {
		return *x.GroupByPeriod
	}
	return calendarperiod.CalendarPeriod(0)
}

func (x *ExportReportDataParams) GetReportingType() v2.ReportingScene {
	if x != nil {
		return x.ReportingType
	}
	return v2.ReportingScene(0)
}

func (x *ExportReportDataParams) GetTenantsIds() []uint64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

func (x *ExportReportDataParams) GetFilterGroups() []*v2.FilterRequestGroup {
	if x != nil {
		return x.FilterGroups
	}
	return nil
}

func (x *ExportReportDataParams) GetAllTenants() bool {
	if x != nil && x.AllTenants != nil {
		return *x.AllTenants
	}
	return false
}

// ExportReportDataResult
type ExportReportDataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *ExportReportDataResult) Reset() {
	*x = ExportReportDataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportReportDataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportReportDataResult) ProtoMessage() {}

func (x *ExportReportDataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportReportDataResult.ProtoReflect.Descriptor instead.
func (*ExportReportDataResult) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{10}
}

func (x *ExportReportDataResult) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

// Query page meta request definition
type QueryPageMetaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tabs to query
	Tabs []v2.InsightsTab `protobuf:"varint,1,rep,packed,name=tabs,proto3,enum=moego.models.reporting.v2.InsightsTab" json:"tabs,omitempty"`
	// reporting scene
	Scene v2.ReportingScene `protobuf:"varint,2,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
}

func (x *QueryPageMetaRequest) Reset() {
	*x = QueryPageMetaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPageMetaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPageMetaRequest) ProtoMessage() {}

func (x *QueryPageMetaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPageMetaRequest.ProtoReflect.Descriptor instead.
func (*QueryPageMetaRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{11}
}

func (x *QueryPageMetaRequest) GetTabs() []v2.InsightsTab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

func (x *QueryPageMetaRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

// Query page meta response definition
type QueryPageMetaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pages
	Pages []*v2.PageMetaDef `protobuf:"bytes,1,rep,name=pages,proto3" json:"pages,omitempty"`
	// last synced time
	LastSyncedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_synced_time,json=lastSyncedTime,proto3" json:"last_synced_time,omitempty"`
}

func (x *QueryPageMetaResponse) Reset() {
	*x = QueryPageMetaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPageMetaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPageMetaResponse) ProtoMessage() {}

func (x *QueryPageMetaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPageMetaResponse.ProtoReflect.Descriptor instead.
func (*QueryPageMetaResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{12}
}

func (x *QueryPageMetaResponse) GetPages() []*v2.PageMetaDef {
	if x != nil {
		return x.Pages
	}
	return nil
}

func (x *QueryPageMetaResponse) GetLastSyncedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncedTime
	}
	return nil
}

// QueryMetasRequest
type QueryMetasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram_ids to query
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
	// reporting scene for current query
	Scene v2.ReportingScene `protobuf:"varint,2,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
	// scope filter
	Scope *v2.ScopeFilter `protobuf:"bytes,3,opt,name=scope,proto3" json:"scope,omitempty"`
}

func (x *QueryMetasRequest) Reset() {
	*x = QueryMetasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryMetasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryMetasRequest) ProtoMessage() {}

func (x *QueryMetasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryMetasRequest.ProtoReflect.Descriptor instead.
func (*QueryMetasRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{13}
}

func (x *QueryMetasRequest) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

func (x *QueryMetasRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

func (x *QueryMetasRequest) GetScope() *v2.ScopeFilter {
	if x != nil {
		return x.Scope
	}
	return nil
}

// QueryMetasResponse
type QueryMetasResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// meta result
	Metas []*v2.DiagramMeta `protobuf:"bytes,1,rep,name=metas,proto3" json:"metas,omitempty"`
}

func (x *QueryMetasResponse) Reset() {
	*x = QueryMetasResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryMetasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryMetasResponse) ProtoMessage() {}

func (x *QueryMetasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryMetasResponse.ProtoReflect.Descriptor instead.
func (*QueryMetasResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{14}
}

func (x *QueryMetasResponse) GetMetas() []*v2.DiagramMeta {
	if x != nil {
		return x.Metas
	}
	return nil
}

// FetchDataRequest
type FetchDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// query scope: business id or all businesses
	Scope *v2.ScopeFilter `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	// query time
	TimeRange *v2.TimeFilter `protobuf:"bytes,3,opt,name=time_range,json=timeRange,proto3,oneof" json:"time_range,omitempty"`
	// pagination params
	Pagination *v21.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// order by params
	OrderBys []*v21.OrderBy `protobuf:"bytes,5,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// filter params
	Filters []*v2.FilterRequest `protobuf:"bytes,6,rep,name=filters,proto3" json:"filters,omitempty"`
	// dimension filter
	Dimension *v2.DimensionFilter `protobuf:"bytes,7,opt,name=dimension,proto3,oneof" json:"dimension,omitempty"`
	// reporting scene for current query
	Scene v2.ReportingScene `protobuf:"varint,8,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
	// metrics field keys params
	MetricKeys []string `protobuf:"bytes,9,rep,name=metric_keys,json=metricKeys,proto3" json:"metric_keys,omitempty"`
	// dynamic column mode, use final dimension to generate columns
	DynamicColumnMode bool `protobuf:"varint,10,opt,name=dynamic_column_mode,json=dynamicColumnMode,proto3" json:"dynamic_column_mode,omitempty"`
}

func (x *FetchDataRequest) Reset() {
	*x = FetchDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDataRequest) ProtoMessage() {}

func (x *FetchDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDataRequest.ProtoReflect.Descriptor instead.
func (*FetchDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{15}
}

func (x *FetchDataRequest) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *FetchDataRequest) GetScope() *v2.ScopeFilter {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *FetchDataRequest) GetTimeRange() *v2.TimeFilter {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *FetchDataRequest) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchDataRequest) GetOrderBys() []*v21.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *FetchDataRequest) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *FetchDataRequest) GetDimension() *v2.DimensionFilter {
	if x != nil {
		return x.Dimension
	}
	return nil
}

func (x *FetchDataRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

func (x *FetchDataRequest) GetMetricKeys() []string {
	if x != nil {
		return x.MetricKeys
	}
	return nil
}

func (x *FetchDataRequest) GetDynamicColumnMode() bool {
	if x != nil {
		return x.DynamicColumnMode
	}
	return false
}

// FetchDataResponse
type FetchDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Data *v2.FetchDataDef `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	// report data last synced time
	LastSyncedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_synced_time,json=lastSyncedTime,proto3,oneof" json:"last_synced_time,omitempty"`
}

func (x *FetchDataResponse) Reset() {
	*x = FetchDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDataResponse) ProtoMessage() {}

func (x *FetchDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDataResponse.ProtoReflect.Descriptor instead.
func (*FetchDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{16}
}

func (x *FetchDataResponse) GetData() *v2.FetchDataDef {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *FetchDataResponse) GetLastSyncedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncedTime
	}
	return nil
}

// ExportDataRequest
type ExportDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id
	DiagramId string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3" json:"diagram_id,omitempty"`
	// query scope: business id or all businesses
	Scope *v2.ScopeFilter `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	// query time
	TimeRange *v2.TimeFilter `protobuf:"bytes,3,opt,name=time_range,json=timeRange,proto3,oneof" json:"time_range,omitempty"`
	// order by params
	OrderBys []*v21.OrderBy `protobuf:"bytes,4,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// filter params
	Filters []*v2.FilterRequest `protobuf:"bytes,5,rep,name=filters,proto3" json:"filters,omitempty"`
	// dimension filter
	Dimension *v2.DimensionFilter `protobuf:"bytes,6,opt,name=dimension,proto3,oneof" json:"dimension,omitempty"`
	// reporting scene for current query
	Scene v2.ReportingScene `protobuf:"varint,7,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
}

func (x *ExportDataRequest) Reset() {
	*x = ExportDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportDataRequest) ProtoMessage() {}

func (x *ExportDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportDataRequest.ProtoReflect.Descriptor instead.
func (*ExportDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{17}
}

func (x *ExportDataRequest) GetDiagramId() string {
	if x != nil {
		return x.DiagramId
	}
	return ""
}

func (x *ExportDataRequest) GetScope() *v2.ScopeFilter {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *ExportDataRequest) GetTimeRange() *v2.TimeFilter {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ExportDataRequest) GetOrderBys() []*v21.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ExportDataRequest) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *ExportDataRequest) GetDimension() *v2.DimensionFilter {
	if x != nil {
		return x.Dimension
	}
	return nil
}

func (x *ExportDataRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

// ExportDataResponse
type ExportDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// file id
	FileId int64 `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
}

func (x *ExportDataResponse) Reset() {
	*x = ExportDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExportDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportDataResponse) ProtoMessage() {}

func (x *ExportDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_reports_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportDataResponse.ProtoReflect.Descriptor instead.
func (*ExportDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP(), []int{18}
}

func (x *ExportDataResponse) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

var File_moego_service_reporting_v2_reports_service_proto protoreflect.FileDescriptor

var file_moego_service_reporting_v2_reports_service_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32,
	0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x81, 0x02, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x50, 0x61, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x50, 0x0a, 0x04, 0x74,
	0x61, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65,
	0x2e, 0x54, 0x61, 0x62, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x61, 0x62, 0x73, 0x12, 0x43, 0x0a,
	0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x50, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x3b, 0x0a, 0x05, 0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x05, 0x70, 0x61, 0x67, 0x65, 0x73, 0x12, 0x44, 0x0a, 0x10,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xfc, 0x02, 0x0a, 0x18, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x28, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09,
	0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x5f, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x50, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65,
	0x6e, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x3e, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x1b, 0x46,
	0x41, 0x56, 0x4f, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03,
	0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x10,
	0x02, 0x22, 0x32, 0x0a, 0x18, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc1, 0x02, 0x0a, 0x1f, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0a, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61,
	0x6d, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0x91, 0x02, 0x0a, 0x16, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01,
	0x08, 0x22, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72,
	0x61, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x0e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x0d, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x0b,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x04, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20,
	0x00, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x49, 0x64, 0x73, 0x22, 0x62, 0x0a,
	0x17, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x4d, 0x65, 0x74,
	0x61, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x47, 0x0a, 0x0c, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x73, 0x22, 0xfb, 0x06, 0x0a, 0x15, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0a, 0x64,
	0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x12, 0x43, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x2d, 0x0a, 0x13, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x73, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x48, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x62, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x43, 0x61,
	0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x48, 0x01, 0x52, 0x0d,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x52, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x12, 0x50, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42, 0x0b,
	0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x73, 0x49, 0x64, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22,
	0xe6, 0x01, 0x0a, 0x15, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x79,
	0x6e, 0x63, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xef, 0x06, 0x0a, 0x16, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x64, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x42, 0x0a,
	0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x43, 0x0a, 0x0a, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x12, 0x43, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x50, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x13, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x62, 0x79, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x10, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62,
	0x79, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x48, 0x0a, 0x0f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x48, 0x01, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x50, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x88, 0x01, 0x01, 0x12, 0x50, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42,
	0x0b, 0x92, 0x01, 0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x73, 0x49, 0x64, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0c,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x24, 0x0a, 0x0b,
	0x61, 0x6c, 0x6c, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x02, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x88,
	0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x62, 0x79, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61,
	0x6c, 0x6c, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x22, 0x31, 0x0a, 0x16, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x9f, 0x01,
	0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x61, 0x62, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x49, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x54, 0x61, 0x62, 0x52, 0x04, 0x74, 0x61,
	0x62, 0x73, 0x12, 0x4b, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x22,
	0x9b, 0x01, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x70, 0x61, 0x67,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x65, 0x66,
	0x52, 0x05, 0x70, 0x61, 0x67, 0x65, 0x73, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c,
	0x61, 0x73, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc3, 0x01,
	0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08,
	0x22, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61,
	0x6d, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05,
	0x73, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x22, 0x52, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x05, 0x6d, 0x65, 0x74,
	0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x05, 0x6d, 0x65, 0x74, 0x61, 0x73, 0x22, 0x94, 0x05, 0x0a, 0x10, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a,
	0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x62, 0x79, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73, 0x12, 0x42, 0x0a,
	0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x12, 0x4d, 0x0a, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x48, 0x02, 0x52, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01,
	0x12, 0x3f, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x4b, 0x65,
	0x79, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x5f, 0x63, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x11, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xb0,
	0x01, 0x0a, 0x11, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x44, 0x65, 0x66, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x49, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x53,
	0x79, 0x6e, 0x63, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11,
	0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x22, 0xed, 0x03, 0x0a, 0x11, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49,
	0x64, 0x12, 0x3c, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x63, 0x6f,
	0x70, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12,
	0x49, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x09, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73,
	0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x4d, 0x0a, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x48, 0x01, 0x52, 0x09, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73,
	0x63, 0x65, 0x6e, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0x2d, 0x0a, 0x12, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64,
	0x32, 0xa9, 0x09, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x7a, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x80,
	0x01, 0x0a, 0x12, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x61, 0x76,
	0x6f, 0x72, 0x69, 0x74, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x61, 0x76,
	0x6f, 0x72, 0x69, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x72, 0x6b, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x70, 0x0a, 0x19, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x61, 0x76, 0x65,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x7b, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x77, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7a, 0x0a, 0x10, 0x45, 0x78, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x71, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6b, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x61, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x09, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x6b, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x86, 0x01, 0x0a,
	0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_reporting_v2_reports_service_proto_rawDescOnce sync.Once
	file_moego_service_reporting_v2_reports_service_proto_rawDescData = file_moego_service_reporting_v2_reports_service_proto_rawDesc
)

func file_moego_service_reporting_v2_reports_service_proto_rawDescGZIP() []byte {
	file_moego_service_reporting_v2_reports_service_proto_rawDescOnce.Do(func() {
		file_moego_service_reporting_v2_reports_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_reporting_v2_reports_service_proto_rawDescData)
	})
	return file_moego_service_reporting_v2_reports_service_proto_rawDescData
}

var file_moego_service_reporting_v2_reports_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_service_reporting_v2_reports_service_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_moego_service_reporting_v2_reports_service_proto_goTypes = []interface{}{
	(MarkReportFavoriteParams_Action)(0),    // 0: moego.service.reporting.v2.MarkReportFavoriteParams.Action
	(*QueryReportPagesParams)(nil),          // 1: moego.service.reporting.v2.QueryReportPagesParams
	(*QueryReportPagesResult)(nil),          // 2: moego.service.reporting.v2.QueryReportPagesResult
	(*MarkReportFavoriteParams)(nil),        // 3: moego.service.reporting.v2.MarkReportFavoriteParams
	(*MarkReportFavoriteResult)(nil),        // 4: moego.service.reporting.v2.MarkReportFavoriteResult
	(*SaveReportCustomizeConfigParams)(nil), // 5: moego.service.reporting.v2.SaveReportCustomizeConfigParams
	(*QueryReportMetasParams)(nil),          // 6: moego.service.reporting.v2.QueryReportMetasParams
	(*QueryReportsMetasResult)(nil),         // 7: moego.service.reporting.v2.QueryReportsMetasResult
	(*FetchReportDataParams)(nil),           // 8: moego.service.reporting.v2.FetchReportDataParams
	(*FetchReportDataResult)(nil),           // 9: moego.service.reporting.v2.FetchReportDataResult
	(*ExportReportDataParams)(nil),          // 10: moego.service.reporting.v2.ExportReportDataParams
	(*ExportReportDataResult)(nil),          // 11: moego.service.reporting.v2.ExportReportDataResult
	(*QueryPageMetaRequest)(nil),            // 12: moego.service.reporting.v2.QueryPageMetaRequest
	(*QueryPageMetaResponse)(nil),           // 13: moego.service.reporting.v2.QueryPageMetaResponse
	(*QueryMetasRequest)(nil),               // 14: moego.service.reporting.v2.QueryMetasRequest
	(*QueryMetasResponse)(nil),              // 15: moego.service.reporting.v2.QueryMetasResponse
	(*FetchDataRequest)(nil),                // 16: moego.service.reporting.v2.FetchDataRequest
	(*FetchDataResponse)(nil),               // 17: moego.service.reporting.v2.FetchDataResponse
	(*ExportDataRequest)(nil),               // 18: moego.service.reporting.v2.ExportDataRequest
	(*ExportDataResponse)(nil),              // 19: moego.service.reporting.v2.ExportDataResponse
	(v2.ReportPage_Tab)(0),                  // 20: moego.models.reporting.v2.ReportPage.Tab
	(*v2.TokenInfo)(nil),                    // 21: moego.models.reporting.v2.TokenInfo
	(v2.ReportingScene)(0),                  // 22: moego.models.reporting.v2.ReportingScene
	(*v2.ReportPage)(nil),                   // 23: moego.models.reporting.v2.ReportPage
	(*timestamppb.Timestamp)(nil),           // 24: google.protobuf.Timestamp
	(*v2.TableCustomizedConfig)(nil),        // 25: moego.models.reporting.v2.TableCustomizedConfig
	(*v2.TableMeta)(nil),                    // 26: moego.models.reporting.v2.TableMeta
	(*interval.Interval)(nil),               // 27: google.type.Interval
	(*v2.FilterRequest)(nil),                // 28: moego.models.reporting.v2.FilterRequest
	(*v21.PaginationRequest)(nil),           // 29: moego.utils.v2.PaginationRequest
	(*v21.OrderBy)(nil),                     // 30: moego.utils.v2.OrderBy
	(calendarperiod.CalendarPeriod)(0),      // 31: google.type.CalendarPeriod
	(*v2.FilterRequestGroup)(nil),           // 32: moego.models.reporting.v2.FilterRequestGroup
	(*v2.TableData)(nil),                    // 33: moego.models.reporting.v2.TableData
	(*v21.PaginationResponse)(nil),          // 34: moego.utils.v2.PaginationResponse
	(v2.InsightsTab)(0),                     // 35: moego.models.reporting.v2.InsightsTab
	(*v2.PageMetaDef)(nil),                  // 36: moego.models.reporting.v2.PageMetaDef
	(*v2.ScopeFilter)(nil),                  // 37: moego.models.reporting.v2.ScopeFilter
	(*v2.DiagramMeta)(nil),                  // 38: moego.models.reporting.v2.DiagramMeta
	(*v2.TimeFilter)(nil),                   // 39: moego.models.reporting.v2.TimeFilter
	(*v2.DimensionFilter)(nil),              // 40: moego.models.reporting.v2.DimensionFilter
	(*v2.FetchDataDef)(nil),                 // 41: moego.models.reporting.v2.FetchDataDef
	(*emptypb.Empty)(nil),                   // 42: google.protobuf.Empty
}
var file_moego_service_reporting_v2_reports_service_proto_depIdxs = []int32{
	20, // 0: moego.service.reporting.v2.QueryReportPagesParams.tabs:type_name -> moego.models.reporting.v2.ReportPage.Tab
	21, // 1: moego.service.reporting.v2.QueryReportPagesParams.token_info:type_name -> moego.models.reporting.v2.TokenInfo
	22, // 2: moego.service.reporting.v2.QueryReportPagesParams.reporting_type:type_name -> moego.models.reporting.v2.ReportingScene
	23, // 3: moego.service.reporting.v2.QueryReportPagesResult.pages:type_name -> moego.models.reporting.v2.ReportPage
	24, // 4: moego.service.reporting.v2.QueryReportPagesResult.last_synced_time:type_name -> google.protobuf.Timestamp
	0,  // 5: moego.service.reporting.v2.MarkReportFavoriteParams.action:type_name -> moego.service.reporting.v2.MarkReportFavoriteParams.Action
	21, // 6: moego.service.reporting.v2.MarkReportFavoriteParams.token_info:type_name -> moego.models.reporting.v2.TokenInfo
	22, // 7: moego.service.reporting.v2.MarkReportFavoriteParams.reporting_type:type_name -> moego.models.reporting.v2.ReportingScene
	25, // 8: moego.service.reporting.v2.SaveReportCustomizeConfigParams.customized_config:type_name -> moego.models.reporting.v2.TableCustomizedConfig
	21, // 9: moego.service.reporting.v2.SaveReportCustomizeConfigParams.token_info:type_name -> moego.models.reporting.v2.TokenInfo
	22, // 10: moego.service.reporting.v2.SaveReportCustomizeConfigParams.reporting_type:type_name -> moego.models.reporting.v2.ReportingScene
	21, // 11: moego.service.reporting.v2.QueryReportMetasParams.token_info:type_name -> moego.models.reporting.v2.TokenInfo
	22, // 12: moego.service.reporting.v2.QueryReportMetasParams.reporting_type:type_name -> moego.models.reporting.v2.ReportingScene
	26, // 13: moego.service.reporting.v2.QueryReportsMetasResult.report_metas:type_name -> moego.models.reporting.v2.TableMeta
	27, // 14: moego.service.reporting.v2.FetchReportDataParams.current_period:type_name -> google.type.Interval
	27, // 15: moego.service.reporting.v2.FetchReportDataParams.previous_period:type_name -> google.type.Interval
	28, // 16: moego.service.reporting.v2.FetchReportDataParams.filters:type_name -> moego.models.reporting.v2.FilterRequest
	29, // 17: moego.service.reporting.v2.FetchReportDataParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	30, // 18: moego.service.reporting.v2.FetchReportDataParams.order_bys:type_name -> moego.utils.v2.OrderBy
	21, // 19: moego.service.reporting.v2.FetchReportDataParams.token_info:type_name -> moego.models.reporting.v2.TokenInfo
	31, // 20: moego.service.reporting.v2.FetchReportDataParams.group_by_period:type_name -> google.type.CalendarPeriod
	32, // 21: moego.service.reporting.v2.FetchReportDataParams.filter_groups:type_name -> moego.models.reporting.v2.FilterRequestGroup
	22, // 22: moego.service.reporting.v2.FetchReportDataParams.reporting_type:type_name -> moego.models.reporting.v2.ReportingScene
	33, // 23: moego.service.reporting.v2.FetchReportDataResult.table_data:type_name -> moego.models.reporting.v2.TableData
	34, // 24: moego.service.reporting.v2.FetchReportDataResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	24, // 25: moego.service.reporting.v2.FetchReportDataResult.last_synced_time:type_name -> google.protobuf.Timestamp
	28, // 26: moego.service.reporting.v2.ExportReportDataParams.filters:type_name -> moego.models.reporting.v2.FilterRequest
	21, // 27: moego.service.reporting.v2.ExportReportDataParams.token_info:type_name -> moego.models.reporting.v2.TokenInfo
	27, // 28: moego.service.reporting.v2.ExportReportDataParams.current_period:type_name -> google.type.Interval
	27, // 29: moego.service.reporting.v2.ExportReportDataParams.previous_period:type_name -> google.type.Interval
	30, // 30: moego.service.reporting.v2.ExportReportDataParams.order_bys:type_name -> moego.utils.v2.OrderBy
	31, // 31: moego.service.reporting.v2.ExportReportDataParams.group_by_period:type_name -> google.type.CalendarPeriod
	22, // 32: moego.service.reporting.v2.ExportReportDataParams.reporting_type:type_name -> moego.models.reporting.v2.ReportingScene
	32, // 33: moego.service.reporting.v2.ExportReportDataParams.filter_groups:type_name -> moego.models.reporting.v2.FilterRequestGroup
	35, // 34: moego.service.reporting.v2.QueryPageMetaRequest.tabs:type_name -> moego.models.reporting.v2.InsightsTab
	22, // 35: moego.service.reporting.v2.QueryPageMetaRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	36, // 36: moego.service.reporting.v2.QueryPageMetaResponse.pages:type_name -> moego.models.reporting.v2.PageMetaDef
	24, // 37: moego.service.reporting.v2.QueryPageMetaResponse.last_synced_time:type_name -> google.protobuf.Timestamp
	22, // 38: moego.service.reporting.v2.QueryMetasRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	37, // 39: moego.service.reporting.v2.QueryMetasRequest.scope:type_name -> moego.models.reporting.v2.ScopeFilter
	38, // 40: moego.service.reporting.v2.QueryMetasResponse.metas:type_name -> moego.models.reporting.v2.DiagramMeta
	37, // 41: moego.service.reporting.v2.FetchDataRequest.scope:type_name -> moego.models.reporting.v2.ScopeFilter
	39, // 42: moego.service.reporting.v2.FetchDataRequest.time_range:type_name -> moego.models.reporting.v2.TimeFilter
	29, // 43: moego.service.reporting.v2.FetchDataRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	30, // 44: moego.service.reporting.v2.FetchDataRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	28, // 45: moego.service.reporting.v2.FetchDataRequest.filters:type_name -> moego.models.reporting.v2.FilterRequest
	40, // 46: moego.service.reporting.v2.FetchDataRequest.dimension:type_name -> moego.models.reporting.v2.DimensionFilter
	22, // 47: moego.service.reporting.v2.FetchDataRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	41, // 48: moego.service.reporting.v2.FetchDataResponse.data:type_name -> moego.models.reporting.v2.FetchDataDef
	24, // 49: moego.service.reporting.v2.FetchDataResponse.last_synced_time:type_name -> google.protobuf.Timestamp
	37, // 50: moego.service.reporting.v2.ExportDataRequest.scope:type_name -> moego.models.reporting.v2.ScopeFilter
	39, // 51: moego.service.reporting.v2.ExportDataRequest.time_range:type_name -> moego.models.reporting.v2.TimeFilter
	30, // 52: moego.service.reporting.v2.ExportDataRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	28, // 53: moego.service.reporting.v2.ExportDataRequest.filters:type_name -> moego.models.reporting.v2.FilterRequest
	40, // 54: moego.service.reporting.v2.ExportDataRequest.dimension:type_name -> moego.models.reporting.v2.DimensionFilter
	22, // 55: moego.service.reporting.v2.ExportDataRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	1,  // 56: moego.service.reporting.v2.ReportService.QueryReportPages:input_type -> moego.service.reporting.v2.QueryReportPagesParams
	3,  // 57: moego.service.reporting.v2.ReportService.MarkReportFavorite:input_type -> moego.service.reporting.v2.MarkReportFavoriteParams
	5,  // 58: moego.service.reporting.v2.ReportService.SaveReportCustomizeConfig:input_type -> moego.service.reporting.v2.SaveReportCustomizeConfigParams
	6,  // 59: moego.service.reporting.v2.ReportService.QueryReportMetas:input_type -> moego.service.reporting.v2.QueryReportMetasParams
	8,  // 60: moego.service.reporting.v2.ReportService.FetchReportData:input_type -> moego.service.reporting.v2.FetchReportDataParams
	10, // 61: moego.service.reporting.v2.ReportService.ExportReportData:input_type -> moego.service.reporting.v2.ExportReportDataParams
	12, // 62: moego.service.reporting.v2.ReportService.QueryPages:input_type -> moego.service.reporting.v2.QueryPageMetaRequest
	14, // 63: moego.service.reporting.v2.ReportService.QueryMetas:input_type -> moego.service.reporting.v2.QueryMetasRequest
	16, // 64: moego.service.reporting.v2.ReportService.FetchData:input_type -> moego.service.reporting.v2.FetchDataRequest
	18, // 65: moego.service.reporting.v2.ReportService.ExportData:input_type -> moego.service.reporting.v2.ExportDataRequest
	2,  // 66: moego.service.reporting.v2.ReportService.QueryReportPages:output_type -> moego.service.reporting.v2.QueryReportPagesResult
	4,  // 67: moego.service.reporting.v2.ReportService.MarkReportFavorite:output_type -> moego.service.reporting.v2.MarkReportFavoriteResult
	42, // 68: moego.service.reporting.v2.ReportService.SaveReportCustomizeConfig:output_type -> google.protobuf.Empty
	7,  // 69: moego.service.reporting.v2.ReportService.QueryReportMetas:output_type -> moego.service.reporting.v2.QueryReportsMetasResult
	9,  // 70: moego.service.reporting.v2.ReportService.FetchReportData:output_type -> moego.service.reporting.v2.FetchReportDataResult
	11, // 71: moego.service.reporting.v2.ReportService.ExportReportData:output_type -> moego.service.reporting.v2.ExportReportDataResult
	13, // 72: moego.service.reporting.v2.ReportService.QueryPages:output_type -> moego.service.reporting.v2.QueryPageMetaResponse
	15, // 73: moego.service.reporting.v2.ReportService.QueryMetas:output_type -> moego.service.reporting.v2.QueryMetasResponse
	17, // 74: moego.service.reporting.v2.ReportService.FetchData:output_type -> moego.service.reporting.v2.FetchDataResponse
	19, // 75: moego.service.reporting.v2.ReportService.ExportData:output_type -> moego.service.reporting.v2.ExportDataResponse
	66, // [66:76] is the sub-list for method output_type
	56, // [56:66] is the sub-list for method input_type
	56, // [56:56] is the sub-list for extension type_name
	56, // [56:56] is the sub-list for extension extendee
	0,  // [0:56] is the sub-list for field type_name
}

func init() { file_moego_service_reporting_v2_reports_service_proto_init() }
func file_moego_service_reporting_v2_reports_service_proto_init() {
	if File_moego_service_reporting_v2_reports_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReportPagesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReportPagesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkReportFavoriteParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MarkReportFavoriteResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveReportCustomizeConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReportMetasParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryReportsMetasResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchReportDataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchReportDataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportReportDataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportReportDataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPageMetaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPageMetaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryMetasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryMetasResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_reports_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExportDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_reporting_v2_reports_service_proto_msgTypes[7].OneofWrappers = []interface{}{}
	file_moego_service_reporting_v2_reports_service_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_service_reporting_v2_reports_service_proto_msgTypes[15].OneofWrappers = []interface{}{}
	file_moego_service_reporting_v2_reports_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_service_reporting_v2_reports_service_proto_msgTypes[17].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_reporting_v2_reports_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_reporting_v2_reports_service_proto_goTypes,
		DependencyIndexes: file_moego_service_reporting_v2_reports_service_proto_depIdxs,
		EnumInfos:         file_moego_service_reporting_v2_reports_service_proto_enumTypes,
		MessageInfos:      file_moego_service_reporting_v2_reports_service_proto_msgTypes,
	}.Build()
	File_moego_service_reporting_v2_reports_service_proto = out.File
	file_moego_service_reporting_v2_reports_service_proto_rawDesc = nil
	file_moego_service_reporting_v2_reports_service_proto_goTypes = nil
	file_moego_service_reporting_v2_reports_service_proto_depIdxs = nil
}
