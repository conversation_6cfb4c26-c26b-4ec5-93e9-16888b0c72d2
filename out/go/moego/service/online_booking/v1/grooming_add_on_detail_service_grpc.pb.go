// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/grooming_add_on_detail_service.proto

package onlinebookingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GroomingAddOnDetailServiceClient is the client API for GroomingAddOnDetailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroomingAddOnDetailServiceClient interface {
	// Create a record, return inserted id.
	CreateGroomingAddOnDetail(ctx context.Context, in *CreateGroomingAddOnDetailRequest, opts ...grpc.CallOption) (*wrapperspb.Int64Value, error)
	// Get a record by id, not include deleted record.
	GetGroomingAddOnDetail(ctx context.Context, in *wrapperspb.Int64Value, opts ...grpc.CallOption) (*GetGroomingAddOnDetailResponse, error)
	// Update a record by id, return updated rows.
	UpdateGroomingAddOnDetail(ctx context.Context, in *UpdateGroomingAddOnDetailRequest, opts ...grpc.CallOption) (*wrapperspb.Int32Value, error)
	// Delete a record by id, return deleted rows.
	DeleteGroomingAddOnDetail(ctx context.Context, in *wrapperspb.Int64Value, opts ...grpc.CallOption) (*wrapperspb.Int32Value, error)
}

type groomingAddOnDetailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewGroomingAddOnDetailServiceClient(cc grpc.ClientConnInterface) GroomingAddOnDetailServiceClient {
	return &groomingAddOnDetailServiceClient{cc}
}

func (c *groomingAddOnDetailServiceClient) CreateGroomingAddOnDetail(ctx context.Context, in *CreateGroomingAddOnDetailRequest, opts ...grpc.CallOption) (*wrapperspb.Int64Value, error) {
	out := new(wrapperspb.Int64Value)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.GroomingAddOnDetailService/CreateGroomingAddOnDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groomingAddOnDetailServiceClient) GetGroomingAddOnDetail(ctx context.Context, in *wrapperspb.Int64Value, opts ...grpc.CallOption) (*GetGroomingAddOnDetailResponse, error) {
	out := new(GetGroomingAddOnDetailResponse)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.GroomingAddOnDetailService/GetGroomingAddOnDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groomingAddOnDetailServiceClient) UpdateGroomingAddOnDetail(ctx context.Context, in *UpdateGroomingAddOnDetailRequest, opts ...grpc.CallOption) (*wrapperspb.Int32Value, error) {
	out := new(wrapperspb.Int32Value)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.GroomingAddOnDetailService/UpdateGroomingAddOnDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groomingAddOnDetailServiceClient) DeleteGroomingAddOnDetail(ctx context.Context, in *wrapperspb.Int64Value, opts ...grpc.CallOption) (*wrapperspb.Int32Value, error) {
	out := new(wrapperspb.Int32Value)
	err := c.cc.Invoke(ctx, "/moego.service.online_booking.v1.GroomingAddOnDetailService/DeleteGroomingAddOnDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroomingAddOnDetailServiceServer is the server API for GroomingAddOnDetailService service.
// All implementations must embed UnimplementedGroomingAddOnDetailServiceServer
// for forward compatibility
type GroomingAddOnDetailServiceServer interface {
	// Create a record, return inserted id.
	CreateGroomingAddOnDetail(context.Context, *CreateGroomingAddOnDetailRequest) (*wrapperspb.Int64Value, error)
	// Get a record by id, not include deleted record.
	GetGroomingAddOnDetail(context.Context, *wrapperspb.Int64Value) (*GetGroomingAddOnDetailResponse, error)
	// Update a record by id, return updated rows.
	UpdateGroomingAddOnDetail(context.Context, *UpdateGroomingAddOnDetailRequest) (*wrapperspb.Int32Value, error)
	// Delete a record by id, return deleted rows.
	DeleteGroomingAddOnDetail(context.Context, *wrapperspb.Int64Value) (*wrapperspb.Int32Value, error)
	mustEmbedUnimplementedGroomingAddOnDetailServiceServer()
}

// UnimplementedGroomingAddOnDetailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedGroomingAddOnDetailServiceServer struct {
}

func (UnimplementedGroomingAddOnDetailServiceServer) CreateGroomingAddOnDetail(context.Context, *CreateGroomingAddOnDetailRequest) (*wrapperspb.Int64Value, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGroomingAddOnDetail not implemented")
}
func (UnimplementedGroomingAddOnDetailServiceServer) GetGroomingAddOnDetail(context.Context, *wrapperspb.Int64Value) (*GetGroomingAddOnDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroomingAddOnDetail not implemented")
}
func (UnimplementedGroomingAddOnDetailServiceServer) UpdateGroomingAddOnDetail(context.Context, *UpdateGroomingAddOnDetailRequest) (*wrapperspb.Int32Value, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGroomingAddOnDetail not implemented")
}
func (UnimplementedGroomingAddOnDetailServiceServer) DeleteGroomingAddOnDetail(context.Context, *wrapperspb.Int64Value) (*wrapperspb.Int32Value, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroomingAddOnDetail not implemented")
}
func (UnimplementedGroomingAddOnDetailServiceServer) mustEmbedUnimplementedGroomingAddOnDetailServiceServer() {
}

// UnsafeGroomingAddOnDetailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroomingAddOnDetailServiceServer will
// result in compilation errors.
type UnsafeGroomingAddOnDetailServiceServer interface {
	mustEmbedUnimplementedGroomingAddOnDetailServiceServer()
}

func RegisterGroomingAddOnDetailServiceServer(s grpc.ServiceRegistrar, srv GroomingAddOnDetailServiceServer) {
	s.RegisterService(&GroomingAddOnDetailService_ServiceDesc, srv)
}

func _GroomingAddOnDetailService_CreateGroomingAddOnDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroomingAddOnDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingAddOnDetailServiceServer).CreateGroomingAddOnDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.GroomingAddOnDetailService/CreateGroomingAddOnDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingAddOnDetailServiceServer).CreateGroomingAddOnDetail(ctx, req.(*CreateGroomingAddOnDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroomingAddOnDetailService_GetGroomingAddOnDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrapperspb.Int64Value)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingAddOnDetailServiceServer).GetGroomingAddOnDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.GroomingAddOnDetailService/GetGroomingAddOnDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingAddOnDetailServiceServer).GetGroomingAddOnDetail(ctx, req.(*wrapperspb.Int64Value))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroomingAddOnDetailService_UpdateGroomingAddOnDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroomingAddOnDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingAddOnDetailServiceServer).UpdateGroomingAddOnDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.GroomingAddOnDetailService/UpdateGroomingAddOnDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingAddOnDetailServiceServer).UpdateGroomingAddOnDetail(ctx, req.(*UpdateGroomingAddOnDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroomingAddOnDetailService_DeleteGroomingAddOnDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrapperspb.Int64Value)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroomingAddOnDetailServiceServer).DeleteGroomingAddOnDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.online_booking.v1.GroomingAddOnDetailService/DeleteGroomingAddOnDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroomingAddOnDetailServiceServer).DeleteGroomingAddOnDetail(ctx, req.(*wrapperspb.Int64Value))
	}
	return interceptor(ctx, in, info, handler)
}

// GroomingAddOnDetailService_ServiceDesc is the grpc.ServiceDesc for GroomingAddOnDetailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroomingAddOnDetailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.GroomingAddOnDetailService",
	HandlerType: (*GroomingAddOnDetailServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGroomingAddOnDetail",
			Handler:    _GroomingAddOnDetailService_CreateGroomingAddOnDetail_Handler,
		},
		{
			MethodName: "GetGroomingAddOnDetail",
			Handler:    _GroomingAddOnDetailService_GetGroomingAddOnDetail_Handler,
		},
		{
			MethodName: "UpdateGroomingAddOnDetail",
			Handler:    _GroomingAddOnDetailService_UpdateGroomingAddOnDetail_Handler,
		},
		{
			MethodName: "DeleteGroomingAddOnDetail",
			Handler:    _GroomingAddOnDetailService_DeleteGroomingAddOnDetail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/online_booking/v1/grooming_add_on_detail_service.proto",
}
