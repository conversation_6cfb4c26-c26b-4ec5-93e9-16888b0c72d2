// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/online_booking/v1/evaluation_test_detail_service.proto

package onlinebookingsvcpb

import (
	grpc "google.golang.org/grpc"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EvaluationTestDetailServiceClient is the client API for EvaluationTestDetailService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EvaluationTestDetailServiceClient interface {
}

type evaluationTestDetailServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEvaluationTestDetailServiceClient(cc grpc.ClientConnInterface) EvaluationTestDetailServiceClient {
	return &evaluationTestDetailServiceClient{cc}
}

// EvaluationTestDetailServiceServer is the server API for EvaluationTestDetailService service.
// All implementations must embed UnimplementedEvaluationTestDetailServiceServer
// for forward compatibility
type EvaluationTestDetailServiceServer interface {
	mustEmbedUnimplementedEvaluationTestDetailServiceServer()
}

// UnimplementedEvaluationTestDetailServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEvaluationTestDetailServiceServer struct {
}

func (UnimplementedEvaluationTestDetailServiceServer) mustEmbedUnimplementedEvaluationTestDetailServiceServer() {
}

// UnsafeEvaluationTestDetailServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EvaluationTestDetailServiceServer will
// result in compilation errors.
type UnsafeEvaluationTestDetailServiceServer interface {
	mustEmbedUnimplementedEvaluationTestDetailServiceServer()
}

func RegisterEvaluationTestDetailServiceServer(s grpc.ServiceRegistrar, srv EvaluationTestDetailServiceServer) {
	s.RegisterService(&EvaluationTestDetailService_ServiceDesc, srv)
}

// EvaluationTestDetailService_ServiceDesc is the grpc.ServiceDesc for EvaluationTestDetailService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EvaluationTestDetailService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.online_booking.v1.EvaluationTestDetailService",
	HandlerType: (*EvaluationTestDetailServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams:     []grpc.StreamDesc{},
	Metadata:    "moego/service/online_booking/v1/evaluation_test_detail_service.proto",
}
