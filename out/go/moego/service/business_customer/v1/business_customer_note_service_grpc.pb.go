// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_customer_note_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerNoteServiceClient is the client API for BusinessCustomerNoteService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerNoteServiceClient interface {
	// Create a customer note
	// A customer can at most have 200 notes
	CreateCustomerNote(ctx context.Context, in *CreateCustomerNoteRequest, opts ...grpc.CallOption) (*CreateCustomerNoteResponse, error)
	// Update a customer note
	UpdateCustomerNote(ctx context.Context, in *UpdateCustomerNoteRequest, opts ...grpc.CallOption) (*UpdateCustomerNoteResponse, error)
	// Delete a customer note
	DeleteCustomerNote(ctx context.Context, in *DeleteCustomerNoteRequest, opts ...grpc.CallOption) (*DeleteCustomerNoteResponse, error)
	// Get a customer note by id
	GetCustomerNote(ctx context.Context, in *GetCustomerNoteRequest, opts ...grpc.CallOption) (*GetCustomerNoteResponse, error)
	// List customer notes of a customer
	ListCustomerNote(ctx context.Context, in *ListCustomerNoteRequest, opts ...grpc.CallOption) (*ListCustomerNoteResponse, error)
	// Batch list customer note of multiple customers
	// Each customer returns a maximum of 50 latest notes
	BatchListCustomerNote(ctx context.Context, in *BatchListCustomerNoteRequest, opts ...grpc.CallOption) (*BatchListCustomerNoteResponse, error)
}

type businessCustomerNoteServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerNoteServiceClient(cc grpc.ClientConnInterface) BusinessCustomerNoteServiceClient {
	return &businessCustomerNoteServiceClient{cc}
}

func (c *businessCustomerNoteServiceClient) CreateCustomerNote(ctx context.Context, in *CreateCustomerNoteRequest, opts ...grpc.CallOption) (*CreateCustomerNoteResponse, error) {
	out := new(CreateCustomerNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerNoteService/CreateCustomerNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerNoteServiceClient) UpdateCustomerNote(ctx context.Context, in *UpdateCustomerNoteRequest, opts ...grpc.CallOption) (*UpdateCustomerNoteResponse, error) {
	out := new(UpdateCustomerNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerNoteService/UpdateCustomerNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerNoteServiceClient) DeleteCustomerNote(ctx context.Context, in *DeleteCustomerNoteRequest, opts ...grpc.CallOption) (*DeleteCustomerNoteResponse, error) {
	out := new(DeleteCustomerNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerNoteService/DeleteCustomerNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerNoteServiceClient) GetCustomerNote(ctx context.Context, in *GetCustomerNoteRequest, opts ...grpc.CallOption) (*GetCustomerNoteResponse, error) {
	out := new(GetCustomerNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerNoteService/GetCustomerNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerNoteServiceClient) ListCustomerNote(ctx context.Context, in *ListCustomerNoteRequest, opts ...grpc.CallOption) (*ListCustomerNoteResponse, error) {
	out := new(ListCustomerNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerNoteService/ListCustomerNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerNoteServiceClient) BatchListCustomerNote(ctx context.Context, in *BatchListCustomerNoteRequest, opts ...grpc.CallOption) (*BatchListCustomerNoteResponse, error) {
	out := new(BatchListCustomerNoteResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerNoteService/BatchListCustomerNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerNoteServiceServer is the server API for BusinessCustomerNoteService service.
// All implementations must embed UnimplementedBusinessCustomerNoteServiceServer
// for forward compatibility
type BusinessCustomerNoteServiceServer interface {
	// Create a customer note
	// A customer can at most have 200 notes
	CreateCustomerNote(context.Context, *CreateCustomerNoteRequest) (*CreateCustomerNoteResponse, error)
	// Update a customer note
	UpdateCustomerNote(context.Context, *UpdateCustomerNoteRequest) (*UpdateCustomerNoteResponse, error)
	// Delete a customer note
	DeleteCustomerNote(context.Context, *DeleteCustomerNoteRequest) (*DeleteCustomerNoteResponse, error)
	// Get a customer note by id
	GetCustomerNote(context.Context, *GetCustomerNoteRequest) (*GetCustomerNoteResponse, error)
	// List customer notes of a customer
	ListCustomerNote(context.Context, *ListCustomerNoteRequest) (*ListCustomerNoteResponse, error)
	// Batch list customer note of multiple customers
	// Each customer returns a maximum of 50 latest notes
	BatchListCustomerNote(context.Context, *BatchListCustomerNoteRequest) (*BatchListCustomerNoteResponse, error)
	mustEmbedUnimplementedBusinessCustomerNoteServiceServer()
}

// UnimplementedBusinessCustomerNoteServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerNoteServiceServer struct {
}

func (UnimplementedBusinessCustomerNoteServiceServer) CreateCustomerNote(context.Context, *CreateCustomerNoteRequest) (*CreateCustomerNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerNote not implemented")
}
func (UnimplementedBusinessCustomerNoteServiceServer) UpdateCustomerNote(context.Context, *UpdateCustomerNoteRequest) (*UpdateCustomerNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerNote not implemented")
}
func (UnimplementedBusinessCustomerNoteServiceServer) DeleteCustomerNote(context.Context, *DeleteCustomerNoteRequest) (*DeleteCustomerNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomerNote not implemented")
}
func (UnimplementedBusinessCustomerNoteServiceServer) GetCustomerNote(context.Context, *GetCustomerNoteRequest) (*GetCustomerNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerNote not implemented")
}
func (UnimplementedBusinessCustomerNoteServiceServer) ListCustomerNote(context.Context, *ListCustomerNoteRequest) (*ListCustomerNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerNote not implemented")
}
func (UnimplementedBusinessCustomerNoteServiceServer) BatchListCustomerNote(context.Context, *BatchListCustomerNoteRequest) (*BatchListCustomerNoteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchListCustomerNote not implemented")
}
func (UnimplementedBusinessCustomerNoteServiceServer) mustEmbedUnimplementedBusinessCustomerNoteServiceServer() {
}

// UnsafeBusinessCustomerNoteServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerNoteServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerNoteServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerNoteServiceServer()
}

func RegisterBusinessCustomerNoteServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerNoteServiceServer) {
	s.RegisterService(&BusinessCustomerNoteService_ServiceDesc, srv)
}

func _BusinessCustomerNoteService_CreateCustomerNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerNoteServiceServer).CreateCustomerNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerNoteService/CreateCustomerNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerNoteServiceServer).CreateCustomerNote(ctx, req.(*CreateCustomerNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerNoteService_UpdateCustomerNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerNoteServiceServer).UpdateCustomerNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerNoteService/UpdateCustomerNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerNoteServiceServer).UpdateCustomerNote(ctx, req.(*UpdateCustomerNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerNoteService_DeleteCustomerNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomerNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerNoteServiceServer).DeleteCustomerNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerNoteService/DeleteCustomerNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerNoteServiceServer).DeleteCustomerNote(ctx, req.(*DeleteCustomerNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerNoteService_GetCustomerNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerNoteServiceServer).GetCustomerNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerNoteService/GetCustomerNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerNoteServiceServer).GetCustomerNote(ctx, req.(*GetCustomerNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerNoteService_ListCustomerNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerNoteServiceServer).ListCustomerNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerNoteService/ListCustomerNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerNoteServiceServer).ListCustomerNote(ctx, req.(*ListCustomerNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerNoteService_BatchListCustomerNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchListCustomerNoteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerNoteServiceServer).BatchListCustomerNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerNoteService/BatchListCustomerNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerNoteServiceServer).BatchListCustomerNote(ctx, req.(*BatchListCustomerNoteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerNoteService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerNoteService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerNoteService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessCustomerNoteService",
	HandlerType: (*BusinessCustomerNoteServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCustomerNote",
			Handler:    _BusinessCustomerNoteService_CreateCustomerNote_Handler,
		},
		{
			MethodName: "UpdateCustomerNote",
			Handler:    _BusinessCustomerNoteService_UpdateCustomerNote_Handler,
		},
		{
			MethodName: "DeleteCustomerNote",
			Handler:    _BusinessCustomerNoteService_DeleteCustomerNote_Handler,
		},
		{
			MethodName: "GetCustomerNote",
			Handler:    _BusinessCustomerNoteService_GetCustomerNote_Handler,
		},
		{
			MethodName: "ListCustomerNote",
			Handler:    _BusinessCustomerNoteService_ListCustomerNote_Handler,
		},
		{
			MethodName: "BatchListCustomerNote",
			Handler:    _BusinessCustomerNoteService_BatchListCustomerNote_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_customer_note_service.proto",
}
