// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_customer_merge_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerMergeServiceClient is the client API for BusinessCustomerMergeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerMergeServiceClient interface {
	// list duplicate customer groups
	ListDuplicateCustomerGroups(ctx context.Context, in *ListDuplicateCustomerGroupsRequest, opts ...grpc.CallOption) (*ListDuplicateCustomerGroupsResponse, error)
	// check if the given customers are duplicate
	CheckCustomerDuplication(ctx context.Context, in *CheckCustomerDuplicationRequest, opts ...grpc.CallOption) (*CheckCustomerDuplicationResponse, error)
	// preview pet merge relation
	PreviewPetMergeRelation(ctx context.Context, in *PreviewPetMergeRelationRequest, opts ...grpc.CallOption) (*PreviewPetMergeRelationResponse, error)
	// Merge customers
	MergeCustomers(ctx context.Context, in *MergeCustomersRequest, opts ...grpc.CallOption) (*MergeCustomersResponse, error)
	// batch check customer merge status
	BatchCheckCustomerMergeStatus(ctx context.Context, in *BatchCheckCustomerMergeStatusRequest, opts ...grpc.CallOption) (*BatchCheckCustomerMergeStatusResponse, error)
}

type businessCustomerMergeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerMergeServiceClient(cc grpc.ClientConnInterface) BusinessCustomerMergeServiceClient {
	return &businessCustomerMergeServiceClient{cc}
}

func (c *businessCustomerMergeServiceClient) ListDuplicateCustomerGroups(ctx context.Context, in *ListDuplicateCustomerGroupsRequest, opts ...grpc.CallOption) (*ListDuplicateCustomerGroupsResponse, error) {
	out := new(ListDuplicateCustomerGroupsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerMergeService/ListDuplicateCustomerGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerMergeServiceClient) CheckCustomerDuplication(ctx context.Context, in *CheckCustomerDuplicationRequest, opts ...grpc.CallOption) (*CheckCustomerDuplicationResponse, error) {
	out := new(CheckCustomerDuplicationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerMergeService/CheckCustomerDuplication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerMergeServiceClient) PreviewPetMergeRelation(ctx context.Context, in *PreviewPetMergeRelationRequest, opts ...grpc.CallOption) (*PreviewPetMergeRelationResponse, error) {
	out := new(PreviewPetMergeRelationResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerMergeService/PreviewPetMergeRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerMergeServiceClient) MergeCustomers(ctx context.Context, in *MergeCustomersRequest, opts ...grpc.CallOption) (*MergeCustomersResponse, error) {
	out := new(MergeCustomersResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerMergeService/MergeCustomers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerMergeServiceClient) BatchCheckCustomerMergeStatus(ctx context.Context, in *BatchCheckCustomerMergeStatusRequest, opts ...grpc.CallOption) (*BatchCheckCustomerMergeStatusResponse, error) {
	out := new(BatchCheckCustomerMergeStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessCustomerMergeService/BatchCheckCustomerMergeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerMergeServiceServer is the server API for BusinessCustomerMergeService service.
// All implementations must embed UnimplementedBusinessCustomerMergeServiceServer
// for forward compatibility
type BusinessCustomerMergeServiceServer interface {
	// list duplicate customer groups
	ListDuplicateCustomerGroups(context.Context, *ListDuplicateCustomerGroupsRequest) (*ListDuplicateCustomerGroupsResponse, error)
	// check if the given customers are duplicate
	CheckCustomerDuplication(context.Context, *CheckCustomerDuplicationRequest) (*CheckCustomerDuplicationResponse, error)
	// preview pet merge relation
	PreviewPetMergeRelation(context.Context, *PreviewPetMergeRelationRequest) (*PreviewPetMergeRelationResponse, error)
	// Merge customers
	MergeCustomers(context.Context, *MergeCustomersRequest) (*MergeCustomersResponse, error)
	// batch check customer merge status
	BatchCheckCustomerMergeStatus(context.Context, *BatchCheckCustomerMergeStatusRequest) (*BatchCheckCustomerMergeStatusResponse, error)
	mustEmbedUnimplementedBusinessCustomerMergeServiceServer()
}

// UnimplementedBusinessCustomerMergeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerMergeServiceServer struct {
}

func (UnimplementedBusinessCustomerMergeServiceServer) ListDuplicateCustomerGroups(context.Context, *ListDuplicateCustomerGroupsRequest) (*ListDuplicateCustomerGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDuplicateCustomerGroups not implemented")
}
func (UnimplementedBusinessCustomerMergeServiceServer) CheckCustomerDuplication(context.Context, *CheckCustomerDuplicationRequest) (*CheckCustomerDuplicationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckCustomerDuplication not implemented")
}
func (UnimplementedBusinessCustomerMergeServiceServer) PreviewPetMergeRelation(context.Context, *PreviewPetMergeRelationRequest) (*PreviewPetMergeRelationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewPetMergeRelation not implemented")
}
func (UnimplementedBusinessCustomerMergeServiceServer) MergeCustomers(context.Context, *MergeCustomersRequest) (*MergeCustomersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MergeCustomers not implemented")
}
func (UnimplementedBusinessCustomerMergeServiceServer) BatchCheckCustomerMergeStatus(context.Context, *BatchCheckCustomerMergeStatusRequest) (*BatchCheckCustomerMergeStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCheckCustomerMergeStatus not implemented")
}
func (UnimplementedBusinessCustomerMergeServiceServer) mustEmbedUnimplementedBusinessCustomerMergeServiceServer() {
}

// UnsafeBusinessCustomerMergeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerMergeServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerMergeServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerMergeServiceServer()
}

func RegisterBusinessCustomerMergeServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerMergeServiceServer) {
	s.RegisterService(&BusinessCustomerMergeService_ServiceDesc, srv)
}

func _BusinessCustomerMergeService_ListDuplicateCustomerGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDuplicateCustomerGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerMergeServiceServer).ListDuplicateCustomerGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerMergeService/ListDuplicateCustomerGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerMergeServiceServer).ListDuplicateCustomerGroups(ctx, req.(*ListDuplicateCustomerGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerMergeService_CheckCustomerDuplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCustomerDuplicationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerMergeServiceServer).CheckCustomerDuplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerMergeService/CheckCustomerDuplication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerMergeServiceServer).CheckCustomerDuplication(ctx, req.(*CheckCustomerDuplicationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerMergeService_PreviewPetMergeRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreviewPetMergeRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerMergeServiceServer).PreviewPetMergeRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerMergeService/PreviewPetMergeRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerMergeServiceServer).PreviewPetMergeRelation(ctx, req.(*PreviewPetMergeRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerMergeService_MergeCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MergeCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerMergeServiceServer).MergeCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerMergeService/MergeCustomers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerMergeServiceServer).MergeCustomers(ctx, req.(*MergeCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerMergeService_BatchCheckCustomerMergeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCheckCustomerMergeStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerMergeServiceServer).BatchCheckCustomerMergeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessCustomerMergeService/BatchCheckCustomerMergeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerMergeServiceServer).BatchCheckCustomerMergeStatus(ctx, req.(*BatchCheckCustomerMergeStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerMergeService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerMergeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerMergeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessCustomerMergeService",
	HandlerType: (*BusinessCustomerMergeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListDuplicateCustomerGroups",
			Handler:    _BusinessCustomerMergeService_ListDuplicateCustomerGroups_Handler,
		},
		{
			MethodName: "CheckCustomerDuplication",
			Handler:    _BusinessCustomerMergeService_CheckCustomerDuplication_Handler,
		},
		{
			MethodName: "PreviewPetMergeRelation",
			Handler:    _BusinessCustomerMergeService_PreviewPetMergeRelation_Handler,
		},
		{
			MethodName: "MergeCustomers",
			Handler:    _BusinessCustomerMergeService_MergeCustomers_Handler,
		},
		{
			MethodName: "BatchCheckCustomerMergeStatus",
			Handler:    _BusinessCustomerMergeService_BatchCheckCustomerMergeStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_customer_merge_service.proto",
}
