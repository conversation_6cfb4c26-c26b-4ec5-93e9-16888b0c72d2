// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/marketing/v1/discount_code_service.proto

package marketingsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DiscountCodeServiceClient is the client API for DiscountCodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DiscountCodeServiceClient interface {
	// generate discount code
	GenerateDiscountCode(ctx context.Context, in *GenerateDiscountCodeInput, opts ...grpc.CallOption) (*GenerateDiscountCodeOutput, error)
	// check discount code
	CheckDiscountCode(ctx context.Context, in *CheckDiscountCodeInput, opts ...grpc.CallOption) (*CheckDiscountCodeOutput, error)
	// create discount code
	CreateDiscountCode(ctx context.Context, in *CreateDiscountCodeInput, opts ...grpc.CallOption) (*CreateDiscountCodeOutput, error)
	// edit discount code
	EditDiscountCode(ctx context.Context, in *EditDiscountCodeInput, opts ...grpc.CallOption) (*EditDiscountCodeOutput, error)
	// get discount code
	GetDiscountCode(ctx context.Context, in *GetDiscountCodeInput, opts ...grpc.CallOption) (*GetDiscountCodeOutput, error)
	// get discount code by code
	GetDiscountCodeByCode(ctx context.Context, in *GetDiscountCodeByCodeInput, opts ...grpc.CallOption) (*GetDiscountCodeByCodeOutput, error)
	// get discount code list
	GetDiscountCodeList(ctx context.Context, in *GetDiscountCodeListInput, opts ...grpc.CallOption) (*GetDiscountCodeListOutput, error)
	// get discount code log overview
	GetDiscountCodeLogOverview(ctx context.Context, in *GetDiscountCodeLogOverviewInput, opts ...grpc.CallOption) (*GetDiscountCodeLogOverviewOutput, error)
	// get discount code log list
	GetDiscountCodeLogList(ctx context.Context, in *GetDiscountCodeLogListInput, opts ...grpc.CallOption) (*GetDiscountCodeLogListOutput, error)
	// change status
	ChangeStatus(ctx context.Context, in *ChangeStatusInput, opts ...grpc.CallOption) (*ChangeStatusOutput, error)
	// check discount code valid for customer
	CheckDiscountCodeValidForCustomer(ctx context.Context, in *CheckDiscountCodeValidForCustomerInput, opts ...grpc.CallOption) (*CheckDiscountCodeValidForCustomerOutput, error)
	// get available discount code list
	GetAvailableDiscountList(ctx context.Context, in *GetAvailableDiscountListInput, opts ...grpc.CallOption) (*GetAvailableDiscountListOutput, error)
	// get available discount code list for existing invoice
	GetAvailableDiscountListForExistingInvoice(ctx context.Context, in *GetAvailableDiscountListForExistingInvoiceInput, opts ...grpc.CallOption) (*GetAvailableDiscountListForExistingInvoiceOutput, error)
	// auto apply discount code
	AutoApplyDiscountCode(ctx context.Context, in *AutoApplyDiscountCodeInput, opts ...grpc.CallOption) (*AutoApplyDiscountCodeOutput, error)
	// delete discount code log
	DeleteDiscountCodeLog(ctx context.Context, in *DeleteDiscountCodeLogInput, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// use discount code
	UseDiscountCode(ctx context.Context, in *UseDiscountCodeInput, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// refresh discount code status
	RefreshDiscountCodeStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// get business discount code config
	// Deprecated: replace to GetDiscountCodeConfig
	GetBusinessDiscountCodeConfig(ctx context.Context, in *GetBusinessDiscountCodeConfigInput, opts ...grpc.CallOption) (*GetBusinessDiscountCodeConfigOutput, error)
	// get business discount code config
	GetDiscountCodeConfig(ctx context.Context, in *GetDiscountCodeConfigInput, opts ...grpc.CallOption) (*GetDiscountCodeConfigOutput, error)
	// migrate discount code
	MigrateDiscountCode(ctx context.Context, in *MigrateDiscountCodeInput, opts ...grpc.CallOption) (*MigrateDiscountCodeInputOutput, error)
}

type discountCodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDiscountCodeServiceClient(cc grpc.ClientConnInterface) DiscountCodeServiceClient {
	return &discountCodeServiceClient{cc}
}

func (c *discountCodeServiceClient) GenerateDiscountCode(ctx context.Context, in *GenerateDiscountCodeInput, opts ...grpc.CallOption) (*GenerateDiscountCodeOutput, error) {
	out := new(GenerateDiscountCodeOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GenerateDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) CheckDiscountCode(ctx context.Context, in *CheckDiscountCodeInput, opts ...grpc.CallOption) (*CheckDiscountCodeOutput, error) {
	out := new(CheckDiscountCodeOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/CheckDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) CreateDiscountCode(ctx context.Context, in *CreateDiscountCodeInput, opts ...grpc.CallOption) (*CreateDiscountCodeOutput, error) {
	out := new(CreateDiscountCodeOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/CreateDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) EditDiscountCode(ctx context.Context, in *EditDiscountCodeInput, opts ...grpc.CallOption) (*EditDiscountCodeOutput, error) {
	out := new(EditDiscountCodeOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/EditDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetDiscountCode(ctx context.Context, in *GetDiscountCodeInput, opts ...grpc.CallOption) (*GetDiscountCodeOutput, error) {
	out := new(GetDiscountCodeOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetDiscountCodeByCode(ctx context.Context, in *GetDiscountCodeByCodeInput, opts ...grpc.CallOption) (*GetDiscountCodeByCodeOutput, error) {
	out := new(GetDiscountCodeByCodeOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeByCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetDiscountCodeList(ctx context.Context, in *GetDiscountCodeListInput, opts ...grpc.CallOption) (*GetDiscountCodeListOutput, error) {
	out := new(GetDiscountCodeListOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetDiscountCodeLogOverview(ctx context.Context, in *GetDiscountCodeLogOverviewInput, opts ...grpc.CallOption) (*GetDiscountCodeLogOverviewOutput, error) {
	out := new(GetDiscountCodeLogOverviewOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeLogOverview", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetDiscountCodeLogList(ctx context.Context, in *GetDiscountCodeLogListInput, opts ...grpc.CallOption) (*GetDiscountCodeLogListOutput, error) {
	out := new(GetDiscountCodeLogListOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeLogList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) ChangeStatus(ctx context.Context, in *ChangeStatusInput, opts ...grpc.CallOption) (*ChangeStatusOutput, error) {
	out := new(ChangeStatusOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/ChangeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) CheckDiscountCodeValidForCustomer(ctx context.Context, in *CheckDiscountCodeValidForCustomerInput, opts ...grpc.CallOption) (*CheckDiscountCodeValidForCustomerOutput, error) {
	out := new(CheckDiscountCodeValidForCustomerOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/CheckDiscountCodeValidForCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetAvailableDiscountList(ctx context.Context, in *GetAvailableDiscountListInput, opts ...grpc.CallOption) (*GetAvailableDiscountListOutput, error) {
	out := new(GetAvailableDiscountListOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetAvailableDiscountList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetAvailableDiscountListForExistingInvoice(ctx context.Context, in *GetAvailableDiscountListForExistingInvoiceInput, opts ...grpc.CallOption) (*GetAvailableDiscountListForExistingInvoiceOutput, error) {
	out := new(GetAvailableDiscountListForExistingInvoiceOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetAvailableDiscountListForExistingInvoice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) AutoApplyDiscountCode(ctx context.Context, in *AutoApplyDiscountCodeInput, opts ...grpc.CallOption) (*AutoApplyDiscountCodeOutput, error) {
	out := new(AutoApplyDiscountCodeOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/AutoApplyDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) DeleteDiscountCodeLog(ctx context.Context, in *DeleteDiscountCodeLogInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/DeleteDiscountCodeLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) UseDiscountCode(ctx context.Context, in *UseDiscountCodeInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/UseDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) RefreshDiscountCodeStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/RefreshDiscountCodeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetBusinessDiscountCodeConfig(ctx context.Context, in *GetBusinessDiscountCodeConfigInput, opts ...grpc.CallOption) (*GetBusinessDiscountCodeConfigOutput, error) {
	out := new(GetBusinessDiscountCodeConfigOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetBusinessDiscountCodeConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) GetDiscountCodeConfig(ctx context.Context, in *GetDiscountCodeConfigInput, opts ...grpc.CallOption) (*GetDiscountCodeConfigOutput, error) {
	out := new(GetDiscountCodeConfigOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discountCodeServiceClient) MigrateDiscountCode(ctx context.Context, in *MigrateDiscountCodeInput, opts ...grpc.CallOption) (*MigrateDiscountCodeInputOutput, error) {
	out := new(MigrateDiscountCodeInputOutput)
	err := c.cc.Invoke(ctx, "/moego.service.marketing.v1.DiscountCodeService/MigrateDiscountCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DiscountCodeServiceServer is the server API for DiscountCodeService service.
// All implementations must embed UnimplementedDiscountCodeServiceServer
// for forward compatibility
type DiscountCodeServiceServer interface {
	// generate discount code
	GenerateDiscountCode(context.Context, *GenerateDiscountCodeInput) (*GenerateDiscountCodeOutput, error)
	// check discount code
	CheckDiscountCode(context.Context, *CheckDiscountCodeInput) (*CheckDiscountCodeOutput, error)
	// create discount code
	CreateDiscountCode(context.Context, *CreateDiscountCodeInput) (*CreateDiscountCodeOutput, error)
	// edit discount code
	EditDiscountCode(context.Context, *EditDiscountCodeInput) (*EditDiscountCodeOutput, error)
	// get discount code
	GetDiscountCode(context.Context, *GetDiscountCodeInput) (*GetDiscountCodeOutput, error)
	// get discount code by code
	GetDiscountCodeByCode(context.Context, *GetDiscountCodeByCodeInput) (*GetDiscountCodeByCodeOutput, error)
	// get discount code list
	GetDiscountCodeList(context.Context, *GetDiscountCodeListInput) (*GetDiscountCodeListOutput, error)
	// get discount code log overview
	GetDiscountCodeLogOverview(context.Context, *GetDiscountCodeLogOverviewInput) (*GetDiscountCodeLogOverviewOutput, error)
	// get discount code log list
	GetDiscountCodeLogList(context.Context, *GetDiscountCodeLogListInput) (*GetDiscountCodeLogListOutput, error)
	// change status
	ChangeStatus(context.Context, *ChangeStatusInput) (*ChangeStatusOutput, error)
	// check discount code valid for customer
	CheckDiscountCodeValidForCustomer(context.Context, *CheckDiscountCodeValidForCustomerInput) (*CheckDiscountCodeValidForCustomerOutput, error)
	// get available discount code list
	GetAvailableDiscountList(context.Context, *GetAvailableDiscountListInput) (*GetAvailableDiscountListOutput, error)
	// get available discount code list for existing invoice
	GetAvailableDiscountListForExistingInvoice(context.Context, *GetAvailableDiscountListForExistingInvoiceInput) (*GetAvailableDiscountListForExistingInvoiceOutput, error)
	// auto apply discount code
	AutoApplyDiscountCode(context.Context, *AutoApplyDiscountCodeInput) (*AutoApplyDiscountCodeOutput, error)
	// delete discount code log
	DeleteDiscountCodeLog(context.Context, *DeleteDiscountCodeLogInput) (*emptypb.Empty, error)
	// use discount code
	UseDiscountCode(context.Context, *UseDiscountCodeInput) (*emptypb.Empty, error)
	// refresh discount code status
	RefreshDiscountCodeStatus(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// get business discount code config
	// Deprecated: replace to GetDiscountCodeConfig
	GetBusinessDiscountCodeConfig(context.Context, *GetBusinessDiscountCodeConfigInput) (*GetBusinessDiscountCodeConfigOutput, error)
	// get business discount code config
	GetDiscountCodeConfig(context.Context, *GetDiscountCodeConfigInput) (*GetDiscountCodeConfigOutput, error)
	// migrate discount code
	MigrateDiscountCode(context.Context, *MigrateDiscountCodeInput) (*MigrateDiscountCodeInputOutput, error)
	mustEmbedUnimplementedDiscountCodeServiceServer()
}

// UnimplementedDiscountCodeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDiscountCodeServiceServer struct {
}

func (UnimplementedDiscountCodeServiceServer) GenerateDiscountCode(context.Context, *GenerateDiscountCodeInput) (*GenerateDiscountCodeOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) CheckDiscountCode(context.Context, *CheckDiscountCodeInput) (*CheckDiscountCodeOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) CreateDiscountCode(context.Context, *CreateDiscountCodeInput) (*CreateDiscountCodeOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) EditDiscountCode(context.Context, *EditDiscountCodeInput) (*EditDiscountCodeOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetDiscountCode(context.Context, *GetDiscountCodeInput) (*GetDiscountCodeOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetDiscountCodeByCode(context.Context, *GetDiscountCodeByCodeInput) (*GetDiscountCodeByCodeOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountCodeByCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetDiscountCodeList(context.Context, *GetDiscountCodeListInput) (*GetDiscountCodeListOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountCodeList not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetDiscountCodeLogOverview(context.Context, *GetDiscountCodeLogOverviewInput) (*GetDiscountCodeLogOverviewOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountCodeLogOverview not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetDiscountCodeLogList(context.Context, *GetDiscountCodeLogListInput) (*GetDiscountCodeLogListOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountCodeLogList not implemented")
}
func (UnimplementedDiscountCodeServiceServer) ChangeStatus(context.Context, *ChangeStatusInput) (*ChangeStatusOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeStatus not implemented")
}
func (UnimplementedDiscountCodeServiceServer) CheckDiscountCodeValidForCustomer(context.Context, *CheckDiscountCodeValidForCustomerInput) (*CheckDiscountCodeValidForCustomerOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckDiscountCodeValidForCustomer not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetAvailableDiscountList(context.Context, *GetAvailableDiscountListInput) (*GetAvailableDiscountListOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableDiscountList not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetAvailableDiscountListForExistingInvoice(context.Context, *GetAvailableDiscountListForExistingInvoiceInput) (*GetAvailableDiscountListForExistingInvoiceOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableDiscountListForExistingInvoice not implemented")
}
func (UnimplementedDiscountCodeServiceServer) AutoApplyDiscountCode(context.Context, *AutoApplyDiscountCodeInput) (*AutoApplyDiscountCodeOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AutoApplyDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) DeleteDiscountCodeLog(context.Context, *DeleteDiscountCodeLogInput) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDiscountCodeLog not implemented")
}
func (UnimplementedDiscountCodeServiceServer) UseDiscountCode(context.Context, *UseDiscountCodeInput) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UseDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) RefreshDiscountCodeStatus(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshDiscountCodeStatus not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetBusinessDiscountCodeConfig(context.Context, *GetBusinessDiscountCodeConfigInput) (*GetBusinessDiscountCodeConfigOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBusinessDiscountCodeConfig not implemented")
}
func (UnimplementedDiscountCodeServiceServer) GetDiscountCodeConfig(context.Context, *GetDiscountCodeConfigInput) (*GetDiscountCodeConfigOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDiscountCodeConfig not implemented")
}
func (UnimplementedDiscountCodeServiceServer) MigrateDiscountCode(context.Context, *MigrateDiscountCodeInput) (*MigrateDiscountCodeInputOutput, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MigrateDiscountCode not implemented")
}
func (UnimplementedDiscountCodeServiceServer) mustEmbedUnimplementedDiscountCodeServiceServer() {}

// UnsafeDiscountCodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DiscountCodeServiceServer will
// result in compilation errors.
type UnsafeDiscountCodeServiceServer interface {
	mustEmbedUnimplementedDiscountCodeServiceServer()
}

func RegisterDiscountCodeServiceServer(s grpc.ServiceRegistrar, srv DiscountCodeServiceServer) {
	s.RegisterService(&DiscountCodeService_ServiceDesc, srv)
}

func _DiscountCodeService_GenerateDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GenerateDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GenerateDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GenerateDiscountCode(ctx, req.(*GenerateDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_CheckDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).CheckDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/CheckDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).CheckDiscountCode(ctx, req.(*CheckDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_CreateDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).CreateDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/CreateDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).CreateDiscountCode(ctx, req.(*CreateDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_EditDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).EditDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/EditDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).EditDiscountCode(ctx, req.(*EditDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetDiscountCode(ctx, req.(*GetDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetDiscountCodeByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountCodeByCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeByCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeByCode(ctx, req.(*GetDiscountCodeByCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetDiscountCodeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountCodeListInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeList(ctx, req.(*GetDiscountCodeListInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetDiscountCodeLogOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountCodeLogOverviewInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeLogOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeLogOverview",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeLogOverview(ctx, req.(*GetDiscountCodeLogOverviewInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetDiscountCodeLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountCodeLogListInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeLogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeLogList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeLogList(ctx, req.(*GetDiscountCodeLogListInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_ChangeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeStatusInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).ChangeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/ChangeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).ChangeStatus(ctx, req.(*ChangeStatusInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_CheckDiscountCodeValidForCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckDiscountCodeValidForCustomerInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).CheckDiscountCodeValidForCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/CheckDiscountCodeValidForCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).CheckDiscountCodeValidForCustomer(ctx, req.(*CheckDiscountCodeValidForCustomerInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetAvailableDiscountList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableDiscountListInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetAvailableDiscountList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetAvailableDiscountList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetAvailableDiscountList(ctx, req.(*GetAvailableDiscountListInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetAvailableDiscountListForExistingInvoice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableDiscountListForExistingInvoiceInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetAvailableDiscountListForExistingInvoice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetAvailableDiscountListForExistingInvoice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetAvailableDiscountListForExistingInvoice(ctx, req.(*GetAvailableDiscountListForExistingInvoiceInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_AutoApplyDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutoApplyDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).AutoApplyDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/AutoApplyDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).AutoApplyDiscountCode(ctx, req.(*AutoApplyDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_DeleteDiscountCodeLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDiscountCodeLogInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).DeleteDiscountCodeLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/DeleteDiscountCodeLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).DeleteDiscountCodeLog(ctx, req.(*DeleteDiscountCodeLogInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_UseDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UseDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).UseDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/UseDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).UseDiscountCode(ctx, req.(*UseDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_RefreshDiscountCodeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).RefreshDiscountCodeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/RefreshDiscountCodeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).RefreshDiscountCodeStatus(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetBusinessDiscountCodeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessDiscountCodeConfigInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetBusinessDiscountCodeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetBusinessDiscountCodeConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetBusinessDiscountCodeConfig(ctx, req.(*GetBusinessDiscountCodeConfigInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_GetDiscountCodeConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDiscountCodeConfigInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/GetDiscountCodeConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).GetDiscountCodeConfig(ctx, req.(*GetDiscountCodeConfigInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscountCodeService_MigrateDiscountCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MigrateDiscountCodeInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscountCodeServiceServer).MigrateDiscountCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.marketing.v1.DiscountCodeService/MigrateDiscountCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscountCodeServiceServer).MigrateDiscountCode(ctx, req.(*MigrateDiscountCodeInput))
	}
	return interceptor(ctx, in, info, handler)
}

// DiscountCodeService_ServiceDesc is the grpc.ServiceDesc for DiscountCodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DiscountCodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.marketing.v1.DiscountCodeService",
	HandlerType: (*DiscountCodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateDiscountCode",
			Handler:    _DiscountCodeService_GenerateDiscountCode_Handler,
		},
		{
			MethodName: "CheckDiscountCode",
			Handler:    _DiscountCodeService_CheckDiscountCode_Handler,
		},
		{
			MethodName: "CreateDiscountCode",
			Handler:    _DiscountCodeService_CreateDiscountCode_Handler,
		},
		{
			MethodName: "EditDiscountCode",
			Handler:    _DiscountCodeService_EditDiscountCode_Handler,
		},
		{
			MethodName: "GetDiscountCode",
			Handler:    _DiscountCodeService_GetDiscountCode_Handler,
		},
		{
			MethodName: "GetDiscountCodeByCode",
			Handler:    _DiscountCodeService_GetDiscountCodeByCode_Handler,
		},
		{
			MethodName: "GetDiscountCodeList",
			Handler:    _DiscountCodeService_GetDiscountCodeList_Handler,
		},
		{
			MethodName: "GetDiscountCodeLogOverview",
			Handler:    _DiscountCodeService_GetDiscountCodeLogOverview_Handler,
		},
		{
			MethodName: "GetDiscountCodeLogList",
			Handler:    _DiscountCodeService_GetDiscountCodeLogList_Handler,
		},
		{
			MethodName: "ChangeStatus",
			Handler:    _DiscountCodeService_ChangeStatus_Handler,
		},
		{
			MethodName: "CheckDiscountCodeValidForCustomer",
			Handler:    _DiscountCodeService_CheckDiscountCodeValidForCustomer_Handler,
		},
		{
			MethodName: "GetAvailableDiscountList",
			Handler:    _DiscountCodeService_GetAvailableDiscountList_Handler,
		},
		{
			MethodName: "GetAvailableDiscountListForExistingInvoice",
			Handler:    _DiscountCodeService_GetAvailableDiscountListForExistingInvoice_Handler,
		},
		{
			MethodName: "AutoApplyDiscountCode",
			Handler:    _DiscountCodeService_AutoApplyDiscountCode_Handler,
		},
		{
			MethodName: "DeleteDiscountCodeLog",
			Handler:    _DiscountCodeService_DeleteDiscountCodeLog_Handler,
		},
		{
			MethodName: "UseDiscountCode",
			Handler:    _DiscountCodeService_UseDiscountCode_Handler,
		},
		{
			MethodName: "RefreshDiscountCodeStatus",
			Handler:    _DiscountCodeService_RefreshDiscountCodeStatus_Handler,
		},
		{
			MethodName: "GetBusinessDiscountCodeConfig",
			Handler:    _DiscountCodeService_GetBusinessDiscountCodeConfig_Handler,
		},
		{
			MethodName: "GetDiscountCodeConfig",
			Handler:    _DiscountCodeService_GetDiscountCodeConfig_Handler,
		},
		{
			MethodName: "MigrateDiscountCode",
			Handler:    _DiscountCodeService_MigrateDiscountCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/marketing/v1/discount_code_service.proto",
}
