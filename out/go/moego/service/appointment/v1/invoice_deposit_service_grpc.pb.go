// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/appointment/v1/invoice_deposit_service.proto

package appointmentsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// InvoiceDepositServiceClient is the client API for InvoiceDepositService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type InvoiceDepositServiceClient interface {
	// get invoice_deposit
	GetInvoiceDepositList(ctx context.Context, in *GetInvoiceDepositListRequest, opts ...grpc.CallOption) (*GetInvoiceDepositListResponse, error)
}

type invoiceDepositServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewInvoiceDepositServiceClient(cc grpc.ClientConnInterface) InvoiceDepositServiceClient {
	return &invoiceDepositServiceClient{cc}
}

func (c *invoiceDepositServiceClient) GetInvoiceDepositList(ctx context.Context, in *GetInvoiceDepositListRequest, opts ...grpc.CallOption) (*GetInvoiceDepositListResponse, error) {
	out := new(GetInvoiceDepositListResponse)
	err := c.cc.Invoke(ctx, "/moego.service.appointment.v1.InvoiceDepositService/GetInvoiceDepositList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// InvoiceDepositServiceServer is the server API for InvoiceDepositService service.
// All implementations must embed UnimplementedInvoiceDepositServiceServer
// for forward compatibility
type InvoiceDepositServiceServer interface {
	// get invoice_deposit
	GetInvoiceDepositList(context.Context, *GetInvoiceDepositListRequest) (*GetInvoiceDepositListResponse, error)
	mustEmbedUnimplementedInvoiceDepositServiceServer()
}

// UnimplementedInvoiceDepositServiceServer must be embedded to have forward compatible implementations.
type UnimplementedInvoiceDepositServiceServer struct {
}

func (UnimplementedInvoiceDepositServiceServer) GetInvoiceDepositList(context.Context, *GetInvoiceDepositListRequest) (*GetInvoiceDepositListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvoiceDepositList not implemented")
}
func (UnimplementedInvoiceDepositServiceServer) mustEmbedUnimplementedInvoiceDepositServiceServer() {}

// UnsafeInvoiceDepositServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to InvoiceDepositServiceServer will
// result in compilation errors.
type UnsafeInvoiceDepositServiceServer interface {
	mustEmbedUnimplementedInvoiceDepositServiceServer()
}

func RegisterInvoiceDepositServiceServer(s grpc.ServiceRegistrar, srv InvoiceDepositServiceServer) {
	s.RegisterService(&InvoiceDepositService_ServiceDesc, srv)
}

func _InvoiceDepositService_GetInvoiceDepositList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvoiceDepositListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(InvoiceDepositServiceServer).GetInvoiceDepositList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.appointment.v1.InvoiceDepositService/GetInvoiceDepositList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(InvoiceDepositServiceServer).GetInvoiceDepositList(ctx, req.(*GetInvoiceDepositListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// InvoiceDepositService_ServiceDesc is the grpc.ServiceDesc for InvoiceDepositService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var InvoiceDepositService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.appointment.v1.InvoiceDepositService",
	HandlerType: (*InvoiceDepositServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetInvoiceDepositList",
			Handler:    _InvoiceDepositService_GetInvoiceDepositList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/appointment/v1/invoice_deposit_service.proto",
}
