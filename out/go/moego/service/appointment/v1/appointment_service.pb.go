// @since 2024-01-15 15:02:36
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v1/appointment_service.proto

package appointmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v15 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	v14 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	interval "google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Appointment date type, determined by appointment date field
type AppointmentDateType int32

const (
	// unspecified
	AppointmentDateType_APPOINTMENT_DATE_TYPE_UNSPECIFIED AppointmentDateType = 0
	// last: appointment_date < currentDate or (appointment_date = currentDate and appointment_end_time < currentMinute)
	AppointmentDateType_LAST AppointmentDateType = 1
	// next: appointment_date > currentDate or (appointment_date = currentDate and appointment_start_time > currentMinute)
	AppointmentDateType_NEXT AppointmentDateType = 2
	// today: appointment_date = currentDate
	AppointmentDateType_TODAY AppointmentDateType = 3
	// NEXT_NOT_END: appointment_end_date > currentDate or (appointment_end_date = currentDate and appointment_end_time >= currentMinute)
	AppointmentDateType_NEXT_NOT_END AppointmentDateType = 4
	// NEXT_AFTER_TODAY: appointment_date > currentDate
	AppointmentDateType_NEXT_AFTER_TODAY AppointmentDateType = 5
)

// Enum value maps for AppointmentDateType.
var (
	AppointmentDateType_name = map[int32]string{
		0: "APPOINTMENT_DATE_TYPE_UNSPECIFIED",
		1: "LAST",
		2: "NEXT",
		3: "TODAY",
		4: "NEXT_NOT_END",
		5: "NEXT_AFTER_TODAY",
	}
	AppointmentDateType_value = map[string]int32{
		"APPOINTMENT_DATE_TYPE_UNSPECIFIED": 0,
		"LAST":                              1,
		"NEXT":                              2,
		"TODAY":                             3,
		"NEXT_NOT_END":                      4,
		"NEXT_AFTER_TODAY":                  5,
	}
)

func (x AppointmentDateType) Enum() *AppointmentDateType {
	p := new(AppointmentDateType)
	*p = x
	return p
}

func (x AppointmentDateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppointmentDateType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_appointment_v1_appointment_service_proto_enumTypes[0].Descriptor()
}

func (AppointmentDateType) Type() protoreflect.EnumType {
	return &file_moego_service_appointment_v1_appointment_service_proto_enumTypes[0]
}

func (x AppointmentDateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppointmentDateType.Descriptor instead.
func (AppointmentDateType) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{0}
}

// PriorityOrderType
type ListAppointmentsRequest_PriorityOrderType int32

const (
	// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
	// unspecified
	ListAppointmentsRequest_UNSPECIFIED ListAppointmentsRequest_PriorityOrderType = 0
	// Unexpired and unconfirmed appointments.
	// Must filter by one and only one customer_id.
	ListAppointmentsRequest_UNEXPIRED_UNCONFIRMED ListAppointmentsRequest_PriorityOrderType = 1
)

// Enum value maps for ListAppointmentsRequest_PriorityOrderType.
var (
	ListAppointmentsRequest_PriorityOrderType_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNEXPIRED_UNCONFIRMED",
	}
	ListAppointmentsRequest_PriorityOrderType_value = map[string]int32{
		"UNSPECIFIED":           0,
		"UNEXPIRED_UNCONFIRMED": 1,
	}
)

func (x ListAppointmentsRequest_PriorityOrderType) Enum() *ListAppointmentsRequest_PriorityOrderType {
	p := new(ListAppointmentsRequest_PriorityOrderType)
	*p = x
	return p
}

func (x ListAppointmentsRequest_PriorityOrderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAppointmentsRequest_PriorityOrderType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_service_appointment_v1_appointment_service_proto_enumTypes[1].Descriptor()
}

func (ListAppointmentsRequest_PriorityOrderType) Type() protoreflect.EnumType {
	return &file_moego_service_appointment_v1_appointment_service_proto_enumTypes[1]
}

func (x ListAppointmentsRequest_PriorityOrderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAppointmentsRequest_PriorityOrderType.Descriptor instead.
func (ListAppointmentsRequest_PriorityOrderType) EnumDescriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{16, 0}
}

// Create a appointment request
type CreateAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment request
	Appointment *v1.AppointmentCreateDef `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// Selected pet and services
	PetDetails []*v1.PetDetailDef `protobuf:"bytes,2,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// Pre-auth
	// Simply request a card on file, and we'll pre-authorize the ticket amount 24 hours before the appointment.
	// Final payment will be automatically charged at the end of the service day.
	PreAuth *v11.PreAuthEnableDef `protobuf:"bytes,3,opt,name=pre_auth,json=preAuth,proto3,oneof" json:"pre_auth,omitempty"`
	// Appointment notes, contains ticket comments and alert notes
	// Ticket comments: For this appointment. Private to business only
	// Alert notes: It will be shown as a red exclamation mark on the appointment card. Private for business.
	Notes []*v1.AppointmentNoteCreateDef `protobuf:"bytes,4,rep,name=notes,proto3" json:"notes,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,6,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// created at, if not set, use current time, used for data migration from other systems
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3,oneof" json:"created_at,omitempty"`
}

func (x *CreateAppointmentRequest) Reset() {
	*x = CreateAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentRequest) ProtoMessage() {}

func (x *CreateAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentRequest.ProtoReflect.Descriptor instead.
func (*CreateAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAppointmentRequest) GetAppointment() *v1.AppointmentCreateDef {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *CreateAppointmentRequest) GetPetDetails() []*v1.PetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CreateAppointmentRequest) GetPreAuth() *v11.PreAuthEnableDef {
	if x != nil {
		return x.PreAuth
	}
	return nil
}

func (x *CreateAppointmentRequest) GetNotes() []*v1.AppointmentNoteCreateDef {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *CreateAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateAppointmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateAppointmentRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateAppointmentRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// Create a appointment response
type CreateAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *CreateAppointmentResponse) Reset() {
	*x = CreateAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentResponse) ProtoMessage() {}

func (x *CreateAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentResponse.ProtoReflect.Descriptor instead.
func (*CreateAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAppointmentResponse) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// Update a appointment request, contains appointment date and pet details
type UpdateAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// appointment
	Appointment *v1.AppointmentUpdateDef `protobuf:"bytes,2,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,6,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *UpdateAppointmentRequest) Reset() {
	*x = UpdateAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest) ProtoMessage() {}

func (x *UpdateAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateAppointmentRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateAppointmentRequest) GetAppointment() *v1.AppointmentUpdateDef {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *UpdateAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateAppointmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *UpdateAppointmentRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// Update a appointment response
type UpdateAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAppointmentResponse) Reset() {
	*x = UpdateAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentResponse) ProtoMessage() {}

func (x *UpdateAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentResponse.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{3}
}

// Get appointment request
type GetAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetAppointmentRequest) Reset() {
	*x = GetAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentRequest) ProtoMessage() {}

func (x *GetAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentRequest.ProtoReflect.Descriptor instead.
func (*GetAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetAppointmentRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *GetAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetAppointmentRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Get appointment response
type GetAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment detail
	Appointment *v1.AppointmentModel `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
}

func (x *GetAppointmentResponse) Reset() {
	*x = GetAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentResponse) ProtoMessage() {}

func (x *GetAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentResponse.ProtoReflect.Descriptor instead.
func (*GetAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAppointmentResponse) GetAppointment() *v1.AppointmentModel {
	if x != nil {
		return x.Appointment
	}
	return nil
}

// Get appointment list request
type GetAppointmentListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId []int64 `protobuf:"varint,1,rep,packed,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	//
	// Deprecated: Do not use.
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetAppointmentListRequest) Reset() {
	*x = GetAppointmentListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentListRequest) ProtoMessage() {}

func (x *GetAppointmentListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentListRequest.ProtoReflect.Descriptor instead.
func (*GetAppointmentListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetAppointmentListRequest) GetAppointmentId() []int64 {
	if x != nil {
		return x.AppointmentId
	}
	return nil
}

func (x *GetAppointmentListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// Deprecated: Do not use.
func (x *GetAppointmentListRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Get appointment list response
type GetAppointmentListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *GetAppointmentListResponse) Reset() {
	*x = GetAppointmentListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAppointmentListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppointmentListResponse) ProtoMessage() {}

func (x *GetAppointmentListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppointmentListResponse.ProtoReflect.Descriptor instead.
func (*GetAppointmentListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetAppointmentListResponse) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// get customer last appointment request
type GetCustomerLastAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id
	CustomerId []int64 `protobuf:"varint,2,rep,packed,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// appointment status, if is empty then return not canceled appointment
	Status *v1.AppointmentStatus `protobuf:"varint,3,opt,name=status,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"status,omitempty"`
	// filter
	Filter *GetCustomerLastAppointmentRequest_Filter `protobuf:"bytes,4,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *GetCustomerLastAppointmentRequest) Reset() {
	*x = GetCustomerLastAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerLastAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerLastAppointmentRequest) ProtoMessage() {}

func (x *GetCustomerLastAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerLastAppointmentRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerLastAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetCustomerLastAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetCustomerLastAppointmentRequest) GetCustomerId() []int64 {
	if x != nil {
		return x.CustomerId
	}
	return nil
}

func (x *GetCustomerLastAppointmentRequest) GetStatus() v1.AppointmentStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.AppointmentStatus(0)
}

func (x *GetCustomerLastAppointmentRequest) GetFilter() *GetCustomerLastAppointmentRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// get customer last appointment response
type GetCustomerLastAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer last appointment
	CustomerLastAppointment map[int64]*v1.AppointmentModel `protobuf:"bytes,1,rep,name=customer_last_appointment,json=customerLastAppointment,proto3" json:"customer_last_appointment,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetCustomerLastAppointmentResponse) Reset() {
	*x = GetCustomerLastAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerLastAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerLastAppointmentResponse) ProtoMessage() {}

func (x *GetCustomerLastAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerLastAppointmentResponse.ProtoReflect.Descriptor instead.
func (*GetCustomerLastAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetCustomerLastAppointmentResponse) GetCustomerLastAppointment() map[int64]*v1.AppointmentModel {
	if x != nil {
		return x.CustomerLastAppointment
	}
	return nil
}

// calculate appointment invoice request
type CalculateAppointmentInvoiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected pet and services
	PetDetails []*v1.PetDetailDef `protobuf:"bytes,3,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,6,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
}

func (x *CalculateAppointmentInvoiceRequest) Reset() {
	*x = CalculateAppointmentInvoiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateAppointmentInvoiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAppointmentInvoiceRequest) ProtoMessage() {}

func (x *CalculateAppointmentInvoiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAppointmentInvoiceRequest.ProtoReflect.Descriptor instead.
func (*CalculateAppointmentInvoiceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{10}
}

func (x *CalculateAppointmentInvoiceRequest) GetPetDetails() []*v1.PetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CalculateAppointmentInvoiceRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CalculateAppointmentInvoiceRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CalculateAppointmentInvoiceRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// calculate appointment invoice response
type CalculateAppointmentInvoiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order detail
	Order *v12.OrderDef `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
}

func (x *CalculateAppointmentInvoiceResponse) Reset() {
	*x = CalculateAppointmentInvoiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalculateAppointmentInvoiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalculateAppointmentInvoiceResponse) ProtoMessage() {}

func (x *CalculateAppointmentInvoiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalculateAppointmentInvoiceResponse.ProtoReflect.Descriptor instead.
func (*CalculateAppointmentInvoiceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{11}
}

func (x *CalculateAppointmentInvoiceResponse) GetOrder() *v12.OrderDef {
	if x != nil {
		return x.Order
	}
	return nil
}

// get in progress appointment request
type GetInProgressAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// service item type
	ServiceItemType v13.ServiceItemType `protobuf:"varint,2,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// customer id
	CustomerId *int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// pet id
	PetId *int64 `protobuf:"varint,5,opt,name=pet_id,json=petId,proto3,oneof" json:"pet_id,omitempty"`
}

func (x *GetInProgressAppointmentRequest) Reset() {
	*x = GetInProgressAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInProgressAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInProgressAppointmentRequest) ProtoMessage() {}

func (x *GetInProgressAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInProgressAppointmentRequest.ProtoReflect.Descriptor instead.
func (*GetInProgressAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetInProgressAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetInProgressAppointmentRequest) GetServiceItemType() v13.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v13.ServiceItemType(0)
}

func (x *GetInProgressAppointmentRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *GetInProgressAppointmentRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *GetInProgressAppointmentRequest) GetPetId() int64 {
	if x != nil && x.PetId != nil {
		return *x.PetId
	}
	return 0
}

// get in progress appointment response
type GetInProgressAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId *int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3,oneof" json:"appointment_id,omitempty"`
}

func (x *GetInProgressAppointmentResponse) Reset() {
	*x = GetInProgressAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInProgressAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInProgressAppointmentResponse) ProtoMessage() {}

func (x *GetInProgressAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInProgressAppointmentResponse.ProtoReflect.Descriptor instead.
func (*GetInProgressAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetInProgressAppointmentResponse) GetAppointmentId() int64 {
	if x != nil && x.AppointmentId != nil {
		return *x.AppointmentId
	}
	return 0
}

// Create block request
type CreateBlockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// created by
	CreatedBy int64 `protobuf:"varint,4,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// start time
	StartTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time
	EndTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,7,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// description
	Description string `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`
	// Source of the appointment
	Source v1.AppointmentSource `protobuf:"varint,9,opt,name=source,proto3,enum=moego.models.appointment.v1.AppointmentSource" json:"source,omitempty"`
}

func (x *CreateBlockRequest) Reset() {
	*x = CreateBlockRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBlockRequest) ProtoMessage() {}

func (x *CreateBlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBlockRequest.ProtoReflect.Descriptor instead.
func (*CreateBlockRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{14}
}

func (x *CreateBlockRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateBlockRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateBlockRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateBlockRequest) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *CreateBlockRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *CreateBlockRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *CreateBlockRequest) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *CreateBlockRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateBlockRequest) GetSource() v1.AppointmentSource {
	if x != nil {
		return x.Source
	}
	return v1.AppointmentSource(0)
}

// Create block response
type CreateBlockResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the block
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateBlockResponse) Reset() {
	*x = CreateBlockResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBlockResponse) ProtoMessage() {}

func (x *CreateBlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBlockResponse.ProtoReflect.Descriptor instead.
func (*CreateBlockResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{15}
}

func (x *CreateBlockResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// List appointments request
type ListAppointmentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessIds []int64 `protobuf:"varint,3,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// filter
	Filter *ListAppointmentsRequest_Filter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// order by (support multi fields), optional
	OrderBys []*v2.OrderBy `protobuf:"bytes,5,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
	// priority order type, optional
	PriorityOrderType *ListAppointmentsRequest_PriorityOrderType `protobuf:"varint,6,opt,name=priority_order_type,json=priorityOrderType,proto3,enum=moego.service.appointment.v1.ListAppointmentsRequest_PriorityOrderType,oneof" json:"priority_order_type,omitempty"`
}

func (x *ListAppointmentsRequest) Reset() {
	*x = ListAppointmentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsRequest) ProtoMessage() {}

func (x *ListAppointmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsRequest.ProtoReflect.Descriptor instead.
func (*ListAppointmentsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{16}
}

func (x *ListAppointmentsRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAppointmentsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAppointmentsRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListAppointmentsRequest) GetFilter() *ListAppointmentsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAppointmentsRequest) GetOrderBys() []*v2.OrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

func (x *ListAppointmentsRequest) GetPriorityOrderType() ListAppointmentsRequest_PriorityOrderType {
	if x != nil && x.PriorityOrderType != nil {
		return *x.PriorityOrderType
	}
	return ListAppointmentsRequest_UNSPECIFIED
}

// List appointments response
type ListAppointmentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// appointment detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,2,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *ListAppointmentsResponse) Reset() {
	*x = ListAppointmentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsResponse) ProtoMessage() {}

func (x *ListAppointmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsResponse.ProtoReflect.Descriptor instead.
func (*ListAppointmentsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{17}
}

func (x *ListAppointmentsResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAppointmentsResponse) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// Create a appointment request
type CreateAppointmentForOnlineBookingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment request
	Appointment *v1.AppointmentCreateForOnlineBookingDef `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// Selected pet and services
	PetDetails []*v1.PetDetailDef `protobuf:"bytes,2,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// Appointment notes, contains ticket comments and alert notes
	// Ticket comments: For this appointment. Private to business only
	// Alert notes: It will be shown as a red exclamation mark on the appointment card. Private for business.
	// Cancel reason: For this appointment. Private to business only
	Notes []*v1.AppointmentNoteCreateDef `protobuf:"bytes,4,rep,name=notes,proto3" json:"notes,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,5,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,6,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// staff id, 0 means operator is system
	StaffId int64 `protobuf:"varint,7,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// booking request id
	BookingRequestId int64 `protobuf:"varint,8,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// booking request identifier
	BookingRequestIdentifier string `protobuf:"bytes,9,opt,name=booking_request_identifier,json=bookingRequestIdentifier,proto3" json:"booking_request_identifier,omitempty"`
}

func (x *CreateAppointmentForOnlineBookingRequest) Reset() {
	*x = CreateAppointmentForOnlineBookingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppointmentForOnlineBookingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentForOnlineBookingRequest) ProtoMessage() {}

func (x *CreateAppointmentForOnlineBookingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentForOnlineBookingRequest.ProtoReflect.Descriptor instead.
func (*CreateAppointmentForOnlineBookingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{18}
}

func (x *CreateAppointmentForOnlineBookingRequest) GetAppointment() *v1.AppointmentCreateForOnlineBookingDef {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *CreateAppointmentForOnlineBookingRequest) GetPetDetails() []*v1.PetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *CreateAppointmentForOnlineBookingRequest) GetNotes() []*v1.AppointmentNoteCreateDef {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *CreateAppointmentForOnlineBookingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateAppointmentForOnlineBookingRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateAppointmentForOnlineBookingRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateAppointmentForOnlineBookingRequest) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *CreateAppointmentForOnlineBookingRequest) GetBookingRequestIdentifier() string {
	if x != nil {
		return x.BookingRequestIdentifier
	}
	return ""
}

// Create a appointment response
type CreateAppointmentForOnlineBookingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *CreateAppointmentForOnlineBookingResponse) Reset() {
	*x = CreateAppointmentForOnlineBookingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAppointmentForOnlineBookingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppointmentForOnlineBookingResponse) ProtoMessage() {}

func (x *CreateAppointmentForOnlineBookingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppointmentForOnlineBookingResponse.ProtoReflect.Descriptor instead.
func (*CreateAppointmentForOnlineBookingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{19}
}

func (x *CreateAppointmentForOnlineBookingResponse) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// List block times request
type ListBlockTimesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessIds []int64 `protobuf:"varint,3,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// filter
	Filter *ListBlockTimesRequest_Filter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListBlockTimesRequest) Reset() {
	*x = ListBlockTimesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBlockTimesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBlockTimesRequest) ProtoMessage() {}

func (x *ListBlockTimesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBlockTimesRequest.ProtoReflect.Descriptor instead.
func (*ListBlockTimesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{20}
}

func (x *ListBlockTimesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListBlockTimesRequest) GetBusinessIds() []int64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *ListBlockTimesRequest) GetFilter() *ListBlockTimesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// List block times response
type ListBlockTimesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// block times
	BlockTimes []*v1.BlockTimeModel `protobuf:"bytes,1,rep,name=block_times,json=blockTimes,proto3" json:"block_times,omitempty"`
}

func (x *ListBlockTimesResponse) Reset() {
	*x = ListBlockTimesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBlockTimesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBlockTimesResponse) ProtoMessage() {}

func (x *ListBlockTimesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBlockTimesResponse.ProtoReflect.Descriptor instead.
func (*ListBlockTimesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{21}
}

func (x *ListBlockTimesResponse) GetBlockTimes() []*v1.BlockTimeModel {
	if x != nil {
		return x.BlockTimes
	}
	return nil
}

// batch quick check-in request
type BatchQuickCheckInRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// selected service id
	ServiceId int64 `protobuf:"varint,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// selected pet ids, if multi pets belong to the same customer, will be in the same appointment
	PetIds []int64 `protobuf:"varint,4,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// selected date
	Date *date.Date `protobuf:"bytes,5,opt,name=date,proto3" json:"date,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// source
	Source v1.AppointmentSource `protobuf:"varint,7,opt,name=source,proto3,enum=moego.models.appointment.v1.AppointmentSource" json:"source,omitempty"`
}

func (x *BatchQuickCheckInRequest) Reset() {
	*x = BatchQuickCheckInRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQuickCheckInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQuickCheckInRequest) ProtoMessage() {}

func (x *BatchQuickCheckInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQuickCheckInRequest.ProtoReflect.Descriptor instead.
func (*BatchQuickCheckInRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{22}
}

func (x *BatchQuickCheckInRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchQuickCheckInRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchQuickCheckInRequest) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *BatchQuickCheckInRequest) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *BatchQuickCheckInRequest) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *BatchQuickCheckInRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *BatchQuickCheckInRequest) GetSource() v1.AppointmentSource {
	if x != nil {
		return x.Source
	}
	return v1.AppointmentSource(0)
}

// quick check-in response
type BatchQuickCheckInResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created appointment ids
	CreatedAppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=created_appointment_ids,json=createdAppointmentIds,proto3" json:"created_appointment_ids,omitempty"`
	// updated appointment ids
	UpdatedAppointmentIds []int64 `protobuf:"varint,2,rep,packed,name=updated_appointment_ids,json=updatedAppointmentIds,proto3" json:"updated_appointment_ids,omitempty"`
}

func (x *BatchQuickCheckInResponse) Reset() {
	*x = BatchQuickCheckInResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchQuickCheckInResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQuickCheckInResponse) ProtoMessage() {}

func (x *BatchQuickCheckInResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQuickCheckInResponse.ProtoReflect.Descriptor instead.
func (*BatchQuickCheckInResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{23}
}

func (x *BatchQuickCheckInResponse) GetCreatedAppointmentIds() []int64 {
	if x != nil {
		return x.CreatedAppointmentIds
	}
	return nil
}

func (x *BatchQuickCheckInResponse) GetUpdatedAppointmentIds() []int64 {
	if x != nil {
		return x.UpdatedAppointmentIds
	}
	return nil
}

// UpdateAppointmentSelective request
type UpdateAppointmentSelectiveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentDate *string `protobuf:"bytes,2,opt,name=appointment_date,json=appointmentDate,proto3,oneof" json:"appointment_date,omitempty"`
	// appointment start time, the number of minutes of the day
	AppointmentStartTime *int32 `protobuf:"varint,3,opt,name=appointment_start_time,json=appointmentStartTime,proto3,oneof" json:"appointment_start_time,omitempty"`
	// appointment end time, the number of minutes of the day
	AppointmentEndTime *int32 `protobuf:"varint,4,opt,name=appointment_end_time,json=appointmentEndTime,proto3,oneof" json:"appointment_end_time,omitempty"`
	// confirmed time in seconds
	// 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
	ConfirmedTime *int64 `protobuf:"varint,5,opt,name=confirmed_time,json=confirmedTime,proto3,oneof" json:"confirmed_time,omitempty"`
	// check in time in seconds
	// 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
	CheckInTime *int64 `protobuf:"varint,6,opt,name=check_in_time,json=checkInTime,proto3,oneof" json:"check_in_time,omitempty"`
	// check out time in seconds
	// 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
	CheckOutTime *int64 `protobuf:"varint,7,opt,name=check_out_time,json=checkOutTime,proto3,oneof" json:"check_out_time,omitempty"`
	// canceled time in seconds
	// 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
	CanceledTime *int64 `protobuf:"varint,8,opt,name=canceled_time,json=canceledTime,proto3,oneof" json:"canceled_time,omitempty"`
	// color code
	ColorCode *string `protobuf:"bytes,9,opt,name=color_code,json=colorCode,proto3,oneof" json:"color_code,omitempty"`
	// cancel by type
	CancelByType *int32 `protobuf:"varint,10,opt,name=cancel_by_type,json=cancelByType,proto3,oneof" json:"cancel_by_type,omitempty"`
	// cancel by
	CancelBy *int64 `protobuf:"varint,11,opt,name=cancel_by,json=cancelBy,proto3,oneof" json:"cancel_by,omitempty"`
	// confirm by type
	ConfirmByType *int32 `protobuf:"varint,12,opt,name=confirm_by_type,json=confirmByType,proto3,oneof" json:"confirm_by_type,omitempty"`
	// confirm by
	ConfirmBy *int64 `protobuf:"varint,13,opt,name=confirm_by,json=confirmBy,proto3,oneof" json:"confirm_by,omitempty"`
	// status before checkin
	StatusBeforeCheckin *v1.AppointmentStatus `protobuf:"varint,14,opt,name=status_before_checkin,json=statusBeforeCheckin,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"status_before_checkin,omitempty"`
	// status before ready
	StatusBeforeReady *v1.AppointmentStatus `protobuf:"varint,15,opt,name=status_before_ready,json=statusBeforeReady,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"status_before_ready,omitempty"`
	// status before finish
	StatusBeforeFinish *v1.AppointmentStatus `protobuf:"varint,16,opt,name=status_before_finish,json=statusBeforeFinish,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"status_before_finish,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`
	// updated by
	UpdatedById *int64 `protobuf:"varint,18,opt,name=updated_by_id,json=updatedById,proto3,oneof" json:"updated_by_id,omitempty"`
	// appointment status
	Status *v1.AppointmentStatus `protobuf:"varint,19,opt,name=status,proto3,enum=moego.models.appointment.v1.AppointmentStatus,oneof" json:"status,omitempty"`
	// ready time in seconds
	// 会有修改为 0 的场景，所以用 int64 而不是 Timestamp
	ReadyTime *int64 `protobuf:"varint,20,opt,name=ready_time,json=readyTime,proto3,oneof" json:"ready_time,omitempty"`
	// is paid
	PaymentStatus *v1.AppointmentPaymentStatus `protobuf:"varint,21,opt,name=payment_status,json=paymentStatus,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus,oneof" json:"payment_status,omitempty"`
	// Indicates if the appointment was booked online
	BookOnlineStatus *v1.AppointmentBookOnlineStatus `protobuf:"varint,22,opt,name=book_online_status,json=bookOnlineStatus,proto3,enum=moego.models.appointment.v1.AppointmentBookOnlineStatus,oneof" json:"book_online_status,omitempty"`
	// If this parameter is not passed, it will not be updated
	PetDetails []*v1.UpdatePetDetailDef `protobuf:"bytes,23,rep,name=pet_details,json=petDetails,proto3" json:"pet_details,omitempty"`
	// updated by type. This field is not used for appointment data updates
	UpdateByType *v1.AppointmentUpdatedBy `protobuf:"varint,24,opt,name=update_by_type,json=updateByType,proto3,enum=moego.models.appointment.v1.AppointmentUpdatedBy,oneof" json:"update_by_type,omitempty"`
	// is_deprecate
	// 支持删除 appointment
	// deprecated by Freeman since 2025/3/17, 这个接口的实现被修改的很奇怪，不要通过设置 is_deprecate 来删除 appointment，use DeleteAppointments instead
	//
	// Deprecated: Do not use.
	IsDeprecate *bool `protobuf:"varint,25,opt,name=is_deprecate,json=isDeprecate,proto3,oneof" json:"is_deprecate,omitempty"`
	// Big invoice ID
	OrderId *int64 `protobuf:"varint,26,opt,name=order_id,json=orderId,proto3,oneof" json:"order_id,omitempty"`
}

func (x *UpdateAppointmentSelectiveRequest) Reset() {
	*x = UpdateAppointmentSelectiveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentSelectiveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentSelectiveRequest) ProtoMessage() {}

func (x *UpdateAppointmentSelectiveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentSelectiveRequest.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentSelectiveRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateAppointmentSelectiveRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetAppointmentDate() string {
	if x != nil && x.AppointmentDate != nil {
		return *x.AppointmentDate
	}
	return ""
}

func (x *UpdateAppointmentSelectiveRequest) GetAppointmentStartTime() int32 {
	if x != nil && x.AppointmentStartTime != nil {
		return *x.AppointmentStartTime
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetAppointmentEndTime() int32 {
	if x != nil && x.AppointmentEndTime != nil {
		return *x.AppointmentEndTime
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetConfirmedTime() int64 {
	if x != nil && x.ConfirmedTime != nil {
		return *x.ConfirmedTime
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetCheckInTime() int64 {
	if x != nil && x.CheckInTime != nil {
		return *x.CheckInTime
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetCheckOutTime() int64 {
	if x != nil && x.CheckOutTime != nil {
		return *x.CheckOutTime
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetCanceledTime() int64 {
	if x != nil && x.CanceledTime != nil {
		return *x.CanceledTime
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetColorCode() string {
	if x != nil && x.ColorCode != nil {
		return *x.ColorCode
	}
	return ""
}

func (x *UpdateAppointmentSelectiveRequest) GetCancelByType() int32 {
	if x != nil && x.CancelByType != nil {
		return *x.CancelByType
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetCancelBy() int64 {
	if x != nil && x.CancelBy != nil {
		return *x.CancelBy
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetConfirmByType() int32 {
	if x != nil && x.ConfirmByType != nil {
		return *x.ConfirmByType
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetConfirmBy() int64 {
	if x != nil && x.ConfirmBy != nil {
		return *x.ConfirmBy
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetStatusBeforeCheckin() v1.AppointmentStatus {
	if x != nil && x.StatusBeforeCheckin != nil {
		return *x.StatusBeforeCheckin
	}
	return v1.AppointmentStatus(0)
}

func (x *UpdateAppointmentSelectiveRequest) GetStatusBeforeReady() v1.AppointmentStatus {
	if x != nil && x.StatusBeforeReady != nil {
		return *x.StatusBeforeReady
	}
	return v1.AppointmentStatus(0)
}

func (x *UpdateAppointmentSelectiveRequest) GetStatusBeforeFinish() v1.AppointmentStatus {
	if x != nil && x.StatusBeforeFinish != nil {
		return *x.StatusBeforeFinish
	}
	return v1.AppointmentStatus(0)
}

func (x *UpdateAppointmentSelectiveRequest) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *UpdateAppointmentSelectiveRequest) GetUpdatedById() int64 {
	if x != nil && x.UpdatedById != nil {
		return *x.UpdatedById
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetStatus() v1.AppointmentStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.AppointmentStatus(0)
}

func (x *UpdateAppointmentSelectiveRequest) GetReadyTime() int64 {
	if x != nil && x.ReadyTime != nil {
		return *x.ReadyTime
	}
	return 0
}

func (x *UpdateAppointmentSelectiveRequest) GetPaymentStatus() v1.AppointmentPaymentStatus {
	if x != nil && x.PaymentStatus != nil {
		return *x.PaymentStatus
	}
	return v1.AppointmentPaymentStatus(0)
}

func (x *UpdateAppointmentSelectiveRequest) GetBookOnlineStatus() v1.AppointmentBookOnlineStatus {
	if x != nil && x.BookOnlineStatus != nil {
		return *x.BookOnlineStatus
	}
	return v1.AppointmentBookOnlineStatus(0)
}

func (x *UpdateAppointmentSelectiveRequest) GetPetDetails() []*v1.UpdatePetDetailDef {
	if x != nil {
		return x.PetDetails
	}
	return nil
}

func (x *UpdateAppointmentSelectiveRequest) GetUpdateByType() v1.AppointmentUpdatedBy {
	if x != nil && x.UpdateByType != nil {
		return *x.UpdateByType
	}
	return v1.AppointmentUpdatedBy(0)
}

// Deprecated: Do not use.
func (x *UpdateAppointmentSelectiveRequest) GetIsDeprecate() bool {
	if x != nil && x.IsDeprecate != nil {
		return *x.IsDeprecate
	}
	return false
}

func (x *UpdateAppointmentSelectiveRequest) GetOrderId() int64 {
	if x != nil && x.OrderId != nil {
		return *x.OrderId
	}
	return 0
}

// UpdateAppointmentSelective response
type UpdateAppointmentSelectiveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// affected rows count
	AffectedRows int32 `protobuf:"varint,1,opt,name=affected_rows,json=affectedRows,proto3" json:"affected_rows,omitempty"`
}

func (x *UpdateAppointmentSelectiveResponse) Reset() {
	*x = UpdateAppointmentSelectiveResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentSelectiveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentSelectiveResponse) ProtoMessage() {}

func (x *UpdateAppointmentSelectiveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentSelectiveResponse.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentSelectiveResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateAppointmentSelectiveResponse) GetAffectedRows() int32 {
	if x != nil {
		return x.AffectedRows
	}
	return 0
}

// list appointments for pet
// Considering the query performance, it is necessary to specify the service and date
type ListAppointmentForPetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// pet id
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// filter
	Filter *ListAppointmentForPetsRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListAppointmentForPetsRequest) Reset() {
	*x = ListAppointmentForPetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentForPetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentForPetsRequest) ProtoMessage() {}

func (x *ListAppointmentForPetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentForPetsRequest.ProtoReflect.Descriptor instead.
func (*ListAppointmentForPetsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{26}
}

func (x *ListAppointmentForPetsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAppointmentForPetsRequest) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListAppointmentForPetsRequest) GetFilter() *ListAppointmentForPetsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list appointments for pet response
type ListAppointmentForPetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// pet id to appointment ids
	PetIdToAppointmentIdList map[int64]*v14.Int64ListValue `protobuf:"bytes,2,rep,name=pet_id_to_appointment_id_list,json=petIdToAppointmentIdList,proto3" json:"pet_id_to_appointment_id_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ListAppointmentForPetsResponse) Reset() {
	*x = ListAppointmentForPetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentForPetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentForPetsResponse) ProtoMessage() {}

func (x *ListAppointmentForPetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentForPetsResponse.ProtoReflect.Descriptor instead.
func (*ListAppointmentForPetsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{27}
}

func (x *ListAppointmentForPetsResponse) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListAppointmentForPetsResponse) GetPetIdToAppointmentIdList() map[int64]*v14.Int64ListValue {
	if x != nil {
		return x.PetIdToAppointmentIdList
	}
	return nil
}

// list appointments for customer
type ListAppointmentsForCustomersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id
	CustomerIds []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// filter
	Filter *ListAppointmentsForCustomersRequest_Filter `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListAppointmentsForCustomersRequest) Reset() {
	*x = ListAppointmentsForCustomersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsForCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsForCustomersRequest) ProtoMessage() {}

func (x *ListAppointmentsForCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsForCustomersRequest.ProtoReflect.Descriptor instead.
func (*ListAppointmentsForCustomersRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{28}
}

func (x *ListAppointmentsForCustomersRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListAppointmentsForCustomersRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListAppointmentsForCustomersRequest) GetFilter() *ListAppointmentsForCustomersRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list appointments for customer response
type ListAppointmentsForCustomersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *ListAppointmentsForCustomersResponse) Reset() {
	*x = ListAppointmentsForCustomersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsForCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsForCustomersResponse) ProtoMessage() {}

func (x *ListAppointmentsForCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsForCustomersResponse.ProtoReflect.Descriptor instead.
func (*ListAppointmentsForCustomersResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{29}
}

func (x *ListAppointmentsForCustomersResponse) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// Cancel appointment request
type CancelAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// cancel by type
	CancelByType v1.AppointmentUpdatedBy `protobuf:"varint,2,opt,name=cancel_by_type,json=cancelByType,proto3,enum=moego.models.appointment.v1.AppointmentUpdatedBy" json:"cancel_by_type,omitempty"`
	// cancel by, client account id or business staff id
	CancelBy int64 `protobuf:"varint,3,opt,name=cancel_by,json=cancelBy,proto3" json:"cancel_by,omitempty"`
	// cancel reason, optional
	CancelReason *string `protobuf:"bytes,4,opt,name=cancel_reason,json=cancelReason,proto3,oneof" json:"cancel_reason,omitempty"`
	// no-show status
	NoShow v1.AppointmentNoShowStatus `protobuf:"varint,5,opt,name=no_show,json=noShow,proto3,enum=moego.models.appointment.v1.AppointmentNoShowStatus" json:"no_show,omitempty"`
	// Repeat appointment modify scope
	RepeatAppointmentModifyScope *v1.RepeatAppointmentModifyScope `protobuf:"varint,6,opt,name=repeat_appointment_modify_scope,json=repeatAppointmentModifyScope,proto3,enum=moego.models.appointment.v1.RepeatAppointmentModifyScope,oneof" json:"repeat_appointment_modify_scope,omitempty"`
}

func (x *CancelAppointmentRequest) Reset() {
	*x = CancelAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelAppointmentRequest) ProtoMessage() {}

func (x *CancelAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelAppointmentRequest.ProtoReflect.Descriptor instead.
func (*CancelAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{30}
}

func (x *CancelAppointmentRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *CancelAppointmentRequest) GetCancelByType() v1.AppointmentUpdatedBy {
	if x != nil {
		return x.CancelByType
	}
	return v1.AppointmentUpdatedBy(0)
}

func (x *CancelAppointmentRequest) GetCancelBy() int64 {
	if x != nil {
		return x.CancelBy
	}
	return 0
}

func (x *CancelAppointmentRequest) GetCancelReason() string {
	if x != nil && x.CancelReason != nil {
		return *x.CancelReason
	}
	return ""
}

func (x *CancelAppointmentRequest) GetNoShow() v1.AppointmentNoShowStatus {
	if x != nil {
		return x.NoShow
	}
	return v1.AppointmentNoShowStatus(0)
}

func (x *CancelAppointmentRequest) GetRepeatAppointmentModifyScope() v1.RepeatAppointmentModifyScope {
	if x != nil && x.RepeatAppointmentModifyScope != nil {
		return *x.RepeatAppointmentModifyScope
	}
	return v1.RepeatAppointmentModifyScope(0)
}

// Cancel appointment response
type CancelAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelAppointmentResponse) Reset() {
	*x = CancelAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelAppointmentResponse) ProtoMessage() {}

func (x *CancelAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelAppointmentResponse.ProtoReflect.Descriptor instead.
func (*CancelAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{31}
}

// Batch book again appointment
type BatchBookAgainAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment ids
	AppointmentIds []int64 `protobuf:"varint,2,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
	// Target date
	TargetDate *date.Date `protobuf:"bytes,3,opt,name=target_date,json=targetDate,proto3" json:"target_date,omitempty"`
	// operate staff id
	RebookBy int64 `protobuf:"varint,4,opt,name=rebook_by,json=rebookBy,proto3" json:"rebook_by,omitempty"`
}

func (x *BatchBookAgainAppointmentRequest) Reset() {
	*x = BatchBookAgainAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchBookAgainAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchBookAgainAppointmentRequest) ProtoMessage() {}

func (x *BatchBookAgainAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchBookAgainAppointmentRequest.ProtoReflect.Descriptor instead.
func (*BatchBookAgainAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{32}
}

func (x *BatchBookAgainAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchBookAgainAppointmentRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

func (x *BatchBookAgainAppointmentRequest) GetTargetDate() *date.Date {
	if x != nil {
		return x.TargetDate
	}
	return nil
}

func (x *BatchBookAgainAppointmentRequest) GetRebookBy() int64 {
	if x != nil {
		return x.RebookBy
	}
	return 0
}

// Batch book again appointment
type BatchBookAgainAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// book again appointment detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *BatchBookAgainAppointmentResponse) Reset() {
	*x = BatchBookAgainAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchBookAgainAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchBookAgainAppointmentResponse) ProtoMessage() {}

func (x *BatchBookAgainAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchBookAgainAppointmentResponse.ProtoReflect.Descriptor instead.
func (*BatchBookAgainAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{33}
}

func (x *BatchBookAgainAppointmentResponse) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// Batch cancel appointment
type BatchCancelAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment ids
	AppointmentIds []int64 `protobuf:"varint,2,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
	// operate staff id
	CancelBy int64 `protobuf:"varint,3,opt,name=cancel_by,json=cancelBy,proto3" json:"cancel_by,omitempty"`
	// cancel reason, optional
	CancelReason *string `protobuf:"bytes,4,opt,name=cancel_reason,json=cancelReason,proto3,oneof" json:"cancel_reason,omitempty"`
}

func (x *BatchCancelAppointmentRequest) Reset() {
	*x = BatchCancelAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCancelAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCancelAppointmentRequest) ProtoMessage() {}

func (x *BatchCancelAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCancelAppointmentRequest.ProtoReflect.Descriptor instead.
func (*BatchCancelAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{34}
}

func (x *BatchCancelAppointmentRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchCancelAppointmentRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

func (x *BatchCancelAppointmentRequest) GetCancelBy() int64 {
	if x != nil {
		return x.CancelBy
	}
	return 0
}

func (x *BatchCancelAppointmentRequest) GetCancelReason() string {
	if x != nil && x.CancelReason != nil {
		return *x.CancelReason
	}
	return ""
}

// Batch cancel appointment by staff and date result
type BatchCancelAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// canceled appointments detail
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *BatchCancelAppointmentResponse) Reset() {
	*x = BatchCancelAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchCancelAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCancelAppointmentResponse) ProtoMessage() {}

func (x *BatchCancelAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCancelAppointmentResponse.ProtoReflect.Descriptor instead.
func (*BatchCancelAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{35}
}

func (x *BatchCancelAppointmentResponse) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// CountAppointmentForPets request
type CountAppointmentForPetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetIds []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
}

func (x *CountAppointmentForPetsRequest) Reset() {
	*x = CountAppointmentForPetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountAppointmentForPetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountAppointmentForPetsRequest) ProtoMessage() {}

func (x *CountAppointmentForPetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountAppointmentForPetsRequest.ProtoReflect.Descriptor instead.
func (*CountAppointmentForPetsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{36}
}

func (x *CountAppointmentForPetsRequest) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// CountAppointmentForPets response
type CountAppointmentForPetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id -> appointment count
	// 不会出现 request 中的 pet id 不存在于 response 中的情况
	PetIdToAppointmentCount map[int64]int32 `protobuf:"bytes,1,rep,name=pet_id_to_appointment_count,json=petIdToAppointmentCount,proto3" json:"pet_id_to_appointment_count,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *CountAppointmentForPetsResponse) Reset() {
	*x = CountAppointmentForPetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountAppointmentForPetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountAppointmentForPetsResponse) ProtoMessage() {}

func (x *CountAppointmentForPetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountAppointmentForPetsResponse.ProtoReflect.Descriptor instead.
func (*CountAppointmentForPetsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{37}
}

func (x *CountAppointmentForPetsResponse) GetPetIdToAppointmentCount() map[int64]int32 {
	if x != nil {
		return x.PetIdToAppointmentCount
	}
	return nil
}

// DeleteAppointments request
type DeleteAppointmentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id, optional
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// appointment ids
	Ids []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteAppointmentsRequest) Reset() {
	*x = DeleteAppointmentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAppointmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAppointmentsRequest) ProtoMessage() {}

func (x *DeleteAppointmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAppointmentsRequest.ProtoReflect.Descriptor instead.
func (*DeleteAppointmentsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{38}
}

func (x *DeleteAppointmentsRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *DeleteAppointmentsRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *DeleteAppointmentsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// DeleteAppointments response
type DeleteAppointmentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// affected rows count
	AffectedRows int32 `protobuf:"varint,1,opt,name=affected_rows,json=affectedRows,proto3" json:"affected_rows,omitempty"`
}

func (x *DeleteAppointmentsResponse) Reset() {
	*x = DeleteAppointmentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAppointmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAppointmentsResponse) ProtoMessage() {}

func (x *DeleteAppointmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAppointmentsResponse.ProtoReflect.Descriptor instead.
func (*DeleteAppointmentsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{39}
}

func (x *DeleteAppointmentsResponse) GetAffectedRows() int32 {
	if x != nil {
		return x.AffectedRows
	}
	return 0
}

// RestoreAppointments request
type RestoreAppointmentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// business id, optional
	BusinessId *int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// appointment ids
	Ids []int64 `protobuf:"varint,3,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *RestoreAppointmentsRequest) Reset() {
	*x = RestoreAppointmentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RestoreAppointmentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreAppointmentsRequest) ProtoMessage() {}

func (x *RestoreAppointmentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreAppointmentsRequest.ProtoReflect.Descriptor instead.
func (*RestoreAppointmentsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{40}
}

func (x *RestoreAppointmentsRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *RestoreAppointmentsRequest) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *RestoreAppointmentsRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// RestoreAppointments response
type RestoreAppointmentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// affected rows count
	AffectedRows int32 `protobuf:"varint,1,opt,name=affected_rows,json=affectedRows,proto3" json:"affected_rows,omitempty"`
}

func (x *RestoreAppointmentsResponse) Reset() {
	*x = RestoreAppointmentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RestoreAppointmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreAppointmentsResponse) ProtoMessage() {}

func (x *RestoreAppointmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreAppointmentsResponse.ProtoReflect.Descriptor instead.
func (*RestoreAppointmentsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{41}
}

func (x *RestoreAppointmentsResponse) GetAffectedRows() int32 {
	if x != nil {
		return x.AffectedRows
	}
	return 0
}

// RescheduleBoardingAppointment request
type RescheduleBoardingAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id, optional
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// start date, optional
	StartDate *string `protobuf:"bytes,3,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date, optional
	EndDate *string `protobuf:"bytes,4,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
}

func (x *RescheduleBoardingAppointmentRequest) Reset() {
	*x = RescheduleBoardingAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBoardingAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBoardingAppointmentRequest) ProtoMessage() {}

func (x *RescheduleBoardingAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBoardingAppointmentRequest.ProtoReflect.Descriptor instead.
func (*RescheduleBoardingAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{42}
}

func (x *RescheduleBoardingAppointmentRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *RescheduleBoardingAppointmentRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleBoardingAppointmentRequest) GetStartDate() string {
	if x != nil && x.StartDate != nil {
		return *x.StartDate
	}
	return ""
}

func (x *RescheduleBoardingAppointmentRequest) GetEndDate() string {
	if x != nil && x.EndDate != nil {
		return *x.EndDate
	}
	return ""
}

// sync appointment to order, temporary for data fix
type SyncAppointmentToOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *SyncAppointmentToOrderRequest) Reset() {
	*x = SyncAppointmentToOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncAppointmentToOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncAppointmentToOrderRequest) ProtoMessage() {}

func (x *SyncAppointmentToOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncAppointmentToOrderRequest.ProtoReflect.Descriptor instead.
func (*SyncAppointmentToOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{43}
}

func (x *SyncAppointmentToOrderRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// sync appointment to order response
type SyncAppointmentToOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncAppointmentToOrderResponse) Reset() {
	*x = SyncAppointmentToOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncAppointmentToOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncAppointmentToOrderResponse) ProtoMessage() {}

func (x *SyncAppointmentToOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncAppointmentToOrderResponse.ProtoReflect.Descriptor instead.
func (*SyncAppointmentToOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{44}
}

// RescheduleBoardingAppointment response
type RescheduleBoardingAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RescheduleBoardingAppointmentResponse) Reset() {
	*x = RescheduleBoardingAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBoardingAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBoardingAppointmentResponse) ProtoMessage() {}

func (x *RescheduleBoardingAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBoardingAppointmentResponse.ProtoReflect.Descriptor instead.
func (*RescheduleBoardingAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{45}
}

// Preview order detail by appointment id, temporary for data fix
type PreviewOrderDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
}

func (x *PreviewOrderDetailRequest) Reset() {
	*x = PreviewOrderDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewOrderDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewOrderDetailRequest) ProtoMessage() {}

func (x *PreviewOrderDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewOrderDetailRequest.ProtoReflect.Descriptor instead.
func (*PreviewOrderDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{46}
}

func (x *PreviewOrderDetailRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// Preview order detail by appointment id, temporary for data fix
type PreviewOrderDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order detail
	OrderDetails []*v12.OrderDetailModel `protobuf:"bytes,1,rep,name=order_details,json=orderDetails,proto3" json:"order_details,omitempty"`
}

func (x *PreviewOrderDetailResponse) Reset() {
	*x = PreviewOrderDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewOrderDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewOrderDetailResponse) ProtoMessage() {}

func (x *PreviewOrderDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewOrderDetailResponse.ProtoReflect.Descriptor instead.
func (*PreviewOrderDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{47}
}

func (x *PreviewOrderDetailResponse) GetOrderDetails() []*v12.OrderDetailModel {
	if x != nil {
		return x.OrderDetails
	}
	return nil
}

// The request message for list order line items
type PreviewOrderLineItemsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *PreviewOrderLineItemsRequest) Reset() {
	*x = PreviewOrderLineItemsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewOrderLineItemsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewOrderLineItemsRequest) ProtoMessage() {}

func (x *PreviewOrderLineItemsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewOrderLineItemsRequest.ProtoReflect.Descriptor instead.
func (*PreviewOrderLineItemsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{48}
}

func (x *PreviewOrderLineItemsRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// The response message for list order line items
type PreviewOrderLineItemsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet services
	PetServices []*v15.PetService `protobuf:"bytes,1,rep,name=pet_services,json=petServices,proto3" json:"pet_services,omitempty"`
	// surcharges
	Surcharges []*v15.SurchargeItem `protobuf:"bytes,2,rep,name=surcharges,proto3" json:"surcharges,omitempty"`
}

func (x *PreviewOrderLineItemsResponse) Reset() {
	*x = PreviewOrderLineItemsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewOrderLineItemsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewOrderLineItemsResponse) ProtoMessage() {}

func (x *PreviewOrderLineItemsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewOrderLineItemsResponse.ProtoReflect.Descriptor instead.
func (*PreviewOrderLineItemsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{49}
}

func (x *PreviewOrderLineItemsResponse) GetPetServices() []*v15.PetService {
	if x != nil {
		return x.PetServices
	}
	return nil
}

func (x *PreviewOrderLineItemsResponse) GetSurcharges() []*v15.SurchargeItem {
	if x != nil {
		return x.Surcharges
	}
	return nil
}

// Get time overlap appointment list
type GetTimeOverlapAppointmentListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// customer id
	CustomerId []int64 `protobuf:"varint,2,rep,packed,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// pet ids
	PetIds []int64 `protobuf:"varint,3,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// date range filter
	Filter *GetTimeOverlapAppointmentListRequest_Filter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *GetTimeOverlapAppointmentListRequest) Reset() {
	*x = GetTimeOverlapAppointmentListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTimeOverlapAppointmentListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTimeOverlapAppointmentListRequest) ProtoMessage() {}

func (x *GetTimeOverlapAppointmentListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTimeOverlapAppointmentListRequest.ProtoReflect.Descriptor instead.
func (*GetTimeOverlapAppointmentListRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{50}
}

func (x *GetTimeOverlapAppointmentListRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetTimeOverlapAppointmentListRequest) GetCustomerId() []int64 {
	if x != nil {
		return x.CustomerId
	}
	return nil
}

func (x *GetTimeOverlapAppointmentListRequest) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *GetTimeOverlapAppointmentListRequest) GetFilter() *GetTimeOverlapAppointmentListRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// Get time overlap appointment list response
type GetTimeOverlapAppointmentListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment list map, key is pet id
	AppointmentList map[int64]*AppointmentList `protobuf:"bytes,1,rep,name=appointment_list,json=appointmentList,proto3" json:"appointment_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetTimeOverlapAppointmentListResponse) Reset() {
	*x = GetTimeOverlapAppointmentListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTimeOverlapAppointmentListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTimeOverlapAppointmentListResponse) ProtoMessage() {}

func (x *GetTimeOverlapAppointmentListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTimeOverlapAppointmentListResponse.ProtoReflect.Descriptor instead.
func (*GetTimeOverlapAppointmentListResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetTimeOverlapAppointmentListResponse) GetAppointmentList() map[int64]*AppointmentList {
	if x != nil {
		return x.AppointmentList
	}
	return nil
}

// Appointment list
type AppointmentList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment list
	Appointments []*v1.AppointmentModel `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *AppointmentList) Reset() {
	*x = AppointmentList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentList) ProtoMessage() {}

func (x *AppointmentList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentList.ProtoReflect.Descriptor instead.
func (*AppointmentList) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{52}
}

func (x *AppointmentList) GetAppointments() []*v1.AppointmentModel {
	if x != nil {
		return x.Appointments
	}
	return nil
}

// The request message for the PreviewEstimateOrder method.
type PreviewEstimateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The company ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// The business ID
	//
	// Deprecated: Do not use.
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// The appointment ID
	AppointmentIds []int64 `protobuf:"varint,3,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
}

func (x *PreviewEstimateOrderRequest) Reset() {
	*x = PreviewEstimateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewEstimateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewEstimateOrderRequest) ProtoMessage() {}

func (x *PreviewEstimateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewEstimateOrderRequest.ProtoReflect.Descriptor instead.
func (*PreviewEstimateOrderRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{53}
}

func (x *PreviewEstimateOrderRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// Deprecated: Do not use.
func (x *PreviewEstimateOrderRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *PreviewEstimateOrderRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// The response message for the PreviewEstimateOrder method.
type PreviewEstimateOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Estimated order
	EstimatedOrders []*PreviewEstimateOrderResponse_EstimatedOrder `protobuf:"bytes,1,rep,name=estimated_orders,json=estimatedOrders,proto3" json:"estimated_orders,omitempty"`
}

func (x *PreviewEstimateOrderResponse) Reset() {
	*x = PreviewEstimateOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewEstimateOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewEstimateOrderResponse) ProtoMessage() {}

func (x *PreviewEstimateOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewEstimateOrderResponse.ProtoReflect.Descriptor instead.
func (*PreviewEstimateOrderResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{54}
}

func (x *PreviewEstimateOrderResponse) GetEstimatedOrders() []*PreviewEstimateOrderResponse_EstimatedOrder {
	if x != nil {
		return x.EstimatedOrders
	}
	return nil
}

// The request message for the ListExtraInfo method.
type ListExtraInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The appointment ID
	AppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
}

func (x *ListExtraInfoRequest) Reset() {
	*x = ListExtraInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExtraInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExtraInfoRequest) ProtoMessage() {}

func (x *ListExtraInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExtraInfoRequest.ProtoReflect.Descriptor instead.
func (*ListExtraInfoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{55}
}

func (x *ListExtraInfoRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// The response message for the ListExtraInfo method.
type ListExtraInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The extra info
	ExtraInfo []*v1.AppointmentExtraInfoModel `protobuf:"bytes,1,rep,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
}

func (x *ListExtraInfoResponse) Reset() {
	*x = ListExtraInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExtraInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExtraInfoResponse) ProtoMessage() {}

func (x *ListExtraInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExtraInfoResponse.ProtoReflect.Descriptor instead.
func (*ListExtraInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{56}
}

func (x *ListExtraInfoResponse) GetExtraInfo() []*v1.AppointmentExtraInfoModel {
	if x != nil {
		return x.ExtraInfo
	}
	return nil
}

// The request message for the CreateExtraInfo method.
type CreateExtraInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The appointment ID
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// Is new order
	IsNewOrder bool `protobuf:"varint,2,opt,name=is_new_order,json=isNewOrder,proto3" json:"is_new_order,omitempty"`
}

func (x *CreateExtraInfoRequest) Reset() {
	*x = CreateExtraInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExtraInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExtraInfoRequest) ProtoMessage() {}

func (x *CreateExtraInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExtraInfoRequest.ProtoReflect.Descriptor instead.
func (*CreateExtraInfoRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{57}
}

func (x *CreateExtraInfoRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *CreateExtraInfoRequest) GetIsNewOrder() bool {
	if x != nil {
		return x.IsNewOrder
	}
	return false
}

// The response message for the CreateExtraInfo method.
type CreateExtraInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The extra info
	ExtraInfo *v1.AppointmentExtraInfoModel `protobuf:"bytes,1,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
}

func (x *CreateExtraInfoResponse) Reset() {
	*x = CreateExtraInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExtraInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExtraInfoResponse) ProtoMessage() {}

func (x *CreateExtraInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExtraInfoResponse.ProtoReflect.Descriptor instead.
func (*CreateExtraInfoResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{58}
}

func (x *CreateExtraInfoResponse) GetExtraInfo() *v1.AppointmentExtraInfoModel {
	if x != nil {
		return x.ExtraInfo
	}
	return nil
}

// filter
type GetCustomerLastAppointmentRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if no start time or end time, default is before current time
	// start time range
	StartTimeRange *interval.Interval `protobuf:"bytes,1,opt,name=start_time_range,json=startTimeRange,proto3,oneof" json:"start_time_range,omitempty"`
	// end_time_range
	EndTimeRange *interval.Interval `protobuf:"bytes,2,opt,name=end_time_range,json=endTimeRange,proto3,oneof" json:"end_time_range,omitempty"`
	// service item type
	ServiceItemTypes []v13.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// filter no start time, default include
	FilterNoStartTime *bool `protobuf:"varint,8,opt,name=filter_no_start_time,json=filterNoStartTime,proto3,oneof" json:"filter_no_start_time,omitempty"`
	// filter booking request, default include
	FilterBookingRequest *bool `protobuf:"varint,9,opt,name=filter_booking_request,json=filterBookingRequest,proto3,oneof" json:"filter_booking_request,omitempty"`
}

func (x *GetCustomerLastAppointmentRequest_Filter) Reset() {
	*x = GetCustomerLastAppointmentRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomerLastAppointmentRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerLastAppointmentRequest_Filter) ProtoMessage() {}

func (x *GetCustomerLastAppointmentRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerLastAppointmentRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetCustomerLastAppointmentRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *GetCustomerLastAppointmentRequest_Filter) GetStartTimeRange() *interval.Interval {
	if x != nil {
		return x.StartTimeRange
	}
	return nil
}

func (x *GetCustomerLastAppointmentRequest_Filter) GetEndTimeRange() *interval.Interval {
	if x != nil {
		return x.EndTimeRange
	}
	return nil
}

func (x *GetCustomerLastAppointmentRequest_Filter) GetServiceItemTypes() []v13.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *GetCustomerLastAppointmentRequest_Filter) GetFilterNoStartTime() bool {
	if x != nil && x.FilterNoStartTime != nil {
		return *x.FilterNoStartTime
	}
	return false
}

func (x *GetCustomerLastAppointmentRequest_Filter) GetFilterBookingRequest() bool {
	if x != nil && x.FilterBookingRequest != nil {
		return *x.FilterBookingRequest
	}
	return false
}

// filter
type ListAppointmentsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start time range
	StartTimeRange *interval.Interval `protobuf:"bytes,1,opt,name=start_time_range,json=startTimeRange,proto3" json:"start_time_range,omitempty"`
	// end_time_range
	EndTimeRange *interval.Interval `protobuf:"bytes,2,opt,name=end_time_range,json=endTimeRange,proto3" json:"end_time_range,omitempty"`
	// last_updated_time_range
	LastUpdatedTimeRange *interval.Interval `protobuf:"bytes,3,opt,name=last_updated_time_range,json=lastUpdatedTimeRange,proto3" json:"last_updated_time_range,omitempty"`
	// status
	Status []v1.AppointmentStatus `protobuf:"varint,4,rep,packed,name=status,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"status,omitempty"`
	// filter wait list status
	WaitListStatuses []v1.WaitListStatus `protobuf:"varint,6,rep,packed,name=wait_list_statuses,json=waitListStatuses,proto3,enum=moego.models.appointment.v1.WaitListStatus" json:"wait_list_statuses,omitempty"`
	// filter no start time, default include
	FilterNoStartTime *bool `protobuf:"varint,8,opt,name=filter_no_start_time,json=filterNoStartTime,proto3,oneof" json:"filter_no_start_time,omitempty"`
	// filter booking request, default include
	FilterBookingRequest *bool `protobuf:"varint,9,opt,name=filter_booking_request,json=filterBookingRequest,proto3,oneof" json:"filter_booking_request,omitempty"`
	// service type include, If empty, it means all service item types
	// 查询等于这个类型的预约,only bitmap value
	// 如果，要查包含某一个类型，需要通过 ServiceItemEnum.getBitValueListByServiceItem 转化
	ServiceTypeIncludes []int32 `protobuf:"varint,10,rep,packed,name=service_type_includes,json=serviceTypeIncludes,proto3" json:"service_type_includes,omitempty"`
	// customer id
	CustomerIds []int64 `protobuf:"varint,5,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// appointment date type
	DateType *AppointmentDateType `protobuf:"varint,11,opt,name=date_type,json=dateType,proto3,enum=moego.service.appointment.v1.AppointmentDateType,oneof" json:"date_type,omitempty"`
	// staff ids
	StaffIds []int64 `protobuf:"varint,12,rep,packed,name=staff_ids,json=staffIds,proto3" json:"staff_ids,omitempty"`
	// appointment date, in yyyy-MM-dd format
	AppointmentDate *date.Date `protobuf:"bytes,13,opt,name=appointment_date,json=appointmentDate,proto3,oneof" json:"appointment_date,omitempty"`
	// get staff appt by operation detail(multi-staff)
	IncludeServiceOperation *bool `protobuf:"varint,14,opt,name=include_service_operation,json=includeServiceOperation,proto3,oneof" json:"include_service_operation,omitempty"`
	// pet ids
	PetIds []int64 `protobuf:"varint,15,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,16,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// include block
	IncludeBlock *bool `protobuf:"varint,17,opt,name=include_block,json=includeBlock,proto3,oneof" json:"include_block,omitempty"`
	// is_waiting_list
	IsWaitingList *bool `protobuf:"varint,18,opt,name=is_waiting_list,json=isWaitingList,proto3,oneof" json:"is_waiting_list,omitempty"`
	// check in time range
	CheckInTimeRange *interval.Interval `protobuf:"bytes,19,opt,name=check_in_time_range,json=checkInTimeRange,proto3" json:"check_in_time_range,omitempty"`
	// check out time range
	CheckOutTimeRange *interval.Interval `protobuf:"bytes,20,opt,name=check_out_time_range,json=checkOutTimeRange,proto3" json:"check_out_time_range,omitempty"`
	// payment status
	PaymentStatuses []v1.AppointmentPaymentStatus `protobuf:"varint,21,rep,packed,name=payment_statuses,json=paymentStatuses,proto3,enum=moego.models.appointment.v1.AppointmentPaymentStatus" json:"payment_statuses,omitempty"`
}

func (x *ListAppointmentsRequest_Filter) Reset() {
	*x = ListAppointmentsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsRequest_Filter) ProtoMessage() {}

func (x *ListAppointmentsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAppointmentsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ListAppointmentsRequest_Filter) GetStartTimeRange() *interval.Interval {
	if x != nil {
		return x.StartTimeRange
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetEndTimeRange() *interval.Interval {
	if x != nil {
		return x.EndTimeRange
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetLastUpdatedTimeRange() *interval.Interval {
	if x != nil {
		return x.LastUpdatedTimeRange
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetStatus() []v1.AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetWaitListStatuses() []v1.WaitListStatus {
	if x != nil {
		return x.WaitListStatuses
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetFilterNoStartTime() bool {
	if x != nil && x.FilterNoStartTime != nil {
		return *x.FilterNoStartTime
	}
	return false
}

func (x *ListAppointmentsRequest_Filter) GetFilterBookingRequest() bool {
	if x != nil && x.FilterBookingRequest != nil {
		return *x.FilterBookingRequest
	}
	return false
}

func (x *ListAppointmentsRequest_Filter) GetServiceTypeIncludes() []int32 {
	if x != nil {
		return x.ServiceTypeIncludes
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetDateType() AppointmentDateType {
	if x != nil && x.DateType != nil {
		return *x.DateType
	}
	return AppointmentDateType_APPOINTMENT_DATE_TYPE_UNSPECIFIED
}

func (x *ListAppointmentsRequest_Filter) GetStaffIds() []int64 {
	if x != nil {
		return x.StaffIds
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetAppointmentDate() *date.Date {
	if x != nil {
		return x.AppointmentDate
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetIncludeServiceOperation() bool {
	if x != nil && x.IncludeServiceOperation != nil {
		return *x.IncludeServiceOperation
	}
	return false
}

func (x *ListAppointmentsRequest_Filter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetIncludeBlock() bool {
	if x != nil && x.IncludeBlock != nil {
		return *x.IncludeBlock
	}
	return false
}

func (x *ListAppointmentsRequest_Filter) GetIsWaitingList() bool {
	if x != nil && x.IsWaitingList != nil {
		return *x.IsWaitingList
	}
	return false
}

func (x *ListAppointmentsRequest_Filter) GetCheckInTimeRange() *interval.Interval {
	if x != nil {
		return x.CheckInTimeRange
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetCheckOutTimeRange() *interval.Interval {
	if x != nil {
		return x.CheckOutTimeRange
	}
	return nil
}

func (x *ListAppointmentsRequest_Filter) GetPaymentStatuses() []v1.AppointmentPaymentStatus {
	if x != nil {
		return x.PaymentStatuses
	}
	return nil
}

// filter
type ListBlockTimesRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start time range
	StartTimeRange *interval.Interval `protobuf:"bytes,1,opt,name=start_time_range,json=startTimeRange,proto3" json:"start_time_range,omitempty"`
	// end_time_range
	EndTimeRange *interval.Interval `protobuf:"bytes,2,opt,name=end_time_range,json=endTimeRange,proto3" json:"end_time_range,omitempty"`
}

func (x *ListBlockTimesRequest_Filter) Reset() {
	*x = ListBlockTimesRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBlockTimesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBlockTimesRequest_Filter) ProtoMessage() {}

func (x *ListBlockTimesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBlockTimesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListBlockTimesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ListBlockTimesRequest_Filter) GetStartTimeRange() *interval.Interval {
	if x != nil {
		return x.StartTimeRange
	}
	return nil
}

func (x *ListBlockTimesRequest_Filter) GetEndTimeRange() *interval.Interval {
	if x != nil {
		return x.EndTimeRange
	}
	return nil
}

// filter
type ListAppointmentForPetsRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	ServiceId int64 `protobuf:"varint,1,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	// date
	Date *date.Date `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	// business id
	BusinessId *int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3,oneof" json:"business_id,omitempty"`
	// appointment status
	Statuses []v1.AppointmentStatus `protobuf:"varint,4,rep,packed,name=statuses,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"statuses,omitempty"`
}

func (x *ListAppointmentForPetsRequest_Filter) Reset() {
	*x = ListAppointmentForPetsRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentForPetsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentForPetsRequest_Filter) ProtoMessage() {}

func (x *ListAppointmentForPetsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentForPetsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAppointmentForPetsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{26, 0}
}

func (x *ListAppointmentForPetsRequest_Filter) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *ListAppointmentForPetsRequest_Filter) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ListAppointmentForPetsRequest_Filter) GetBusinessId() int64 {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return 0
}

func (x *ListAppointmentForPetsRequest_Filter) GetStatuses() []v1.AppointmentStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

// filter
type ListAppointmentsForCustomersRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment date type
	DateType AppointmentDateType `protobuf:"varint,1,opt,name=date_type,json=dateType,proto3,enum=moego.service.appointment.v1.AppointmentDateType" json:"date_type,omitempty"`
	// appointment status
	Statuses []v1.AppointmentStatus `protobuf:"varint,2,rep,packed,name=statuses,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"statuses,omitempty"`
	// service type include, If empty, it means all service item types
	ServiceTypeIncludes []int32 `protobuf:"varint,10,rep,packed,name=service_type_includes,json=serviceTypeIncludes,proto3" json:"service_type_includes,omitempty"`
}

func (x *ListAppointmentsForCustomersRequest_Filter) Reset() {
	*x = ListAppointmentsForCustomersRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentsForCustomersRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentsForCustomersRequest_Filter) ProtoMessage() {}

func (x *ListAppointmentsForCustomersRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentsForCustomersRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAppointmentsForCustomersRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{28, 0}
}

func (x *ListAppointmentsForCustomersRequest_Filter) GetDateType() AppointmentDateType {
	if x != nil {
		return x.DateType
	}
	return AppointmentDateType_APPOINTMENT_DATE_TYPE_UNSPECIFIED
}

func (x *ListAppointmentsForCustomersRequest_Filter) GetStatuses() []v1.AppointmentStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *ListAppointmentsForCustomersRequest_Filter) GetServiceTypeIncludes() []int32 {
	if x != nil {
		return x.ServiceTypeIncludes
	}
	return nil
}

// Date range filter
type GetTimeOverlapAppointmentListRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date range
	DateRange *interval.Interval `protobuf:"bytes,1,opt,name=date_range,json=dateRange,proto3" json:"date_range,omitempty"`
}

func (x *GetTimeOverlapAppointmentListRequest_Filter) Reset() {
	*x = GetTimeOverlapAppointmentListRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTimeOverlapAppointmentListRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTimeOverlapAppointmentListRequest_Filter) ProtoMessage() {}

func (x *GetTimeOverlapAppointmentListRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTimeOverlapAppointmentListRequest_Filter.ProtoReflect.Descriptor instead.
func (*GetTimeOverlapAppointmentListRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{50, 0}
}

func (x *GetTimeOverlapAppointmentListRequest_Filter) GetDateRange() *interval.Interval {
	if x != nil {
		return x.DateRange
	}
	return nil
}

// The estimated order
type PreviewEstimateOrderResponse_EstimatedOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// The service subtotal
	ServicesSubtotal *money.Money `protobuf:"bytes,2,opt,name=services_subtotal,json=servicesSubtotal,proto3" json:"services_subtotal,omitempty"`
	// The service charges total
	ServicesChargesTotal *money.Money `protobuf:"bytes,3,opt,name=services_charges_total,json=servicesChargesTotal,proto3" json:"services_charges_total,omitempty"`
	// The estimated total
	EstimatedTotal *money.Money `protobuf:"bytes,4,opt,name=estimated_total,json=estimatedTotal,proto3" json:"estimated_total,omitempty"`
}

func (x *PreviewEstimateOrderResponse_EstimatedOrder) Reset() {
	*x = PreviewEstimateOrderResponse_EstimatedOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewEstimateOrderResponse_EstimatedOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewEstimateOrderResponse_EstimatedOrder) ProtoMessage() {}

func (x *PreviewEstimateOrderResponse_EstimatedOrder) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewEstimateOrderResponse_EstimatedOrder.ProtoReflect.Descriptor instead.
func (*PreviewEstimateOrderResponse_EstimatedOrder) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP(), []int{54, 0}
}

func (x *PreviewEstimateOrderResponse_EstimatedOrder) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *PreviewEstimateOrderResponse_EstimatedOrder) GetServicesSubtotal() *money.Money {
	if x != nil {
		return x.ServicesSubtotal
	}
	return nil
}

func (x *PreviewEstimateOrderResponse_EstimatedOrder) GetServicesChargesTotal() *money.Money {
	if x != nil {
		return x.ServicesChargesTotal
	}
	return nil
}

func (x *PreviewEstimateOrderResponse_EstimatedOrder) GetEstimatedTotal() *money.Money {
	if x != nil {
		return x.EstimatedTotal
	}
	return nil
}

var File_moego_service_appointment_v1_appointment_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v1_appointment_service_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x6f,
	0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32,
	0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xdf, 0x04, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x5d, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x5d, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42,
	0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x53,
	0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x41, 0x75,
	0x74, 0x68, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x08, 0x01, 0x48, 0x00, 0x52, 0x07, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x88, 0x01, 0x01, 0x12, 0x5e, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01,
	0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x6e, 0x6f,
	0x74, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x01, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x72,
	0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x22, 0x42, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x9f, 0x02, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a,
	0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x22, 0x1b, 0x0a, 0x19, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x20, 0x00, 0x40,
	0x01, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x20, 0x00, 0x40, 0x01, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x69, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x22, 0xa4, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x33, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01,
	0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x09, 0x18, 0x01, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x6f, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xfb, 0x05, 0x0a, 0x21,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c,
	0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x63, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x4c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x01, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a, 0xb6, 0x03, 0x0a, 0x06, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x40, 0x0a, 0x0e, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x01, 0x52, 0x0c, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a,
	0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x02, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4e, 0x6f,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x39, 0x0a, 0x16,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x03, 0x52, 0x14,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42,
	0x17, 0x0a, 0x15, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xbb, 0x02, 0x0a, 0x22, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x99, 0x01, 0x0a, 0x19, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x5d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c,
	0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c,
	0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x17, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x79, 0x0a, 0x1c,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf9, 0x01, 0x0a, 0x22, 0x43, 0x61, 0x6c, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5d,
	0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x11,
	0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x49, 0x64, 0x22, 0x5c, 0x0a, 0x23, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x05, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x66, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x22, 0xce, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x55, 0x0a,
	0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x01, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x23, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x02, 0x52, 0x05, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x69, 0x64, 0x22, 0x61, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x22, 0xd6, 0x03, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x43, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2,
	0x01, 0x02, 0x08, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x3f, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x10, 0x52, 0x09, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x25,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x8d, 0x10, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x33, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x08,
	0x00, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x54, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x09,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x73, 0x12, 0x88, 0x01, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01,
	0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x11, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x1a, 0xcd, 0x0b,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x0e, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4c, 0x0a, 0x17, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x14,
	0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6a, 0x0a, 0x12,
	0x77, 0x61, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x18, 0x01, 0x22,
	0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x10, 0x77, 0x61, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x14, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x4e, 0x6f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x39,
	0x0a, 0x16, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01,
	0x52, 0x14, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3e, 0x0a, 0x15, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04,
	0x08, 0x00, 0x18, 0x01, 0x52, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x0c, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x5f,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00,
	0x48, 0x02, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x2e, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x10, 0xf4, 0x03, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x12,
	0x41, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x03, 0x52, 0x0f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x3f, 0x0a, 0x19, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x17, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x31,
	0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x10, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x10, 0x64, 0x18, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x28, 0x0a, 0x0d, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x48, 0x05, 0x52, 0x0c, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0f, 0x69,
	0x73, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x08, 0x48, 0x06, 0x52, 0x0d, 0x69, 0x73, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x88, 0x01, 0x01, 0x12, 0x44, 0x0a, 0x13, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x10, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x46,
	0x0a, 0x14, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6f, 0x75, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x52, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x6a, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18,
	0x01, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x6f,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x69, 0x73,
	0x5f, 0x77, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x3f, 0x0a,
	0x11, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x4e, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44,
	0x5f, 0x55, 0x4e, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x10, 0x01, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x16, 0x0a,
	0x14, 0x5f, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x51, 0x0a, 0x0c, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xcd, 0x04,
	0x0a, 0x28, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6d, 0x0a, 0x0b, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46,
	0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x5d, 0x0a, 0x0b, 0x70, 0x65, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01,
	0x0b, 0x08, 0x01, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5e, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x4e, 0x6f, 0x74, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x11,
	0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x00, 0x10, 0x64, 0x22, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x35,
	0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x1a, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03,
	0x18, 0xff, 0x01, 0x52, 0x18, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x52, 0x0a,
	0x29, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0xd1, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01,
	0x0a, 0x08, 0x01, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x52, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x86, 0x01, 0x0a,
	0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0c, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x66, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4c, 0x0a, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0xea, 0x02,
	0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0a,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x03, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x08, 0x01, 0x10,
	0x64, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x19, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x15, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x36, 0x0a, 0x17, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x15, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0xc4, 0x11, 0x0a, 0x21, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x48, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d,
	0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x48, 0x00, 0x52, 0x0f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x45, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x48, 0x01, 0x52,
	0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b,
	0x28, 0x00, 0x48, 0x02, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x0e, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x03, 0x52, 0x0d,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x30, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00,
	0x48, 0x04, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x32, 0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6f, 0x75, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x28, 0x00, 0x48, 0x05, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x4f, 0x75, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x06, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x18, 0x14, 0x48, 0x07, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x32, 0x0a, 0x0e, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x08, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x09, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x28, 0x00, 0x48, 0x09, 0x52, 0x08, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x42, 0x79, 0x88, 0x01, 0x01, 0x12, 0x34, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x48, 0x0a, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x48,
	0x0b, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x42, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x71, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65,
	0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x0c, 0x52, 0x13, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x6d, 0x0a, 0x13, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x0d, 0x52, 0x11, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x64, 0x79, 0x88, 0x01,
	0x01, 0x12, 0x6f, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x0e, 0x52, 0x12, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x88,
	0x01, 0x01, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x0f, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x48, 0x10, 0x52, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x11, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x0a, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x28, 0x00, 0x48, 0x12, 0x52, 0x09, 0x72, 0x65, 0x61, 0x64, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x6b, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x13, 0x52, 0x0d,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x75, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01,
	0x48, 0x14, 0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a, 0x0b, 0x70, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x00, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x66, 0x0a, 0x0e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x48, 0x15, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61, 0x74, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x02, 0x18, 0x01, 0x48, 0x16, 0x52, 0x0b, 0x69, 0x73, 0x44, 0x65, 0x70, 0x72, 0x65,
	0x63, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x17, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x19, 0x0a, 0x17,
	0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x6f, 0x75, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0c, 0x0a, 0x0a,
	0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x5f, 0x62, 0x79, 0x42, 0x18, 0x0a,
	0x16, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x42,
	0x17, 0x0a, 0x15, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x11,
	0x0a, 0x0f, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x70, 0x72, 0x65, 0x63, 0x61,
	0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22,
	0x49, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x6f, 0x77, 0x73, 0x22, 0xcc, 0x03, 0x0a, 0x1d, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f,
	0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x01, 0x10,
	0xe8, 0x07, 0x18, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x64, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xee, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x20, 0x00,
	0x40, 0x01, 0x52, 0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2d,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x22, 0xfe, 0x02, 0x0a, 0x1e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72,
	0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x9b, 0x01, 0x0a, 0x1d, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x64, 0x54, 0x6f, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x18, 0x70, 0x65, 0x74, 0x49, 0x64, 0x54, 0x6f, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0x6b, 0x0a,
	0x1d, 0x50, 0x65, 0x74, 0x49, 0x64, 0x54, 0x6f, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf8, 0x03, 0x0a, 0x23, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x46,
	0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0c, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x12, 0xfa, 0x42, 0x0f, 0x92, 0x01, 0x0c, 0x08, 0x01, 0x10, 0x64, 0x18, 0x01, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x12, 0x6a, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x85, 0x02,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x5a, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x5f, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x10, 0x0a,
	0x18, 0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x15, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x73, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x00, 0x18, 0x01,
	0x52, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x73, 0x22, 0x79, 0x0a, 0x24, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a,
	0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x22, 0xad, 0x04, 0x0a, 0x18, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x61, 0x0a,
	0x0e, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x24, 0x0a, 0x09, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x42, 0x79, 0x12, 0x33, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x18, 0x80, 0x80, 0x40, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x07, 0x6e,
	0x6f, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x06,
	0x6e, 0x6f, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x91, 0x01, 0x0a, 0x1f, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x70, 0x65, 0x61, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x1c, 0x72, 0x65, 0x70, 0x65, 0x61,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x42, 0x22, 0x0a, 0x20,
	0x5f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x70, 0x65,
	0x22, 0x1b, 0x0a, 0x19, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe0, 0x01,
	0x0a, 0x20, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x41, 0x67, 0x61, 0x69, 0x6e,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xe8, 0x07,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0a,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x09, 0x72, 0x65,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x62, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x72, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x42, 0x79,
	0x22, 0x76, 0x0a, 0x21, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x41, 0x67, 0x61,
	0x69, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xef, 0x01, 0x0a, 0x1d, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e,
	0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x24,
	0x0a, 0x09, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x42, 0x79, 0x12, 0x32, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x72, 0x03, 0x18, 0x80, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x63, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x73, 0x0a, 0x1e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x51, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22,
	0x4a, 0x0a, 0x1e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0x90, 0x4e, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64, 0x73, 0x22, 0x88, 0x02, 0x0a, 0x1f,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x46, 0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x98, 0x01, 0x0a, 0x1b, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x64, 0x54, 0x6f, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x17, 0x70, 0x65, 0x74, 0x49, 0x64, 0x54, 0x6f, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x4a, 0x0a, 0x1c, 0x50, 0x65,
	0x74, 0x49, 0x64, 0x54, 0x6f, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb9, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x01, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x21, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0f, 0xfa,
	0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0x90, 0x4e, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x22, 0x41, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x6f, 0x77,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x52, 0x6f, 0x77, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x2d, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48,
	0x01, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x12, 0x21, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0f, 0xfa,
	0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0x90, 0x4e, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x22, 0x42, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x6f,
	0x77, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x52, 0x6f, 0x77, 0x73, 0x22, 0xa6, 0x02, 0x0a, 0x24, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x18, 0xfa, 0x42, 0x15, 0x72, 0x13, 0x32, 0x11, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c,
	0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x48, 0x01, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x38, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0xfa, 0x42,
	0x15, 0x72, 0x13, 0x32, 0x11, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x48, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22,
	0x4f, 0x0a, 0x1d, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x20, 0x0a, 0x1e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x27, 0x0a, 0x25, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x57, 0x0a, 0x19, 0x50,
	0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x22, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x73, 0x22, 0x6a, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4c, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x4e, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0xb7, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x4a,
	0x0a, 0x0a, 0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a,
	0x73, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22, 0xc7, 0x02, 0x0a, 0x24, 0x47,
	0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x0b, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x07, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09,
	0x92, 0x01, 0x06, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x61, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x1a, 0x3e, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x34,
	0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x22, 0xa0, 0x02, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x83,
	0x01, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x1a, 0x71, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x64, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xa6, 0x01,
	0x0a, 0x1b, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0f, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xe8, 0x07,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x96, 0x03, 0x0a, 0x1c, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x10, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x0f, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x1a, 0xff, 0x01,
	0x0a, 0x0e, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x5f, 0x73, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x53, 0x75, 0x62, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x48, 0x0a, 0x16, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x0f, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0e, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x52, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x73, 0x22, 0x6e, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x0a,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x6a, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0c, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22,
	0x70, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x0a, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x2a, 0x83, 0x01, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x50, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x08, 0x0a, 0x04, 0x4c, 0x41, 0x53, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x45,
	0x58, 0x54, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x4f, 0x44, 0x41, 0x59, 0x10, 0x03, 0x12,
	0x10, 0x0a, 0x0c, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x44, 0x10,
	0x04, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x45, 0x58, 0x54, 0x5f, 0x41, 0x46, 0x54, 0x45, 0x52, 0x5f,
	0x54, 0x4f, 0x44, 0x41, 0x59, 0x10, 0x05, 0x32, 0x9e, 0x21, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x84,
	0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9f, 0x01, 0x0a,
	0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9f, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4c,
	0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x4c, 0x61, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa2, 0x01, 0x0a, 0x1b, 0x43, 0x61, 0x6c, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x76, 0x6f, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x76,
	0x6f, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x99, 0x01, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x72, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01, 0x0a,
	0x10, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7b, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb4, 0x01,
	0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x12, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x4f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75,
	0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51,
	0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46,
	0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0xa5, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x12, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x11, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x9c, 0x01, 0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x41, 0x67,
	0x61, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x42, 0x6f, 0x6f, 0x6b, 0x41, 0x67, 0x61, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x93, 0x01, 0x0a, 0x16, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x17, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x74,
	0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x46, 0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x46,
	0x6f, 0x72, 0x50, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87,
	0x01, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa8, 0x01, 0x0a, 0x1d, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x93, 0x01, 0x0a, 0x16, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x12, 0x50, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x37, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65,
	0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x90, 0x01, 0x0a, 0x15, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65,
	0x77, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4c, 0x69, 0x6e, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xa8, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f,
	0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x6c, 0x61, 0x70, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x4f, 0x76, 0x65, 0x72, 0x6c, 0x61, 0x70, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8d,
	0x01, 0x0a, 0x14, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x78,
	0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7e, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_appointment_v1_appointment_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v1_appointment_service_proto_rawDescData = file_moego_service_appointment_v1_appointment_service_proto_rawDesc
)

func file_moego_service_appointment_v1_appointment_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v1_appointment_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v1_appointment_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v1_appointment_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v1_appointment_service_proto_rawDescData
}

var file_moego_service_appointment_v1_appointment_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_service_appointment_v1_appointment_service_proto_msgTypes = make([]protoimpl.MessageInfo, 70)
var file_moego_service_appointment_v1_appointment_service_proto_goTypes = []interface{}{
	(AppointmentDateType)(0),                          // 0: moego.service.appointment.v1.AppointmentDateType
	(ListAppointmentsRequest_PriorityOrderType)(0),    // 1: moego.service.appointment.v1.ListAppointmentsRequest.PriorityOrderType
	(*CreateAppointmentRequest)(nil),                  // 2: moego.service.appointment.v1.CreateAppointmentRequest
	(*CreateAppointmentResponse)(nil),                 // 3: moego.service.appointment.v1.CreateAppointmentResponse
	(*UpdateAppointmentRequest)(nil),                  // 4: moego.service.appointment.v1.UpdateAppointmentRequest
	(*UpdateAppointmentResponse)(nil),                 // 5: moego.service.appointment.v1.UpdateAppointmentResponse
	(*GetAppointmentRequest)(nil),                     // 6: moego.service.appointment.v1.GetAppointmentRequest
	(*GetAppointmentResponse)(nil),                    // 7: moego.service.appointment.v1.GetAppointmentResponse
	(*GetAppointmentListRequest)(nil),                 // 8: moego.service.appointment.v1.GetAppointmentListRequest
	(*GetAppointmentListResponse)(nil),                // 9: moego.service.appointment.v1.GetAppointmentListResponse
	(*GetCustomerLastAppointmentRequest)(nil),         // 10: moego.service.appointment.v1.GetCustomerLastAppointmentRequest
	(*GetCustomerLastAppointmentResponse)(nil),        // 11: moego.service.appointment.v1.GetCustomerLastAppointmentResponse
	(*CalculateAppointmentInvoiceRequest)(nil),        // 12: moego.service.appointment.v1.CalculateAppointmentInvoiceRequest
	(*CalculateAppointmentInvoiceResponse)(nil),       // 13: moego.service.appointment.v1.CalculateAppointmentInvoiceResponse
	(*GetInProgressAppointmentRequest)(nil),           // 14: moego.service.appointment.v1.GetInProgressAppointmentRequest
	(*GetInProgressAppointmentResponse)(nil),          // 15: moego.service.appointment.v1.GetInProgressAppointmentResponse
	(*CreateBlockRequest)(nil),                        // 16: moego.service.appointment.v1.CreateBlockRequest
	(*CreateBlockResponse)(nil),                       // 17: moego.service.appointment.v1.CreateBlockResponse
	(*ListAppointmentsRequest)(nil),                   // 18: moego.service.appointment.v1.ListAppointmentsRequest
	(*ListAppointmentsResponse)(nil),                  // 19: moego.service.appointment.v1.ListAppointmentsResponse
	(*CreateAppointmentForOnlineBookingRequest)(nil),  // 20: moego.service.appointment.v1.CreateAppointmentForOnlineBookingRequest
	(*CreateAppointmentForOnlineBookingResponse)(nil), // 21: moego.service.appointment.v1.CreateAppointmentForOnlineBookingResponse
	(*ListBlockTimesRequest)(nil),                     // 22: moego.service.appointment.v1.ListBlockTimesRequest
	(*ListBlockTimesResponse)(nil),                    // 23: moego.service.appointment.v1.ListBlockTimesResponse
	(*BatchQuickCheckInRequest)(nil),                  // 24: moego.service.appointment.v1.BatchQuickCheckInRequest
	(*BatchQuickCheckInResponse)(nil),                 // 25: moego.service.appointment.v1.BatchQuickCheckInResponse
	(*UpdateAppointmentSelectiveRequest)(nil),         // 26: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest
	(*UpdateAppointmentSelectiveResponse)(nil),        // 27: moego.service.appointment.v1.UpdateAppointmentSelectiveResponse
	(*ListAppointmentForPetsRequest)(nil),             // 28: moego.service.appointment.v1.ListAppointmentForPetsRequest
	(*ListAppointmentForPetsResponse)(nil),            // 29: moego.service.appointment.v1.ListAppointmentForPetsResponse
	(*ListAppointmentsForCustomersRequest)(nil),       // 30: moego.service.appointment.v1.ListAppointmentsForCustomersRequest
	(*ListAppointmentsForCustomersResponse)(nil),      // 31: moego.service.appointment.v1.ListAppointmentsForCustomersResponse
	(*CancelAppointmentRequest)(nil),                  // 32: moego.service.appointment.v1.CancelAppointmentRequest
	(*CancelAppointmentResponse)(nil),                 // 33: moego.service.appointment.v1.CancelAppointmentResponse
	(*BatchBookAgainAppointmentRequest)(nil),          // 34: moego.service.appointment.v1.BatchBookAgainAppointmentRequest
	(*BatchBookAgainAppointmentResponse)(nil),         // 35: moego.service.appointment.v1.BatchBookAgainAppointmentResponse
	(*BatchCancelAppointmentRequest)(nil),             // 36: moego.service.appointment.v1.BatchCancelAppointmentRequest
	(*BatchCancelAppointmentResponse)(nil),            // 37: moego.service.appointment.v1.BatchCancelAppointmentResponse
	(*CountAppointmentForPetsRequest)(nil),            // 38: moego.service.appointment.v1.CountAppointmentForPetsRequest
	(*CountAppointmentForPetsResponse)(nil),           // 39: moego.service.appointment.v1.CountAppointmentForPetsResponse
	(*DeleteAppointmentsRequest)(nil),                 // 40: moego.service.appointment.v1.DeleteAppointmentsRequest
	(*DeleteAppointmentsResponse)(nil),                // 41: moego.service.appointment.v1.DeleteAppointmentsResponse
	(*RestoreAppointmentsRequest)(nil),                // 42: moego.service.appointment.v1.RestoreAppointmentsRequest
	(*RestoreAppointmentsResponse)(nil),               // 43: moego.service.appointment.v1.RestoreAppointmentsResponse
	(*RescheduleBoardingAppointmentRequest)(nil),      // 44: moego.service.appointment.v1.RescheduleBoardingAppointmentRequest
	(*SyncAppointmentToOrderRequest)(nil),             // 45: moego.service.appointment.v1.SyncAppointmentToOrderRequest
	(*SyncAppointmentToOrderResponse)(nil),            // 46: moego.service.appointment.v1.SyncAppointmentToOrderResponse
	(*RescheduleBoardingAppointmentResponse)(nil),     // 47: moego.service.appointment.v1.RescheduleBoardingAppointmentResponse
	(*PreviewOrderDetailRequest)(nil),                 // 48: moego.service.appointment.v1.PreviewOrderDetailRequest
	(*PreviewOrderDetailResponse)(nil),                // 49: moego.service.appointment.v1.PreviewOrderDetailResponse
	(*PreviewOrderLineItemsRequest)(nil),              // 50: moego.service.appointment.v1.PreviewOrderLineItemsRequest
	(*PreviewOrderLineItemsResponse)(nil),             // 51: moego.service.appointment.v1.PreviewOrderLineItemsResponse
	(*GetTimeOverlapAppointmentListRequest)(nil),      // 52: moego.service.appointment.v1.GetTimeOverlapAppointmentListRequest
	(*GetTimeOverlapAppointmentListResponse)(nil),     // 53: moego.service.appointment.v1.GetTimeOverlapAppointmentListResponse
	(*AppointmentList)(nil),                           // 54: moego.service.appointment.v1.AppointmentList
	(*PreviewEstimateOrderRequest)(nil),               // 55: moego.service.appointment.v1.PreviewEstimateOrderRequest
	(*PreviewEstimateOrderResponse)(nil),              // 56: moego.service.appointment.v1.PreviewEstimateOrderResponse
	(*ListExtraInfoRequest)(nil),                      // 57: moego.service.appointment.v1.ListExtraInfoRequest
	(*ListExtraInfoResponse)(nil),                     // 58: moego.service.appointment.v1.ListExtraInfoResponse
	(*CreateExtraInfoRequest)(nil),                    // 59: moego.service.appointment.v1.CreateExtraInfoRequest
	(*CreateExtraInfoResponse)(nil),                   // 60: moego.service.appointment.v1.CreateExtraInfoResponse
	(*GetCustomerLastAppointmentRequest_Filter)(nil),  // 61: moego.service.appointment.v1.GetCustomerLastAppointmentRequest.Filter
	nil,                                    // 62: moego.service.appointment.v1.GetCustomerLastAppointmentResponse.CustomerLastAppointmentEntry
	(*ListAppointmentsRequest_Filter)(nil), // 63: moego.service.appointment.v1.ListAppointmentsRequest.Filter
	(*ListBlockTimesRequest_Filter)(nil),   // 64: moego.service.appointment.v1.ListBlockTimesRequest.Filter
	(*ListAppointmentForPetsRequest_Filter)(nil), // 65: moego.service.appointment.v1.ListAppointmentForPetsRequest.Filter
	nil, // 66: moego.service.appointment.v1.ListAppointmentForPetsResponse.PetIdToAppointmentIdListEntry
	(*ListAppointmentsForCustomersRequest_Filter)(nil), // 67: moego.service.appointment.v1.ListAppointmentsForCustomersRequest.Filter
	nil, // 68: moego.service.appointment.v1.CountAppointmentForPetsResponse.PetIdToAppointmentCountEntry
	(*GetTimeOverlapAppointmentListRequest_Filter)(nil), // 69: moego.service.appointment.v1.GetTimeOverlapAppointmentListRequest.Filter
	nil, // 70: moego.service.appointment.v1.GetTimeOverlapAppointmentListResponse.AppointmentListEntry
	(*PreviewEstimateOrderResponse_EstimatedOrder)(nil), // 71: moego.service.appointment.v1.PreviewEstimateOrderResponse.EstimatedOrder
	(*v1.AppointmentCreateDef)(nil),                     // 72: moego.models.appointment.v1.AppointmentCreateDef
	(*v1.PetDetailDef)(nil),                             // 73: moego.models.appointment.v1.PetDetailDef
	(*v11.PreAuthEnableDef)(nil),                        // 74: moego.models.payment.v1.PreAuthEnableDef
	(*v1.AppointmentNoteCreateDef)(nil),                 // 75: moego.models.appointment.v1.AppointmentNoteCreateDef
	(*timestamppb.Timestamp)(nil),                       // 76: google.protobuf.Timestamp
	(*v1.AppointmentUpdateDef)(nil),                     // 77: moego.models.appointment.v1.AppointmentUpdateDef
	(*v1.AppointmentModel)(nil),                         // 78: moego.models.appointment.v1.AppointmentModel
	(v1.AppointmentStatus)(0),                           // 79: moego.models.appointment.v1.AppointmentStatus
	(*v12.OrderDef)(nil),                                // 80: moego.models.order.v1.OrderDef
	(v13.ServiceItemType)(0),                            // 81: moego.models.offering.v1.ServiceItemType
	(v1.AppointmentSource)(0),                           // 82: moego.models.appointment.v1.AppointmentSource
	(*v2.PaginationRequest)(nil),                        // 83: moego.utils.v2.PaginationRequest
	(*v2.OrderBy)(nil),                                  // 84: moego.utils.v2.OrderBy
	(*v2.PaginationResponse)(nil),                       // 85: moego.utils.v2.PaginationResponse
	(*v1.AppointmentCreateForOnlineBookingDef)(nil),     // 86: moego.models.appointment.v1.AppointmentCreateForOnlineBookingDef
	(*v1.BlockTimeModel)(nil),                           // 87: moego.models.appointment.v1.BlockTimeModel
	(*date.Date)(nil),                                   // 88: google.type.Date
	(v1.AppointmentPaymentStatus)(0),                    // 89: moego.models.appointment.v1.AppointmentPaymentStatus
	(v1.AppointmentBookOnlineStatus)(0),                 // 90: moego.models.appointment.v1.AppointmentBookOnlineStatus
	(*v1.UpdatePetDetailDef)(nil),                       // 91: moego.models.appointment.v1.UpdatePetDetailDef
	(v1.AppointmentUpdatedBy)(0),                        // 92: moego.models.appointment.v1.AppointmentUpdatedBy
	(v1.AppointmentNoShowStatus)(0),                     // 93: moego.models.appointment.v1.AppointmentNoShowStatus
	(v1.RepeatAppointmentModifyScope)(0),                // 94: moego.models.appointment.v1.RepeatAppointmentModifyScope
	(*v12.OrderDetailModel)(nil),                        // 95: moego.models.order.v1.OrderDetailModel
	(*v15.PetService)(nil),                              // 96: moego.models.fulfillment.v1.PetService
	(*v15.SurchargeItem)(nil),                           // 97: moego.models.fulfillment.v1.SurchargeItem
	(*v1.AppointmentExtraInfoModel)(nil),                // 98: moego.models.appointment.v1.AppointmentExtraInfoModel
	(*interval.Interval)(nil),                           // 99: google.type.Interval
	(v1.WaitListStatus)(0),                              // 100: moego.models.appointment.v1.WaitListStatus
	(*v14.Int64ListValue)(nil),                          // 101: moego.utils.v1.Int64ListValue
	(*money.Money)(nil),                                 // 102: google.type.Money
}
var file_moego_service_appointment_v1_appointment_service_proto_depIdxs = []int32{
	72,  // 0: moego.service.appointment.v1.CreateAppointmentRequest.appointment:type_name -> moego.models.appointment.v1.AppointmentCreateDef
	73,  // 1: moego.service.appointment.v1.CreateAppointmentRequest.pet_details:type_name -> moego.models.appointment.v1.PetDetailDef
	74,  // 2: moego.service.appointment.v1.CreateAppointmentRequest.pre_auth:type_name -> moego.models.payment.v1.PreAuthEnableDef
	75,  // 3: moego.service.appointment.v1.CreateAppointmentRequest.notes:type_name -> moego.models.appointment.v1.AppointmentNoteCreateDef
	76,  // 4: moego.service.appointment.v1.CreateAppointmentRequest.created_at:type_name -> google.protobuf.Timestamp
	77,  // 5: moego.service.appointment.v1.UpdateAppointmentRequest.appointment:type_name -> moego.models.appointment.v1.AppointmentUpdateDef
	78,  // 6: moego.service.appointment.v1.GetAppointmentResponse.appointment:type_name -> moego.models.appointment.v1.AppointmentModel
	78,  // 7: moego.service.appointment.v1.GetAppointmentListResponse.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	79,  // 8: moego.service.appointment.v1.GetCustomerLastAppointmentRequest.status:type_name -> moego.models.appointment.v1.AppointmentStatus
	61,  // 9: moego.service.appointment.v1.GetCustomerLastAppointmentRequest.filter:type_name -> moego.service.appointment.v1.GetCustomerLastAppointmentRequest.Filter
	62,  // 10: moego.service.appointment.v1.GetCustomerLastAppointmentResponse.customer_last_appointment:type_name -> moego.service.appointment.v1.GetCustomerLastAppointmentResponse.CustomerLastAppointmentEntry
	73,  // 11: moego.service.appointment.v1.CalculateAppointmentInvoiceRequest.pet_details:type_name -> moego.models.appointment.v1.PetDetailDef
	80,  // 12: moego.service.appointment.v1.CalculateAppointmentInvoiceResponse.order:type_name -> moego.models.order.v1.OrderDef
	81,  // 13: moego.service.appointment.v1.GetInProgressAppointmentRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	76,  // 14: moego.service.appointment.v1.CreateBlockRequest.start_time:type_name -> google.protobuf.Timestamp
	76,  // 15: moego.service.appointment.v1.CreateBlockRequest.end_time:type_name -> google.protobuf.Timestamp
	82,  // 16: moego.service.appointment.v1.CreateBlockRequest.source:type_name -> moego.models.appointment.v1.AppointmentSource
	83,  // 17: moego.service.appointment.v1.ListAppointmentsRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	63,  // 18: moego.service.appointment.v1.ListAppointmentsRequest.filter:type_name -> moego.service.appointment.v1.ListAppointmentsRequest.Filter
	84,  // 19: moego.service.appointment.v1.ListAppointmentsRequest.order_bys:type_name -> moego.utils.v2.OrderBy
	1,   // 20: moego.service.appointment.v1.ListAppointmentsRequest.priority_order_type:type_name -> moego.service.appointment.v1.ListAppointmentsRequest.PriorityOrderType
	85,  // 21: moego.service.appointment.v1.ListAppointmentsResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	78,  // 22: moego.service.appointment.v1.ListAppointmentsResponse.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	86,  // 23: moego.service.appointment.v1.CreateAppointmentForOnlineBookingRequest.appointment:type_name -> moego.models.appointment.v1.AppointmentCreateForOnlineBookingDef
	73,  // 24: moego.service.appointment.v1.CreateAppointmentForOnlineBookingRequest.pet_details:type_name -> moego.models.appointment.v1.PetDetailDef
	75,  // 25: moego.service.appointment.v1.CreateAppointmentForOnlineBookingRequest.notes:type_name -> moego.models.appointment.v1.AppointmentNoteCreateDef
	64,  // 26: moego.service.appointment.v1.ListBlockTimesRequest.filter:type_name -> moego.service.appointment.v1.ListBlockTimesRequest.Filter
	87,  // 27: moego.service.appointment.v1.ListBlockTimesResponse.block_times:type_name -> moego.models.appointment.v1.BlockTimeModel
	88,  // 28: moego.service.appointment.v1.BatchQuickCheckInRequest.date:type_name -> google.type.Date
	82,  // 29: moego.service.appointment.v1.BatchQuickCheckInRequest.source:type_name -> moego.models.appointment.v1.AppointmentSource
	79,  // 30: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.status_before_checkin:type_name -> moego.models.appointment.v1.AppointmentStatus
	79,  // 31: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.status_before_ready:type_name -> moego.models.appointment.v1.AppointmentStatus
	79,  // 32: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.status_before_finish:type_name -> moego.models.appointment.v1.AppointmentStatus
	76,  // 33: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.update_time:type_name -> google.protobuf.Timestamp
	79,  // 34: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.status:type_name -> moego.models.appointment.v1.AppointmentStatus
	89,  // 35: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.payment_status:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	90,  // 36: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.book_online_status:type_name -> moego.models.appointment.v1.AppointmentBookOnlineStatus
	91,  // 37: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.pet_details:type_name -> moego.models.appointment.v1.UpdatePetDetailDef
	92,  // 38: moego.service.appointment.v1.UpdateAppointmentSelectiveRequest.update_by_type:type_name -> moego.models.appointment.v1.AppointmentUpdatedBy
	65,  // 39: moego.service.appointment.v1.ListAppointmentForPetsRequest.filter:type_name -> moego.service.appointment.v1.ListAppointmentForPetsRequest.Filter
	78,  // 40: moego.service.appointment.v1.ListAppointmentForPetsResponse.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	66,  // 41: moego.service.appointment.v1.ListAppointmentForPetsResponse.pet_id_to_appointment_id_list:type_name -> moego.service.appointment.v1.ListAppointmentForPetsResponse.PetIdToAppointmentIdListEntry
	67,  // 42: moego.service.appointment.v1.ListAppointmentsForCustomersRequest.filter:type_name -> moego.service.appointment.v1.ListAppointmentsForCustomersRequest.Filter
	78,  // 43: moego.service.appointment.v1.ListAppointmentsForCustomersResponse.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	92,  // 44: moego.service.appointment.v1.CancelAppointmentRequest.cancel_by_type:type_name -> moego.models.appointment.v1.AppointmentUpdatedBy
	93,  // 45: moego.service.appointment.v1.CancelAppointmentRequest.no_show:type_name -> moego.models.appointment.v1.AppointmentNoShowStatus
	94,  // 46: moego.service.appointment.v1.CancelAppointmentRequest.repeat_appointment_modify_scope:type_name -> moego.models.appointment.v1.RepeatAppointmentModifyScope
	88,  // 47: moego.service.appointment.v1.BatchBookAgainAppointmentRequest.target_date:type_name -> google.type.Date
	78,  // 48: moego.service.appointment.v1.BatchBookAgainAppointmentResponse.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	78,  // 49: moego.service.appointment.v1.BatchCancelAppointmentResponse.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	68,  // 50: moego.service.appointment.v1.CountAppointmentForPetsResponse.pet_id_to_appointment_count:type_name -> moego.service.appointment.v1.CountAppointmentForPetsResponse.PetIdToAppointmentCountEntry
	95,  // 51: moego.service.appointment.v1.PreviewOrderDetailResponse.order_details:type_name -> moego.models.order.v1.OrderDetailModel
	96,  // 52: moego.service.appointment.v1.PreviewOrderLineItemsResponse.pet_services:type_name -> moego.models.fulfillment.v1.PetService
	97,  // 53: moego.service.appointment.v1.PreviewOrderLineItemsResponse.surcharges:type_name -> moego.models.fulfillment.v1.SurchargeItem
	69,  // 54: moego.service.appointment.v1.GetTimeOverlapAppointmentListRequest.filter:type_name -> moego.service.appointment.v1.GetTimeOverlapAppointmentListRequest.Filter
	70,  // 55: moego.service.appointment.v1.GetTimeOverlapAppointmentListResponse.appointment_list:type_name -> moego.service.appointment.v1.GetTimeOverlapAppointmentListResponse.AppointmentListEntry
	78,  // 56: moego.service.appointment.v1.AppointmentList.appointments:type_name -> moego.models.appointment.v1.AppointmentModel
	71,  // 57: moego.service.appointment.v1.PreviewEstimateOrderResponse.estimated_orders:type_name -> moego.service.appointment.v1.PreviewEstimateOrderResponse.EstimatedOrder
	98,  // 58: moego.service.appointment.v1.ListExtraInfoResponse.extra_info:type_name -> moego.models.appointment.v1.AppointmentExtraInfoModel
	98,  // 59: moego.service.appointment.v1.CreateExtraInfoResponse.extra_info:type_name -> moego.models.appointment.v1.AppointmentExtraInfoModel
	99,  // 60: moego.service.appointment.v1.GetCustomerLastAppointmentRequest.Filter.start_time_range:type_name -> google.type.Interval
	99,  // 61: moego.service.appointment.v1.GetCustomerLastAppointmentRequest.Filter.end_time_range:type_name -> google.type.Interval
	81,  // 62: moego.service.appointment.v1.GetCustomerLastAppointmentRequest.Filter.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	78,  // 63: moego.service.appointment.v1.GetCustomerLastAppointmentResponse.CustomerLastAppointmentEntry.value:type_name -> moego.models.appointment.v1.AppointmentModel
	99,  // 64: moego.service.appointment.v1.ListAppointmentsRequest.Filter.start_time_range:type_name -> google.type.Interval
	99,  // 65: moego.service.appointment.v1.ListAppointmentsRequest.Filter.end_time_range:type_name -> google.type.Interval
	99,  // 66: moego.service.appointment.v1.ListAppointmentsRequest.Filter.last_updated_time_range:type_name -> google.type.Interval
	79,  // 67: moego.service.appointment.v1.ListAppointmentsRequest.Filter.status:type_name -> moego.models.appointment.v1.AppointmentStatus
	100, // 68: moego.service.appointment.v1.ListAppointmentsRequest.Filter.wait_list_statuses:type_name -> moego.models.appointment.v1.WaitListStatus
	0,   // 69: moego.service.appointment.v1.ListAppointmentsRequest.Filter.date_type:type_name -> moego.service.appointment.v1.AppointmentDateType
	88,  // 70: moego.service.appointment.v1.ListAppointmentsRequest.Filter.appointment_date:type_name -> google.type.Date
	99,  // 71: moego.service.appointment.v1.ListAppointmentsRequest.Filter.check_in_time_range:type_name -> google.type.Interval
	99,  // 72: moego.service.appointment.v1.ListAppointmentsRequest.Filter.check_out_time_range:type_name -> google.type.Interval
	89,  // 73: moego.service.appointment.v1.ListAppointmentsRequest.Filter.payment_statuses:type_name -> moego.models.appointment.v1.AppointmentPaymentStatus
	99,  // 74: moego.service.appointment.v1.ListBlockTimesRequest.Filter.start_time_range:type_name -> google.type.Interval
	99,  // 75: moego.service.appointment.v1.ListBlockTimesRequest.Filter.end_time_range:type_name -> google.type.Interval
	88,  // 76: moego.service.appointment.v1.ListAppointmentForPetsRequest.Filter.date:type_name -> google.type.Date
	79,  // 77: moego.service.appointment.v1.ListAppointmentForPetsRequest.Filter.statuses:type_name -> moego.models.appointment.v1.AppointmentStatus
	101, // 78: moego.service.appointment.v1.ListAppointmentForPetsResponse.PetIdToAppointmentIdListEntry.value:type_name -> moego.utils.v1.Int64ListValue
	0,   // 79: moego.service.appointment.v1.ListAppointmentsForCustomersRequest.Filter.date_type:type_name -> moego.service.appointment.v1.AppointmentDateType
	79,  // 80: moego.service.appointment.v1.ListAppointmentsForCustomersRequest.Filter.statuses:type_name -> moego.models.appointment.v1.AppointmentStatus
	99,  // 81: moego.service.appointment.v1.GetTimeOverlapAppointmentListRequest.Filter.date_range:type_name -> google.type.Interval
	54,  // 82: moego.service.appointment.v1.GetTimeOverlapAppointmentListResponse.AppointmentListEntry.value:type_name -> moego.service.appointment.v1.AppointmentList
	102, // 83: moego.service.appointment.v1.PreviewEstimateOrderResponse.EstimatedOrder.services_subtotal:type_name -> google.type.Money
	102, // 84: moego.service.appointment.v1.PreviewEstimateOrderResponse.EstimatedOrder.services_charges_total:type_name -> google.type.Money
	102, // 85: moego.service.appointment.v1.PreviewEstimateOrderResponse.EstimatedOrder.estimated_total:type_name -> google.type.Money
	2,   // 86: moego.service.appointment.v1.AppointmentService.CreateAppointment:input_type -> moego.service.appointment.v1.CreateAppointmentRequest
	4,   // 87: moego.service.appointment.v1.AppointmentService.UpdateAppointment:input_type -> moego.service.appointment.v1.UpdateAppointmentRequest
	26,  // 88: moego.service.appointment.v1.AppointmentService.UpdateAppointmentSelective:input_type -> moego.service.appointment.v1.UpdateAppointmentSelectiveRequest
	6,   // 89: moego.service.appointment.v1.AppointmentService.GetAppointment:input_type -> moego.service.appointment.v1.GetAppointmentRequest
	8,   // 90: moego.service.appointment.v1.AppointmentService.GetAppointmentList:input_type -> moego.service.appointment.v1.GetAppointmentListRequest
	10,  // 91: moego.service.appointment.v1.AppointmentService.GetCustomerLastAppointment:input_type -> moego.service.appointment.v1.GetCustomerLastAppointmentRequest
	12,  // 92: moego.service.appointment.v1.AppointmentService.CalculateAppointmentInvoice:input_type -> moego.service.appointment.v1.CalculateAppointmentInvoiceRequest
	14,  // 93: moego.service.appointment.v1.AppointmentService.GetInProgressAppointment:input_type -> moego.service.appointment.v1.GetInProgressAppointmentRequest
	16,  // 94: moego.service.appointment.v1.AppointmentService.CreateBlock:input_type -> moego.service.appointment.v1.CreateBlockRequest
	18,  // 95: moego.service.appointment.v1.AppointmentService.ListAppointments:input_type -> moego.service.appointment.v1.ListAppointmentsRequest
	22,  // 96: moego.service.appointment.v1.AppointmentService.ListBlockTimes:input_type -> moego.service.appointment.v1.ListBlockTimesRequest
	20,  // 97: moego.service.appointment.v1.AppointmentService.CreateAppointmentForOnlineBooking:input_type -> moego.service.appointment.v1.CreateAppointmentForOnlineBookingRequest
	24,  // 98: moego.service.appointment.v1.AppointmentService.BatchQuickCheckIn:input_type -> moego.service.appointment.v1.BatchQuickCheckInRequest
	28,  // 99: moego.service.appointment.v1.AppointmentService.ListAppointmentForPets:input_type -> moego.service.appointment.v1.ListAppointmentForPetsRequest
	30,  // 100: moego.service.appointment.v1.AppointmentService.ListAppointmentsForCustomers:input_type -> moego.service.appointment.v1.ListAppointmentsForCustomersRequest
	32,  // 101: moego.service.appointment.v1.AppointmentService.CancelAppointment:input_type -> moego.service.appointment.v1.CancelAppointmentRequest
	34,  // 102: moego.service.appointment.v1.AppointmentService.BatchBookAgainAppointment:input_type -> moego.service.appointment.v1.BatchBookAgainAppointmentRequest
	36,  // 103: moego.service.appointment.v1.AppointmentService.BatchCancelAppointment:input_type -> moego.service.appointment.v1.BatchCancelAppointmentRequest
	38,  // 104: moego.service.appointment.v1.AppointmentService.CountAppointmentForPets:input_type -> moego.service.appointment.v1.CountAppointmentForPetsRequest
	40,  // 105: moego.service.appointment.v1.AppointmentService.DeleteAppointments:input_type -> moego.service.appointment.v1.DeleteAppointmentsRequest
	42,  // 106: moego.service.appointment.v1.AppointmentService.RestoreAppointments:input_type -> moego.service.appointment.v1.RestoreAppointmentsRequest
	44,  // 107: moego.service.appointment.v1.AppointmentService.RescheduleBoardingAppointment:input_type -> moego.service.appointment.v1.RescheduleBoardingAppointmentRequest
	45,  // 108: moego.service.appointment.v1.AppointmentService.SyncAppointmentToOrder:input_type -> moego.service.appointment.v1.SyncAppointmentToOrderRequest
	48,  // 109: moego.service.appointment.v1.AppointmentService.PreviewOrderDetail:input_type -> moego.service.appointment.v1.PreviewOrderDetailRequest
	50,  // 110: moego.service.appointment.v1.AppointmentService.PreviewOrderLineItems:input_type -> moego.service.appointment.v1.PreviewOrderLineItemsRequest
	52,  // 111: moego.service.appointment.v1.AppointmentService.GetTimeOverlapAppointmentList:input_type -> moego.service.appointment.v1.GetTimeOverlapAppointmentListRequest
	55,  // 112: moego.service.appointment.v1.AppointmentService.PreviewEstimateOrder:input_type -> moego.service.appointment.v1.PreviewEstimateOrderRequest
	57,  // 113: moego.service.appointment.v1.AppointmentService.ListExtraInfo:input_type -> moego.service.appointment.v1.ListExtraInfoRequest
	59,  // 114: moego.service.appointment.v1.AppointmentService.CreateExtraInfo:input_type -> moego.service.appointment.v1.CreateExtraInfoRequest
	3,   // 115: moego.service.appointment.v1.AppointmentService.CreateAppointment:output_type -> moego.service.appointment.v1.CreateAppointmentResponse
	5,   // 116: moego.service.appointment.v1.AppointmentService.UpdateAppointment:output_type -> moego.service.appointment.v1.UpdateAppointmentResponse
	27,  // 117: moego.service.appointment.v1.AppointmentService.UpdateAppointmentSelective:output_type -> moego.service.appointment.v1.UpdateAppointmentSelectiveResponse
	7,   // 118: moego.service.appointment.v1.AppointmentService.GetAppointment:output_type -> moego.service.appointment.v1.GetAppointmentResponse
	9,   // 119: moego.service.appointment.v1.AppointmentService.GetAppointmentList:output_type -> moego.service.appointment.v1.GetAppointmentListResponse
	11,  // 120: moego.service.appointment.v1.AppointmentService.GetCustomerLastAppointment:output_type -> moego.service.appointment.v1.GetCustomerLastAppointmentResponse
	13,  // 121: moego.service.appointment.v1.AppointmentService.CalculateAppointmentInvoice:output_type -> moego.service.appointment.v1.CalculateAppointmentInvoiceResponse
	15,  // 122: moego.service.appointment.v1.AppointmentService.GetInProgressAppointment:output_type -> moego.service.appointment.v1.GetInProgressAppointmentResponse
	17,  // 123: moego.service.appointment.v1.AppointmentService.CreateBlock:output_type -> moego.service.appointment.v1.CreateBlockResponse
	19,  // 124: moego.service.appointment.v1.AppointmentService.ListAppointments:output_type -> moego.service.appointment.v1.ListAppointmentsResponse
	23,  // 125: moego.service.appointment.v1.AppointmentService.ListBlockTimes:output_type -> moego.service.appointment.v1.ListBlockTimesResponse
	21,  // 126: moego.service.appointment.v1.AppointmentService.CreateAppointmentForOnlineBooking:output_type -> moego.service.appointment.v1.CreateAppointmentForOnlineBookingResponse
	25,  // 127: moego.service.appointment.v1.AppointmentService.BatchQuickCheckIn:output_type -> moego.service.appointment.v1.BatchQuickCheckInResponse
	29,  // 128: moego.service.appointment.v1.AppointmentService.ListAppointmentForPets:output_type -> moego.service.appointment.v1.ListAppointmentForPetsResponse
	31,  // 129: moego.service.appointment.v1.AppointmentService.ListAppointmentsForCustomers:output_type -> moego.service.appointment.v1.ListAppointmentsForCustomersResponse
	33,  // 130: moego.service.appointment.v1.AppointmentService.CancelAppointment:output_type -> moego.service.appointment.v1.CancelAppointmentResponse
	35,  // 131: moego.service.appointment.v1.AppointmentService.BatchBookAgainAppointment:output_type -> moego.service.appointment.v1.BatchBookAgainAppointmentResponse
	37,  // 132: moego.service.appointment.v1.AppointmentService.BatchCancelAppointment:output_type -> moego.service.appointment.v1.BatchCancelAppointmentResponse
	39,  // 133: moego.service.appointment.v1.AppointmentService.CountAppointmentForPets:output_type -> moego.service.appointment.v1.CountAppointmentForPetsResponse
	41,  // 134: moego.service.appointment.v1.AppointmentService.DeleteAppointments:output_type -> moego.service.appointment.v1.DeleteAppointmentsResponse
	43,  // 135: moego.service.appointment.v1.AppointmentService.RestoreAppointments:output_type -> moego.service.appointment.v1.RestoreAppointmentsResponse
	47,  // 136: moego.service.appointment.v1.AppointmentService.RescheduleBoardingAppointment:output_type -> moego.service.appointment.v1.RescheduleBoardingAppointmentResponse
	46,  // 137: moego.service.appointment.v1.AppointmentService.SyncAppointmentToOrder:output_type -> moego.service.appointment.v1.SyncAppointmentToOrderResponse
	49,  // 138: moego.service.appointment.v1.AppointmentService.PreviewOrderDetail:output_type -> moego.service.appointment.v1.PreviewOrderDetailResponse
	51,  // 139: moego.service.appointment.v1.AppointmentService.PreviewOrderLineItems:output_type -> moego.service.appointment.v1.PreviewOrderLineItemsResponse
	53,  // 140: moego.service.appointment.v1.AppointmentService.GetTimeOverlapAppointmentList:output_type -> moego.service.appointment.v1.GetTimeOverlapAppointmentListResponse
	56,  // 141: moego.service.appointment.v1.AppointmentService.PreviewEstimateOrder:output_type -> moego.service.appointment.v1.PreviewEstimateOrderResponse
	58,  // 142: moego.service.appointment.v1.AppointmentService.ListExtraInfo:output_type -> moego.service.appointment.v1.ListExtraInfoResponse
	60,  // 143: moego.service.appointment.v1.AppointmentService.CreateExtraInfo:output_type -> moego.service.appointment.v1.CreateExtraInfoResponse
	115, // [115:144] is the sub-list for method output_type
	86,  // [86:115] is the sub-list for method input_type
	86,  // [86:86] is the sub-list for extension type_name
	86,  // [86:86] is the sub-list for extension extendee
	0,   // [0:86] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v1_appointment_service_proto_init() }
func file_moego_service_appointment_v1_appointment_service_proto_init() {
	if File_moego_service_appointment_v1_appointment_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAppointmentListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerLastAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerLastAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateAppointmentInvoiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalculateAppointmentInvoiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInProgressAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInProgressAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBlockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBlockResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppointmentForOnlineBookingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAppointmentForOnlineBookingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBlockTimesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBlockTimesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQuickCheckInRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchQuickCheckInResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentSelectiveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentSelectiveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentForPetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentForPetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsForCustomersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsForCustomersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchBookAgainAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchBookAgainAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCancelAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchCancelAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountAppointmentForPetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountAppointmentForPetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAppointmentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAppointmentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RestoreAppointmentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RestoreAppointmentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBoardingAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncAppointmentToOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncAppointmentToOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBoardingAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewOrderDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewOrderDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewOrderLineItemsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewOrderLineItemsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTimeOverlapAppointmentListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTimeOverlapAppointmentListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewEstimateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewEstimateOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExtraInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExtraInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExtraInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExtraInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomerLastAppointmentRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBlockTimesRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentForPetsRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentsForCustomersRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTimeOverlapAppointmentListRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewEstimateOrderResponse_EstimatedOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[16].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[17].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[24].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[30].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[34].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[38].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[40].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[42].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[59].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[61].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_service_proto_msgTypes[63].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v1_appointment_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   70,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v1_appointment_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v1_appointment_service_proto_depIdxs,
		EnumInfos:         file_moego_service_appointment_v1_appointment_service_proto_enumTypes,
		MessageInfos:      file_moego_service_appointment_v1_appointment_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v1_appointment_service_proto = out.File
	file_moego_service_appointment_v1_appointment_service_proto_rawDesc = nil
	file_moego_service_appointment_v1_appointment_service_proto_goTypes = nil
	file_moego_service_appointment_v1_appointment_service_proto_depIdxs = nil
}
