// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/sms/v1/admin_atp_service.proto

package smssvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AdminAtpServiceClient is the client API for AdminAtpService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AdminAtpServiceClient interface {
	// submit or flush atp status
	SubmitAndFlushAtpStatus(ctx context.Context, in *SubmitAndFlushAtpStatusRequest, opts ...grpc.CallOption) (*SubmitAndFlushAtpStatusResponse, error)
}

type adminAtpServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminAtpServiceClient(cc grpc.ClientConnInterface) AdminAtpServiceClient {
	return &adminAtpServiceClient{cc}
}

func (c *adminAtpServiceClient) SubmitAndFlushAtpStatus(ctx context.Context, in *SubmitAndFlushAtpStatusRequest, opts ...grpc.CallOption) (*SubmitAndFlushAtpStatusResponse, error) {
	out := new(SubmitAndFlushAtpStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.sms.v1.AdminAtpService/SubmitAndFlushAtpStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminAtpServiceServer is the server API for AdminAtpService service.
// All implementations must embed UnimplementedAdminAtpServiceServer
// for forward compatibility
type AdminAtpServiceServer interface {
	// submit or flush atp status
	SubmitAndFlushAtpStatus(context.Context, *SubmitAndFlushAtpStatusRequest) (*SubmitAndFlushAtpStatusResponse, error)
	mustEmbedUnimplementedAdminAtpServiceServer()
}

// UnimplementedAdminAtpServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAdminAtpServiceServer struct {
}

func (UnimplementedAdminAtpServiceServer) SubmitAndFlushAtpStatus(context.Context, *SubmitAndFlushAtpStatusRequest) (*SubmitAndFlushAtpStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitAndFlushAtpStatus not implemented")
}
func (UnimplementedAdminAtpServiceServer) mustEmbedUnimplementedAdminAtpServiceServer() {}

// UnsafeAdminAtpServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminAtpServiceServer will
// result in compilation errors.
type UnsafeAdminAtpServiceServer interface {
	mustEmbedUnimplementedAdminAtpServiceServer()
}

func RegisterAdminAtpServiceServer(s grpc.ServiceRegistrar, srv AdminAtpServiceServer) {
	s.RegisterService(&AdminAtpService_ServiceDesc, srv)
}

func _AdminAtpService_SubmitAndFlushAtpStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitAndFlushAtpStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminAtpServiceServer).SubmitAndFlushAtpStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.sms.v1.AdminAtpService/SubmitAndFlushAtpStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminAtpServiceServer).SubmitAndFlushAtpStatus(ctx, req.(*SubmitAndFlushAtpStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AdminAtpService_ServiceDesc is the grpc.ServiceDesc for AdminAtpService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdminAtpService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.sms.v1.AdminAtpService",
	HandlerType: (*AdminAtpServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubmitAndFlushAtpStatus",
			Handler:    _AdminAtpService_SubmitAndFlushAtpStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/sms/v1/admin_atp_service.proto",
}
