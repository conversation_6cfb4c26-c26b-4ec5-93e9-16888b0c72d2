// @since 2024-06-13 11:16:32
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/membership/v1/subscription_api.proto

package membershipapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/subscription/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/subscription/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// import subscriptions request
type ImportSubscriptionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subscription data
	Data *v1.SubscriptionData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportSubscriptionsParams) Reset() {
	*x = ImportSubscriptionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportSubscriptionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportSubscriptionsParams) ProtoMessage() {}

func (x *ImportSubscriptionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportSubscriptionsParams.ProtoReflect.Descriptor instead.
func (*ImportSubscriptionsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{0}
}

func (x *ImportSubscriptionsParams) GetData() *v1.SubscriptionData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import subscriptions response
type ImportSubscriptionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported subscription data
	Imported *v1.SubscriptionData `protobuf:"bytes,1,opt,name=imported,proto3" json:"imported,omitempty"`
}

func (x *ImportSubscriptionsResult) Reset() {
	*x = ImportSubscriptionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportSubscriptionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportSubscriptionsResult) ProtoMessage() {}

func (x *ImportSubscriptionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportSubscriptionsResult.ProtoReflect.Descriptor instead.
func (*ImportSubscriptionsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{1}
}

func (x *ImportSubscriptionsResult) GetImported() *v1.SubscriptionData {
	if x != nil {
		return x.Imported
	}
	return nil
}

// import quantity entitlements request
type ImportQuantityEntitlementsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// quantity entitlement data
	Data *v1.QuantityEntitlementData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ImportQuantityEntitlementsParams) Reset() {
	*x = ImportQuantityEntitlementsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportQuantityEntitlementsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportQuantityEntitlementsParams) ProtoMessage() {}

func (x *ImportQuantityEntitlementsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportQuantityEntitlementsParams.ProtoReflect.Descriptor instead.
func (*ImportQuantityEntitlementsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{2}
}

func (x *ImportQuantityEntitlementsParams) GetData() *v1.QuantityEntitlementData {
	if x != nil {
		return x.Data
	}
	return nil
}

// import quantity entitlements response
type ImportQuantityEntitlementsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// successfully imported quantity entitlement data
	Imported *v1.QuantityEntitlementData `protobuf:"bytes,1,opt,name=imported,proto3" json:"imported,omitempty"`
}

func (x *ImportQuantityEntitlementsResult) Reset() {
	*x = ImportQuantityEntitlementsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportQuantityEntitlementsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportQuantityEntitlementsResult) ProtoMessage() {}

func (x *ImportQuantityEntitlementsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportQuantityEntitlementsResult.ProtoReflect.Descriptor instead.
func (*ImportQuantityEntitlementsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{3}
}

func (x *ImportQuantityEntitlementsResult) GetImported() *v1.QuantityEntitlementData {
	if x != nil {
		return x.Imported
	}
	return nil
}

// create subscription params
type CreateSellLinkParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription def
	SellLinkDef *v1.SellLinkCreateDef `protobuf:"bytes,1,opt,name=sell_link_def,json=sellLinkDef,proto3" json:"sell_link_def,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *CreateSellLinkParams) Reset() {
	*x = CreateSellLinkParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSellLinkParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSellLinkParams) ProtoMessage() {}

func (x *CreateSellLinkParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSellLinkParams.ProtoReflect.Descriptor instead.
func (*CreateSellLinkParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateSellLinkParams) GetSellLinkDef() *v1.SellLinkCreateDef {
	if x != nil {
		return x.SellLinkDef
	}
	return nil
}

func (x *CreateSellLinkParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// create subscription result
type CreateSellLinkResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the link public token
	PublicToken string `protobuf:"bytes,1,opt,name=public_token,json=publicToken,proto3" json:"public_token,omitempty"`
}

func (x *CreateSellLinkResult) Reset() {
	*x = CreateSellLinkResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSellLinkResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSellLinkResult) ProtoMessage() {}

func (x *CreateSellLinkResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSellLinkResult.ProtoReflect.Descriptor instead.
func (*CreateSellLinkResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreateSellLinkResult) GetPublicToken() string {
	if x != nil {
		return x.PublicToken
	}
	return ""
}

// create card params
type CreateCardParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the sell link public token
	PublicToken string `protobuf:"bytes,1,opt,name=public_token,json=publicToken,proto3" json:"public_token,omitempty"`
	// use new card with token,
	// will always save card on file for next charge.
	ExternalCardToken string `protobuf:"bytes,2,opt,name=external_card_token,json=externalCardToken,proto3" json:"external_card_token,omitempty"`
}

func (x *CreateCardParams) Reset() {
	*x = CreateCardParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCardParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCardParams) ProtoMessage() {}

func (x *CreateCardParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCardParams.ProtoReflect.Descriptor instead.
func (*CreateCardParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{6}
}

func (x *CreateCardParams) GetPublicToken() string {
	if x != nil {
		return x.PublicToken
	}
	return ""
}

func (x *CreateCardParams) GetExternalCardToken() string {
	if x != nil {
		return x.ExternalCardToken
	}
	return ""
}

// create card result
type CreateCardResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the card
	Card *v11.CreditCardModelPublicView `protobuf:"bytes,1,opt,name=card,proto3" json:"card,omitempty"`
}

func (x *CreateCardResult) Reset() {
	*x = CreateCardResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCardResult) ProtoMessage() {}

func (x *CreateCardResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCardResult.ProtoReflect.Descriptor instead.
func (*CreateCardResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{7}
}

func (x *CreateCardResult) GetCard() *v11.CreditCardModelPublicView {
	if x != nil {
		return x.Card
	}
	return nil
}

// create subscription params
type SellMembershipParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription def
	SubscriptionDef *v1.SubscriptionCreateDef `protobuf:"bytes,1,opt,name=subscription_def,json=subscriptionDef,proto3" json:"subscription_def,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *SellMembershipParams) Reset() {
	*x = SellMembershipParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellMembershipParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellMembershipParams) ProtoMessage() {}

func (x *SellMembershipParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellMembershipParams.ProtoReflect.Descriptor instead.
func (*SellMembershipParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{8}
}

func (x *SellMembershipParams) GetSubscriptionDef() *v1.SubscriptionCreateDef {
	if x != nil {
		return x.SubscriptionDef
	}
	return nil
}

func (x *SellMembershipParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// create subscription result
type SellMembershipResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the created subscription
	Subscription *v1.SubscriptionModel `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
	// client secret token
	ExternalClientSecretId string `protobuf:"bytes,2,opt,name=external_client_secret_id,json=externalClientSecretId,proto3" json:"external_client_secret_id,omitempty"`
}

func (x *SellMembershipResult) Reset() {
	*x = SellMembershipResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellMembershipResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellMembershipResult) ProtoMessage() {}

func (x *SellMembershipResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellMembershipResult.ProtoReflect.Descriptor instead.
func (*SellMembershipResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{9}
}

func (x *SellMembershipResult) GetSubscription() *v1.SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *SellMembershipResult) GetExternalClientSecretId() string {
	if x != nil {
		return x.ExternalClientSecretId
	}
	return ""
}

// get subscription params
type GetSubscriptionDetailParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetSubscriptionDetailParams) Reset() {
	*x = GetSubscriptionDetailParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubscriptionDetailParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubscriptionDetailParams) ProtoMessage() {}

func (x *GetSubscriptionDetailParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubscriptionDetailParams.ProtoReflect.Descriptor instead.
func (*GetSubscriptionDetailParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetSubscriptionDetailParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// get subscription result
// TODO(junbao): optimize subscription detail model
type GetSubscriptionDetailResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription
	Subscription *v1.SubscriptionModel `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
	// the membership, latest revision
	Membership *v1.MembershipModel `protobuf:"bytes,2,opt,name=membership,proto3" json:"membership,omitempty"`
	// card info
	Card *v11.CreditCardModelPublicView `protobuf:"bytes,3,opt,name=card,proto3" json:"card,omitempty"`
}

func (x *GetSubscriptionDetailResult) Reset() {
	*x = GetSubscriptionDetailResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubscriptionDetailResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubscriptionDetailResult) ProtoMessage() {}

func (x *GetSubscriptionDetailResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubscriptionDetailResult.ProtoReflect.Descriptor instead.
func (*GetSubscriptionDetailResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetSubscriptionDetailResult) GetSubscription() *v1.SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *GetSubscriptionDetailResult) GetMembership() *v1.MembershipModel {
	if x != nil {
		return x.Membership
	}
	return nil
}

func (x *GetSubscriptionDetailResult) GetCard() *v11.CreditCardModelPublicView {
	if x != nil {
		return x.Card
	}
	return nil
}

// update subscription params
type UpdateSubscriptionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the subscription def
	SubscriptionDef *v1.SubscriptionUpdateDef `protobuf:"bytes,2,opt,name=subscription_def,json=subscriptionDef,proto3" json:"subscription_def,omitempty"`
}

func (x *UpdateSubscriptionParams) Reset() {
	*x = UpdateSubscriptionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSubscriptionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubscriptionParams) ProtoMessage() {}

func (x *UpdateSubscriptionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubscriptionParams.ProtoReflect.Descriptor instead.
func (*UpdateSubscriptionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateSubscriptionParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSubscriptionParams) GetSubscriptionDef() *v1.SubscriptionUpdateDef {
	if x != nil {
		return x.SubscriptionDef
	}
	return nil
}

// update subscription result
type UpdateSubscriptionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the updated subscription
	Subscription *v1.SubscriptionModel `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
}

func (x *UpdateSubscriptionResult) Reset() {
	*x = UpdateSubscriptionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSubscriptionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubscriptionResult) ProtoMessage() {}

func (x *UpdateSubscriptionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubscriptionResult.ProtoReflect.Descriptor instead.
func (*UpdateSubscriptionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateSubscriptionResult) GetSubscription() *v1.SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

// list subscription params
type ListSubscriptionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by customer id
	CustomerId *int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// filter by status
	StatusIn []v1.SubscriptionModel_Status `protobuf:"varint,2,rep,packed,name=status_in,json=statusIn,proto3,enum=moego.models.membership.v1.SubscriptionModel_Status" json:"status_in,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListSubscriptionsParams) Reset() {
	*x = ListSubscriptionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubscriptionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubscriptionsParams) ProtoMessage() {}

func (x *ListSubscriptionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubscriptionsParams.ProtoReflect.Descriptor instead.
func (*ListSubscriptionsParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{14}
}

func (x *ListSubscriptionsParams) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *ListSubscriptionsParams) GetStatusIn() []v1.SubscriptionModel_Status {
	if x != nil {
		return x.StatusIn
	}
	return nil
}

func (x *ListSubscriptionsParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list subscription result
type ListSubscriptionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscriptions
	MembershipSubscriptions []*v1.MembershipSubscriptionModel `protobuf:"bytes,1,rep,name=membership_subscriptions,json=membershipSubscriptions,proto3" json:"membership_subscriptions,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListSubscriptionsResult) Reset() {
	*x = ListSubscriptionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubscriptionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubscriptionsResult) ProtoMessage() {}

func (x *ListSubscriptionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubscriptionsResult.ProtoReflect.Descriptor instead.
func (*ListSubscriptionsResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{15}
}

func (x *ListSubscriptionsResult) GetMembershipSubscriptions() []*v1.MembershipSubscriptionModel {
	if x != nil {
		return x.MembershipSubscriptions
	}
	return nil
}

func (x *ListSubscriptionsResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// cancel subscription params
type CancelSubscriptionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 退款策略，默认不退款
	RefundPolicy v12.CancelSubscriptionRequest_RefundPolicy `protobuf:"varint,2,opt,name=refund_policy,json=refundPolicy,proto3,enum=moego.service.subscription.v1.CancelSubscriptionRequest_RefundPolicy" json:"refund_policy,omitempty"`
	// 取消策略，默认到期取消
	CancelPolicy v12.CancelSubscriptionRequest_CancelPolicy `protobuf:"varint,3,opt,name=cancel_policy,json=cancelPolicy,proto3,enum=moego.service.subscription.v1.CancelSubscriptionRequest_CancelPolicy" json:"cancel_policy,omitempty"`
}

func (x *CancelSubscriptionParams) Reset() {
	*x = CancelSubscriptionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelSubscriptionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelSubscriptionParams) ProtoMessage() {}

func (x *CancelSubscriptionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelSubscriptionParams.ProtoReflect.Descriptor instead.
func (*CancelSubscriptionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{16}
}

func (x *CancelSubscriptionParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CancelSubscriptionParams) GetRefundPolicy() v12.CancelSubscriptionRequest_RefundPolicy {
	if x != nil {
		return x.RefundPolicy
	}
	return v12.CancelSubscriptionRequest_RefundPolicy(0)
}

func (x *CancelSubscriptionParams) GetCancelPolicy() v12.CancelSubscriptionRequest_CancelPolicy {
	if x != nil {
		return x.CancelPolicy
	}
	return v12.CancelSubscriptionRequest_CancelPolicy(0)
}

// cancel subscription result
type CancelSubscriptionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the updated subscription
	Subscription *v1.SubscriptionModel `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
}

func (x *CancelSubscriptionResult) Reset() {
	*x = CancelSubscriptionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelSubscriptionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelSubscriptionResult) ProtoMessage() {}

func (x *CancelSubscriptionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelSubscriptionResult.ProtoReflect.Descriptor instead.
func (*CancelSubscriptionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{17}
}

func (x *CancelSubscriptionResult) GetSubscription() *v1.SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

// renew subscription params
type RenewSubscriptionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// required for cancelled subscription
	CardOnFileId *string `protobuf:"bytes,2,opt,name=card_on_file_id,json=cardOnFileId,proto3,oneof" json:"card_on_file_id,omitempty"`
}

func (x *RenewSubscriptionParams) Reset() {
	*x = RenewSubscriptionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RenewSubscriptionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenewSubscriptionParams) ProtoMessage() {}

func (x *RenewSubscriptionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenewSubscriptionParams.ProtoReflect.Descriptor instead.
func (*RenewSubscriptionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{18}
}

func (x *RenewSubscriptionParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RenewSubscriptionParams) GetCardOnFileId() string {
	if x != nil && x.CardOnFileId != nil {
		return *x.CardOnFileId
	}
	return ""
}

// renew subscription result
type RenewSubscriptionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the updated subscription
	Subscription *v1.SubscriptionModel `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
	// client secret token
	ExternalClientSecretId string `protobuf:"bytes,2,opt,name=external_client_secret_id,json=externalClientSecretId,proto3" json:"external_client_secret_id,omitempty"`
}

func (x *RenewSubscriptionResult) Reset() {
	*x = RenewSubscriptionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RenewSubscriptionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenewSubscriptionResult) ProtoMessage() {}

func (x *RenewSubscriptionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenewSubscriptionResult.ProtoReflect.Descriptor instead.
func (*RenewSubscriptionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{19}
}

func (x *RenewSubscriptionResult) GetSubscription() *v1.SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *RenewSubscriptionResult) GetExternalClientSecretId() string {
	if x != nil {
		return x.ExternalClientSecretId
	}
	return ""
}

// pause subscription params
type PauseSubscriptionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// auto resume setting
	AutoResumeSetting *v1.SubscriptionModel_AutoResumeSetting `protobuf:"bytes,2,opt,name=auto_resume_setting,json=autoResumeSetting,proto3" json:"auto_resume_setting,omitempty"`
}

func (x *PauseSubscriptionParams) Reset() {
	*x = PauseSubscriptionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseSubscriptionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseSubscriptionParams) ProtoMessage() {}

func (x *PauseSubscriptionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseSubscriptionParams.ProtoReflect.Descriptor instead.
func (*PauseSubscriptionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{20}
}

func (x *PauseSubscriptionParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PauseSubscriptionParams) GetAutoResumeSetting() *v1.SubscriptionModel_AutoResumeSetting {
	if x != nil {
		return x.AutoResumeSetting
	}
	return nil
}

// pause subscription result
type PauseSubscriptionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the updated subscription
	Subscription *v1.SubscriptionModel `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
}

func (x *PauseSubscriptionResult) Reset() {
	*x = PauseSubscriptionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PauseSubscriptionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseSubscriptionResult) ProtoMessage() {}

func (x *PauseSubscriptionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseSubscriptionResult.ProtoReflect.Descriptor instead.
func (*PauseSubscriptionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{21}
}

func (x *PauseSubscriptionResult) GetSubscription() *v1.SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

// resume subscription params
type ResumeSubscriptionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the subscription id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ResumeSubscriptionParams) Reset() {
	*x = ResumeSubscriptionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeSubscriptionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeSubscriptionParams) ProtoMessage() {}

func (x *ResumeSubscriptionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeSubscriptionParams.ProtoReflect.Descriptor instead.
func (*ResumeSubscriptionParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{22}
}

func (x *ResumeSubscriptionParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// resume subscription result
type ResumeSubscriptionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the updated subscription
	Subscription *v1.SubscriptionModel `protobuf:"bytes,1,opt,name=subscription,proto3" json:"subscription,omitempty"`
}

func (x *ResumeSubscriptionResult) Reset() {
	*x = ResumeSubscriptionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResumeSubscriptionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResumeSubscriptionResult) ProtoMessage() {}

func (x *ResumeSubscriptionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResumeSubscriptionResult.ProtoReflect.Descriptor instead.
func (*ResumeSubscriptionResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{23}
}

func (x *ResumeSubscriptionResult) GetSubscription() *v1.SubscriptionModel {
	if x != nil {
		return x.Subscription
	}
	return nil
}

// list membership buyers params
type ListBuyersParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the membership id
	MembershipId int64 `protobuf:"varint,1,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
	// filter
	Filter *v1.SubscriptionBuyerListFilter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListBuyersParams) Reset() {
	*x = ListBuyersParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBuyersParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBuyersParams) ProtoMessage() {}

func (x *ListBuyersParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBuyersParams.ProtoReflect.Descriptor instead.
func (*ListBuyersParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{24}
}

func (x *ListBuyersParams) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

func (x *ListBuyersParams) GetFilter() *v1.SubscriptionBuyerListFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListBuyersParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list membership buyers result
type ListBuyersResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the buyers
	Buyers []*v1.SubscriptionBuyerView `protobuf:"bytes,1,rep,name=buyers,proto3" json:"buyers,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListBuyersResult) Reset() {
	*x = ListBuyersResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBuyersResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBuyersResult) ProtoMessage() {}

func (x *ListBuyersResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBuyersResult.ProtoReflect.Descriptor instead.
func (*ListBuyersResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{25}
}

func (x *ListBuyersResult) GetBuyers() []*v1.SubscriptionBuyerView {
	if x != nil {
		return x.Buyers
	}
	return nil
}

func (x *ListBuyersResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get membership buyer report params
type GetBuyerReportParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the membership id
	MembershipId int64 `protobuf:"varint,1,opt,name=membership_id,json=membershipId,proto3" json:"membership_id,omitempty"`
}

func (x *GetBuyerReportParams) Reset() {
	*x = GetBuyerReportParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBuyerReportParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBuyerReportParams) ProtoMessage() {}

func (x *GetBuyerReportParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBuyerReportParams.ProtoReflect.Descriptor instead.
func (*GetBuyerReportParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{26}
}

func (x *GetBuyerReportParams) GetMembershipId() int64 {
	if x != nil {
		return x.MembershipId
	}
	return 0
}

// get membership buyer report result
type GetBuyerReportResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the report
	Content *v13.Report `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *GetBuyerReportResult) Reset() {
	*x = GetBuyerReportResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBuyerReportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBuyerReportResult) ProtoMessage() {}

func (x *GetBuyerReportResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBuyerReportResult.ProtoReflect.Descriptor instead.
func (*GetBuyerReportResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{27}
}

func (x *GetBuyerReportResult) GetContent() *v13.Report {
	if x != nil {
		return x.Content
	}
	return nil
}

// list payment history params
type ListPaymentHistoryParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v1.PaymentHistoryItemFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPaymentHistoryParams) Reset() {
	*x = ListPaymentHistoryParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentHistoryParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentHistoryParams) ProtoMessage() {}

func (x *ListPaymentHistoryParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentHistoryParams.ProtoReflect.Descriptor instead.
func (*ListPaymentHistoryParams) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{28}
}

func (x *ListPaymentHistoryParams) GetFilter() *v1.PaymentHistoryItemFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListPaymentHistoryParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// list payment history result
type ListPaymentHistoryHistoryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the payment history
	PaymentHistory []*v1.PaymentHistoryItemView `protobuf:"bytes,1,rep,name=payment_history,json=paymentHistory,proto3" json:"payment_history,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *ListPaymentHistoryHistoryResult) Reset() {
	*x = ListPaymentHistoryHistoryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPaymentHistoryHistoryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPaymentHistoryHistoryResult) ProtoMessage() {}

func (x *ListPaymentHistoryHistoryResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_membership_v1_subscription_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPaymentHistoryHistoryResult.ProtoReflect.Descriptor instead.
func (*ListPaymentHistoryHistoryResult) Descriptor() ([]byte, []int) {
	return file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP(), []int{29}
}

func (x *ListPaymentHistoryHistoryResult) GetPaymentHistory() []*v1.PaymentHistoryItemView {
	if x != nil {
		return x.PaymentHistory
	}
	return nil
}

func (x *ListPaymentHistoryHistoryResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

var File_moego_api_membership_v1_subscription_api_proto protoreflect.FileDescriptor

var file_moego_api_membership_v1_subscription_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6d, 0x69, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x65, 0x6c, 0x6c, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x64, 0x65, 0x66, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5d, 0x0a, 0x19, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x40, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x65, 0x0a, 0x19, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x22, 0x6b, 0x0a,
	0x20, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x47, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x73, 0x0a, 0x20, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4f,
	0x0a, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75,
	0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x22,
	0x9d, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x6c, 0x6c, 0x4c, 0x69,
	0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5b, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x6c,
	0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c,
	0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x73, 0x65, 0x6c, 0x6c, 0x4c, 0x69,
	0x6e, 0x6b, 0x44, 0x65, 0x66, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22,
	0x39, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x6c, 0x6c, 0x4c, 0x69, 0x6e,
	0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x7c, 0x0a, 0x10, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c,
	0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x14, 0x18, 0x64, 0x52,
	0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x3a, 0x0a, 0x13,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43,
	0x61, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x5a, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x04,
	0x63, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04,
	0x63, 0x61, 0x72, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x6c, 0x6c, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x66, 0x0a,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x66, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22,
	0xa4, 0x01, 0x0a, 0x14, 0x53, 0x65, 0x6c, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x19, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x49, 0x64, 0x22, 0x36, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x85,
	0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51,
	0x0a, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4b, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x46,
	0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x04, 0x63, 0x61, 0x72, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x66, 0x0a, 0x10,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x66, 0x22, 0x6d, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x51, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x95, 0x02, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x2d, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x64,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18,
	0x01, 0x22, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x49, 0x6e, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xd1, 0x01, 0x0a, 0x17,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x72, 0x0a, 0x18, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x8b, 0x02, 0x0a, 0x18, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x6a, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x6a, 0x0a, 0x0d, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x6d, 0x0a,
	0x18, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x0c, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x72, 0x0a, 0x17,
	0x52, 0x65, 0x6e, 0x65, 0x77, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x2a, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x61, 0x72,
	0x64, 0x4f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10,
	0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x22, 0xa7, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x0c,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x39, 0x0a, 0x19, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x16, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x49, 0x64, 0x22, 0xa3, 0x01, 0x0a, 0x17, 0x50,
	0x61, 0x75, 0x73, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x6f, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x41, 0x75, 0x74, 0x6f,
	0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x11, 0x61,
	0x75, 0x74, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x22, 0x6c, 0x0a, 0x17, 0x50, 0x61, 0x75, 0x73, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x0c, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33,
	0x0a, 0x18, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x6d, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x51, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xe4, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x75, 0x79, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x41, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xa1, 0x01, 0x0a, 0x10, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x49,
	0x0a, 0x06, 0x62, 0x75, 0x79, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x79, 0x65, 0x72, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x06, 0x62, 0x75, 0x79, 0x65, 0x72, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x44, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0xab, 0x01, 0x0a, 0x18,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xc2, 0x01, 0x0a, 0x1f, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5b, 0x0a,
	0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x32, 0xaa,
	0x0e, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6e, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x6c, 0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x6c, 0x6c, 0x4c, 0x69, 0x6e,
	0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x6c, 0x6c, 0x4c, 0x69, 0x6e, 0x6b,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x62, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x61, 0x72, 0x64, 0x12, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x61, 0x72, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x6e, 0x0a, 0x0e, 0x53, 0x65,
	0x6c, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x12, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c, 0x6c, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x83, 0x01, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x7a, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x77, 0x0a, 0x11,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7a, 0x0a, 0x12, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x77, 0x0a, 0x11, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x77, 0x0a, 0x11, 0x50, 0x61,
	0x75, 0x73, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x75, 0x73, 0x65, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x75, 0x73,
	0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x7a, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6d, 0x65, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x62, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72, 0x73, 0x12, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x79, 0x65,
	0x72, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x6e, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x42, 0x75, 0x79, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x81, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7d, 0x0a, 0x13, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x92, 0x01, 0x0a, 0x1a, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x81, 0x01, 0x0a, 0x1f,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x50,
	0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f,
	0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b,
	0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_membership_v1_subscription_api_proto_rawDescOnce sync.Once
	file_moego_api_membership_v1_subscription_api_proto_rawDescData = file_moego_api_membership_v1_subscription_api_proto_rawDesc
)

func file_moego_api_membership_v1_subscription_api_proto_rawDescGZIP() []byte {
	file_moego_api_membership_v1_subscription_api_proto_rawDescOnce.Do(func() {
		file_moego_api_membership_v1_subscription_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_membership_v1_subscription_api_proto_rawDescData)
	})
	return file_moego_api_membership_v1_subscription_api_proto_rawDescData
}

var file_moego_api_membership_v1_subscription_api_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_moego_api_membership_v1_subscription_api_proto_goTypes = []interface{}{
	(*ImportSubscriptionsParams)(nil),               // 0: moego.api.membership.v1.ImportSubscriptionsParams
	(*ImportSubscriptionsResult)(nil),               // 1: moego.api.membership.v1.ImportSubscriptionsResult
	(*ImportQuantityEntitlementsParams)(nil),        // 2: moego.api.membership.v1.ImportQuantityEntitlementsParams
	(*ImportQuantityEntitlementsResult)(nil),        // 3: moego.api.membership.v1.ImportQuantityEntitlementsResult
	(*CreateSellLinkParams)(nil),                    // 4: moego.api.membership.v1.CreateSellLinkParams
	(*CreateSellLinkResult)(nil),                    // 5: moego.api.membership.v1.CreateSellLinkResult
	(*CreateCardParams)(nil),                        // 6: moego.api.membership.v1.CreateCardParams
	(*CreateCardResult)(nil),                        // 7: moego.api.membership.v1.CreateCardResult
	(*SellMembershipParams)(nil),                    // 8: moego.api.membership.v1.SellMembershipParams
	(*SellMembershipResult)(nil),                    // 9: moego.api.membership.v1.SellMembershipResult
	(*GetSubscriptionDetailParams)(nil),             // 10: moego.api.membership.v1.GetSubscriptionDetailParams
	(*GetSubscriptionDetailResult)(nil),             // 11: moego.api.membership.v1.GetSubscriptionDetailResult
	(*UpdateSubscriptionParams)(nil),                // 12: moego.api.membership.v1.UpdateSubscriptionParams
	(*UpdateSubscriptionResult)(nil),                // 13: moego.api.membership.v1.UpdateSubscriptionResult
	(*ListSubscriptionsParams)(nil),                 // 14: moego.api.membership.v1.ListSubscriptionsParams
	(*ListSubscriptionsResult)(nil),                 // 15: moego.api.membership.v1.ListSubscriptionsResult
	(*CancelSubscriptionParams)(nil),                // 16: moego.api.membership.v1.CancelSubscriptionParams
	(*CancelSubscriptionResult)(nil),                // 17: moego.api.membership.v1.CancelSubscriptionResult
	(*RenewSubscriptionParams)(nil),                 // 18: moego.api.membership.v1.RenewSubscriptionParams
	(*RenewSubscriptionResult)(nil),                 // 19: moego.api.membership.v1.RenewSubscriptionResult
	(*PauseSubscriptionParams)(nil),                 // 20: moego.api.membership.v1.PauseSubscriptionParams
	(*PauseSubscriptionResult)(nil),                 // 21: moego.api.membership.v1.PauseSubscriptionResult
	(*ResumeSubscriptionParams)(nil),                // 22: moego.api.membership.v1.ResumeSubscriptionParams
	(*ResumeSubscriptionResult)(nil),                // 23: moego.api.membership.v1.ResumeSubscriptionResult
	(*ListBuyersParams)(nil),                        // 24: moego.api.membership.v1.ListBuyersParams
	(*ListBuyersResult)(nil),                        // 25: moego.api.membership.v1.ListBuyersResult
	(*GetBuyerReportParams)(nil),                    // 26: moego.api.membership.v1.GetBuyerReportParams
	(*GetBuyerReportResult)(nil),                    // 27: moego.api.membership.v1.GetBuyerReportResult
	(*ListPaymentHistoryParams)(nil),                // 28: moego.api.membership.v1.ListPaymentHistoryParams
	(*ListPaymentHistoryHistoryResult)(nil),         // 29: moego.api.membership.v1.ListPaymentHistoryHistoryResult
	(*v1.SubscriptionData)(nil),                     // 30: moego.models.membership.v1.SubscriptionData
	(*v1.QuantityEntitlementData)(nil),              // 31: moego.models.membership.v1.QuantityEntitlementData
	(*v1.SellLinkCreateDef)(nil),                    // 32: moego.models.membership.v1.SellLinkCreateDef
	(*v11.CreditCardModelPublicView)(nil),           // 33: moego.models.payment.v1.CreditCardModelPublicView
	(*v1.SubscriptionCreateDef)(nil),                // 34: moego.models.membership.v1.SubscriptionCreateDef
	(*v1.SubscriptionModel)(nil),                    // 35: moego.models.membership.v1.SubscriptionModel
	(*v1.MembershipModel)(nil),                      // 36: moego.models.membership.v1.MembershipModel
	(*v1.SubscriptionUpdateDef)(nil),                // 37: moego.models.membership.v1.SubscriptionUpdateDef
	(v1.SubscriptionModel_Status)(0),                // 38: moego.models.membership.v1.SubscriptionModel.Status
	(*v2.PaginationRequest)(nil),                    // 39: moego.utils.v2.PaginationRequest
	(*v1.MembershipSubscriptionModel)(nil),          // 40: moego.models.membership.v1.MembershipSubscriptionModel
	(*v2.PaginationResponse)(nil),                   // 41: moego.utils.v2.PaginationResponse
	(v12.CancelSubscriptionRequest_RefundPolicy)(0), // 42: moego.service.subscription.v1.CancelSubscriptionRequest.RefundPolicy
	(v12.CancelSubscriptionRequest_CancelPolicy)(0), // 43: moego.service.subscription.v1.CancelSubscriptionRequest.CancelPolicy
	(*v1.SubscriptionModel_AutoResumeSetting)(nil),  // 44: moego.models.membership.v1.SubscriptionModel.AutoResumeSetting
	(*v1.SubscriptionBuyerListFilter)(nil),          // 45: moego.models.membership.v1.SubscriptionBuyerListFilter
	(*v1.SubscriptionBuyerView)(nil),                // 46: moego.models.membership.v1.SubscriptionBuyerView
	(*v13.Report)(nil),                              // 47: moego.models.subscription.v1.Report
	(*v1.PaymentHistoryItemFilter)(nil),             // 48: moego.models.membership.v1.PaymentHistoryItemFilter
	(*v1.PaymentHistoryItemView)(nil),               // 49: moego.models.membership.v1.PaymentHistoryItemView
}
var file_moego_api_membership_v1_subscription_api_proto_depIdxs = []int32{
	30, // 0: moego.api.membership.v1.ImportSubscriptionsParams.data:type_name -> moego.models.membership.v1.SubscriptionData
	30, // 1: moego.api.membership.v1.ImportSubscriptionsResult.imported:type_name -> moego.models.membership.v1.SubscriptionData
	31, // 2: moego.api.membership.v1.ImportQuantityEntitlementsParams.data:type_name -> moego.models.membership.v1.QuantityEntitlementData
	31, // 3: moego.api.membership.v1.ImportQuantityEntitlementsResult.imported:type_name -> moego.models.membership.v1.QuantityEntitlementData
	32, // 4: moego.api.membership.v1.CreateSellLinkParams.sell_link_def:type_name -> moego.models.membership.v1.SellLinkCreateDef
	33, // 5: moego.api.membership.v1.CreateCardResult.card:type_name -> moego.models.payment.v1.CreditCardModelPublicView
	34, // 6: moego.api.membership.v1.SellMembershipParams.subscription_def:type_name -> moego.models.membership.v1.SubscriptionCreateDef
	35, // 7: moego.api.membership.v1.SellMembershipResult.subscription:type_name -> moego.models.membership.v1.SubscriptionModel
	35, // 8: moego.api.membership.v1.GetSubscriptionDetailResult.subscription:type_name -> moego.models.membership.v1.SubscriptionModel
	36, // 9: moego.api.membership.v1.GetSubscriptionDetailResult.membership:type_name -> moego.models.membership.v1.MembershipModel
	33, // 10: moego.api.membership.v1.GetSubscriptionDetailResult.card:type_name -> moego.models.payment.v1.CreditCardModelPublicView
	37, // 11: moego.api.membership.v1.UpdateSubscriptionParams.subscription_def:type_name -> moego.models.membership.v1.SubscriptionUpdateDef
	35, // 12: moego.api.membership.v1.UpdateSubscriptionResult.subscription:type_name -> moego.models.membership.v1.SubscriptionModel
	38, // 13: moego.api.membership.v1.ListSubscriptionsParams.status_in:type_name -> moego.models.membership.v1.SubscriptionModel.Status
	39, // 14: moego.api.membership.v1.ListSubscriptionsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	40, // 15: moego.api.membership.v1.ListSubscriptionsResult.membership_subscriptions:type_name -> moego.models.membership.v1.MembershipSubscriptionModel
	41, // 16: moego.api.membership.v1.ListSubscriptionsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	42, // 17: moego.api.membership.v1.CancelSubscriptionParams.refund_policy:type_name -> moego.service.subscription.v1.CancelSubscriptionRequest.RefundPolicy
	43, // 18: moego.api.membership.v1.CancelSubscriptionParams.cancel_policy:type_name -> moego.service.subscription.v1.CancelSubscriptionRequest.CancelPolicy
	35, // 19: moego.api.membership.v1.CancelSubscriptionResult.subscription:type_name -> moego.models.membership.v1.SubscriptionModel
	35, // 20: moego.api.membership.v1.RenewSubscriptionResult.subscription:type_name -> moego.models.membership.v1.SubscriptionModel
	44, // 21: moego.api.membership.v1.PauseSubscriptionParams.auto_resume_setting:type_name -> moego.models.membership.v1.SubscriptionModel.AutoResumeSetting
	35, // 22: moego.api.membership.v1.PauseSubscriptionResult.subscription:type_name -> moego.models.membership.v1.SubscriptionModel
	35, // 23: moego.api.membership.v1.ResumeSubscriptionResult.subscription:type_name -> moego.models.membership.v1.SubscriptionModel
	45, // 24: moego.api.membership.v1.ListBuyersParams.filter:type_name -> moego.models.membership.v1.SubscriptionBuyerListFilter
	39, // 25: moego.api.membership.v1.ListBuyersParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	46, // 26: moego.api.membership.v1.ListBuyersResult.buyers:type_name -> moego.models.membership.v1.SubscriptionBuyerView
	41, // 27: moego.api.membership.v1.ListBuyersResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	47, // 28: moego.api.membership.v1.GetBuyerReportResult.content:type_name -> moego.models.subscription.v1.Report
	48, // 29: moego.api.membership.v1.ListPaymentHistoryParams.filter:type_name -> moego.models.membership.v1.PaymentHistoryItemFilter
	39, // 30: moego.api.membership.v1.ListPaymentHistoryParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	49, // 31: moego.api.membership.v1.ListPaymentHistoryHistoryResult.payment_history:type_name -> moego.models.membership.v1.PaymentHistoryItemView
	41, // 32: moego.api.membership.v1.ListPaymentHistoryHistoryResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	4,  // 33: moego.api.membership.v1.SubscriptionService.CreateSellLink:input_type -> moego.api.membership.v1.CreateSellLinkParams
	6,  // 34: moego.api.membership.v1.SubscriptionService.CreateCard:input_type -> moego.api.membership.v1.CreateCardParams
	8,  // 35: moego.api.membership.v1.SubscriptionService.SellMembership:input_type -> moego.api.membership.v1.SellMembershipParams
	10, // 36: moego.api.membership.v1.SubscriptionService.GetSubscriptionDetail:input_type -> moego.api.membership.v1.GetSubscriptionDetailParams
	12, // 37: moego.api.membership.v1.SubscriptionService.UpdateSubscription:input_type -> moego.api.membership.v1.UpdateSubscriptionParams
	14, // 38: moego.api.membership.v1.SubscriptionService.ListSubscriptions:input_type -> moego.api.membership.v1.ListSubscriptionsParams
	16, // 39: moego.api.membership.v1.SubscriptionService.CancelSubscription:input_type -> moego.api.membership.v1.CancelSubscriptionParams
	18, // 40: moego.api.membership.v1.SubscriptionService.RenewSubscription:input_type -> moego.api.membership.v1.RenewSubscriptionParams
	20, // 41: moego.api.membership.v1.SubscriptionService.PauseSubscription:input_type -> moego.api.membership.v1.PauseSubscriptionParams
	22, // 42: moego.api.membership.v1.SubscriptionService.ResumeSubscription:input_type -> moego.api.membership.v1.ResumeSubscriptionParams
	24, // 43: moego.api.membership.v1.SubscriptionService.ListBuyers:input_type -> moego.api.membership.v1.ListBuyersParams
	26, // 44: moego.api.membership.v1.SubscriptionService.GetBuyerReport:input_type -> moego.api.membership.v1.GetBuyerReportParams
	28, // 45: moego.api.membership.v1.SubscriptionService.ListPaymentHistory:input_type -> moego.api.membership.v1.ListPaymentHistoryParams
	0,  // 46: moego.api.membership.v1.SubscriptionService.ImportSubscriptions:input_type -> moego.api.membership.v1.ImportSubscriptionsParams
	2,  // 47: moego.api.membership.v1.SubscriptionService.ImportQuantityEntitlements:input_type -> moego.api.membership.v1.ImportQuantityEntitlementsParams
	5,  // 48: moego.api.membership.v1.SubscriptionService.CreateSellLink:output_type -> moego.api.membership.v1.CreateSellLinkResult
	7,  // 49: moego.api.membership.v1.SubscriptionService.CreateCard:output_type -> moego.api.membership.v1.CreateCardResult
	9,  // 50: moego.api.membership.v1.SubscriptionService.SellMembership:output_type -> moego.api.membership.v1.SellMembershipResult
	11, // 51: moego.api.membership.v1.SubscriptionService.GetSubscriptionDetail:output_type -> moego.api.membership.v1.GetSubscriptionDetailResult
	13, // 52: moego.api.membership.v1.SubscriptionService.UpdateSubscription:output_type -> moego.api.membership.v1.UpdateSubscriptionResult
	15, // 53: moego.api.membership.v1.SubscriptionService.ListSubscriptions:output_type -> moego.api.membership.v1.ListSubscriptionsResult
	17, // 54: moego.api.membership.v1.SubscriptionService.CancelSubscription:output_type -> moego.api.membership.v1.CancelSubscriptionResult
	19, // 55: moego.api.membership.v1.SubscriptionService.RenewSubscription:output_type -> moego.api.membership.v1.RenewSubscriptionResult
	21, // 56: moego.api.membership.v1.SubscriptionService.PauseSubscription:output_type -> moego.api.membership.v1.PauseSubscriptionResult
	23, // 57: moego.api.membership.v1.SubscriptionService.ResumeSubscription:output_type -> moego.api.membership.v1.ResumeSubscriptionResult
	25, // 58: moego.api.membership.v1.SubscriptionService.ListBuyers:output_type -> moego.api.membership.v1.ListBuyersResult
	27, // 59: moego.api.membership.v1.SubscriptionService.GetBuyerReport:output_type -> moego.api.membership.v1.GetBuyerReportResult
	29, // 60: moego.api.membership.v1.SubscriptionService.ListPaymentHistory:output_type -> moego.api.membership.v1.ListPaymentHistoryHistoryResult
	1,  // 61: moego.api.membership.v1.SubscriptionService.ImportSubscriptions:output_type -> moego.api.membership.v1.ImportSubscriptionsResult
	3,  // 62: moego.api.membership.v1.SubscriptionService.ImportQuantityEntitlements:output_type -> moego.api.membership.v1.ImportQuantityEntitlementsResult
	48, // [48:63] is the sub-list for method output_type
	33, // [33:48] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_moego_api_membership_v1_subscription_api_proto_init() }
func file_moego_api_membership_v1_subscription_api_proto_init() {
	if File_moego_api_membership_v1_subscription_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportSubscriptionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportSubscriptionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportQuantityEntitlementsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportQuantityEntitlementsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSellLinkParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSellLinkResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCardParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCardResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellMembershipParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellMembershipResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubscriptionDetailParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubscriptionDetailResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSubscriptionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSubscriptionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubscriptionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubscriptionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelSubscriptionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelSubscriptionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RenewSubscriptionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RenewSubscriptionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseSubscriptionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PauseSubscriptionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeSubscriptionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResumeSubscriptionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBuyersParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBuyersResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBuyerReportParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBuyerReportResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentHistoryParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_membership_v1_subscription_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPaymentHistoryHistoryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_membership_v1_subscription_api_proto_msgTypes[14].OneofWrappers = []interface{}{}
	file_moego_api_membership_v1_subscription_api_proto_msgTypes[18].OneofWrappers = []interface{}{}
	file_moego_api_membership_v1_subscription_api_proto_msgTypes[24].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_membership_v1_subscription_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_membership_v1_subscription_api_proto_goTypes,
		DependencyIndexes: file_moego_api_membership_v1_subscription_api_proto_depIdxs,
		MessageInfos:      file_moego_api_membership_v1_subscription_api_proto_msgTypes,
	}.Build()
	File_moego_api_membership_v1_subscription_api_proto = out.File
	file_moego_api_membership_v1_subscription_api_proto_rawDesc = nil
	file_moego_api_membership_v1_subscription_api_proto_goTypes = nil
	file_moego_api_membership_v1_subscription_api_proto_depIdxs = nil
}
