// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/business_customer/v1/business_pet_color_api.proto

package businesscustomerapipb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// status
type BindingColorParams_Status int32

const (
	// unspecified
	BindingColorParams_STATUS_UNSPECIFIED BindingColorParams_Status = 0
	// ..
	BindingColorParams_NORMAL BindingColorParams_Status = 1
	// ..
	BindingColorParams_DELETED BindingColorParams_Status = 2
)

// Enum value maps for BindingColorParams_Status.
var (
	BindingColorParams_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "NORMAL",
		2: "DELETED",
	}
	BindingColorParams_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"NORMAL":             1,
		"DELETED":            2,
	}
)

func (x BindingColorParams_Status) Enum() *BindingColorParams_Status {
	p := new(BindingColorParams_Status)
	*p = x
	return p
}

func (x BindingColorParams_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BindingColorParams_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_enumTypes[0].Descriptor()
}

func (BindingColorParams_Status) Type() protoreflect.EnumType {
	return &file_moego_api_business_customer_v1_business_pet_color_api_proto_enumTypes[0]
}

func (x BindingColorParams_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BindingColorParams_Status.Descriptor instead.
func (BindingColorParams_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{8, 0}
}

// list pet color params
type ListPetColorParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetColorParams) Reset() {
	*x = ListPetColorParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetColorParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetColorParams) ProtoMessage() {}

func (x *ListPetColorParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetColorParams.ProtoReflect.Descriptor instead.
func (*ListPetColorParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{0}
}

// list pet color result
type ListPetColorResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list color
	Colors []*ListPetColorResult_Color `protobuf:"bytes,1,rep,name=colors,proto3" json:"colors,omitempty"`
}

func (x *ListPetColorResult) Reset() {
	*x = ListPetColorResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetColorResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetColorResult) ProtoMessage() {}

func (x *ListPetColorResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetColorResult.ProtoReflect.Descriptor instead.
func (*ListPetColorResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPetColorResult) GetColors() []*ListPetColorResult_Color {
	if x != nil {
		return x.Colors
	}
	return nil
}

// create pet color params
type CreatePetColorParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet color name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CreatePetColorParams) Reset() {
	*x = CreatePetColorParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetColorParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetColorParams) ProtoMessage() {}

func (x *CreatePetColorParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetColorParams.ProtoReflect.Descriptor instead.
func (*CreatePetColorParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePetColorParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// create pet color result
type CreatePetColorResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// create color
	Color *CreatePetColorResult_Color `protobuf:"bytes,2,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *CreatePetColorResult) Reset() {
	*x = CreatePetColorResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetColorResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetColorResult) ProtoMessage() {}

func (x *CreatePetColorResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetColorResult.ProtoReflect.Descriptor instead.
func (*CreatePetColorResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePetColorResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *CreatePetColorResult) GetColor() *CreatePetColorResult_Color {
	if x != nil {
		return x.Color
	}
	return nil
}

// delete pet color params
type DeletePetColorParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet color id
	ColorId int64 `protobuf:"varint,1,opt,name=color_id,json=colorId,proto3" json:"color_id,omitempty"`
}

func (x *DeletePetColorParams) Reset() {
	*x = DeletePetColorParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetColorParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetColorParams) ProtoMessage() {}

func (x *DeletePetColorParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetColorParams.ProtoReflect.Descriptor instead.
func (*DeletePetColorParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{4}
}

func (x *DeletePetColorParams) GetColorId() int64 {
	if x != nil {
		return x.ColorId
	}
	return 0
}

// delete pet color result
type DeletePetColorResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *DeletePetColorResult) Reset() {
	*x = DeletePetColorResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetColorResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetColorResult) ProtoMessage() {}

func (x *DeletePetColorResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetColorResult.ProtoReflect.Descriptor instead.
func (*DeletePetColorResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{5}
}

func (x *DeletePetColorResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// list pet binding list params
type ListPetBindingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
}

func (x *ListPetBindingParams) Reset() {
	*x = ListPetBindingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBindingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBindingParams) ProtoMessage() {}

func (x *ListPetBindingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBindingParams.ProtoReflect.Descriptor instead.
func (*ListPetBindingParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{6}
}

func (x *ListPetBindingParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// list pet binding list result
type ListPetBindingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// binding list
	PetColorsBindings []*ListPetBindingResult_PetColorBinding `protobuf:"bytes,1,rep,name=pet_colors_bindings,json=petColorsBindings,proto3" json:"pet_colors_bindings,omitempty"`
}

func (x *ListPetBindingResult) Reset() {
	*x = ListPetBindingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBindingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBindingResult) ProtoMessage() {}

func (x *ListPetBindingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBindingResult.ProtoReflect.Descriptor instead.
func (*ListPetBindingResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{7}
}

func (x *ListPetBindingResult) GetPetColorsBindings() []*ListPetBindingResult_PetColorBinding {
	if x != nil {
		return x.PetColorsBindings
	}
	return nil
}

// binding color
type BindingColorParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// color id
	ColorId int64 `protobuf:"varint,1,opt,name=color_id,json=colorId,proto3" json:"color_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// status
	Status BindingColorParams_Status `protobuf:"varint,3,opt,name=status,proto3,enum=moego.api.business_customer.v1.BindingColorParams_Status" json:"status,omitempty"`
}

func (x *BindingColorParams) Reset() {
	*x = BindingColorParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingColorParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingColorParams) ProtoMessage() {}

func (x *BindingColorParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingColorParams.ProtoReflect.Descriptor instead.
func (*BindingColorParams) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{8}
}

func (x *BindingColorParams) GetColorId() int64 {
	if x != nil {
		return x.ColorId
	}
	return 0
}

func (x *BindingColorParams) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *BindingColorParams) GetStatus() BindingColorParams_Status {
	if x != nil {
		return x.Status
	}
	return BindingColorParams_STATUS_UNSPECIFIED
}

// response
type BindingColorResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *BindingColorResult) Reset() {
	*x = BindingColorResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingColorResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingColorResult) ProtoMessage() {}

func (x *BindingColorResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingColorResult.ProtoReflect.Descriptor instead.
func (*BindingColorResult) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{9}
}

func (x *BindingColorResult) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// color
type ListPetColorResult_Color struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	ColorId int64 `protobuf:"varint,1,opt,name=color_id,json=colorId,proto3" json:"color_id,omitempty"`
	// name
	ColorName string `protobuf:"bytes,2,opt,name=color_name,json=colorName,proto3" json:"color_name,omitempty"`
	// status
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *ListPetColorResult_Color) Reset() {
	*x = ListPetColorResult_Color{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetColorResult_Color) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetColorResult_Color) ProtoMessage() {}

func (x *ListPetColorResult_Color) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetColorResult_Color.ProtoReflect.Descriptor instead.
func (*ListPetColorResult_Color) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListPetColorResult_Color) GetColorId() int64 {
	if x != nil {
		return x.ColorId
	}
	return 0
}

func (x *ListPetColorResult_Color) GetColorName() string {
	if x != nil {
		return x.ColorName
	}
	return ""
}

func (x *ListPetColorResult_Color) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListPetColorResult_Color) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ListPetColorResult_Color) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// color
type CreatePetColorResult_Color struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	ColorId int64 `protobuf:"varint,1,opt,name=color_id,json=colorId,proto3" json:"color_id,omitempty"`
	// name
	ColorName string `protobuf:"bytes,2,opt,name=color_name,json=colorName,proto3" json:"color_name,omitempty"`
	// status
	Status int32 `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *CreatePetColorResult_Color) Reset() {
	*x = CreatePetColorResult_Color{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetColorResult_Color) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetColorResult_Color) ProtoMessage() {}

func (x *CreatePetColorResult_Color) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetColorResult_Color.ProtoReflect.Descriptor instead.
func (*CreatePetColorResult_Color) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *CreatePetColorResult_Color) GetColorId() int64 {
	if x != nil {
		return x.ColorId
	}
	return 0
}

func (x *CreatePetColorResult_Color) GetColorName() string {
	if x != nil {
		return x.ColorName
	}
	return ""
}

func (x *CreatePetColorResult_Color) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CreatePetColorResult_Color) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CreatePetColorResult_Color) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// pet binding list
type ListPetBindingResult_PetColorBinding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// color
	ColorId int64 `protobuf:"varint,2,opt,name=color_id,json=colorId,proto3" json:"color_id,omitempty"`
	// color name
	ColorName string `protobuf:"bytes,3,opt,name=color_name,json=colorName,proto3" json:"color_name,omitempty"`
	// status
	Status int32 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ListPetBindingResult_PetColorBinding) Reset() {
	*x = ListPetBindingResult_PetColorBinding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBindingResult_PetColorBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBindingResult_PetColorBinding) ProtoMessage() {}

func (x *ListPetBindingResult_PetColorBinding) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBindingResult_PetColorBinding.ProtoReflect.Descriptor instead.
func (*ListPetBindingResult_PetColorBinding) Descriptor() ([]byte, []int) {
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ListPetBindingResult_PetColorBinding) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListPetBindingResult_PetColorBinding) GetColorId() int64 {
	if x != nil {
		return x.ColorId
	}
	return 0
}

func (x *ListPetBindingResult_PetColorBinding) GetColorName() string {
	if x != nil {
		return x.ColorName
	}
	return ""
}

func (x *ListPetBindingResult_PetColorBinding) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

var File_moego_api_business_customer_v1_business_pet_color_api_proto protoreflect.FileDescriptor

var file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x14, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xbc, 0x02,
	0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x06, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x06,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x73, 0x1a, 0xd3, 0x01, 0x0a, 0x05, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x35, 0x0a, 0x14,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0xd6, 0x02, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52,
	0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x1a, 0xd3, 0x01, 0x0a, 0x05, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3a, 0x0a, 0x14,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x22, 0x0a, 0x08, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x2e, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x36, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x1e, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x22, 0x88, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x74, 0x0a, 0x13, 0x70, 0x65, 0x74,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x73, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x11, 0x70, 0x65,
	0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x73, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x1a,
	0x7a, 0x0a, 0x0f, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd4, 0x01, 0x0a, 0x12,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x15, 0x0a,
	0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x12, 0x51, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x39, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52,
	0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0x02, 0x22, 0x2c, 0x0a, 0x12, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x32, 0x83, 0x05, 0x0a, 0x17, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x65, 0x74,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x0c,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x7c, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x76,
	0x0a, 0x0c, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x32,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x95, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescOnce sync.Once
	file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescData = file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDesc
)

func file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescGZIP() []byte {
	file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescOnce.Do(func() {
		file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescData)
	})
	return file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDescData
}

var file_moego_api_business_customer_v1_business_pet_color_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_api_business_customer_v1_business_pet_color_api_proto_goTypes = []interface{}{
	(BindingColorParams_Status)(0),               // 0: moego.api.business_customer.v1.BindingColorParams.Status
	(*ListPetColorParams)(nil),                   // 1: moego.api.business_customer.v1.ListPetColorParams
	(*ListPetColorResult)(nil),                   // 2: moego.api.business_customer.v1.ListPetColorResult
	(*CreatePetColorParams)(nil),                 // 3: moego.api.business_customer.v1.CreatePetColorParams
	(*CreatePetColorResult)(nil),                 // 4: moego.api.business_customer.v1.CreatePetColorResult
	(*DeletePetColorParams)(nil),                 // 5: moego.api.business_customer.v1.DeletePetColorParams
	(*DeletePetColorResult)(nil),                 // 6: moego.api.business_customer.v1.DeletePetColorResult
	(*ListPetBindingParams)(nil),                 // 7: moego.api.business_customer.v1.ListPetBindingParams
	(*ListPetBindingResult)(nil),                 // 8: moego.api.business_customer.v1.ListPetBindingResult
	(*BindingColorParams)(nil),                   // 9: moego.api.business_customer.v1.BindingColorParams
	(*BindingColorResult)(nil),                   // 10: moego.api.business_customer.v1.BindingColorResult
	(*ListPetColorResult_Color)(nil),             // 11: moego.api.business_customer.v1.ListPetColorResult.Color
	(*CreatePetColorResult_Color)(nil),           // 12: moego.api.business_customer.v1.CreatePetColorResult.Color
	(*ListPetBindingResult_PetColorBinding)(nil), // 13: moego.api.business_customer.v1.ListPetBindingResult.PetColorBinding
	(*timestamppb.Timestamp)(nil),                // 14: google.protobuf.Timestamp
}
var file_moego_api_business_customer_v1_business_pet_color_api_proto_depIdxs = []int32{
	11, // 0: moego.api.business_customer.v1.ListPetColorResult.colors:type_name -> moego.api.business_customer.v1.ListPetColorResult.Color
	12, // 1: moego.api.business_customer.v1.CreatePetColorResult.color:type_name -> moego.api.business_customer.v1.CreatePetColorResult.Color
	13, // 2: moego.api.business_customer.v1.ListPetBindingResult.pet_colors_bindings:type_name -> moego.api.business_customer.v1.ListPetBindingResult.PetColorBinding
	0,  // 3: moego.api.business_customer.v1.BindingColorParams.status:type_name -> moego.api.business_customer.v1.BindingColorParams.Status
	14, // 4: moego.api.business_customer.v1.ListPetColorResult.Color.create_time:type_name -> google.protobuf.Timestamp
	14, // 5: moego.api.business_customer.v1.ListPetColorResult.Color.update_time:type_name -> google.protobuf.Timestamp
	14, // 6: moego.api.business_customer.v1.CreatePetColorResult.Color.create_time:type_name -> google.protobuf.Timestamp
	14, // 7: moego.api.business_customer.v1.CreatePetColorResult.Color.update_time:type_name -> google.protobuf.Timestamp
	1,  // 8: moego.api.business_customer.v1.BusinessPetColorService.ListPetColor:input_type -> moego.api.business_customer.v1.ListPetColorParams
	3,  // 9: moego.api.business_customer.v1.BusinessPetColorService.CreatePetColor:input_type -> moego.api.business_customer.v1.CreatePetColorParams
	5,  // 10: moego.api.business_customer.v1.BusinessPetColorService.DeletePetColor:input_type -> moego.api.business_customer.v1.DeletePetColorParams
	7,  // 11: moego.api.business_customer.v1.BusinessPetColorService.ListPetBinding:input_type -> moego.api.business_customer.v1.ListPetBindingParams
	9,  // 12: moego.api.business_customer.v1.BusinessPetColorService.BindingColor:input_type -> moego.api.business_customer.v1.BindingColorParams
	2,  // 13: moego.api.business_customer.v1.BusinessPetColorService.ListPetColor:output_type -> moego.api.business_customer.v1.ListPetColorResult
	4,  // 14: moego.api.business_customer.v1.BusinessPetColorService.CreatePetColor:output_type -> moego.api.business_customer.v1.CreatePetColorResult
	6,  // 15: moego.api.business_customer.v1.BusinessPetColorService.DeletePetColor:output_type -> moego.api.business_customer.v1.DeletePetColorResult
	8,  // 16: moego.api.business_customer.v1.BusinessPetColorService.ListPetBinding:output_type -> moego.api.business_customer.v1.ListPetBindingResult
	10, // 17: moego.api.business_customer.v1.BusinessPetColorService.BindingColor:output_type -> moego.api.business_customer.v1.BindingColorResult
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_business_customer_v1_business_pet_color_api_proto_init() }
func file_moego_api_business_customer_v1_business_pet_color_api_proto_init() {
	if File_moego_api_business_customer_v1_business_pet_color_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetColorParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetColorResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetColorParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetColorResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetColorParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetColorResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBindingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBindingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindingColorParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindingColorResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetColorResult_Color); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetColorResult_Color); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBindingResult_PetColorBinding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_business_customer_v1_business_pet_color_api_proto_goTypes,
		DependencyIndexes: file_moego_api_business_customer_v1_business_pet_color_api_proto_depIdxs,
		EnumInfos:         file_moego_api_business_customer_v1_business_pet_color_api_proto_enumTypes,
		MessageInfos:      file_moego_api_business_customer_v1_business_pet_color_api_proto_msgTypes,
	}.Build()
	File_moego_api_business_customer_v1_business_pet_color_api_proto = out.File
	file_moego_api_business_customer_v1_business_pet_color_api_proto_rawDesc = nil
	file_moego_api_business_customer_v1_business_pet_color_api_proto_goTypes = nil
	file_moego_api_business_customer_v1_business_pet_color_api_proto_depIdxs = nil
}
