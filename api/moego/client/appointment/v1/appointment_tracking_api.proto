syntax = "proto3";

package moego.client.appointment.v1;

import "moego/models/appointment/v1/appointment_tracking.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.appointment.v1";

// The appointment tracking service
service AppointmentTrackingService {
  // get appointment tracking
  rpc GetAppointmentTracking(GetAppointmentTrackingParams) returns (GetAppointmentTrackingResult);
}

// get appointment tracking request
message GetAppointmentTrackingParams {
  // appointment id
  int64 appointment_id = 1;
}

// get appointment tracking response
message GetAppointmentTrackingResult {
  // appointment tracking
  moego.models.appointment.v1.AppointmentTrackingView appointment_tracking = 1;
}
