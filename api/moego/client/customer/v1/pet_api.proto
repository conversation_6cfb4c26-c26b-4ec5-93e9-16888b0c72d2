syntax = "proto3";

package moego.client.customer.v1;

import "moego/models/customer/v1/customer_pet_models.proto";
import "moego/models/customer/v1/customer_pet_vaccine_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/customer/v1;customerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.customer.v1";

// get pet list request
message GetPetListRequest {}

// get pet list response
message GetPetListResponse {
  // pet model
  repeated GetPetResponse pets = 1;
}

// get pet response
message GetPetResponse {
  // pet model
  moego.models.customer.v1.CustomerPetModel pet = 1;
  // pet vaccine list
  repeated moego.models.customer.v1.PetVaccineSimpleView vaccines = 2;
}

// pet service
service PetService {
  // get pet list
  rpc GetPetList(GetPetListRequest) returns (GetPetListResponse);
}
