syntax = "proto3";

package moego.client.account.v1;

import "google/type/date.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/account/v1;accountapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.account.v1";

// The params message for ListAccountPets
message ListAccountPetsParams {
  // The company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// The result message for ListAccountPets
message ListAccountPetsResult {
  // The list of account pets
  repeated AccountPetItem pets = 1;

  // Account pet item
  message AccountPetItem {
    // The business pet profile
    moego.models.business_customer.v1.BusinessCustomerPetBrandedAppView business_pet = 1;
    // The business pet vaccine profile
    repeated AccountPetVaccineRecordItem vaccine_records = 2;
  }

  // pet vaccine record item
  message AccountPetVaccineRecordItem {
    // vaccine record id
    int64 id = 1;

    // pet id
    int64 pet_id = 2;

    // vaccine id
    int64 vaccine_id = 3;

    // expiration date, may not exist
    optional google.type.Date expiration_date = 4;

    // vaccine document urls
    repeated string document_urls = 5;

    // vaccine name
    string vaccine_name = 6;
  }
}

// The params message for CreateAccountPet
message CreateAccountPetParams {
  // The company id
  optional int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // The pet profile
  AccountPetCreateDef pet = 2 [(validate.rules).message = {required: true}];
}

// The account pet create definition
message AccountPetCreateDef {
  // pet name
  string name = 1 [(validate.rules).string = {max_len: 50}];

  // avatar path, default is empty
  string avatar_path = 2 [(validate.rules).string = {
    ignore_empty: true
    uri: true
    max_len: 255
  }];

  // pet type id, required
  moego.models.customer.v1.PetType pet_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // breed, default is empty
  optional string breed = 4 [(validate.rules).string = {max_len: 50}];

  // breed mixed
  optional bool breed_mixed = 5;

  // weight, optional
  // should be a number string
  optional string weight = 7 [(validate.rules).string = {max_len: 50}];

  // coat type, optional
  optional string coat_type = 8 [(validate.rules).string = {max_len: 50}];
}

// The result message for CreateAccountPet
message CreateAccountPetResult {
  // The pet id
  int64 pet_id = 1;
}

// AccountPetService is the service for manage account pet profile. Requires C-side login
service AccountPetService {
  // List account pets
  rpc ListAccountPets(ListAccountPetsParams) returns (ListAccountPetsResult);

  // Create a new account pet
  rpc CreateAccountPet(CreateAccountPetParams) returns (CreateAccountPetResult);
}
