// @since 2024-08-29 10:29:03
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.online_booking.v1;

import "moego/models/offering/v1/service_enum.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// set customer block status request
message SetCustomerBlockStatusRequest {
  // service item types
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 1 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 10
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // customer ids
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // need to block, true: block, false: unblock
  bool need_block = 3;
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// set customer block status response
message SetCustomerBlockStatusResponse {}

// list blocked customer request
message ListBlockedCustomerRequest {
  // service item types, only support grooming/boarding/daycare
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 1 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    max_items: 10
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // customer ids
  repeated int64 customer_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message = {required: true}];
  // company id, if it's zero, will ignore this field
  int64 company_id = 4 [(validate.rules).int64 = {gte: 0}];
}

// list blocked customer response
message ListBlockedCustomerResponse {
  // block customer ids
  repeated CustomerBlockInfo customer_block_infos = 1;
  // info
  message CustomerBlockInfo {
    // customer id
    int64 customer_id = 1;
    // service item types
    repeated moego.models.offering.v1.ServiceItemType service_item_types = 2;
  }
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// customer availability service
service CustomerAvailabilityService {
  // set customer block status
  rpc SetCustomerBlockStatus(SetCustomerBlockStatusRequest) returns (SetCustomerBlockStatusResponse);
  // list blocked customer
  rpc ListBlockedCustomer(ListBlockedCustomerRequest) returns (ListBlockedCustomerResponse);
}
