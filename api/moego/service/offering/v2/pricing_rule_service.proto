// @since 2025-02-26 16:46:24
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.offering.v2;

import "moego/models/offering/v2/pricing_rule_defs.proto";
import "moego/models/offering/v2/pricing_rule_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v2;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v2";

// upsert pricing rule request
message UpsertPricingRuleRequest {
  // pricing_rule def
  moego.models.offering.v2.PricingRuleUpsertDef pricing_rule_def = 1 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 6 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 7 [(validate.rules).int64 = {gte: 0}];
}

// upsert pricing rule response
message UpsertPricingRuleResponse {
  // the created pricing_rule
  moego.models.offering.v2.PricingRule pricing_rule = 1;
}

// get pricing rule request
message GetPricingRuleRequest {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 6 [(validate.rules).int64 = {gt: 0}];
}

// get pricing rule response
message GetPricingRuleResponse {
  // the pricing rule
  moego.models.offering.v2.PricingRule pricing_rule = 1;
}

// list pricing rule request
message ListPricingRulesRequest {
  // filter
  moego.models.offering.v2.ListPricingRuleFilter filter = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2;
  // company id
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// list pricing rule response
message ListPricingRulesResponse {
  // the pricing rule list
  repeated moego.models.offering.v2.PricingRule pricing_rules = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// calculate pricing rule request
message CalculatePricingRuleRequest {
  // pet detail list
  repeated moego.models.offering.v2.PetDetailCalculateDef pet_details = 1 [(validate.rules).repeated = {min_items: 1}];
  // discount setting
  optional moego.models.offering.v2.DiscountSettingDef setting = 3;
  // calculate for preview
  bool is_preview = 4;
  // company id
  int64 company_id = 9 [(validate.rules).int64 = {gt: 0}];
}

// calculate pricing rule response
message CalculatePricingRuleResponse {
  // pet detail list
  repeated moego.models.offering.v2.PetDetailCalculateResultDef pet_details = 1;
  // formula for preview calculation
  optional string formula = 2;
}

// delete pricing rule request
message DeletePricingRuleRequest {
  // the unique id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// delete pricing rule response
message DeletePricingRuleResponse {}

// get discount setting params
message GetDiscountSettingRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get discount setting result
message GetDiscountSettingResponse {
  // discount setting
  moego.models.offering.v2.DiscountSettingDef setting = 1;
}

// update discount setting params
message UpdateDiscountSettingRequest {
  // discount setting
  moego.models.offering.v2.DiscountSettingDef setting = 1 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 3 [(validate.rules).int64 = {gte: 0}];
}

// update discount setting result
message UpdateDiscountSettingResponse {
  // discount setting
  moego.models.offering.v2.DiscountSettingDef setting = 1;
}

// pricing rule service
service PricingRuleService {
  // upsert pricing rule
  rpc UpsertPricingRule(UpsertPricingRuleRequest) returns (UpsertPricingRuleResponse);
  // get pricing rule
  rpc GetPricingRule(GetPricingRuleRequest) returns (GetPricingRuleResponse);
  // list pricing rule
  rpc ListPricingRules(ListPricingRulesRequest) returns (ListPricingRulesResponse);
  // calculate pricing rule
  rpc CalculatePricingRule(CalculatePricingRuleRequest) returns (CalculatePricingRuleResponse);
  // delete pricing rule
  rpc DeletePricingRule(DeletePricingRuleRequest) returns (DeletePricingRuleResponse);

  // get discount setting
  rpc GetDiscountSetting(GetDiscountSettingRequest) returns (GetDiscountSettingResponse);
  // update discount setting
  rpc UpdateDiscountSetting(UpdateDiscountSettingRequest) returns (UpdateDiscountSettingResponse);
}
