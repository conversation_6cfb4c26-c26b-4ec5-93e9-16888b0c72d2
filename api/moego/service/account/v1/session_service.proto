syntax = "proto3";

package moego.service.account.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "moego/models/account/v1/session_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// get session token request
message GetSessionTokenRequest {
  option deprecated = true;
  // session token
  string session_token = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 256
  }];
}

// get session request
message GetSessionRequest {
  // session identifier
  oneof identifier {
    option (validate.required) = true;

    // id
    int64 id = 1;

    // session token
    string session_token = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 256
    }];
  }
}

// create session request
message CreateSessionRequest {
  // account id
  int64 account_id = 1;

  // ip
  string ip = 2 [(validate.rules).string = {ip: true}];

  // user agent
  string user_agent = 3 [(validate.rules).string = {max_len: 256}];

  // referer link
  string referer_link = 4 [(validate.rules).string = {
    ignore_empty: true
    max_len: 256
  }];

  // referer session id
  int64 referer_session_id = 5;

  // device id
  string device_id = 13 [(validate.rules).string = {max_len: 64}];

  // impersonator
  string impersonator = 6 [(validate.rules).string = {max_len: 64}];

  // max age, second
  google.protobuf.Duration max_age = 7 [(validate.rules).duration = {
    required: true
    gt: {
      seconds: 0
      nanos: 0
    }
  }];

  // source
  string source = 8 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];

  // renewable, if not set, default is true
  optional bool renewable = 9;

  // allow frozen account to create session, if not set, default is false (not allow)
  optional bool allow_frozen_account = 11;

  // allow deleted account to create session, if not set, default is false (not allow)
  optional bool allow_deleted_account = 12;

  // session data
  google.protobuf.Struct session_data = 10;
}

// update session request
message UpdateSessionRequest {
  // id
  int64 id = 1;

  // ip
  optional string ip = 2 [(validate.rules).string = {ip: true}];

  // user agent
  optional string user_agent = 3 [(validate.rules).string = {max_len: 256}];

  // account_id
  optional int64 account_id = 4;

  // referer session id
  optional int64 referer_session_id = 5;

  // max age, second
  optional google.protobuf.Duration max_age = 7 [(validate.rules).duration = {
    gt: {
      seconds: 0
      nanos: 0
    }
  }];

  // renewable
  optional bool renewable = 9;

  // session data, existing values will be overwritten and null values will be deleted
  optional google.protobuf.Struct session_data = 10;
}

// batch update session request
message BatchUpdateSessionRequest {
  // session id list. No longer used. Use ByIds instead.
  repeated int64 ids = 1 [deprecated = true];

  // account_id. No longer used. Use ByAccountAndSource instead.
  optional int64 account_id = 2 [deprecated = true];

  // referer session id
  optional int64 referer_session_id = 3;

  // max age, second
  optional google.protobuf.Duration max_age = 4 [(validate.rules).duration = {
    gt: {
      seconds: 0
      nanos: 0
    }
  }];

  // ... TODO: add other fields when needed

  // condition, at least one condition should be set
  oneof condition {
    option (validate.required) = true;

    // update by session id list
    ByIds by_ids = 10;

    // update by account id
    ByAccountAndSource by_account_and_source = 11;
  }

  // by ids
  message ByIds {
    // session id list
    repeated int64 ids = 1 [(validate.rules).repeated = {
      min_items: 1
      max_items: 1000
      unique: true
      items: {
        int64: {gt: 0}
      }
    }];
  }

  // by account and source
  message ByAccountAndSource {
    // account id
    int64 account_id = 1;

    // source. If set, only sessions of this source will be updated, else all sessions of this account will be updated.
    optional string source = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];
  }
}

// batch update session response
message BatchUpdateSessionResponse {
  // session list. Temporarily, this field should not be used because no data will be returned.
  repeated moego.models.account.v1.SessionModel sessions = 1 [deprecated = true];
}

// create session response
message CreateSessionResponse {
  // session token
  string session_token = 1;
  // session id
  int64 id = 2;
  // if this is the first session from given source
  bool first_session = 3;
}

// validate session request
message ValidateSessionRequest {
  // session token
  string session_token = 1 [(validate.rules).string = {max_len: 256}];

  // ip
  string ip = 2 [(validate.rules).string = {ip: true}];

  // user agent
  string user_agent = 3 [(validate.rules).string = {max_len: 256}];
}

// get session list by account id request
message GetSessionListByAccountIdRequest {
  // account id
  int64 account_id = 1;

  // include impersonator
  bool include_impersonator = 2;
}

// get session list by account id response
message GetSessionListByAccountIdResponse {
  // session list
  repeated moego.models.account.v1.SessionModel sessions = 1;
}

// delete session by id request
message DeleteSessionByIdRequest {
  // session id
  int64 id = 1;
}

// delete all sessions by account id request
message DeleteAllSessionsByAccountIdRequest {
  // account id
  int64 account_id = 1;
  // ignored session ids
  repeated int64 ignored_session_ids = 2 [(validate.rules).repeated = {
    max_items: 500
    ignore_empty: true
  }];
}

// session service
service SessionService {
  // Get session by session token.
  // Deprecated: use GetSession instead.
  //
  // Error codes:
  // - CODE_SESSION_NOT_EXIST: The session does not exist or has been deleted.
  // - CODE_SESSION_EXPIRED: The session has expired.
  // - CODE_SESSION_TOKEN_INVALID: The session token is invalid.
  // - CODE_PARAMS_ERROR: The format of session token is incorrect.
  rpc GetSessionByToken(GetSessionTokenRequest) returns (moego.models.account.v1.SessionModel) {
    option deprecated = true;
  }

  // Get session by id or session token.
  // If get by session token, only legal and not deleted and not expired session can be fetched.
  // If get by id, the session can be fetched without checking status, expiration time or token.
  //
  // Error codes:
  // - CODE_SESSION_NOT_EXIST: The session does not exist, or is deleted but gotten by session token.
  // - CODE_SESSION_EXPIRED: The session has expired.
  // - CODE_SESSION_TOKEN_INVALID: The session token is invalid.
  // - CODE_PARAMS_ERROR: The format of session token is incorrect.
  rpc GetSession(GetSessionRequest) returns (moego.models.account.v1.SessionModel);

  // Get session list by account id.
  // Expired sessions can be found while deleted sessions can not.
  // If impersonator's sessions are needed, set `include_impersonator` = true in the request.
  rpc GetSessionListByAccountId(GetSessionListByAccountIdRequest) returns (GetSessionListByAccountIdResponse);

  // Create a new session.
  rpc CreateSession(CreateSessionRequest) returns (CreateSessionResponse);

  // Update an existing session, even if it has expired or been deleted.
  // If the inputted session data is empty, the session data in the database will not be updated or deleted.
  // If the values are NULL for certain keys, these keys will be deleted from database.
  // If the values are not NULL for certain keys, these keys will be created or updated into database.
  // If certain keys show in database but not show in the inputted session data, these key will remain in database.
  //
  // Error codes:
  // - CODE_SESSION_NOT_EXIST: The session does not exist.
  rpc UpdateSession(UpdateSessionRequest) returns (moego.models.account.v1.SessionModel);

  // Batch update sessions. Two update conditions are supported.
  // - By ids:
  //   Update sessions by given ids. Expired sessions and deleted sessions can be updated.
  // - By account and source:
  //   Update sessions by given account id and source. Expired sessions can be updated, while deleted sessions cannot be updated.
  //   If source is set, only sessions of this source will be updated, else all sessions of this account will be updated.
  rpc BatchUpdateSession(BatchUpdateSessionRequest) returns (BatchUpdateSessionResponse);

  // Delete a session.
  // Request is idempotent if the session has been deleted.
  //
  // Error codes:
  // - CODE_SESSION_NOT_EXIST: The session does not exist.
  rpc DeleteSessionById(DeleteSessionByIdRequest) returns (google.protobuf.Empty);

  // Delete all sessions by account id.
  // This method won't throw an exception if no sessions need to be deleted.
  rpc DeleteAllSessionsByAccountId(DeleteAllSessionsByAccountIdRequest) returns (google.protobuf.Empty);
}
