syntax = "proto3";

package moego.models.grooming.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1;groomingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.grooming.v1";

// appointment type
enum AppointmentSource {
  // unspecified
  APPOINTMENT_SOURCE_UNSPECIFIED = 0;
  // web
  APPOINTMENT_SOURCE_WEB = 22018;
  // online booking
  APPOINTMENT_SOURCE_OB = 22168;
  // android
  APPOINTMENT_SOURCE_ANDROID = 17216;
  // ios
  APPOINTMENT_SOURCE_IOS = 17802;
  // auto dm
  APPOINTMENT_SOURCE_AUTO_DM = 23426;
  // google calendar
  APPOINTMENT_SOURCE_GOOGLE_CALENDAR = 19826;
  // open api
  APPOINTMENT_SOURCE_OPEN_API = 23333;
}

// appointment status
enum AppointmentStatus {
  // unspecified
  APPOINTMENT_STATUS_UNSPECIFIED = 0;
  // unconfirmed
  APPOINTMENT_STATUS_UNCONFIRMED = 1;
  // confirmed
  APPOINTMENT_STATUS_CONFIRMED = 2;
  // finished
  APPOINTMENT_STATUS_FINISHED = 3;
  // canceled
  APPOINTMENT_STATUS_CANCELED = 4;
  // ready to check in
  APPOINTMENT_STATUS_READY = 5;
  // checked in
  APPOINTMENT_STATUS_CHECK_IN = 6;
}

// appointment payment status
enum AppointmentPaymentStatus {
  // unspecified
  APPOINTMENT_PAYMENT_STATUS_UNSPECIFIED = 0;
  // fully paid
  APPOINTMENT_PAYMENT_STATUS_FULLY_PAID = 1;
  // unpaid
  APPOINTMENT_PAYMENT_STATUS_UNPAID = 2;
  // partial paid
  APPOINTMENT_PAYMENT_STATUS_PARTIAL_PAID = 3;
  // prepaid
  APPOINTMENT_PAYMENT_STATUS_PREPAID = 4;
}

// appointment type
enum AppointmentType {
  // unspecified
  APPOINTMENT_TYPE_UNSPECIFIED = 0;
  // upcoming appointment
  APPOINTMENT_TYPE_UPCOMING = 1;
  // history appointment
  APPOINTMENT_TYPE_PAST = 2;
  // booking request
  APPOINTMENT_TYPE_PENDING = 3;
}

// appointment list sort field
enum AppointmentSortField {
  // unspecified
  APPOINTMENT_SORT_FIELD_UNSPECIFIED = 0;
  // appointment date
  APPOINTMENT_SORT_FIELD_APPOINTMENT_DATE = 1;
  // appointment start time
  APPOINTMENT_SORT_FIELD_APPOINTMENT_START_TIME = 2;
}

// AppointmentScheduleType
enum AppointmentScheduleType {
  // unspecified
  APPOINTMENT_SCHEDULE_TYPE_UNSPECIFIED = 0;
  // normal repeat
  NORMAL_REPEAT = 1;
  // smart schedule repeat
  SMART_SCHEDULE_REPEAT = 2;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// AppointmentUpdatedBy
enum AppointmentUpdatedBy {
  // by business
  BY_BUSINESS = 0;
  // by customer reply msg
  BY_CUSTOMER_REPLY_MESSAGE = 1;
  // by delete pet
  BY_DELETE_PET = 2;
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// AppointmentNotificationStatus
enum AppointmentNotificationStatus {
  // not sent
  NOTIFICATION_NOT_SENT = 0;
  // success
  NOTIFICATION_SUCCESS = 1;
  // failed
  NOTIFICATION_FAILED = 2;
}

// AppointmentReminderType
enum AppointmentReminderType {
  // unspecified
  APPOINTMENT_REMINDER_TYPE_UNSPECIFIED = 0;
  // message
  MESSAGE = 1;
  // email
  EMAIL = 2;
  // phone call
  PHONE_CALL = 3;
}
