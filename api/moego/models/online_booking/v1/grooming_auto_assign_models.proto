syntax = "proto3";

package moego.models.online_booking.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.online_booking.v1";

// appointment auto assign record
message GroomingAutoAssignModel {
  // id
  int64 id = 1;
  // bookingRequestId
  int64 booking_request_id = 2;
  // auto assign staff id
  optional int64 staff_id = 3;
  // auto assign appointment time, unit minute, 540 means 09:00
  optional int32 start_time = 4;
  // createdAt
  google.protobuf.Timestamp created_at = 5;
  // updatedAt
  google.protobuf.Timestamp updated_at = 6;
  // grooming service detail id
  int64 grooming_service_detail_id = 7;
}

// grooming auto assign view
message GroomingAutoAssignView {
  // id
  int64 id = 1;
  // bookingRequestId
  int64 booking_request_id = 2;
  // auto assign staff id
  optional int64 staff_id = 3;
  // auto assign appointment time, unit minute, 540 means 09:00
  optional int32 start_time = 4;
}
