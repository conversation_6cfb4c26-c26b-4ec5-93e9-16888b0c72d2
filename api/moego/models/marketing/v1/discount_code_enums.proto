// @since 2023-09-11 14:34:19
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.marketing.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1;marketingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.marketing.v1";

// discount code type
enum DiscountCodeType {
  // unspecified value
  DISCOUNT_CODE_TYPE_UNSPECIFIED = 0;
  // discount code percentage
  DISCOUNT_CODE_TYPE_PERCENTAGE = 1;
  // discount code fixed amount
  DISCOUNT_CODE_TYPE_FIXED_AMOUNT = 2;
  // discount code credit
  DISCOUNT_CODE_TYPE_CREDIT = 3;
}

// discount code status
enum DiscountCodeStatus {
  // unspecified value
  DISCOUNT_CODE_STATUS_UNSPECIFIED = 0;
  // discount is active
  DISCOUNT_CODE_STATUS_ACTIVE = 1;
  // discount is inactive
  DISCOUNT_CODE_STATUS_INACTIVE = 2;
  // discount is archived
  DISCOUNT_CODE_STATUS_ARCHIVED = 3;
  // discount is deleted
  DISCOUNT_CODE_STATUS_DELETED = 4;
  // discount is expired
  DISCOUNT_CODE_STATUS_EXPIRED = 5;
}

// redeem type
enum RedeemType {
  // unspecified value
  REDEEM_TYPE_UNSPECIFIED = 0;
  // redeem by appointment
  REDEEM_TYPE_APPOINTMENT = 1;
  // redeem by online booking
  REDEEM_TYPE_ONLINE_BOOKING = 2;
  // redeem by product
  REDEEM_TYPE_PRODUCT = 3;
  // redeem by package
  REDEEM_TYPE_PACKAGE = 4;
}

// expiry type
enum ExpiryType {
  // unspecified value
  EXPIRY_TYPE_UNSPECIFIED = 0;
  // never expiry
  EXPIRY_TYPE_NEVER = 1;
  // expiry by time
  EXPIRY_TYPE_TIME = 2;
}
