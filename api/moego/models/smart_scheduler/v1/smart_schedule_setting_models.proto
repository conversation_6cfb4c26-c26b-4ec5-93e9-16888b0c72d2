syntax = "proto3";

package moego.models.smart_scheduler.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/smart_scheduler/v1;smartschedulerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.smart_scheduler.v1";

// service model
message SmartScheduleSettingModel {
  // company id
  int64 company_id = 1;
  // certain area for certain days, mobile business setting(company level)
  bool service_area_enable = 2;
  // appointment buffer time(company level, if param set business_id, use business level)
  int32 buffer_time = 3;
}

// business setting override model
message BusinessSettingOverrideModel {
  // business id
  int64 business_id = 1;
  // buffer time
  optional int32 buffer_time = 2;
}
