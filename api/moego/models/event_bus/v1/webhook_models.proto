syntax = "proto3";

package moego.models.event_bus.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// model for webhook delivery sent
message WebhookDeliverySentEvent {
  // webhook delivery id
  int64 id = 1;
  // webhook id
  int64 webhook_id = 2;
  // event id
  string event_id = 3;
}
