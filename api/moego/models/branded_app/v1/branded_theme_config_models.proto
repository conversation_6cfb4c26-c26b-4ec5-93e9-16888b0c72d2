// @since 2024-06-03 14:43:06
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.branded_app.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/branded_app/v1;brandedapppb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.branded_app.v1";

// The Branded App theme config model
message BrandedThemeConfigModel {
  // the unique id
  int64 id = 1;

  // the branded app id
  string branded_app_id = 2;

  // the theme name
  string theme_name = 3;

  // the logo type
  LogoType logo_type = 4;

  // the home icon url
  string home_icon_url = 5;

  // branded logo url
  string branded_logo_url = 6;

  // branded font url
  string branded_font_url = 7;

  // introduction image url
  string introduction_image_url = 8;

  // introduction text color
  string introduction_text_color = 9;

  // theme color
  string theme_color = 10;

  // sub theme color
  string sub_theme_color = 11;

  // custom background image url
  string custom_background_image_url = 12;

  // custom theme
  string custom_theme = 13;

  // Logo type
  enum LogoType {
    // Unspecified
    LOGO_TYPE_UNSPECIFIED = 0;
    // logo only
    LOGO_ONLY = 1;
    // logo with text
    LOGO_WITH_TEXT = 2;
  }
}
