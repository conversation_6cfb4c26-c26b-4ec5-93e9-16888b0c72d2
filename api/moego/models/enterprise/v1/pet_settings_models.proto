syntax = "proto3";

package moego.models.enterprise.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/business_customer/v1/business_pet_metadata_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// pet code
message PetCode {
  // pet code id
  int64 id = 1;
  // pet code abbreviation
  string abbreviation = 2;
  // pet code description
  string description = 3;
  // pet code color
  string color = 4;
  // pet code sort. The larger the sort number, the higher the priority.
  int32 sort = 5;
  // updated at
  google.protobuf.Timestamp updated_at = 6;
  // pushed at
  optional google.protobuf.Timestamp pushed_at = 7;
}

// pet code update def
message PetCodeUpdateDef {
  // pet code abbreviation
  optional string abbreviation = 1;
  // pet code description
  optional string description = 2;
  // pet code color
  optional string color = 3;
}

// pet code create def
message PetCodeCreateDef {
  // pet code abbreviation
  string abbreviation = 1;
  // pet code description
  string description = 2;
  // pet code color
  string color = 3;
}

// pet codes
message PetCodeLimitationDef {
  // limit type
  enum LimitType {
    // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
    // all pet codes
    ALL_PET_CODES = 0;
    // required pet codes
    REQUIRED_PET_CODES = 1;
    // excluded pet codes
    EXCLUDED_PET_CODES = 2;
  }
  // limit type
  LimitType limit_type = 1;
  // pet codes
  repeated int64 pet_code_ids = 2;
}

// pet metadata
message PetMetadata {
  // id
  int64 id = 1;
  // enterprise id
  int64 enterprise_id = 2;
  // metadata name
  models.business_customer.v1.BusinessPetMetadataName metadata_name = 3;
  // metadata value
  string metadata_value = 4;
  // extra json data, can customize additional metadata information.
  map<string, string> extra_json = 5;
  // sort number
  int32 sort = 6;
  // created at
  google.protobuf.Timestamp created_at = 7;
  // updated at
  google.protobuf.Timestamp updated_at = 8;
  // pushed at
  optional google.protobuf.Timestamp pushed_at = 9;
}

// pet metadata update def
message PetMetadataUpdateDef {
  // metadata value
  optional string metadata_value = 1;
  // use to check whether extra_json should be updated
  bool update_extra_json = 2;
  // extra json data, can customize additional metadata information.
  map<string, string> extra_json = 3;
}

// pet metadata create def
message PetMetadataCreateDef {
  // metadata name
  models.business_customer.v1.BusinessPetMetadataName metadata_name = 1;
  // metadata value
  string metadata_value = 2;
  // extra json data, can customize additional metadata information.
  map<string, string> extra_json = 3;
}
