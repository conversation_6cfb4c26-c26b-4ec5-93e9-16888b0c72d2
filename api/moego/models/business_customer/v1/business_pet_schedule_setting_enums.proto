syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// Business pet schedule type for feeding and medication
enum BusinessPetScheduleType {
  // unspecified
  BUSINESS_PET_SCHEDULE_TYPE_UNSPECIFIED = 0;
  // feeding
  FEEDING = 1;
  // medication
  MEDICATION = 2;
}
