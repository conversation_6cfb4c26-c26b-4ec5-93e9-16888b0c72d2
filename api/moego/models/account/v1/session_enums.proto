syntax = "proto3";

package moego.models.account.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// session status
enum SessionStatus {
  // unspecified
  SESSION_STATUS_UNSPECIFIED = 0;
  // active
  SESSION_STATUS_ACTIVE = 1;
  // deleted
  SESSION_STATUS_DELETED = 2;
}
