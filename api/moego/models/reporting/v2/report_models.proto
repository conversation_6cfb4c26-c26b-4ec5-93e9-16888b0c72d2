syntax = "proto3";

package moego.models.reporting.v2;

import "moego/models/reporting/v2/diagram_model.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.reporting.v2";

// A page of reports
message ReportPage {
  // A report tab
  enum Tab {
    // Unspecified report page tab
    TAB_UNSPECIFIED = 0;
    // all report pages
    ALL = 1;
    // A report tab for a finance report
    FINANCE = 2;
    // A report tab for a sales report
    SALES = 3;
    // A report tab for an appointment report
    APPOINTMENT = 4;
    // A report tab for an employee report
    EMPLOYEE = 5;
    // A report tab for a payroll report
    PAYROLL = 6;
    // A report tab for a client insights report
    CLIENT_INSIGHTS = 7;
    // A report tab for a customer report
    CUSTOMER = 8;
    // A report tab for a tenant report
    TENANT = 9;
    // Customized report tab
    CUSTOMIZED = 10;
    // Legacy reports tab for mobile app
    LEGACY_APPOINTMENT = 101;
  }

  // The title of the page
  string title = 1;
  // The tab of the report
  Tab tab = 2;
  // The reports of the page
  repeated ReportDiagram diagrams = 3;
  // Current page's required permission code
  string permission_code = 6;
}

// A report entry
message ReportDiagram {
  // The diagram id
  string diagram_id = 1;
  // The diagram type
  moego.models.reporting.v2.DiagramType type = 2;
  // The title of the report
  string title = 3;
  // The description of the report
  string description = 4;
  // Whether the report is a favorite
  bool favorite = 5;
  // Current diagram's required permission code
  string permission_code = 6;
  // Whether the report is a custom report
  bool is_customized = 7;
}
