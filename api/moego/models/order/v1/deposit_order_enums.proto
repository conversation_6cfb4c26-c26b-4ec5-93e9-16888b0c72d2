syntax = "proto3";

package moego.models.order.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// Deposit change log type.
enum DepositChangeType {
  // Unspecified.
  DEPOSIT_CHANGE_TYPE_UNSPECIFIED = 0;
  // Increase.
  INCREASE = 1;
  // Decrease.
  DECREASE = 2;
}

// Deposit change reason
enum DepositChangeReason {
  // Unspecified.
  DEPOSIT_CHANGE_REASON_UNSPECIFIED = 0;
  // Top-up.
  TOP_UP = 1;
  // Deduction.
  DEDUCTION = 2;
  // Overpayment reversal.
  OVERPAYMENT_REVERSAL = 3;
  // Forfeit.
  FORFEIT = 4;
}
