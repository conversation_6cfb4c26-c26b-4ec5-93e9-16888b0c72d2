syntax = "proto3";

package moego.models.order.v1;

import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v1";

// customized tip config
message CustomizedTipConfig {
  // staff id
  int64 staff_id = 1;
  // tips amount
  double amount = 2;
  // tips percentage
  int32 percentage = 3;
}

// edit staff
message EditStaffDef {
  // order id
  int64 order_id = 1;
  // order def
  repeated EditStaffOrderItemDef edit_staff_order_item_defs = 2;
}

// edit staff order item commission
message EditStaffOrderItemDef {
  // order item id
  int64 order_item_id = 1;
  // staff id list
  repeated StaffOperationDef staffs = 2;

  // multi staff operation
  // ref: moego/models/appointment/v1/service_operation_defs.proto:12
  message StaffOperationDef {
    // staff id
    int64 staff_id = 1;
  }
}

// staff tip config
message StaffTipConfig {
  // staff id
  int64 staff_id = 1;
  // tips amount
  google.type.Money amount = 2;
  // tips percentage
  double percentage = 3;
}
