syntax = "proto3";

package moego.models.message.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v1";

// email status
enum MarketingEmailStatus {
  // unspecific status
  MARKETING_EMAIL_STATUS_UNSPECIFIED = 0;
  // sent
  MARKETING_EMAIL_STATUS_SENT = 1;
  // schedule
  MARKETING_EMAIL_STATUS_SCHEDULE = 2;
  // draft
  MARKETING_EMAIL_STATUS_DRAFT = 3;
}

// recipient status
enum MarketingEmailRecipientStatus {
  // unspecific status
  MARKETING_EMAIL_RECIPIENT_STATUS_UNSPECIFIED = 0;
  // sent
  MARKETING_EMAIL_RECIPIENT_STATUS_SENT = 1;
  // opened
  MARKETING_EMAIL_RECIPIENT_STATUS_OPENED = 2;
  // clicked
  MARKETING_EMAIL_RECIPIENT_STATUS_CLICKED = 3;
  // replied
  MARKETING_EMAIL_RECIPIENT_STATUS_REPLIED = 4;
}

// recipient send status
enum MarketingEmailRecipientSendStatus {
  // unspecific status
  MARKETING_EMAIL_RECIPIENT_SEND_STATUS_UNSPECIFIED = 0;
  // waiting
  MARKETING_EMAIL_RECIPIENT_SEND_STATUS_WAITING = 1;
  // sent
  MARKETING_EMAIL_RECIPIENT_SEND_STATUS_SENT = 2;
  // failed
  MARKETING_EMAIL_RECIPIENT_SEND_STATUS_FAILED = 3;
  // schedule
  MARKETING_EMAIL_RECIPIENT_SEND_STATUS_SCHEDULE = 4;
}

// email event type
enum EmailEventType {
  // unspecific status
  EMAIL_EVENT_TYPE_UNSPECIFIED = 0;
  // delivered
  EMAIL_EVENT_TYPE_DELIVERED = 1;
  // opened
  EMAIL_EVENT_TYPE_OPENED = 2;
  // clicked
  EMAIL_EVENT_TYPE_CLICKED = 3;
  // replied
  EMAIL_EVENT_TYPE_REPLIED = 4;
}
