syntax = "proto3";

package moego.models.message.v2;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v2;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v2";

// message send channel
enum Channel {
  // unspecified
  CHANNEL_UNSPECIFIED = 0;
  // sms
  SMS = 1;
  // email
  EMAIL = 2;
  // call
  CALL = 3;
  // In-App msg
  APP = 4;
}

// compliance channel
enum ComplianceChannel {
  // unspecified
  COMPLIANCE_CHANNEL_UNSPECIFIED = 0;
  // sms
  COMPLIANCE_CHANNEL_SMS = 1;
  // email
  COMPLIANCE_CHANNEL_EMAIL = 2;
  // auto call
  COMPLIANCE_CHANNEL_AUTO_CALL = 3;
}

// message sender and receiver role
enum Role {
  // unspecified
  ROLE_UNSPECIFIED = 0;
  // business
  BUSINESS = 1;
  // customer
  CUSTOMER = 2;
  // platform
  PLATFORM = 3;
}

// message status
enum MessageStatus {
  // 未知状态
  MSG_STATUS_UNSPECIFIED = 0;
  // 消息已创建
  MSG_CREATED = 100;

  // 消息已投递
  MSG_DELIVERED = 200;
  // 消息投递失败
  MSG_DELIVER_FAILED = 250;

  // 消息已删除
  MSG_DELETED = 400;
}

// 消息内容的类型
enum ContentType {
  // 未指定类型
  CONTENT_TYPE_UNSPECIFIED = 0;
  // 纯文本
  CONTENT_PLAINTEXT = 1;
  // 图片
  CONTENT_PICTURE = 2;
  // 视频
  CONTENT_VIDEO = 3;
}

// chat status
enum ChatStatus {
  // 未指定状态
  CHAT_STATUS_UNSPECIFIED = 0;
  // 会话已打开
  CHAT_OPENED = 100;
  // 会话已关闭
  CHAT_CLOSED = 200;
  // 会话已封锁
  CHAT_BLOCKED = 300;
  // 会话已删除
  CHAT_DELETED = 400;
}
