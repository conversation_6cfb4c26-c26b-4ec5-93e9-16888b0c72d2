syntax = "proto3";

package moego.models.customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.customer.v1";

// preferred frequency type
enum PreferredFrequencyType {
  // unspecified
  PREFERRED_FREQUENCY_TYPE_UNSPECIFIED = 0;
  // day
  PREFERRED_FREQUENCY_TYPE_DAY = 1;
  // week
  PREFERRED_FREQUENCY_TYPE_WEEK = 2;
  // month
  PREFERRED_FREQUENCY_TYPE_MONTH = 3;
}
