syntax = "proto3";

package moego.models.payment.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// credit card definition
message CreditCardCreateDef {
  // id of the credit card
  string id = 1 [(validate.rules).string = {
    pattern: "^tok_.*$"
    max_len: 255
  }];
}
