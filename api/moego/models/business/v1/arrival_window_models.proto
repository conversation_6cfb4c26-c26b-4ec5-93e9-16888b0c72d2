syntax = "proto3";

package moego.models.business.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1;businesspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business.v1";

// business arrival window setting model, only for mobile
message ArrivalWindowModel {
  // id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // is enable
  bool is_enable = 3;
  // before minutes
  int32 before_minutes = 4;
  // after minutes
  int32 after_minutes = 5;
  // create time
  google.protobuf.Timestamp create_time = 6;
  // update time
  google.protobuf.Timestamp update_time = 7;
}

// business arrival window setting model public view
message ArrivalWindowModelPublicView {
  // business id
  int64 business_id = 1;
  // is enable
  bool is_enable = 2;
  // before minutes
  int32 before_minutes = 3;
  // after minutes
  int32 after_minutes = 4;
}
