// @since 2023-09-05 17:03:16
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.risk_control.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1;riskcontrolpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.risk_control.v1";

// risk control token definition
message RiskControlDef {
  // risk control token
  string token = 1;
}
