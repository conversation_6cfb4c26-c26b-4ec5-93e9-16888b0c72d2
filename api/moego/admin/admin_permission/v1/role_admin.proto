// @since 2023-05-27 21:28:41
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.admin_permission.v1;

import "moego/models/admin_permission/v1/permission_models.proto";
import "moego/models/admin_permission/v1/role_models.proto";
import "moego/models/admin_permission/v1/role_permission_defs.proto";
import "moego/models/admin_permission/v1/role_permission_models.proto";
import "moego/utils/v1/struct.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/admin_permission/v1;adminpermissionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.admin_permission.v1";

// create role req
message CreateRoleParams {
  // name
  string name = 1 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // description
  string description = 2 [(validate.rules).string = {max_len: 255}];
}

// crate role res
message CreateRoleResult {
  // role
  moego.models.admin_permission.v1.RoleModel role = 1;
}

// get role list
message DescribeRolesParams {
  // filter by ids
  optional moego.utils.v1.Int64ListValue ids = 1;
  // filter by names
  optional moego.utils.v1.StringListValue names = 2;

  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// get role list res
message DescribeRolesResult {
  // roles
  repeated moego.models.admin_permission.v1.RoleModel roles = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// get role req
message GetRoleParams {
  // identifier
  oneof identifier {
    option (validate.required) = true;
    // id
    int64 id = 1 [(validate.rules).int64 = {gt: 0}];
    // name
    string name = 2 [(validate.rules).string = {
      min_len: 1
      max_len: 50
    }];
  }
}

// get role res
message GetRoleResult {
  // role model
  moego.models.admin_permission.v1.RoleModel role = 1;
}

// update role req
message UpdateRoleParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // name
  optional string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 50
  }];
  // description
  optional string description = 3 [(validate.rules).string = {max_len: 255}];
}

// update role res
message UpdateRoleResult {
  // role
  moego.models.admin_permission.v1.RoleModel role = 1;
}

// delete role req
message DeleteRoleParams {
  // role id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete role res
message DeleteRoleResult {}

// create role permission req
message CreateRolePermissionParams {
  // role id, only allow to create role permission for visible roles
  int64 role_id = 1 [(validate.rules).int64 = {gt: 0}];
  // perm point
  string permission = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // filters
  repeated moego.models.admin_permission.v1.FilterDef filters = 3 [(validate.rules).repeated = {max_items: 100}];
}

// create role perm res
message CreateRolePermissionResult {
  // created role permission
  moego.models.admin_permission.v1.RolePermissionModel role_permission = 1;
}

// update role permission req
message UpdateRolePermissionParams {
  // role permission id
  // only allow to update role permission for visible roles
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];

  // perm point
  string permission = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 100
  }];
  // filters
  repeated moego.models.admin_permission.v1.FilterDef filters = 3 [(validate.rules).repeated = {max_items: 100}];
}

// update role perm res
message UpdateRolePermissionResult {
  // updated role permission
  moego.models.admin_permission.v1.RolePermissionModel role_permission = 1;
}

// delete role perm
message DeleteRolePermissionParams {
  // role permission id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// delete role permission res
message DeleteRolePermissionResult {}

// get role permissions req
message DescribeRolePermissionsParams {
  // role id
  int64 role_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// describe role permissions res
message DescribeRolePermissionsResult {
  // role permissions
  repeated moego.models.admin_permission.v1.RolePermissionModel role_permissions = 1;
}

// DescribePermissionsParams
message DescribePermissionsParams {
  // list point
  // list tree by default
  // set as true to list all type=point permissions
  optional bool list_point = 1;
}

// DescribePermissionsResult
message DescribePermissionsResult {
  // permissions tree
  repeated moego.models.admin_permission.v1.PermissionModel permissions = 1;
}

// the role service
service RoleService {
  // create role
  rpc CreateRole(CreateRoleParams) returns (CreateRoleResult);
  // get roles
  rpc DescribeRoles(DescribeRolesParams) returns (DescribeRolesResult);
  // get role detail
  rpc GetRole(GetRoleParams) returns (GetRoleResult);
  // update role
  rpc UpdateRole(UpdateRoleParams) returns (UpdateRoleResult);
  // delete role
  rpc DeleteRole(DeleteRoleParams) returns (DeleteRoleResult);

  // create role perm point
  // disallowed state: the perm point or its mutually exclusive points is already in use
  rpc CreateRolePermission(CreateRolePermissionParams) returns (CreateRolePermissionResult);
  // get role perm point list
  rpc DescribeRolePermissions(DescribeRolePermissionsParams) returns (DescribeRolePermissionsResult);
  // update role perm point
  // disallowed state: the perm point or its mutually exclusive points is already in use by others
  rpc UpdateRolePermission(UpdateRolePermissionParams) returns (UpdateRolePermissionResult);
  // delete rol perm point
  rpc DeleteRolePermission(DeleteRolePermissionParams) returns (DeleteRolePermissionResult);

  // describe permissions
  rpc DescribePermissions(DescribePermissionsParams) returns (DescribePermissionsResult);
}
