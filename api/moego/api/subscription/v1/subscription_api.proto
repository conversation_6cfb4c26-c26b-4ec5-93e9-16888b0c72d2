syntax = "proto3";

package moego.api.subscription.v1;

import "google/protobuf/timestamp.proto";
import "google/type/decimal.proto";
import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/subscription/v1/subscription_models.proto";
import "moego/utils/v1/time_period.proto";
import "moego/utils/v2/pagination_messages.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/subscription/v1;subscriptionapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.subscription.v1";

// product
message ProductView {
  // ID
  int64 id = 1;
  // Name
  string name = 2;
  // Description
  string description = 3;
  // 类型
  moego.models.subscription.v1.Product.Type type = 4;
  // seller
  moego.models.subscription.v1.User seller = 5;
  // features
  repeated moego.models.subscription.v1.Feature features = 6;
  // business types
  moego.models.subscription.v1.Product.BusinessType business_type = 7;
  // price
  repeated PriceView prices = 8;
  // extra
  moego.models.subscription.v1.ProductExtra extra = 9;
}

// 价格
message PriceView {
  // ID
  int64 id = 1;
  // 类型
  moego.models.subscription.v1.Price.Type type = 2;
  // 单价
  google.type.Money unit_amount = 3;
  // 计费周期
  moego.utils.v1.TimePeriod billing_cycle = 4;
  // 预审规则
  moego.models.subscription.v1.Price.Prequalification prequalification = 5;
  // 能应用的discounts
  repeated moego.models.subscription.v1.Discount discounts = 6;
  // 应用后的单价
  google.type.Money discounted_unit_amount = 7;
  // 优惠百分比，由 (1 - discounted_unit_amount / unit_amount) * 100 得到
  google.type.Decimal discount_percentage_off = 8;
}

// 订阅
message SubscriptionView {
  // ID
  int64 id = 1;
  // 名称，继承自 plan product
  string name = 2;
  // 描述，继承自 plan product
  string description = 3;
  // 状态
  moego.models.subscription.v1.Subscription.Status status = 4;
  // 订阅的有效期，起点为当前扣款时间，终点为下一次扣款时间
  google.type.Interval validity_period = 6;
  // 计费周期
  moego.utils.v1.TimePeriod billing_cycle = 7;
  // 宽限期
  moego.utils.v1.TimePeriod grace_period = 8;
  // 是否在周期结束时取消订阅
  bool cancel_at_period_end = 9;
  // 买家
  moego.models.subscription.v1.User buyer = 10;
  // 卖家
  moego.models.subscription.v1.User seller = 11;
  // purchase details
  repeated PurchaseDetailView purchase_details = 12;
}

// 购买行为
message PurchaseDetailView {
  // id
  int64 id = 1;
  // subscription_id
  int64 subscription_id = 2;
  // Product
  ProductView product = 3;
  // Price
  PriceView price = 4;
  // Quantity
  int32 quantity = 5;
  // 总价
  google.type.Money total_price = 6;
  // 应用的折扣
  repeated moego.models.subscription.v1.Discount discounts = 7;
  // 原价
  google.type.Money original_total_price = 8;
  // 优惠百分比，由 (1 - total_price / original_total_price) * 100 得到
  google.type.Decimal discount_percentage_off = 9;
}

// List Products Params.
message ListProductsParams {
  // pagination
  utils.v2.PaginationRequest pagination = 1;
  // filter by business type
  repeated moego.models.subscription.v1.Product.BusinessType business_types = 2;
  // filter by id
  repeated int64 ids = 3;
}

// List Products Result.
message ListProductsResult {
  // pagination
  utils.v2.PaginationResponse pagination = 1;
  // products
  repeated ProductView products = 2;
}

// create subscription Params
message CreateSubscriptionParams {
  // the purchase
  repeated moego.models.subscription.v1.Purchase purchases = 1;
  // 非 TRIAL 必填
  optional string card_on_file_id = 2;
}

// create subscription Result
message CreateSubscriptionResult {
  // the subscription
  SubscriptionView subscription = 1;
  // 前端调 stripe sdk 时需要通过 client_secret 来确认支付状态
  // To be deprecated
  string external_stripe_client_secret = 2;
}

// ListSubscriptions Params.
message ListSubscriptionsParams {
  // pagination
  utils.v2.PaginationRequest pagination = 1;
  // statuses
  repeated moego.models.subscription.v1.Subscription.Status statuses = 2;
  // business type
  repeated moego.models.subscription.v1.Product.BusinessType plan_product_business_types = 3;
  // subscription ids
  repeated int64 ids = 4;
}

// ListSubscriptions Result.
message ListSubscriptionsResult {
  // pagination
  utils.v2.PaginationResponse pagination = 1;
  // subscriptions
  repeated SubscriptionView subscriptions = 2;
}

// CancelSubscription Params.
message CancelSubscriptionParams {
  // subscription id
  int64 subscription_id = 1;
}

// CancelSubscription Result.
message CancelSubscriptionResult {
  // subscription
  SubscriptionView subscription = 1;
}

// PreviewPurchases Params.
message PreviewPurchasesParams {
  // the purchase
  repeated moego.models.subscription.v1.Purchase purchases = 1;
}

// PreviewPurchases Result.
message PreviewPurchasesResult {
  // the purchase details
  repeated PurchaseDetailView purchase_details = 1;
}

// ListEntitlements Params.
message ListEntitlementsParams {
  // pagination
  utils.v2.PaginationRequest pagination = 1;
  // feature key
  repeated moego.models.subscription.v1.Feature.Key feature_keys = 2;
  // license statuses
  repeated moego.models.subscription.v1.License.Status license_statuses = 3;
}

// ListEntitlements Result.
message ListEntitlementsResult {
  // pagination
  utils.v2.PaginationResponse pagination = 1;
  // entitlements
  repeated moego.models.subscription.v1.Entitlement entitlements = 2;
}

// update points Params
message UpdateCreditParams {
  // change points user
  models.subscription.v1.User user = 1;
  // credit can be positive or negative
  // used to control increase/decrease
  int64 credit = 2;
  // type
  models.subscription.v1.UpdateCredit.Type type = 3;
  // reason, enum
  optional models.subscription.v1.UpdateCredit.Reason reason = 4;
  // note, customize
  optional string note = 5;
  // invoice id
  optional int64 invoice_id = 6;
  // appointment id
  optional int64 appointment_id = 7;
}

// update credit Result
message UpdateCreditResult {
  // success
  bool success = 1;
}

// list credit history change history
message ListCreditChangeHistoryParams {
  // user
  models.subscription.v1.User user = 1;
  // page
  moego.utils.v2.PaginationRequest pagination = 99;
}

// list credit change history result
message ListCreditChangeHistoryResult {
  // credit change history
  message CreditChangeHistory {
    // association
    // for example, if the type is payment the invoice id not empty
    message Association {
      // invoice id
      int64 invoice_id = 1;
      // appointment id
      int64 appointment_id = 2;
    }
    // id
    int64 id = 1;
    // credit, it needs to be an integer,
    // which can default to cents.
    // for example, if it's 1.22, please enter 122,
    // if it's 100, please enter 10000.
    int64 credit = 2;
    // type
    models.subscription.v1.UpdateCredit.Type type = 3;
    // reason
    models.subscription.v1.UpdateCredit.Reason reason = 4;
    // note
    string note = 5;
    // operator
    models.subscription.v1.User user = 6;
    // association
    Association association = 7;
    // time
    google.protobuf.Timestamp created_time = 8;
  }
  // change history list
  repeated CreditChangeHistory histories = 1;
  // page
  moego.utils.v2.PaginationResponse pagination = 99;
}

// GetCreditParams
message GetCreditParams {
  // user
  models.subscription.v1.User user = 1;
}

// GetCreditResult
message GetCreditResult {
  // credit, the unit is cents
  int64 credit = 1;
}

// The service for subscription.
service SubscriptionService {
  // List Products.
  rpc ListProducts(ListProductsParams) returns (ListProductsResult);

  // Create Subscription.
  rpc CreateSubscription(CreateSubscriptionParams) returns (CreateSubscriptionResult);
  // List Subscriptions.
  rpc ListSubscriptions(ListSubscriptionsParams) returns (ListSubscriptionsResult);
  // Cancel Subscription.
  rpc CancelSubscription(CancelSubscriptionParams) returns (CancelSubscriptionResult);

  // PreviewPurchases.
  rpc PreviewPurchases(PreviewPurchasesParams) returns (PreviewPurchasesResult);

  // ListEntitlements
  rpc ListEntitlements(ListEntitlementsParams) returns (ListEntitlementsResult);

  /**
     credit
  */
  // update credit
  rpc UpdateCredit(UpdateCreditParams) returns (UpdateCreditResult);
  // list history
  rpc ListCreditChangeHistory(ListCreditChangeHistoryParams) returns (ListCreditChangeHistoryResult);
  // get credit
  rpc GetCredit(GetCreditParams) returns (GetCreditResult);
}
