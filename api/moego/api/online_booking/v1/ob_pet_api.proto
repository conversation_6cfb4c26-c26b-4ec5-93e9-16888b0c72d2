syntax = "proto3";

package moego.api.online_booking.v1;

import "moego/models/customer/v1/customer_pet_defs.proto";
import "moego/models/customer/v1/customer_pet_models.proto";
import "moego/models/customer/v1/customer_pet_vaccine_defs.proto";
import "moego/models/customer/v1/customer_pet_vaccine_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// add pet request params
message AddOBPetRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet
  moego.models.customer.v1.PetDef pet = 3;
  // pet vaccine list
  repeated moego.models.customer.v1.VaccineDef vaccine_list = 4;
  // pet custom question answers
  map<string, string> pet_question_answers = 5;
}

// update pet request params
message UpdateOBPetRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet id
  int32 pet_id = 3 [(validate.rules).int32 = {gt: 0}];
  // pet
  moego.models.customer.v1.PetDef pet = 4;
  // pet vaccine list
  repeated moego.models.customer.v1.VaccineDef vaccine_list = 5;
  // pet custom question answers
  map<string, string> pet_question_answers = 6;
}

// get pet request params
message GetOBPetRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet id
  int32 pet_id = 3 [(validate.rules).int32 = {gt: 0}];
}

// get pet request params
message GetOBPetListRequest {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// add pet response result
message AddOBPetResponse {
  // pet id
  int32 pet_id = 1;
}

// update pet response result
message UpdateOBPetResponse {
  // pet id
  int32 pet_id = 1;
}

// single pet info response
message OBPetInfoResponse {
  // pet info
  moego.models.customer.v1.CustomerPetOnlineBookingView pet = 1;
  // ob custom question answers
  map<string, string> pet_question_answers = 2;
  // pet vaccine list
  repeated moego.models.customer.v1.PetVaccineOnlineBookingView vaccine_list = 3;
}

// pet list response
message OBPetListResponse {
  // pet list
  repeated OBPetInfoResponse pets = 1;
}

// pet service
service OBPetService {
  // get single pet info
  rpc GetOBPetInfo(GetOBPetRequest) returns (OBPetInfoResponse);
  // get pet info list
  rpc GetOBPetList(GetOBPetListRequest) returns (OBPetListResponse);
  // online booking add pet info
  rpc AddOBPet(AddOBPetRequest) returns (AddOBPetResponse);
  // online booking update pet info
  rpc UpdateOBPet(UpdateOBPetRequest) returns (UpdateOBPetResponse);
}
