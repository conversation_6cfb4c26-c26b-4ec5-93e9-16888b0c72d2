syntax = "proto3";

package moego.api.organization.v1;

import "moego/models/offering/v1/lodging_unit_models.proto";
import "moego/models/organization/v1/camera_defs.proto";
import "moego/models/organization/v1/camera_enums.proto";
import "moego/models/organization/v1/camera_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1;organizationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.organization.v1";

// get camera list params
message GetCameraListParams {
  // filter
  oneof filter {
    option (validate.required) = true;
    // iDogCamFilter
    models.organization.v1.IdogcamFilter idogcam_filter = 1;
    // abcKamFilter
    models.organization.v1.AbckamFilter abckam_filter = 2;
    // cameraFilter
    models.organization.v1.CameraFilter camera_filter = 3;
  }
}

// get camera list result
message GetCameraListResult {
  // camera list
  repeated CameraView cameras = 1;
  //camera view
  message CameraView {
    // camera info
    models.organization.v1.CameraModel camera = 1;
    // id of the lodging unit
    optional models.offering.v1.LodgingUnitView lodging_unit = 2;
  }
}

//update camera param
message UpdateCameraParams {
  // camera id
  int64 camera_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Is Active
  optional bool is_active = 10;
  // Visibility Type
  optional models.organization.v1.VisibilityType visibility_type = 11;
  // id of the lodging unit
  optional int64 lodging_unit_id = 14;
}

//update camera result
message UpdateCameraResult {}

// camera service
service CameraService {
  // get camera list
  rpc GetCameraList(GetCameraListParams) returns (GetCameraListResult);
  // update camera
  rpc UpdateCamera(UpdateCameraParams) returns (UpdateCameraResult);
}
