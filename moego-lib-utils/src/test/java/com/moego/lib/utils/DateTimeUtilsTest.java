package com.moego.lib.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.Instant;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.Test;

public class DateTimeUtilsTest {

    @Test
    public void testTransferDateTime() {
        long t = 1652127154567L;
        String text = "2022-05-09 20:12:34.567";
        String pattern = "yyyy-MM-dd HH:mm:ss.SSS";
        assertEquals(text, DateTimeUtils.toString(t, pattern, "UTC"));
        assertEquals(t, DateTimeUtils.toMilliseconds(text, pattern, "UTC"));

        assertEquals("2022-05-10 04:12:34.567", DateTimeUtils.toString(t, pattern, "Asia/Shanghai"));
        assertEquals("2022-05-10 04:12:34.567", DateTimeUtils.toString(t, pattern, "UTC+8"));
        assertEquals("2022-05-10 05:12:34.567", DateTimeUtils.toString(t, pattern, "UTC+9"));
        assertEquals(moveTimeHours(t, -8), DateTimeUtils.toMilliseconds(text, pattern, "Asia/Shanghai"));
        assertEquals(moveTimeHours(t, -8), DateTimeUtils.toMilliseconds(text, pattern, "UTC+8"));
        assertEquals(moveTimeHours(t, -9), DateTimeUtils.toMilliseconds(text, pattern, "UTC+9"));

        assertEquals(Instant.ofEpochMilli(t), DateTimeUtils.toInstant(text, pattern, "UTC"));
        assertEquals(
                Instant.ofEpochMilli(moveTimeHours(t, -8)), DateTimeUtils.toInstant(text, pattern, "Asia/Shanghai"));
        assertEquals(Instant.ofEpochMilli(moveTimeHours(t, -8)), DateTimeUtils.toInstant(text, pattern, "UTC+8"));
        assertEquals(Instant.ofEpochMilli(moveTimeHours(t, -9)), DateTimeUtils.toInstant(text, pattern, "UTC+9"));

        assertDateTimeWithUTC("2022-05-09 20:12:34.567", DateTimeUtils.toDateTime(t, "UTC"));
        assertDateTimeWithUTC("2022-05-09 20:12:34.567", DateTimeUtils.toDateTime(t, "Asia/Shanghai"));
        assertDateTimeWithUTC("2022-05-09 20:12:34.567", DateTimeUtils.toDateTime(t, "UTC+8"));
        assertDateTimeWithUTC("2022-05-09 20:12:34.567", DateTimeUtils.toDateTime(t, "UTC+9"));
        assertDateTimeWithUTC("2022-05-09 20:12:34.567", DateTimeUtils.toDateTime(text, pattern, "UTC"));
        assertDateTimeWithUTC("2022-05-09 12:12:34.567", DateTimeUtils.toDateTime(text, pattern, "Asia/Shanghai"));
        assertDateTimeWithUTC("2022-05-09 12:12:34.567", DateTimeUtils.toDateTime(text, pattern, "UTC+8"));
        assertDateTimeWithUTC("2022-05-09 11:12:34.567", DateTimeUtils.toDateTime(text, pattern, "UTC+9"));

        assertEquals("2022-05-10 03:12:34.567", DateTimeUtils.toString(t, pattern, 7 * 3600));
        assertEquals("2022-05-10 04:12:34.567", DateTimeUtils.toString(t, pattern, "Asia/Shanghai"));
        assertEquals("2022-05-10 05:12:34.567", DateTimeUtils.toString(t, pattern, "UTC+9"));
        assertEquals("2022-05-10 04:12:34.567", DateTimeUtils.toString(text, pattern, "UTC", pattern, "Asia/Shanghai"));
        assertEquals("2022-05-10 04:12:34.567", DateTimeUtils.toString(text, pattern, "UTC", pattern, "UTC+8"));
        assertEquals("2022-05-10 05:12:34.567", DateTimeUtils.toString(text, pattern, "UTC", pattern, "UTC+9"));
        assertEquals(
                "2022-05-09 19:12:34.567", DateTimeUtils.toString(text, pattern, "Asia/Shanghai", pattern, "UTC+7"));
        assertEquals(
                "2022-05-09 21:12:34.567", DateTimeUtils.toString(text, pattern, "Asia/Shanghai", pattern, "UTC+9"));
        assertEquals("2022-05-09 19:12:34.567", DateTimeUtils.toString(text, pattern, "UTC+8", pattern, "UTC+7"));
        assertEquals("2022-05-09 21:12:34.567", DateTimeUtils.toString(text, pattern, "UTC+8", pattern, "UTC+9"));
        assertEquals("2022-05-09 19:12:34.567", DateTimeUtils.toString(text, pattern, 8 * 3600, pattern, 7 * 3600));

        System.out.println("test time conversion ok !");
    }

    private static long moveTimeHours(long t, long n) {
        return t + (n * 60 * 60 * 1000);
    }

    private static void assertDateTimeWithUTC(String text, ZonedDateTime t) {
        assertEquals(
                DateTimeUtils.toMilliseconds(text, "yyyy-MM-dd HH:mm:ss.SSS", "UTC"), DateTimeUtils.toMilliseconds(t));
    }
}
