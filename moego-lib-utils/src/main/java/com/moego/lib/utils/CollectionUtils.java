package com.moego.lib.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Deque;
import java.util.List;
import java.util.function.Consumer;

public class CollectionUtils {

    // get the first element in a collection
    public static <T> T firstElement(Collection<T> items) {
        if (items == null || items.isEmpty()) {
            return null;
        }
        if (items instanceof List<T> list) {
            return list.get(0);
        }

        var iter = items.iterator();
        return iter.hasNext() ? iter.next() : null;
    }

    // get the last element in a collection
    public static <T> T lastElement(Collection<T> items) {
        if (items == null || items.isEmpty()) {
            return null;
        }
        if (items instanceof List<T> list) {
            return list.get(list.size() - 1);
        }
        if (items instanceof Deque<T> deque) {
            return deque.getLast();
        }

        T item = null;
        for (var iter = items.iterator(); iter.hasNext(); item = iter.next())
            ;
        return item;
    }

    // consume a list by batches
    public static <T> void consumeInBatch(List<T> items, int step, Consumer<List<T>> consumer) {
        int size = (0 < step ? step : 1);
        if (items != null && !items.isEmpty()) {
            for (int start = 0; ; ) {
                int endIndex = start + size;
                if (endIndex < items.size()) {
                    consumer.accept(items.subList(start, endIndex));
                    start = endIndex;
                } else {
                    consumer.accept(items.subList(start, items.size()));
                    break;
                }
            }
        }
    }

    // split a list
    // splitList([1, 2, 3, 4, 5, 6, 7], 0) => [[1, 2, 3, 4, 5, 6, 7]]
    // splitList([1, 2, 3, 4, 5, 6, 7], 1) => [[1, 2, 3, 4, 5, 6, 7]]
    // splitList([1, 2, 3, 4, 5, 6, 7], 2) => [[1, 2, 3, 4], [5, 6, 7]]
    // splitList([1, 2, 3, 4, 5, 6, 7], 3) => [[1, 2, 3], [4, 5], [6, 7]]
    // splitList([1, 2, 3, 4, 5, 6, 7], 4) => [[1, 2], [3, 4], [5, 6], [7]]
    // splitList([1, 2, 3, 4, 5, 6, 7], 5) => [[1, 2], [3, 4], [5], [6], [7]]
    // splitList([1, 2, 3, 4, 5, 6, 7], 6) => [[1, 2], [3], [4], [5], [6], [7]]
    // splitList([1, 2, 3, 4, 5, 6, 7], 7) => [[1], [2], [3], [4], [5], [6], [7]]
    // splitList([1, 2, 3, 4, 5, 6, 7], 8) => [[1], [2], [3], [4], [5], [6], [7]]
    public static <T> List<List<T>> splitList(List<T> items, int batch) {
        if (items == null || items.isEmpty()) {
            return List.of();
        }
        if (batch < 2) {
            return List.of(items);
        }
        if (items.size() <= batch) {
            return items.stream().map(List::of).toList();
        }
        List<List<T>> result = new ArrayList<>();
        List<Integer> steps = new ArrayList<>();
        int step = items.size() / batch;
        int mod = items.size() % batch;
        for (int i = 0; i < batch; ++i) {
            steps.add(step + (i < mod ? 1 : 0));
        }

        int start = 0;
        for (var size : steps) {
            int end = start + size;
            result.add(items.subList(start, end));
            start = end;
        }

        return result;
    }
}
