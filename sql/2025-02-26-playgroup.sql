create table pet_playgroup
(
  id             bigint auto_increment
        primary key,
  company_id     bigint      default 0                 not null,
  business_id    bigint      default 0                 not null,
  playgroup_id   bigint      default 0                 not null,
  appointment_id bigint      default 0                 not null,
  pet_id         bigint      default 0                 not null,
  date           varchar(10) default ''                not null,
  sort           int         default 0                 not null comment 'pet playgroup list sort. start with 1 and put the smallest first',
  created_at     datetime    default CURRENT_TIMESTAMP not null,
  updated_at     datetime    default CURRENT_TIMESTAMP not null,
  constraint pet_playgroup_appointment_index
    unique (appointment_id, pet_id, date)
);

create index pet_playgroup_business_daily_index
  on pet_playgroup (business_id, date);

create index pet_playgroup_company_daily_index
  on pet_playgroup (company_id, date);

