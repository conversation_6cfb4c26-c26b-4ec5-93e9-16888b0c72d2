package com.moego.server.customer.service;

import static com.moego.common.enums.QuestionConst.IS_ALLOW_CHANGE_FALSE;
import static com.moego.common.enums.QuestionConst.IS_ALLOW_CHANGE_TRUE;
import static com.moego.common.enums.QuestionConst.IS_ALLOW_DELETE_FALSE;
import static com.moego.common.enums.QuestionConst.IS_ALLOW_EDIT_FALSE;
import static com.moego.common.enums.QuestionConst.IS_REQUIRED_FALSE;
import static com.moego.common.enums.QuestionConst.IS_REQUIRED_TRUE;
import static com.moego.common.enums.QuestionConst.IS_SHOW_FALSE;
import static com.moego.common.enums.QuestionConst.IS_SHOW_TURE;
import static com.moego.common.enums.QuestionConst.QUESTION_TYPE_DATE;
import static com.moego.common.enums.QuestionConst.QUESTION_TYPE_DROP_DOWN;
import static com.moego.common.enums.QuestionConst.QUESTION_TYPE_SHORT;
import static com.moego.common.enums.QuestionConst.TYPE_PET_OWNER_QUESTION;
import static com.moego.common.enums.QuestionConst.TYPE_PET_QUESTION;

import com.moego.common.utils.DateUtil;
import com.moego.server.customer.mapper.MoeIntakeFormDetailMapper;
import com.moego.server.customer.mapperbean.MoeIntakeFormDetail;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2021/1/13 2:51 PM
 */
@Service
@Slf4j
public class IntakeFormDetailService {

    private static final String[] PET_QUESTION = new String[] {"Pet type", "Pet breed", "Pet name"};
    private static final String[] PET_QUESTION2 = new String[] {
        "Gender",
        "Weight",
        "Birthday",
        "Hair length",
        "Fixed",
        "Behavior",
        "Vet name",
        "Vet phone number",
        "Vet address",
        "Vaccine",
        "Vaccine document",
        "Pet image",
        "Health issues",
    };
    private static final String[] PET_OWNER_QUESTION =
            new String[] {"First name", "Last name", "Phone number", "Email"};

    // FIXME @jett: 这里这一坨都应该放到表里维护, 写死在代码里真的天才设计, 我不想重构, 有请下一位吃屎人
    private static final String[] PET_OWNER_OPTIONAL_QUESTION = new String[] {
        "Address",
        "Birthday",
        IntakeFormDetailService.PET_OWNER_REFERRAL_SOURCE,
        "Emergency contact",
        "People authorized to pickup pets"
    };

    private static final Map<String, Byte> PET_OWNER_OPTIONAL_QUESTION_TYPE_MAP = new HashMap<>() {
        {
            put("Address", QUESTION_TYPE_SHORT);
            put("Birthday", QUESTION_TYPE_DATE);
            put(IntakeFormDetailService.PET_OWNER_REFERRAL_SOURCE, QUESTION_TYPE_DROP_DOWN);
        }
    };

    public static final String PET_OWNER_REFERRAL_SOURCE = "Referral source";

    @Autowired
    private MoeIntakeFormDetailMapper moeIntakeFormDetailMapper;

    private MoeIntakeFormDetail createQuestion(
            Long companyId,
            Integer businessId,
            String question,
            Integer formId,
            Byte type,
            Byte isShow,
            Byte isRequired,
            Byte isAllowDelete,
            Byte isAllowChange,
            Byte isAllowEdit,
            Byte questionType) {
        log.info("create intake form question.");
        MoeIntakeFormDetail record = new MoeIntakeFormDetail();
        record.setFormId(formId);
        record.setCompanyId(companyId);
        record.setBusinessId(businessId);
        record.setQuestion(question);
        record.setType(type);
        record.setIsShow(isShow);
        record.setIsRequired(isRequired);
        record.setIsAllowChange(isAllowChange);
        record.setIsAllowDelete(isAllowDelete);
        record.setIsAllowEdit(isAllowEdit);
        record.setCreateTime(DateUtil.get10Timestamp());
        record.setUpdateTime(record.getCreateTime());
        record.setQuestionType(questionType);
        moeIntakeFormDetailMapper.insertSelective(record);
        return record;
    }

    public MoeIntakeFormDetail createReferralSourceQuestion(Long companyId, Integer businessId, Integer formId) {
        return createQuestion(
                companyId,
                businessId,
                PET_OWNER_REFERRAL_SOURCE,
                formId,
                TYPE_PET_OWNER_QUESTION,
                IS_SHOW_FALSE,
                IS_REQUIRED_FALSE,
                IS_ALLOW_DELETE_FALSE,
                IS_ALLOW_CHANGE_TRUE,
                IS_ALLOW_EDIT_FALSE,
                QUESTION_TYPE_DROP_DOWN);
    }

    public void initFormQuestions(Long companyId, Integer businessId, Integer formId) {
        log.info("init intake form questions");
        if (CollectionUtils.isEmpty(moeIntakeFormDetailMapper.selectByFormId(formId))) {
            for (String petQuestion : PET_QUESTION) {
                createQuestion(
                        companyId,
                        businessId,
                        petQuestion,
                        formId,
                        TYPE_PET_QUESTION,
                        IS_SHOW_TURE,
                        IS_REQUIRED_TRUE,
                        IS_ALLOW_DELETE_FALSE,
                        IS_ALLOW_CHANGE_FALSE,
                        IS_ALLOW_EDIT_FALSE,
                        QUESTION_TYPE_SHORT);
            }
            for (String petQuestion2 : PET_QUESTION2) {
                createQuestion(
                        companyId,
                        businessId,
                        petQuestion2,
                        formId,
                        TYPE_PET_QUESTION,
                        IS_SHOW_FALSE,
                        IS_REQUIRED_FALSE,
                        IS_ALLOW_DELETE_FALSE,
                        IS_ALLOW_CHANGE_TRUE,
                        IS_ALLOW_EDIT_FALSE,
                        QUESTION_TYPE_SHORT);
            }
            for (String ownerQuestion : PET_OWNER_QUESTION) {
                createQuestion(
                        companyId,
                        businessId,
                        ownerQuestion,
                        formId,
                        TYPE_PET_OWNER_QUESTION,
                        IS_SHOW_TURE,
                        IS_REQUIRED_TRUE,
                        IS_ALLOW_DELETE_FALSE,
                        IS_ALLOW_CHANGE_FALSE,
                        IS_ALLOW_EDIT_FALSE,
                        QUESTION_TYPE_SHORT);
            }
            for (String optionalQuestion : PET_OWNER_OPTIONAL_QUESTION) {
                createQuestion(
                        companyId,
                        businessId,
                        optionalQuestion,
                        formId,
                        TYPE_PET_OWNER_QUESTION,
                        IS_SHOW_FALSE,
                        IS_REQUIRED_FALSE,
                        IS_ALLOW_DELETE_FALSE,
                        IS_ALLOW_CHANGE_TRUE,
                        IS_ALLOW_EDIT_FALSE,
                        PET_OWNER_OPTIONAL_QUESTION_TYPE_MAP.getOrDefault(optionalQuestion, QUESTION_TYPE_SHORT));
            }
            log.info(" question is initialized for {}", formId);
        } else {
            log.warn(" question has already been initialized for {}", formId);
        }
    }
}
