package com.moego.server.customer.mapstruct;

import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.server.customer.dto.PetVaccineListDTO;
import com.moego.server.customer.mapperbean.MoePetVaccine;
import com.moego.server.customer.web.params.MoePetVaccineParams;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PetVaccineConverter {
    PetVaccineConverter INSTANCE = Mappers.getMapper(PetVaccineConverter.class);

    MoePetVaccine toEntity(MoePetVaccineParams params);

    @Mapping(target = ".", source = "model.availability")
    PetVaccineListDTO toListDTO(BusinessPetVaccineModel model);

    @AfterMapping
    default void setAvailablePetTypes(BusinessPetVaccineModel model, @MappingTarget PetVaccineListDTO dto) {
        if (model.hasAvailability()
                && !CollectionUtils.isEmpty(model.getAvailability().getAvailablePetTypesList())) {
            dto.setAvailablePetTypes(model.getAvailability().getAvailablePetTypesList());
        }
    }
}
