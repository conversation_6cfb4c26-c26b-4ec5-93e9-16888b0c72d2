package com.moego.server.customer.mapstruct;

import com.moego.common.enums.CustomerContactEnum;
import com.moego.idl.models.business_customer.v1.BusinessCustomerContactModel;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.server.customer.mapperbean.MoeCustomerContact;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface CustomerContactConverter {
    CustomerContactConverter INSTANCE = Mappers.getMapper(CustomerContactConverter.class);

    @Mapping(target = "name", ignore = true)
    @Mapping(target = "customerContactId", source = "id")
    @Mapping(target = "contactId", source = "id")
    CustomerContactDto entityToDto(MoeCustomerContact entity);

    default CustomerContactDto modelToDTO(BusinessCustomerContactModel model) {

        var dto = new CustomerContactDto();
        dto.setCustomerContactId((int) model.getId());
        dto.setContactId((int) model.getId());
        dto.setCustomerId((int) model.getCustomerId());

        dto.setTitle(model.getTitle());
        dto.setName(model.getFirstName() + " " + model.getLastName());
        dto.setFirstName(model.getFirstName());
        dto.setLastName(model.getLastName());
        dto.setEmail(model.getEmail());
        dto.setPhoneNumber(model.getPhoneNumber());
        dto.setIsPrimary(
                model.getIsPrimary()
                        ? CustomerContactEnum.IS_PRIMARY_TRUE.intValue()
                        : CustomerContactEnum.IS_PRIMARY_FALSE.intValue());
        // @jett: 数据库里有一个type不传回来, 用一个判断, 然后再赋值一个枚举
        // 那为什么不直接把数据库的值传回来呢? 传回来直接对应枚举不行吗
        // 下游int 转 bool, 上游bool 再转回int, 天才
        // dto.setType(model.getIsMainContact() ? CustomerContactEnum.TYPE_OWNER : CustomerContactEnum.TYPE_ADDITIONAL);
        Optional.of(model.getType()).map(Integer::byteValue).ifPresent(dto::setType);
        return dto;
    }
}
