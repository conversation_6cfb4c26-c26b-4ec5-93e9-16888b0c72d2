/*
 * @since 2021-11-17 18:36:42
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.customer.server;

import com.moego.common.dto.PageDTO;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.GsonUtil;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.customer.api.IPetServiceBase;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.CustomerPetReminderDTO;
import com.moego.server.customer.dto.GroomingCalenderPetInfo;
import com.moego.server.customer.dto.WebsitePetSummaryDTO;
import com.moego.server.customer.params.CustomerPetAddParams;
import com.moego.server.customer.params.CustomerPetBirthdayReminderParams;
import com.moego.server.customer.params.CustomerPetUpdateParams;
import com.moego.server.customer.service.CustomerPetService;
import com.moego.server.grooming.params.CustomerIdWithPetIdsParams;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class PetServer extends IPetServiceBase {

    private final CustomerPetService customerPetService;
    private final MigrateHelper migrateHelper;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    @Override
    public List<CustomerPetDetailDTO> getCustomerPetListByIdList(@RequestBody List<Integer> petIdList) {
        if (CollectionUtils.isEmpty(petIdList)) {
            return List.of();
        }
        return customerPetService.getCustomerPetListByIdList(petIdList);
    }

    @Override
    public List<CustomerPetPetCodeDTO> getCustomerPetInfoListByIdList(List<Integer> petIdList) {
        if (petIdList.isEmpty()) {
            return List.of();
        }
        return customerPetService.getCustomerPetPetCodeListByIdList(petIdList);
    }

    /**
     * 返回未去世&未删除的pet信息，根据customerId查询
     *
     * @param customerId
     * @param businessId
     * @return
     */
    @Override
    public List<GroomingCalenderPetInfo> getPetNameBreedByCustomerId(
            @RequestParam Integer customerId, @RequestParam @Deprecated Integer businessId) {
        return customerPetService.getGroomingCalenderPetInfo(null, customerId);
    }

    @Override
    public List<CustomerPetDetailDTO> getCustomerPetListByCustomerId(List<Integer> customerIdList) {
        return customerPetService.getCustomerPetListByCustomerId(customerIdList);
    }

    @Override
    public boolean linkPet(Map<Integer, Long> petIdMap) {
        return customerPetService.linkPet(petIdMap);
    }

    @Override
    public List<GroomingCalenderPetInfo> getGroomingCalenderPetInfo(@RequestBody List<Integer> petIds) {
        return customerPetService.getGroomingCalenderPetInfo(petIds, null);
    }

    /**
     * 获取stbusiness customer pet list
     *
     * @param customerPetBirthdayReminderParams
     * @return
     */
    @Override
    public PageDTO<CustomerPetReminderDTO> getReminderBirthdayCustomerPet(
            @RequestBody CustomerPetBirthdayReminderParams customerPetBirthdayReminderParams) {
        log.info("CustomerPetBirthdayReminderParams = {}", GsonUtil.toJson(customerPetBirthdayReminderParams, false));
        return customerPetService.getReminderBirthdayCustomerPet(customerPetBirthdayReminderParams);
    }

    /**
     * 获取business customer pet notification birthday
     *
     * @param customerPetBirthdayReminderParams
     * @return
     */
    @Override
    public List<CustomerPetReminderDTO> getNotificationBirthdayCustomerPet(
            @RequestBody CustomerPetBirthdayReminderParams customerPetBirthdayReminderParams) {
        log.info("CustomerPetBirthdayReminderParams = {}", GsonUtil.toJson(customerPetBirthdayReminderParams, false));
        return customerPetService.getNotificationBirthdayCustomerPet(customerPetBirthdayReminderParams);
    }

    @Override
    public ResponseResult<Integer> insertCustomerPet(
            Long tokenCompanyId, Integer tokenBusinessId, @RequestBody CustomerPetAddParams customerPetParams) {
        boolean migrated;
        if (tokenCompanyId == null) {
            var migrateInfo = migrateHelper.getMigrationInfo(tokenBusinessId);
            tokenCompanyId = migrateInfo.companyId();
            migrated = migrateInfo.isMigrate();
        } else {
            migrated = migrateHelper.isMigrate(tokenCompanyId);
        }
        customerPetParams.setCompanyId(tokenCompanyId);
        customerPetParams.setBusinessId(tokenBusinessId);
        return customerPetService.saveCustomerPet(migrated, customerPetParams, false);
    }

    @Override
    public Boolean checkBusinessCustomerPet(
            @RequestParam("petId") Integer petId,
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId) {

        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        var companyId = migrateInfo.companyId();
        var migrated = migrateInfo.isMigrate();
        try {
            customerPetService.checkingBizByPetId(petId, companyId, migrated ? null : businessId, customerId);
            return true;
        } catch (Exception e) {
            log.error("pet mismatch: ", e);
            return false;
        }
    }

    @Override
    public Boolean checkBusinessPet(
            @RequestParam("petId") Integer petId, @RequestParam("businessId") Integer businessId) {

        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        var companyId = migrateInfo.companyId();
        var migrated = migrateInfo.isMigrate();

        try {
            customerPetService.checkingBizByPetId(petId, companyId, migrated ? null : businessId, null);
            return true;
        } catch (Exception e) {
            log.error("pet mismatch: ", e);
            return false;
        }
    }

    @Override
    public WebsitePetSummaryDTO countBetween(@Nullable Integer startId, @Nullable Integer endId) {
        return customerPetService.countBetween(startId, endId);
    }

    @Override
    public CustomerPetDetailDTO getPetWithVaccine(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("petId") Integer petId) {
        var companyId = businessServiceBlockingStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build())
                .getCompanyId();
        return customerPetService.getPetWithVaccine(companyId, businessId, customerId, petId);
    }

    @Override
    public List<CustomerPetDetailDTO> getPetListWithVaccine(@RequestBody CustomerIdWithPetIdsParams params) {
        var companyId = businessServiceBlockingStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(params.getBusinessId())
                        .build())
                .getCompanyId();

        return customerPetService.getPetListWithVaccine(params, companyId);
    }

    @Override
    public List<CustomerPetDetailDTO> getAllPetListWithVaccine(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerIds") List<Integer> customerIds) {
        var companyId = businessServiceBlockingStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build())
                .getCompanyId();

        return customerPetService.getAllPetListWithVaccine(companyId, customerIds);
    }

    @Override
    public Boolean updatePetWithVaccine(Integer businessId, CustomerPetUpdateParams updateParams) {
        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        var companyId = migrateInfo.companyId();
        var migrated = migrateInfo.isMigrate();

        updateParams.setCompanyId(companyId);
        updateParams.setBusinessId(businessId);
        ResponseResult<Integer> result = customerPetService.updateCustomerPet(migrated, updateParams, null);
        return result.getSuccess();
    }

    @Override
    public Boolean batchUpdatePetWithVaccine(
            @RequestParam("businessId") Integer businessId,
            @RequestBody List<CustomerPetUpdateParams> updateParamsList) {
        if (CollectionUtils.isEmpty(updateParamsList)) {
            return Boolean.FALSE;
        }

        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        var companyId = migrateInfo.companyId();
        var migrated = migrateInfo.isMigrate();

        updateParamsList.forEach(customerPetUpdateParams -> {
            customerPetUpdateParams.setCompanyId(companyId);
            customerPetUpdateParams.setBusinessId(businessId);
            customerPetService.updateCustomerPet(migrated, customerPetUpdateParams, null);
        });
        return Boolean.TRUE;
    }
}
