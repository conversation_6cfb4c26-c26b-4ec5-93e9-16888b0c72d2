package com.moego.server.customer.mapperbean;

public class MoeIntakeFormAgreement {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_agreement.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_agreement.form_id
     *
     * @mbg.generated
     */
    private Integer formId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_agreement.agreement_id
     *
     * @mbg.generated
     */
    private Integer agreementId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_agreement.is_show
     *
     * @mbg.generated
     */
    private Byte isShow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_agreement.is_required
     *
     * @mbg.generated
     */
    private Byte isRequired;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_agreement.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_agreement.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_agreement.id
     *
     * @return the value of moe_intake_form_agreement.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_agreement.id
     *
     * @param id the value for moe_intake_form_agreement.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_agreement.form_id
     *
     * @return the value of moe_intake_form_agreement.form_id
     *
     * @mbg.generated
     */
    public Integer getFormId() {
        return formId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_agreement.form_id
     *
     * @param formId the value for moe_intake_form_agreement.form_id
     *
     * @mbg.generated
     */
    public void setFormId(Integer formId) {
        this.formId = formId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_agreement.agreement_id
     *
     * @return the value of moe_intake_form_agreement.agreement_id
     *
     * @mbg.generated
     */
    public Integer getAgreementId() {
        return agreementId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_agreement.agreement_id
     *
     * @param agreementId the value for moe_intake_form_agreement.agreement_id
     *
     * @mbg.generated
     */
    public void setAgreementId(Integer agreementId) {
        this.agreementId = agreementId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_agreement.is_show
     *
     * @return the value of moe_intake_form_agreement.is_show
     *
     * @mbg.generated
     */
    public Byte getIsShow() {
        return isShow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_agreement.is_show
     *
     * @param isShow the value for moe_intake_form_agreement.is_show
     *
     * @mbg.generated
     */
    public void setIsShow(Byte isShow) {
        this.isShow = isShow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_agreement.is_required
     *
     * @return the value of moe_intake_form_agreement.is_required
     *
     * @mbg.generated
     */
    public Byte getIsRequired() {
        return isRequired;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_agreement.is_required
     *
     * @param isRequired the value for moe_intake_form_agreement.is_required
     *
     * @mbg.generated
     */
    public void setIsRequired(Byte isRequired) {
        this.isRequired = isRequired;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_agreement.create_time
     *
     * @return the value of moe_intake_form_agreement.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_agreement.create_time
     *
     * @param createTime the value for moe_intake_form_agreement.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_agreement.update_time
     *
     * @return the value of moe_intake_form_agreement.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_agreement.update_time
     *
     * @param updateTime the value for moe_intake_form_agreement.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
