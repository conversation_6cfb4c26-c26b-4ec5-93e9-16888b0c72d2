<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.customer.mapper.MoeCustomerTagBindingMapper">
    <resultMap id="BaseResultMap" type="com.moego.server.customer.mapperbean.MoeCustomerTagBinding">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="customer_id" jdbcType="INTEGER" property="customerId"/>
        <result column="customer_tag_id" jdbcType="INTEGER" property="customerTagId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, customer_id, customer_tag_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from moe_customer_tag_binding
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        delete from moe_customer_tag_binding
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTagBinding">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into moe_customer_tag_binding (customer_id, customer_tag_id)
        values (#{customerId,jdbcType=INTEGER}, #{customerTagId,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTagBinding">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into moe_customer_tag_binding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="customerTagId != null">
                customer_tag_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                #{customerId,jdbcType=INTEGER},
            </if>
            <if test="customerTagId != null">
                #{customerTagId,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTagBinding">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update moe_customer_tag_binding
        <set>
            <if test="customerId != null">
                customer_id = #{customerId,jdbcType=INTEGER},
            </if>
            <if test="customerTagId != null">
                customer_tag_id = #{customerTagId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTagBinding">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update moe_customer_tag_binding
        set customer_id = #{customerId,jdbcType=INTEGER},
        customer_tag_id = #{customerTagId,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="getTagBindingForCustomer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from moe_customer_tag_binding
        where customer_id = #{customerId,jdbcType=INTEGER}
    </select>
    <delete id="removeBindingsForCustomer" parameterType="java.lang.Integer">
        delete from moe_customer_tag_binding
        where customer_id = #{customerId,jdbcType=INTEGER}
    </delete>
    <delete id="removeBindingsForCustomerTag" parameterType="java.lang.Integer">
        delete from moe_customer_tag_binding
        where customer_tag_id = #{customerTagId,jdbcType=INTEGER}
    </delete>
    <insert id="addBindingsForCustomer">
        insert into moe_customer_tag_binding (customer_id, customer_tag_id)
        values
        <foreach collection="tagIds" index="index" item="tagId" separator=",">
            (#{customerId},#{tagId})
        </foreach>
    </insert>
    <select id="queryCustomerIdByTagIds" resultType="int">
        select
        customer_id
        from moe_customer_tag_binding
        <where>
            customer_tag_id in
            <foreach close=")" collection="tagIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="selectTagBindWithTagByCustomerIdList"
            resultType="com.moego.server.customer.service.dto.CustomerTagBindWithTagNameDto">
        SELECT tb.customer_id as customerId,t.`name` as name ,t.id as tagId from moe_customer_tag_binding as tb LEFT
        JOIN
        moe_customer_tag t on (t.id=tb.customer_tag_id)
        <where>
            tb.customer_id in
            <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </where>
        ORDER BY t.sort DESC
    </select>

  <select id="listCustomerIdByFilter" resultType="int">
    <foreach collection="clientsFilter.filters" item="filter">
    SELECT
        DISTINCT mbc.id customer_id
    FROM
        moe_business_customer mbc
    LEFT JOIN
        moe_customer_tag_binding mctb
    ON mbc.id = mctb.customer_id
       AND mctb.customer_tag_id IN
       <foreach collection="filter.values" item="value" open="(" separator="," close=")">
          #{value}
       </foreach>
    WHERE
      mbc.company_id = #{clientsFilter.companyId}
      <if test="clientsFilter.preferredBusinessIds != null and clientsFilter.preferredBusinessIds.size() > 0">
        AND mbc.business_id IN
        <foreach collection="clientsFilter.preferredBusinessIds" item="businessId" open="(" close=")" separator=",">
          #{businessId,jdbcType=INTEGER}
        </foreach>
      </if>

      <if test="clientsFilter.customerIds != null and clientsFilter.customerIds.size > 0">
        AND mbc.id IN
        <foreach collection="clientsFilter.customerIds" item="customerId" open="(" separator="," close=")">
          #{customerId}
        </foreach>
      </if>
      AND mbc.status = 1
      AND mctb.customer_id IS
      <choose>
        <when test="filter.operator == @com.moego.common.enums.filter.OperatorEnum@OPERATOR_IN">
          NOT NULL
        </when>
        <when test="filter.operator == @com.moego.common.enums.filter.OperatorEnum@OPERATOR_NOT_IN">
          NULL
        </when>
      </choose>
    </foreach>
  </select>

    <select id="getTagBindingForCustomers" resultMap="BaseResultMap">
      select
          <include refid="Base_Column_List"/>
      from
          moe_customer_tag_binding
      where
          1 = 1
          <if test="customerIds != null and customerIds.size > 0">
              and customer_id in
              <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                  #{customerId}
              </foreach>
          </if>
    </select>

    <insert id="batchInsert" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTagBinding" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO
        moe_customer_tag_binding (customer_id, customer_tag_id)
    VALUES
    <foreach collection="list" item="item" separator=",">
        (#{item.customerId,jdbcType=INTEGER}, #{item.customerTagId,jdbcType=INTEGER})
    </foreach>
  </insert>
</mapper>
