package com.moego.server.customer.helper;

import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetPetInfoRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/5/14
 */
@Component
@RequiredArgsConstructor
public class PetHelper {

    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub petStub;

    /**
     * Get pet info by id, throw exception if not found.
     *
     * @param companyId company id, optional
     * @param petId     pet id
     * @return pet info
     */
    public BusinessCustomerPetInfoModel mustGetPet(@Nullable Long companyId, long petId) {

        var builder = GetPetInfoRequest.newBuilder();
        if (companyId != null) {
            builder.setTenant(Tenant.newBuilder().setCompanyId(companyId).build());
        }
        builder.setId(petId);

        var resp = petStub.getPetInfo(builder.build());
        if (!resp.hasPet()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Pet not found, companyId: %s, petId: %s".formatted(companyId, petId));
        }

        return resp.getPet();
    }
}
