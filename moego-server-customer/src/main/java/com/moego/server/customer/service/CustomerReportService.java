package com.moego.server.customer.service;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.PetTypeEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.DateUtil;
import com.moego.server.customer.dto.CustomerWithAddress;
import com.moego.server.customer.dto.CustomerWithAddressAndContact;
import com.moego.server.customer.dto.DashboardClientSummaryDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.ReportWebApptCustomerInfo;
import com.moego.server.customer.dto.ReportWebApptCustomerRequest;
import com.moego.server.customer.dto.ReportWebApptCustomerResponse;
import com.moego.server.customer.dto.ReportWebPet;
import com.moego.server.customer.mapper.MoeBusinessCustomerMapper;
import com.moego.server.customer.mapper.MoeCustomerPetMapper;
import com.moego.server.customer.mapperbean.MoeBusinessCustomer;
import com.moego.server.customer.mapperbean.MoeCustomerAddress;
import com.moego.server.customer.mapperbean.MoeCustomerContact;
import com.moego.server.customer.mapperbean.MoeCustomerPet;
import com.moego.server.customer.params.DashboardClientSummaryRequest;
import com.moego.server.customer.service.dto.CustomerWithAddressDto;
import com.moego.server.customer.service.dto.PetVaccineInfo;
import com.moego.server.customer.service.dto.ReportPet;
import com.moego.server.customer.service.grpc.GrpcClientPetSettingService;
import jakarta.annotation.Nullable;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@Service
public class CustomerReportService {

    @Autowired
    MoeCustomerPetMapper petMapper;

    @Autowired
    MoeBusinessCustomerMapper customerMapper;

    @Autowired
    private GrpcClientPetSettingService grpcClientPetSettingService;

    public DashboardClientSummaryDTO getDashboardClientSummary(DashboardClientSummaryRequest request)
            throws ParseException {
        if (CollectionUtils.isEmpty(request.getCustomerIds()) || CollectionUtils.isEmpty(request.getPetIds())) {
            return DashboardClientSummaryDTO.builder()
                    .cats(0)
                    .dogs(0)
                    .existingClients(0)
                    .newClients(0)
                    .others(0)
                    .totalClients(0)
                    .totalPets(0)
                    .build();
        }

        List<Integer> uniquePetIds = request.getPetIds().stream().distinct().collect(Collectors.toList());
        List<MoeCustomerPet> pets = petMapper.selectPetByIdList(uniquePetIds);
        Map<Integer, MoeCustomerPet> idToPet =
                pets.stream().collect(Collectors.toMap(MoeCustomerPet::getId, Function.identity()));

        int dogs = 0, cats = 0;
        for (Integer petId : request.getPetIds()) {
            MoeCustomerPet pet = idToPet.get(petId);
            if (pet != null) {
                if (Objects.equals(PetTypeEnum.CAT.getType(), pet.getPetTypeId())) {
                    cats++;
                } else if (Objects.equals(PetTypeEnum.DOG.getType(), pet.getPetTypeId())) {
                    dogs++;
                }
            }
        }

        List<MoeBusinessCustomer> customers = customerMapper.queryByCustomerIds(request.getCustomerIds());
        int newCustomers = 0;
        long startSeconds =
                DateUtil.parseDate(request.getStartDate(), "yyyy-MM-dd").getTime() / 1000;
        for (MoeBusinessCustomer customer : customers) {
            if (customer.getCreateTime() >= startSeconds) {
                newCustomers++;
            }
        }

        return DashboardClientSummaryDTO.builder()
                .totalPets(request.getPetIds().size())
                .totalClients(request.getCustomerIds().size())
                .dogs(dogs)
                .cats(cats)
                .others(request.getPetIds().size() - cats - dogs)
                .newClients(newCustomers)
                .existingClients(customers.size() - newCustomers)
                .build();
    }

    public List<MoeBusinessCustomerDTO> queryCustomerBasicInfo(List<Integer> customerIds) {
        List<MoeBusinessCustomer> customers = customerMapper.queryCustomerAvatars(customerIds);
        return customers.stream()
                .map(c -> {
                    MoeBusinessCustomerDTO res = new MoeBusinessCustomerDTO();
                    res.setCustomerId(c.getId());
                    res.setBusinessId(c.getBusinessId());
                    res.setPreferredBusinessId(c.getBusinessId().longValue());
                    res.setAvatarPath(c.getAvatarPath());
                    res.setFirstName(c.getFirstName());
                    res.setLastName(c.getLastName());
                    res.setCompanyId(c.getCompanyId());
                    return res;
                })
                .collect(Collectors.toList());
    }

    public Integer getRecurringCustomerRate(List<Integer> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return 0;
        }
        int recurringCount = customerMapper.getRecurringCount(customerIds);
        return recurringCount * 100 / customerIds.size();
    }

    public ReportWebApptCustomerResponse queryReportWebApptCustomerInfo(ReportWebApptCustomerRequest request) {
        ReportWebApptCustomerResponse result = new ReportWebApptCustomerResponse();
        if (!CollectionUtils.isEmpty(request.getCustomerIds())) {
            List<ReportWebApptCustomerInfo> customers;
            if (request.getType() == 1) {
                customers = customerMapper.queryCustomersWithNumberAndAddress(request.getCustomerIds());
            } else {
                customers = customerMapper.queryCustomersWithNumber(request.getCustomerIds());
            }
            Map<Integer, ReportWebApptCustomerInfo> customerMap = customers.stream()
                    .collect(Collectors.toMap(p -> p.getClientId(), Function.identity(), (v1, v2) -> v1));
            result.setIdToCustomerInfo(customerMap);
        }

        if (!CollectionUtils.isEmpty(request.getPetIds())) {
            List<MoeCustomerPet> moeCustomerPets = petMapper.selectPetByIdList(request.getPetIds());
            Map<Integer, String> petNameMap =
                    moeCustomerPets.stream().collect(Collectors.toMap(p -> p.getId(), p -> p.getPetName()));
            result.setIdToPetName(petNameMap);
        }

        return result;
    }

    public List<ReportWebPet> queryReportWebPet(
            Integer businessId, Integer reportId, @Nullable String startDate, @Nullable String endDate) {
        switch (reportId) {
            case 3001:
                return getPetsReport(businessId);
            case 3002:
                return getPetByVaccination(businessId, startDate, endDate);
            case 3003:
                return getPetByBreed(businessId);
            case 3004:
                return getPetByPetCode(businessId);
            default:
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Unknown reportId: " + reportId);
        }
    }

    private List<ReportWebPet> getPetsReport(Integer businessId) {
        // 0相当于查字典表, 部分 1.0 商家可能数据不全
        Map<Integer, String> petTypeNameMap = grpcClientPetSettingService.getPetTypeNameMap();

        List<ReportPet> reportPets = petMapper.queryPetsReport(businessId);
        List<ReportWebPet> result = new ArrayList<>();
        for (ReportPet pet : reportPets) {
            ReportWebPet webPet = new ReportWebPet();
            result.add(webPet);

            BeanUtils.copyProperties(pet, webPet);
            webPet.setIsMix(Byte.valueOf("1").equals(pet.getBreedMix()));
            webPet.setGender(CustomerPetEnum.GENDER_MALE.equals(pet.getGender()) ? "Male" : "Female");
            webPet.setVetContactNumber(pet.getVetPhone());
            webPet.setEmergencyContactNumber(pet.getEmergencyContactPhone());
            webPet.setHealthIssue(pet.getHealthIssues());
            webPet.setIsPassAway(CustomerPetEnum.LIFE_STATUS_DIE.equals(pet.getLifeStatus()));
            webPet.setOwnerName(pet.getFirstName() + " " + pet.getLastName());
            // 设置 petType name, 默认 other
            webPet.setType(petTypeNameMap.getOrDefault(pet.getPetTypeId(), PetTypeEnum.OTHER.getName()));

            List<String> petCodes = pet.getPetCodes().stream()
                    .map(c -> c.getCodeNumber())
                    .distinct()
                    .collect(Collectors.toList());
            webPet.setPetCodes(petCodes);
        }
        return result;
    }

    private List<ReportWebPet> getPetByVaccination(
            Integer businessId, @Nullable String startDate, @Nullable String endDate) {
        List<ReportPet> reportPets = petMapper.queryPetVaccine(businessId, startDate, endDate);
        List<ReportWebPet> result = new ArrayList<>();
        for (ReportPet pet : reportPets) {
            MoeCustomerContact contact = null;
            if (!CollectionUtils.isEmpty(pet.getCustomerContacts())) {
                contact = pet.getCustomerContacts().get(0);
            } else {
                log.error("No customer found for pet, customerId: {}", pet.getCustomerId());
            }
            for (PetVaccineInfo vaccine : pet.getVaccines()) {
                String expirationDate = vaccine.getExpirationDate();
                if (!StringUtils.hasText(expirationDate)) {
                    // 没有过期时间，数据可能来自 intake form，不需要展示
                    continue;
                }
                ReportWebPet webPet = new ReportWebPet();
                result.add(webPet);

                webPet.setVaccination(vaccine.getName());
                webPet.setPetId(pet.getPetId());
                webPet.setPetName(pet.getPetName());
                if (contact != null) {
                    webPet.setOwnerName((contact.getFirstName() + " " + contact.getLastName()).trim());
                    webPet.setEmail(contact.getEmail());
                    webPet.setPrimaryNumber(contact.getPhoneNumber());
                }
                LocalDate expiredDate = LocalDate.parse(expirationDate);
                LocalDate now = LocalDate.now();
                boolean expired = expiredDate.isBefore(now) || expiredDate.isEqual(now);
                webPet.setStatus(expired ? "Expired" : "Valid");
                webPet.setExpirationDate(expirationDate);
                webPet.setVetName(pet.getVetName());
                webPet.setVetContactNumber(pet.getVetPhone());
                webPet.setVetAddress(pet.getVetAddress());
                webPet.setEmergencyContactName(pet.getEmergencyContactName());
                webPet.setEmergencyContactNumber(pet.getEmergencyContactPhone());
                webPet.setHealthIssue(pet.getHealthIssues());
            }
        }
        return result;
    }

    private List<ReportWebPet> getPetByBreed(Integer businessId) {
        return petMapper.queryPetsByBusinessId(businessId).stream()
                .map(pet -> {
                    String type = PetTypeEnum.OTHER.getName();
                    if (Objects.equals(PetTypeEnum.CAT.getType(), pet.getPetTypeId())) {
                        type = PetTypeEnum.CAT.getName();
                    } else if (Objects.equals(PetTypeEnum.DOG.getType(), pet.getPetTypeId())) {
                        type = PetTypeEnum.DOG.getName();
                    }
                    return Joiner.on(Strings.LINE_SEPARATOR).join(pet.getBreed(), type);
                })
                // 分组计数
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet()
                .stream()
                .map(entry -> {
                    ReportWebPet webPet = new ReportWebPet();
                    webPet.setTotalPets(Math.toIntExact(entry.getValue()));
                    List<String> keys = Splitter.on(Strings.LINE_SEPARATOR).splitToList(entry.getKey());
                    webPet.setBreed(keys.get(0));
                    webPet.setType(keys.get(1));
                    return webPet;
                })
                .collect(Collectors.toList());
    }

    private List<ReportWebPet> getPetByPetCode(Integer businessId) {
        return petMapper.countPetCodes(businessId);
    }

    public List<CustomerWithAddressAndContact> queryCustomerWithAddressAndContact(
            @RequestBody List<Integer> customerIds) {
        List<CustomerWithAddressDto> moeCustomers = customerMapper.queryCustomersWithAddressAndContact(customerIds);

        return moeCustomers.stream()
                .map(mc -> {
                    CustomerWithAddressAndContact mcContact = new CustomerWithAddressAndContact();
                    BeanUtils.copyProperties(mc, mcContact);
                    if (CollectionUtils.isEmpty(mc.getAddresses())) {
                        mcContact.setAddresses(Collections.emptyList());
                    } else {
                        mcContact.setAddresses(mc.getAddresses().stream()
                                .map(addr -> buildAddress(addr))
                                .collect(Collectors.toList()));
                    }
                    List<String> tmpContacts = new ArrayList<>();
                    mc.getContacts().forEach(contact -> {
                        if (CustomerContactEnum.IS_PRIMARY_TRUE.equals(contact.getIsPrimary())) {
                            mcContact.setPrimaryContact(contact.getPhoneNumber());
                        } else {
                            tmpContacts.add(contact.getPhoneNumber());
                        }
                    });
                    mcContact.setAdditionalContact(tmpContacts);
                    return mcContact;
                })
                .collect(Collectors.toList());
    }

    public List<CustomerWithAddress> queryCustomerWithAddress(List<Integer> customerIds) {
        List<CustomerWithAddressDto> moeCustomers = customerMapper.queryCustomersWithAddress(customerIds);

        return moeCustomers.stream()
                .map(mc -> {
                    CustomerWithAddress customer = new CustomerWithAddress();
                    BeanUtils.copyProperties(mc, customer);
                    customer.setIsRecurring(Byte.valueOf("1").equals(mc.getIsRecurring()));
                    if (CollectionUtils.isEmpty(mc.getAddresses())) {
                        customer.setAddress("");
                    } else {
                        customer.setAddress(buildAddress(mc.getAddresses().get(0)));
                    }
                    return customer;
                })
                .collect(Collectors.toList());
    }

    String buildAddress(MoeCustomerAddress address) {
        StringBuilder sb = new StringBuilder();
        if (!StringUtils.isEmpty(address.getAddress1())) {
            sb.append(address.getAddress1()).append(", ");
        }
        if (!StringUtils.isEmpty(address.getAddress2())) {
            sb.append(address.getAddress2()).append(", ");
        }
        if (!StringUtils.isEmpty(address.getCity())) {
            sb.append(address.getCity()).append(", ");
        }
        if (!StringUtils.isEmpty(address.getState())) {
            sb.append(address.getState()).append(", ");
        }
        if (!StringUtils.isEmpty(address.getZipcode())) {
            sb.append(address.getZipcode()).append(", ");
        }
        if (!StringUtils.isEmpty(address.getCountry())) {
            sb.append(address.getCountry()).append(", ");
        }
        if (sb.length() > 2) {
            sb.setLength(sb.length() - 2);
        }
        return sb.toString();
    }
}
