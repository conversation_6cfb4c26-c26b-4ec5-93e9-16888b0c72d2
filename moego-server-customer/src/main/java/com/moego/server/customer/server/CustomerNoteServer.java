/*
 * @since 2021-11-17 18:37:28
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.customer.server;

import com.moego.server.customer.api.ICustomerNoteServiceBase;
import com.moego.server.customer.dto.AddResultDTO;
import com.moego.server.customer.dto.CustomerNoteDto;
import com.moego.server.customer.mapper.MoeCustomerNoteMapper;
import com.moego.server.customer.mapperbean.MoeCustomerNote;
import com.moego.server.customer.mapstruct.CustomerNoteConverter;
import com.moego.server.customer.params.CustomerNoteSaveVo;
import com.moego.server.customer.service.CustomerService;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class CustomerNoteServer extends ICustomerNoteServiceBase {

    @Autowired
    private CustomerService customerService;

    @Autowired
    private MoeCustomerNoteMapper customerNoteMapper;

    @Override
    public AddResultDTO createCustomerNote(
            Integer tokenBusinessId, Integer tokenStaffId, @RequestBody CustomerNoteSaveVo saveVo) {
        MoeCustomerNote customerNote = new MoeCustomerNote();
        customerNote.setLastStaffId(tokenStaffId);
        customerNote.setStaffId(tokenStaffId);
        customerNote.setCustomerId(saveVo.getCustomerId());
        customerNote.setNote(saveVo.getNote());
        return customerService.insertCustomerNote(customerNote);
    }

    @Override
    public AddResultDTO createIdempotentCustomerNote(
            Integer tokenBusinessId, Integer tokenStaffId, CustomerNoteSaveVo saveVo) {
        CustomerNoteDto latestNote = customerNoteMapper.getCustomerLatestNote(saveVo.getCustomerId());

        String latestNoteStr = (latestNote == null || latestNote.getNote() == null)
                ? null
                : latestNote.getNote().trim();

        // Idempotent check
        if (Objects.isNull(latestNoteStr)
                || !Objects.equals(latestNoteStr, saveVo.getNote().trim())) {
            return this.createCustomerNote(tokenBusinessId, tokenStaffId, saveVo);
        }
        AddResultDTO result = new AddResultDTO();
        result.setResult(Boolean.FALSE);
        return result;
    }

    @Override
    public CustomerNoteDto getCustomerNoteById(int customerNoteId) {
        MoeCustomerNote customerNote = customerNoteMapper.selectByPrimaryKey(customerNoteId);
        return CustomerNoteConverter.INSTANCE.entityToDTO(customerNote);
    }
}
