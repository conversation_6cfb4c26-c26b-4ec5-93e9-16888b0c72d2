package com.moego.server.customer.service.dto;

import com.moego.server.customer.dto.AppointmentInfoDto;
import com.moego.server.customer.dto.CustomerGroomingAppointmentDTOC;
import com.moego.server.customer.dto.CustomerPaymentsSummaryDto;
import java.util.List;
import lombok.Data;

@Data
public class CustomerDetailDto {

    private List<PetDto> petList;

    private CustomerDto customerDetail;

    private CustomerPaymentsSummaryDto payments;

    private AppointmentInfoDto appointmentInfo;
    private CustomerGroomingAppointmentDTOC nextAppointment;
    private CustomerGroomingAppointmentDTOC lastAppointment;
    private String lastAppointmentAlertNote;
}
