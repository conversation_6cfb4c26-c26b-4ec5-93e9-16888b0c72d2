<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.customer.mapper.CustomerMigrationMapper">

  <resultMap id="PetSizeMap" type="com.moego.server.customer.mapperbean.MoePetSize">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="weight_low" jdbcType="INTEGER" property="weightLow" />
    <result column="weight_high" jdbcType="INTEGER" property="weightHigh" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>

  <resultMap id="DefaultPreferredFrequencyMap" type="com.moego.server.customer.mapperbean.MoeDefaultPreferredFrequency">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="frequency_type" jdbcType="INTEGER" property="frequencyType" />
    <result column="calendar_period" jdbcType="INTEGER" property="calendarPeriod" />
    <result column="value" jdbcType="INTEGER" property="value" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>

  <resultMap id="MigrateBindingIdMap" type="com.moego.server.customer.mapperbean.MigrateBindingId">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="old_value" jdbcType="INTEGER" property="oldValue" />
  </resultMap>

  <select id="selectCustomerSource" resultMap="com.moego.server.customer.mapper.MoeCustomerSourceMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoeCustomerSourceMapper.Base_Column_List" />
    from moe_customer_source where company_id = #{companyId} and status = 1
  </select>

  <select id="selectCustomerTag" resultMap="com.moego.server.customer.mapper.MoeCustomerTagMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoeCustomerTagMapper.Base_Column_List" />
    from moe_customer_tag where company_id = #{companyId} and status = 1
  </select>

  <select id="selectCustomerTagBinding" resultMap="com.moego.server.customer.mapper.MoeCustomerTagBindingMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoeCustomerTagBindingMapper.Base_Column_List" />
    from moe_customer_tag_binding where customer_id in
    <foreach collection="customerIds" open="(" close=")" separator="," item="customerId">
      #{customerId}
    </foreach>
  </select>

  <select id="selectDefaultPreferredFrequency" resultMap="DefaultPreferredFrequencyMap">
    select id, company_id, business_id, frequency_type, calendar_period, value, created_at, updated_at
    from moe_default_preferred_frequency where company_id = #{companyId}
  </select>

  <select id="selectPetType" resultMap="com.moego.server.customer.mapper.MoePetTypeMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoePetTypeMapper.Base_Column_List" />
    from moe_pet_type where company_id = #{companyId} and status = 1
  </select>

  <select id="selectPetBreed" resultMap="com.moego.server.customer.mapper.MoePetBreedMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoePetBreedMapper.Base_Column_List" />
    from moe_pet_breed where company_id = #{companyId} and pet_type_id = #{petTypeId} and status = 1
  </select>

  <select id="selectPetCode" resultMap="com.moego.server.customer.mapper.MoePetCodeMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoePetCodeMapper.Base_Column_List" />
    from moe_pet_code where company_id = #{companyId} and status = 1
  </select>

  <select id="selectPetCodeBinding" resultMap="com.moego.server.customer.mapper.MoePetPetCodeBindingMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoePetPetCodeBindingMapper.Base_Column_List" />
    from moe_pet_pet_code_binding where pet_id in
    <foreach collection="petIds" open="(" close=")" separator="," item="petId">
      #{petId}
    </foreach>
  </select>

  <select id="selectPetSize" resultMap="PetSizeMap">
    select id, company_id, business_id, name, weight_low, weight_high, status, created_at, updated_at
    from moe_pet_size where company_id = #{companyId} and status = 1
  </select>

  <select id="selectPetVaccine" resultMap="com.moego.server.customer.mapper.MoePetVaccineMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoePetVaccineMapper.Base_Column_List" />
    from moe_pet_vaccine where company_id = #{companyId} and status = 1
  </select>

  <select id="selectPetCoatType" resultMap="com.moego.server.customer.mapper.MoePetHairLengthMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoePetHairLengthMapper.Base_Column_List" />
    from moe_pet_hair_length where company_id = #{companyId} and status = 1
  </select>

  <select id="selectPetFixed" resultMap="com.moego.server.customer.mapper.MoePetFixedMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoePetFixedMapper.Base_Column_List" />
    from moe_pet_fixed where company_id = #{companyId} and status = 1
  </select>

  <select id="selectPetBehavior" resultMap="com.moego.server.customer.mapper.MoePetBehaviorMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoePetBehaviorMapper.Base_Column_List" />
    from moe_pet_behavior where company_id = #{companyId} and status = 1
  </select>

  <select id="selectAllCustomer" resultMap="com.moego.server.customer.mapper.MoeBusinessCustomerMapper.BaseResultMap">
    select
        customer.id as id,
        customer.first_name as first_name,
        customer.last_name as last_name,
        customer.business_id as business_id,
        customer.company_id as company_id,
        contact.phone_number as phone_number
    from moe_business_customer as customer
    left join moe_customer_contact as contact
    on customer.id = contact.customer_id
    where customer.company_id = #{companyId} and customer.status = 1
    and contact.type = 1 and contact.status = 1
  </select>

  <select id="findClientsInSameName" resultMap="com.moego.server.customer.mapper.MoeBusinessCustomerMapper.BaseResultMap">
    select distinct c1.id as id,
           c1.first_name as first_name,
           c1.last_name as last_name,
           c1.business_id as business_id,
           c1.company_id as company_id
    from moe_business_customer c1 join moe_business_customer c2
                                       on  c1.first_name = c2.first_name
                                         and c1.last_name = c2.last_name
                                         and c1.id != c2.id
                                         and c1.company_id = #{companyId}
                                         and c2.company_id = #{companyId}
                                         and c1.status = 1
                                         and c2.status = 1
  </select>

  <select id="queryMainContact" resultMap="com.moego.server.customer.mapper.MoeCustomerContactMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoeCustomerContactMapper.Base_Column_List" />
    from moe_customer_contact where company_id = #{companyId} and type = 1 and status = 1
    and customer_id in
    <foreach collection="customerIds" open="(" close=")" separator="," item="customerId">
      #{customerId}
    </foreach>
  </select>

  <select id="queryAllPet" resultMap="com.moego.server.customer.mapper.MoeCustomerPetMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoeCustomerPetMapper.Base_Column_List" />
    from moe_customer_pet where company_id = #{companyId} and status = 1
    and customer_id in
    <foreach collection="customerIds" open="(" close=")" separator="," item="customerId">
      #{customerId}
    </foreach>
  </select>

  <select id="queryAllContact" resultMap="com.moego.server.customer.mapper.MoeCustomerContactMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoeCustomerContactMapper.Base_Column_List" />
    from moe_customer_contact where company_id = #{companyId} and status = 1
    and customer_id in
    <foreach collection="customerIds" open="(" close=")" separator="," item="customerId">
      #{customerId}
    </foreach>
  </select>

  <select id="queryAllAddress" resultMap="com.moego.server.customer.mapper.MoeCustomerAddressMapper.BaseResultMap">
    select <include refid="com.moego.server.customer.mapper.MoeCustomerAddressMapper.Base_Column_List" />
    from moe_customer_address where company_id = #{companyId} and status = 1
    and customer_id in
    <foreach collection="customerIds" open="(" close=")" separator="," item="customerId">
      #{customerId}
    </foreach>
    order by id
  </select>

  <update id="remainAndDelete">
    <if test="deleteIds != null and deleteIds.size() > 0">
      update ${tableName} set status = 2
        <if test="updateTime != null">
          ,update_time = #{updateTime}
        </if>
        <if test="updatedAt != null">
          ,updated_at = #{updatedAt}
        </if>
      where id in
      <foreach collection="deleteIds" open="(" close=")" separator="," item="deleteId">
        #{deleteId}
      </foreach>
      ;
    </if>
    <if test="remainIds != null and remainIds.size() > 0">
      update ${tableName} set business_id = 0
        <if test="updateTime != null">
          ,update_time = #{updateTime}
        </if>
        <if test="updatedAt != null">
          ,updated_at = #{updatedAt}
        </if>
      where business_id != 0 and id in
      <foreach collection="remainIds" open="(" close=")" separator="," item="remainId">
        #{remainId}
      </foreach>
      ;
    </if>
    select 1;
  </update>

  <update id="migrateBinding">
    <if test="mappings != null and mappings.size() > 0">
      <foreach collection="mappings.entrySet()" index="targetId" item="sourceIds">
        update ${tableName} set ${columnName} = #{targetId}
        where ${columnName} in
        <foreach collection="sourceIds" open="(" close=")" separator="," item="sourceId">
          #{sourceId}
        </foreach>
        <if test="companyId != null">
          and company_id = #{companyId}
        </if>
        ;
      </foreach>
    </if>
  </update>

  <select id="selectPetBindingId" resultMap="MigrateBindingIdMap">
    select id, ${columnName} as old_value
    from ${tableName}
    where pet_id in (select id from moe_customer_pet where company_id = #{companyId})
    and ${columnName} in
    <foreach collection="oldValues" open="(" close=")" separator="," item="oldValue">
      #{oldValue}
    </foreach>
  </select>

  <update id="updateBindingById">
    update ${tableName}
    set ${columnName} = #{newValue}
    where id = #{id}
  </update>

  <delete id="deleteCustomerTagBinding">
    delete from moe_customer_tag_binding where id in
    <foreach collection="ids" open="(" close=")" separator="," item="id">
      #{id}
    </foreach>
  </delete>

  <delete id="deletePetCodeBinding">
    delete from moe_pet_pet_code_binding where id in
    <foreach collection="ids" open="(" close=")" separator="," item="id">
      #{id}
    </foreach>
  </delete>
</mapper>
