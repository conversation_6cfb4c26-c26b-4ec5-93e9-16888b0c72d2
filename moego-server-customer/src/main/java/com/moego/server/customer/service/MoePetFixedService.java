package com.moego.server.customer.service;

import com.moego.common.dto.SortDto;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.SortUtils;
import com.moego.server.customer.dto.AddResultDTO;
import com.moego.server.customer.dto.MoePetFixedDTO;
import com.moego.server.customer.dto.PetFixedListDTO;
import com.moego.server.customer.mapper.MoePetFixedMapper;
import com.moego.server.customer.mapperbean.MoePetFixed;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
public class MoePetFixedService {

    @Autowired
    private MoePetFixedMapper moePetFixedMapper;

    @Transactional
    public ResponseResult<AddResultDTO> addMoePetFixed(MoePetFixed moePetFixed) {
        if (StringUtils.isBlank(moePetFixed.getName())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "name is not null");
        }

        ResponseResult<Boolean> booleanResponseResult =
                checkFixedName(moePetFixed.getBusinessId(), moePetFixed.getName(), null);
        if (!booleanResponseResult.getData()) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Pet fixed is already in use");
        }

        moePetFixed.setCreateTime(CommonUtil.get10Timestamp());
        moePetFixed.setUpdateTime(CommonUtil.get10Timestamp());
        moePetFixed.setStatus((byte) 1);

        moePetFixedMapper.addMoePetFixed(moePetFixed);

        moePetFixed.setSort(moePetFixed.getId());
        moePetFixedMapper.updateByPrimaryKeySelective(moePetFixed);

        AddResultDTO addResultDTO = new AddResultDTO();
        addResultDTO.setId(moePetFixed.getId());
        addResultDTO.setResult(true);
        return ResponseResult.success(addResultDTO);
    }

    @Transactional
    public ResponseResult<Integer> modifyMoePetFixed(MoePetFixed moePetFixed) {
        if (StringUtils.isBlank(moePetFixed.getName())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "name is not null");
        }

        ResponseResult<Boolean> booleanResponseResult =
                checkFixedName(moePetFixed.getBusinessId(), moePetFixed.getName(), moePetFixed.getId());
        if (!booleanResponseResult.getData()) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Pet fixed is already in use");
        }

        MoePetFixed moePetFixed1 = moePetFixedMapper.selectByPrimaryKey(moePetFixed.getId());

        if (moePetFixed1 == null || !moePetFixed1.getBusinessId().equals(moePetFixed.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }
        moePetFixed.setUpdateTime(CommonUtil.get10Timestamp());
        int i = moePetFixedMapper.updateByPrimaryKeySelective(moePetFixed);
        return ResponseResult.success(i);
    }

    public ResponseResult<Integer> deleteMoePetFixed(Integer tokenBusinessId, Integer id) {
        MoePetFixed moePetFixed = moePetFixedMapper.selectByPrimaryKey(id);
        if (moePetFixed == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "no found object");
        }

        if (!tokenBusinessId.equals(moePetFixed.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "no auth");
        }

        Integer row = moePetFixedMapper.deleteMoePetFixed(id);
        return ResponseResult.success(row);
    }

    public ResponseResult<Integer> sortMoePetFixed(Integer tokenBusinessId, List<Integer> idList) {
        List<SortDto> sort = SortUtils.sort(idList);
        if (sort.size() == 0) {
            return ResponseResult.success(0);
        }
        moePetFixedMapper.sortMoePetFixed(tokenBusinessId, sort);
        return ResponseResult.success(sort.size());
    }

    public List<MoePetFixedDTO> findMoePetFixed(Integer businessId) {
        List<MoePetFixedDTO> result = Collections.emptyList();
        if (businessId == null) {
            return result;
        }
        List<MoePetFixed> beans = moePetFixedMapper.selectByBusinessId(businessId);
        if (CollectionUtils.isEmpty(beans)) {
            return result;
        }

        final List<MoePetFixedDTO> resultDtos = new ArrayList<>();
        beans.forEach(bean -> {
            MoePetFixedDTO fixedDto = new MoePetFixedDTO();
            BeanUtils.copyProperties(bean, fixedDto);
            resultDtos.add(fixedDto);
        });
        return resultDtos;
    }

    public ResponseResult<List<PetFixedListDTO>> queryMoePetFixed(Integer tokenBusinessId, String name) {
        List<PetFixedListDTO> petFixedListDTOS = moePetFixedMapper.queryMoePetFixed(tokenBusinessId, name);
        return ResponseResult.success(petFixedListDTOS);
    }

    public ResponseResult<Boolean> checkFixedName(Integer tokenBusinessId, String name, Integer id) {
        MoePetFixed moePetFixed = moePetFixedMapper.selectFixedByName(tokenBusinessId, name);

        if (moePetFixed != null) {
            if (id != null) {
                if (id.equals(moePetFixed.getId())) {
                    return ResponseResult.success(true);
                }
            }
            return ResponseResult.success(false);
        }
        return ResponseResult.success(true);
    }
}
