package com.moego.server.customer.web;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.customer.params.CustomerLoginParams;
import com.moego.server.customer.web.dto.ob.OBLoginResultDTO;
import com.moego.server.customer.web.dto.ob.OBMigrateTokenResultDTO;
import com.moego.server.customer.web.params.OBClientLoginByEmailParams;
import com.moego.server.customer.web.params.OBClientLoginByPhoneNumberParams;
import com.moego.server.customer.web.params.OBClientLoginByTokenParams;
import com.moego.server.customer.web.params.OBClientLoginParams;
import com.moego.server.customer.web.params.OBClientMigrateTokenParams;
import com.moego.server.customer.web.vo.OBClientInfoVO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 这里的接口都是已经在前端移除调用了的，只有极个别用户仍停留在旧的前端页面，才会触发这些接口调用。
 * 这些接口的请求频率都是每隔好几天才会出现几次，流量几乎可以忽略不计。
 * 但是为了引导用户去刷新页面，拿到最新的前端代码调用后端的新接口，统一把这些接口汇总到这里返回 403，并给出报错文案提示。
 * 当这些接口彻底没有流量时，便可以移除。
 */
@Slf4j
@RestController
@AllArgsConstructor
public class LegacyController {

    /**
     * 返回 403 的错误码，并给出特殊的报错文案提示
     */
    private static final BizException ERROR = ExceptionUtil.bizException(
            Code.CODE_FORBIDDEN, "Your page is out of date, please refresh the page and try again.");

    /**
     * 批量迁移 customer token，合入主会话，不检查 token 有效性，不报错
     * 必须在 OB 主会话中调用该接口
     * 这是一个临时接口，应当在 2023-06-30 下线, 该日期后前端不应再调这个接口迁移
     */
    @Deprecated
    @PostMapping("/customer/ob/v2/client/token-migrate")
    @Auth(AuthType.ANONYMOUS)
    public OBMigrateTokenResultDTO migrateToken(
            @RequestBody OBClientMigrateTokenParams params, OBAnonymousParams anonymousParams) {
        throw ERROR;
    }

    /**
     * 请使用新接口 moego-api-v3: /moego.api.online_booking.v1.OBClientService/GetOBClientInfo
     */
    @Deprecated
    @GetMapping("/customer/ob/v2/client/info")
    @Auth(AuthType.ANONYMOUS)
    public OBClientInfoVO getCustomerInfo(OBAnonymousParams params) {
        throw ERROR;
    }

    /**
     * OB 3.0 通过手机号验证码登录 existing client
     * 该登录方法会创建一个会话，并返回会话 token 给前端作为 customer token
     *
     * 请使用新接口 moego-api-v3: /moego.api.online_booking.v1.OBAccessService/Login (login_method 为 by_verification_code)
     */
    @Deprecated
    @PostMapping("/customer/ob/v2/client/login")
    @Auth(AuthType.ANONYMOUS)
    public String login(@RequestBody CustomerLoginParams params, OBAnonymousParams anonymousParams) {
        throw ERROR;
    }

    /**
     * OB 3.0 通过邮箱验证码登录 existing client
     * 该登录方法会创建一个会话，并返回会话 token 给前端作为 customer token
     *
     * 请使用新接口 moego-api-v3: /moego.api.online_booking.v1.OBAccessService/Login (login_method 为 by_verification_code)
     */
    @Deprecated
    @PostMapping("/customer/ob/v2/client/email-login")
    @Auth(AuthType.ANONYMOUS)
    public String loginByEmail(
            @RequestBody OBClientLoginParams obClientLoginParams, OBAnonymousParams anonymousParams) {
        throw ERROR;
    }

    /**
     * 请使用新接口 moego-api-v3: /moego.api.online_booking.v1.OBAccessService/Login (login_method 为 by_verification_code)
     *
     * OB 3.0 通过手机号验证码登录 existing client
     * 该登录方法必须要有主会话和 ob 的 name 参数
     * 基于主会话创建一个子会话，子会话仅用于记录 existing client 的 customer id
     * 前端凭主会话登录即可
     * 该方法不返回任何 token
     *
     */
    @Deprecated
    @PostMapping("/customer/ob/v2/client/login/phone-number")
    @Auth(AuthType.ANONYMOUS)
    public OBLoginResultDTO loginByPhoneNumber(
            @RequestBody OBClientLoginByPhoneNumberParams params, OBAnonymousParams anonymousParams) {
        throw ERROR;
    }

    /**
     * 请使用新接口 moego-api-v3: /moego.api.online_booking.v1.OBAccessService/Login (login_method 为 by_verification_code)
     *
     * OB 3.0 通过邮箱验证码登录 existing client
     * 该登录方法必须要有主会话和 ob 的 name 参数
     * 基于主会话创建一个子会话，子会话仅用于记录 existing client 的 customer id
     * 前端凭主会话登录即可
     * 该方法不返回任何 token
     *
     * 注：入参里传 phone number 而不是 email，是因为商家的 customer 数据以 phone number 唯一标识，
     * 通过 phone number 找到对应的 customer id 和 email，来检查邮箱验证码是否正确
     */
    @Deprecated
    @PostMapping("/customer/ob/v2/client/login/email")
    @Auth(AuthType.ANONYMOUS)
    public OBLoginResultDTO loginByEmailV2(
            @RequestBody OBClientLoginByEmailParams params, OBAnonymousParams anonymousParams) {
        throw ERROR;
    }

    /**
     * 请使用新接口 moego-api-v3: /moego.api.online_booking.v1.OBAccessService/Login (login_method 为 by_ppp)
     *
     * 使用 token 登录 OB
     * 必须在 OB 主会话中调用该接口
     *
     * @param params
     * @param anonymousParams
     * @return
     */
    @Deprecated
    @PostMapping("/customer/ob/v2/client/login/token")
    @Auth(AuthType.ANONYMOUS)
    public OBLoginResultDTO loginByToken(
            @RequestBody OBClientLoginByTokenParams params, OBAnonymousParams anonymousParams) {
        throw ERROR;
    }

    /**
     * 请使用新接口 moego-api-v3: /moego.api.online_booking.v1.OBClientService/GetOBClientInfo
     *
     * 如果有 OB 登录态，则请求对应的 customer info 数据
     * 如果没有，则返回 null，不会报 401 no auth
     *
     * @param context
     * @param params
     * @return
     */
    @Deprecated
    @GetMapping("/customer/ob/v2/client/info-v2")
    @Auth(AuthType.ANONYMOUS)
    public OBClientInfoVO getCustomerInfoV2(AuthContext context, OBAnonymousParams params) {
        throw ERROR;
    }
}
