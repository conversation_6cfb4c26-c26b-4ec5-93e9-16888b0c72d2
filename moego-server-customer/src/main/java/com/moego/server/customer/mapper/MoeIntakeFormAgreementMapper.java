package com.moego.server.customer.mapper;

import com.moego.server.customer.mapperbean.MoeIntakeFormAgreement;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeIntakeFormAgreementMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_agreement
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_agreement
     *
     * @mbg.generated
     */
    int insert(MoeIntakeFormAgreement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_agreement
     *
     * @mbg.generated
     */
    int insertSelective(MoeIntakeFormAgreement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_agreement
     *
     * @mbg.generated
     */
    MoeIntakeFormAgreement selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_agreement
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeIntakeFormAgreement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_intake_form_agreement
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeIntakeFormAgreement record);

    List<MoeIntakeFormAgreement> selectByFormAndAgreementId(
            @Param("formId") Integer formId, @Param("agreementId") Integer agreementId);

    int deleteByAgreementId(Integer agreementId);
}
