package com.moego.server.customer.web.vo.ob;

import com.moego.common.enums.DeleteStatusEnum;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.mapperbean.ProfileRequestAddress;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/27
 */
@Data
public class OBClientAddressVO {

    private Integer addressId;

    @Schema(description = "1-primary, 0-additional")
    private Byte isPrimary;

    @Schema(description = "1-enable, 2-deleted")
    private Byte status;

    @Schema(description = "out of service area")
    private Boolean outOfArea;

    private String address1;
    private String address2;
    private String city;
    private String country;
    private String lat;
    private String lng;
    private String state;
    private String zipcode;

    /**
     * @see CustomerAddressDto#getIsProfileRequestAddress()
     */
    private Boolean isProfileRequestAddress = false;

    public static OBClientAddressVO fromProfileRequestAddress(ProfileRequestAddress address) {
        OBClientAddressVO vo = new OBClientAddressVO();
        vo.setAddress1(address.getAddress1());
        vo.setAddress2(address.getAddress2());
        vo.setCity(address.getCity());
        vo.setState(address.getState());
        vo.setZipcode(address.getZipcode());
        vo.setCountry(address.getCountry());
        vo.setLat(address.getLat());
        vo.setLng(address.getLng());
        vo.setStatus(DeleteStatusEnum.STATUS_NORMAL);
        vo.setIsPrimary(
                Boolean.TRUE.equals(address.getIsPrimary())
                        ? CustomerAddressDto.IsPrimary.TRUE.getValue()
                        : CustomerAddressDto.IsPrimary.FALSE.getValue());
        if (address.getCustomerAddressId() == null) {
            vo.setAddressId(address.getId());
            vo.setIsProfileRequestAddress(true);
        } else {
            vo.setAddressId(address.getCustomerAddressId());
            vo.setIsProfileRequestAddress(false);
        }
        return vo;
    }
}
