package com.moego.server.customer.mapper;

import com.moego.server.customer.mapperbean.MoeCustomerAccountBlackList;
import java.util.List;

public interface MoeCustomerAccountBlackListMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_customer_account_black_list
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_customer_account_black_list
     *
     * @mbg.generated
     */
    int insert(MoeCustomerAccountBlackList record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_customer_account_black_list
     *
     * @mbg.generated
     */
    int insertSelective(MoeCustomerAccountBlackList record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_customer_account_black_list
     *
     * @mbg.generated
     */
    MoeCustomerAccountBlackList selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_customer_account_black_list
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeCustomerAccountBlackList record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_customer_account_black_list
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeCustomerAccountBlackList record);

    int checkPhoneNumber(List<String> phoneNumberList);

    int checkEmail(List<String> emailList);
}
