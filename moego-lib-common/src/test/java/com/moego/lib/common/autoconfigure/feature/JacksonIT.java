package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;

/**
 * {@link Jackson} tester.
 */
@SpringBootTest(
        classes = JacksonIT.Cfg.class,
        properties = {"moego.grpc.server.enabled=false"})
class JacksonIT {

    @Autowired
    ObjectMapper objectMapper;

    @Test
    @SneakyThrows
    void testLongValue() {
        Map<String, Long> map = Map.of(
                "pivot",
                (1L << 53) - 1, // 9007199254740991
                "bigLong",
                1L << 53);

        String json = objectMapper.writeValueAsString(map);
        assertThat(json).contains("9007199254740991").doesNotContain("\"9007199254740991\"");
        assertThat(json).contains("\"9007199254740992\"");

        Map<String, Long> deserializedMap = objectMapper.readValue(json, new TypeReference<>() {});
        assertThat(deserializedMap).isEqualTo(map);
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    static class Cfg {}
}
