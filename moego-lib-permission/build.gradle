apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
  api(project(':moego-lib-common'))
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

  // support http thread invoke grpc client and grpc thread invoke http client
  compileOnly 'org.springframework.boot:spring-boot-starter-web'
  compileOnly 'org.springframework.boot:spring-boot-starter-validation'
  compileOnly 'org.springframework.cloud:spring-cloud-starter-openfeign'
}
