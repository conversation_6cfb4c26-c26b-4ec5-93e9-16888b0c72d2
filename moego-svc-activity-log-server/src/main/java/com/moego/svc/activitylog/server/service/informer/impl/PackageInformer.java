package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.retail.client.IPackageClient;
import com.moego.server.retail.dto.PackageInfoDto;
import com.moego.server.retail.param.GetPackageParams;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#PACKAGE}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PackageInformer extends AbstractStaffOperatorInformer<PackageInfoDto> {

    private final IPackageClient packageApi;

    @Override
    public String resourceType() {
        return ResourceType.PACKAGE.toString();
    }

    @Override
    public String resourceName(PackageInfoDto packageInfo) {
        return packageInfo.getName();
    }

    @Override
    public PackageInfoDto resource(String resourceId) {
        var request = new GetPackageParams(Integer.parseInt(resourceId));
        return packageApi.getPackage(request).packageInfo();
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
