package page

import (
	"gorm.io/gorm"

	utilspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
)

func NewPaginationResponse(pageReq *utilspb.PaginationRequest, total int64) *utilspb.PaginationResponse {
	return &utilspb.PaginationResponse{
		Total:    int32(total),
		PageSize: pageReq.GetPageSize(),
		PageNum:  pageReq.GetPageNum(),
	}
}

func BuildGORMScope(pagination *utilspb.PaginationRequest) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if pagination.GetPageNum() == 0 || pagination.GetPageSize() == 0 {
			return db
		}
		return db.Offset(int((pagination.GetPageNum() - 1) * pagination.GetPageSize())).Limit(int(pagination.GetPageSize()))
	}
}
