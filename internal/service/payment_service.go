package service

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/zlog"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	paymentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2"

	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/featureflag"
	legacyPayment "github.com/MoeGolibrary/moego-svc-payment/internal/logic/legacy/payment"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/payment"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/paymentsetting"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/recurring"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/transaction"
	"github.com/MoeGolibrary/moego-svc-payment/internal/utils/money"
)

type paymentService struct {
	payment       *payment.Payment
	recurring     *recurring.Recurring
	featureflag   *featureflag.FeatureFlag
	legacyPayment *legacyPayment.Payment
	transaction   *transaction.Transaction
	setting       paymentsetting.Logic

	paymentsvcpb.UnimplementedPaymentServiceServer
}

func newPaymentService(
	payment *payment.Payment,
	recurring *recurring.Recurring,
	featureflag *featureflag.FeatureFlag,
	legacyPayment *legacyPayment.Payment,
	transaction *transaction.Transaction,
	setting paymentsetting.Logic,
) *paymentService {
	return &paymentService{
		payment:       payment,
		recurring:     recurring,
		featureflag:   featureflag,
		legacyPayment: legacyPayment,
		transaction:   transaction,
		setting:       setting,
	}
}

func (s *paymentService) GetPaymentVersion(ctx context.Context,
	req *paymentsvcpb.GetPaymentVersionRequest) (*paymentsvcpb.GetPaymentVersionResponse, error) {
	return s.featureflag.GetPaymentVersion(ctx, req.GetPayee())
}

// CreatePayment 创建支付单据，返回支付单据 ID
func (s *paymentService) CreatePayment(ctx context.Context, req *paymentsvcpb.CreatePaymentRequest) (
	*paymentsvcpb.CreatePaymentResponse, error,
) {
	zlog.Info(ctx, "create payment", zap.Any("request", req))
	// 参数校验
	if !money.IsPositive(req.GetAmount()) {
		return nil, status.Error(codes.InvalidArgument, "invalid amount")
	}
	// 执行真正的逻辑
	model, err := s.payment.CreatePayment(ctx, req)
	if err != nil {
		return nil, err
	}
	return &paymentsvcpb.CreatePaymentResponse{
		Payment: model,
	}, nil
}

func (s *paymentService) CreateCombinedPayment(ctx context.Context, req *paymentsvcpb.CreateCombinedPaymentRequest,
) (*paymentsvcpb.CreateCombinedPaymentResponse, error) {
	zlog.Info(ctx, "create combined payment", zap.Any("request", req))
	// 参数校验
	if req.GetPaymentType() != paymentpb.PaymentModel_STANDARD {
		return nil, status.Errorf(codes.InvalidArgument, "create combined payment only allow standard payment type,%s",
			req.GetPaymentType().String())
	}
	if len(req.GetCombinedItems()) == 0 {
		return nil, status.Errorf(codes.InvalidArgument, "combined item is empty")
	}
	for _, item := range req.GetCombinedItems() {
		if !money.IsPositive(item.GetAmount()) {
			return nil, status.Error(codes.InvalidArgument, "invalid amount")
		}
	}

	// 执行业务逻辑
	transactionID, models, err := s.payment.CreateCombinedPayment(ctx, req)
	if err != nil {
		return nil, err
	}
	return &paymentsvcpb.CreateCombinedPaymentResponse{
		TransactionId: transactionID,
		PaymentModels: models,
	}, nil
}

// GetPayData 获取支付数据，用于前端加载第三方支付组件
func (s *paymentService) GetPayData(context.Context, *paymentsvcpb.GetPayDataRequest) (
	*paymentsvcpb.GetPayDataResponse, error,
) {
	return nil, nil
}

// PayPayment 确认支付 Confirm，在用户提交完所有支付凭证后调用
func (s *paymentService) PayPayment(ctx context.Context, req *paymentsvcpb.PayPaymentRequest) (
	*paymentsvcpb.PayPaymentResponse, error,
) {
	zlog.Info(ctx, "pay payment", zap.Any("request", req))
	if req.GetDetail() == nil {
		return nil, status.Error(codes.InvalidArgument, "missing payment detail")
	}

	return s.payment.Pay(ctx, req)
}

// SubmitPayDetail 提交支付detail凭证，一般是在完成 Pay 要求的 Action 后调用
func (s *paymentService) SubmitActionDetail(ctx context.Context, req *paymentsvcpb.SubmitActionDetailRequest) (
	*paymentsvcpb.SubmitActionDetailResponse, error,
) {
	return s.payment.SubmitActionResult(ctx, req)
}

// // Capture 支付确认，只出现于 pre-auth 场景
// func (s *paymentService) Capture(context.Context, *paymentsvcpb.CaptureRequest) (
// 	*paymentsvcpb.CaptureResponse, error,
// ) {
// 	return nil, nil
// }

// Cancel 取消支付
func (s *paymentService) CancelPayment(context.Context, *paymentsvcpb.CancelPaymentRequest) (
	*paymentsvcpb.CancelPaymentResponse, error,
) {
	return nil, nil
}

// GetPayment 获取支付单据
func (s *paymentService) GetPayment(ctx context.Context, req *paymentsvcpb.GetPaymentRequest) (
	*paymentsvcpb.GetPaymentResponse, error,
) {
	return s.payment.GetPayment(ctx, req)
}

func (s *paymentService) GetPaymentView(ctx context.Context, req *paymentsvcpb.GetPaymentViewRequest) (
	*paymentsvcpb.GetPaymentViewResponse, error,
) {
	return s.payment.GetPaymentView(ctx, req)
}

func (s *paymentService) GetChannelPayment(ctx context.Context, req *paymentsvcpb.GetChannelPaymentRequest) (
	*paymentsvcpb.GetChannelPaymentResponse, error) {
	if req.GetPayee() == nil {
		return nil, status.Error(codes.InvalidArgument, "payee is empty")
	}
	return s.payment.CreateChannelPaymentForOnline(ctx, req.GetPayee())
}

func (s *paymentService) ListPayment(context.Context, *paymentsvcpb.ListPaymentRequest) (
	*paymentsvcpb.ListPaymentResponse, error,
) {
	return nil, status.Error(codes.Unimplemented, "not implemented")
}

func (s *paymentService) ListTransaction(ctx context.Context,
	req *paymentsvcpb.ListTransactionRequest,
) (*paymentsvcpb.ListTransactionResponse, error) {
	return s.legacyPayment.ListTransaction(ctx, req)
}

func (s *paymentService) ExportTransactionList(ctx context.Context,
	req *paymentsvcpb.ExportTransactionListRequest,
) (*paymentsvcpb.ExportTransactionListResponse, error) {
	fileID, err := s.legacyPayment.ListPaymentTransactionExport(ctx, req)
	if err != nil {
		return nil, err
	}
	return &paymentsvcpb.ExportTransactionListResponse{
		FileId: fileID,
	}, nil
}

func (s *paymentService) ListPaymentByTransaction(ctx context.Context,
	req *paymentsvcpb.ListPaymentByTransactionRequest,
) (*paymentsvcpb.ListPaymentByTransactionResponse, error) {
	return s.legacyPayment.ListByTransactionID(ctx, req)
}

func (s *paymentService) GetTransaction(ctx context.Context,
	req *paymentsvcpb.GetTransactionRequest) (*paymentsvcpb.GetTransactionResponse, error) {
	return s.transaction.GetTransaction(ctx, req)
}

// AddRecurringPaymentMethod 添加绑定的支付方式
func (s *paymentService) AddRecurringPaymentMethod(
	ctx context.Context, req *paymentsvcpb.AddRecurringPaymentMethodRequest,
) (*paymentsvcpb.AddRecurringPaymentMethodResponse, error) {
	return s.recurring.Add(ctx, req)
}

// DeleteRecurringPaymentMethod 删除绑定的支付方式
func (s *paymentService) DeleteRecurringPaymentMethod(ctx context.Context,
	req *paymentsvcpb.DeleteRecurringPaymentMethodRequest) (
	*paymentsvcpb.DeleteRecurringPaymentMethodResponse, error,
) {
	if req.GetPaymentMethodId() <= 0 {
		return nil, status.Error(codes.InvalidArgument, "invalid payment method id")
	}
	if err := s.recurring.Delete(ctx, req.GetPaymentMethodId()); err != nil {
		return nil, err
	}
	return &paymentsvcpb.DeleteRecurringPaymentMethodResponse{}, nil
}

// SetRecurringPaymentMethodPrimary 将payment method设置为primary
func (s *paymentService) SetRecurringPaymentMethodPrimary(ctx context.Context,
	req *paymentsvcpb.SetRecurringPaymentMethodPrimaryRequest) (
	*paymentsvcpb.SetRecurringPaymentMethodPrimaryResponse, error) {
	if req.GetPaymentMethodId() <= 0 {
		return nil, status.Errorf(codes.InvalidArgument, "invalid payment method id")
	}
	method, err := s.recurring.SetPrimary(ctx, req.GetPaymentMethodId())
	if err != nil {
		return nil, err
	}
	return &paymentsvcpb.SetRecurringPaymentMethodPrimaryResponse{
		RecurringPaymentMethod: method.ToRecurringPaymentMethodModel(),
	}, nil
}

// ListRecurringPaymentMethods 获取用户的所有绑定的支付方式，一般是 Card On File
func (s *paymentService) ListRecurringPaymentMethods(ctx context.Context,
	req *paymentsvcpb.ListRecurringPaymentMethodsRequest) (
	*paymentsvcpb.ListRecurringPaymentMethodsResponse, error,
) {
	return s.recurring.List(ctx, req)
}

func (s *paymentService) GetPaymentSetting(ctx context.Context,
	req *paymentsvcpb.GetPaymentSettingRequest) (*paymentsvcpb.GetPaymentSettingResponse, error) {
	setting, err := s.setting.Get(ctx, req.GetUser())
	if err != nil {
		return nil, err
	}
	return &paymentsvcpb.GetPaymentSettingResponse{
		PaymentSetting: setting,
	}, nil
}

func (s *paymentService) UpdatePaymentSetting(ctx context.Context,
	req *paymentsvcpb.UpdatePaymentSettingRequest) (*paymentsvcpb.UpdatePaymentSettingResponse, error) {
	setting, err := s.setting.Update(ctx, req)
	if err != nil {
		return nil, err
	}
	return &paymentsvcpb.UpdatePaymentSettingResponse{
		PaymentSetting: setting,
	}, nil
}
