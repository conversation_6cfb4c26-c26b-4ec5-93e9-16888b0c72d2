//go:build wireinject
// +build wireinject

package service

import (
	"github.com/google/wire"

	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/event"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/featureflag"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/fee"
	legacypayment "github.com/MoeGolibrary/moego-svc-payment/internal/logic/legacy/payment"
	legacyprocessor "github.com/MoeGolibrary/moego-svc-payment/internal/logic/legacy/processor"
	stripeprocessor "github.com/MoeGolibrary/moego-svc-payment/internal/logic/legacy/processor/stripe"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/mergeclient"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/onboard"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/onboard/adyen"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/ops"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/payment"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/paymentsetting"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/processor"
	adyenprocesser "github.com/MoeGolibrary/moego-svc-payment/internal/logic/processor/adyen"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/recurring"
	adyenrecurring "github.com/MoeGolibrary/moego-svc-payment/internal/logic/recurring/adyen"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/refund"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/splitpayment"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/terminal"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/transaction"
	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/webhook"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/account"
	adyenclient "github.com/MoeGolibrary/moego-svc-payment/internal/repo/adyen"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/business"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/customer"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/db"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/eventbus"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/exceptionlog"
	featureflagrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/featureflag"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/file"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/grooming"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb"
	legacyeventbus "github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/eventbus"
	legacypaydetailrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/paydetail"
	legacypaymentrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/payment"
	legacyrefundrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/refund"
	legacysettingrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/setting"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/stripeaccount"
	legacystripecustomerrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/stripecustomer"
	legacytransactionrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/transaction"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacytx"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/locker"
	mergeclientrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/mergeclient"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/message"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/mq"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/organization"
	paymentrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/payment"
	paymentmethodrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/paymentmethod"
	paymentsettingrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/paymentsetting"
	recurringrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/recurring"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/redis"
	refundrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/refund"
	splitpaymentrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/splitpayment"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/stripe"
	terminalrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/terminal"
	transactionrepo "github.com/MoeGolibrary/moego-svc-payment/internal/repo/transaction"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/tx"
)

func InitServices() *Services {
	wire.Build(newServices,
		// services
		newMergeClientService,
		newOnboardService,
		newPaymentService,
		NewRefundService,
		NewWebhookService,
		NewTaskService,
		NewPaymentOpsService,
		newTerminalService,
		// logic
		payment.NewPayment,
		transaction.NewTransaction,
		mergeclient.NewPayment,
		onboard.New,
		adyen.New,
		processor.NewHandler,
		processor.NewFactory,
		adyenprocesser.NewAdyenProcessor,
		legacyprocessor.NewHandler,
		legacyprocessor.NewFactory,
		stripeprocessor.NewProcessor,
		webhook.New,
		splitpayment.New,
		refund.New,
		featureflag.New,
		event.New,
		recurring.New,
		adyenrecurring.New,
		ops.New,
		legacypayment.NewPayment,
		terminal.New,
		paymentsetting.New,
		fee.New,
		// repo
		mergeclientrepo.NewPaymentRepo,
		account.New,
		db.New,
		organization.New,
		adyenclient.New,
		transactionrepo.New,
		paymentrepo.New,
		paymentrepo.NewClient,
		customer.NewClient,
		grooming.NewClient,
		paymentsettingrepo.New,
		paymentmethodrepo.New,
		splitpaymentrepo.New,
		splitpaymentrepo.NewClient,
		locker.NewDistributedLockerFactory,
		redis.New,
		redis.NewCache,
		tx.New,
		refundrepo.New,
		exceptionlog.New,
		recurringrepo.New,
		eventbus.New,
		legacyeventbus.New,
		mq.NewProducer,
		featureflagrepo.New,
		legacytx.New,
		legacydb.NewLegacyDB,
		legacypaymentrepo.New,
		legacystripecustomerrepo.New,
		legacypaydetailrepo.New,
		legacyrefundrepo.New,
		legacytransactionrepo.New,
		legacysettingrepo.New,
		legacydb.NewMmStripeCustomer,
		legacydb.NewMoeCreditCard,
		stripe.New,
		customer.New,
		stripeaccount.New,
		business.NewClient,
		file.New,
		message.NewClient,
		terminalrepo.New,
	)

	return &Services{}
}
