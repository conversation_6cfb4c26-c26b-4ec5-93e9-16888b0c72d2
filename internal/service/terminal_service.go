package service

import (
	"context"

	paymentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2"

	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/terminal"
)

type terminalService struct {
	terminal *terminal.Terminal

	paymentsvcpb.UnimplementedPaymentTerminalServiceServer
}

func newTerminalService(terminal *terminal.Terminal) *terminalService {
	return &terminalService{
		terminal: terminal,
	}
}

func (s *terminalService) GetTerminal(ctx context.Context,
	req *paymentsvcpb.GetTerminalRequest) (*paymentsvcpb.GetTerminalResponse, error) {
	terminal, err := s.terminal.Get(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	return &paymentsvcpb.GetTerminalResponse{
		Terminal: terminal,
	}, nil
}

func (s *terminalService) ListTerminals(ctx context.Context,
	req *paymentsvcpb.ListTerminalsRequest) (*paymentsvcpb.ListTerminalsResponse, error) {
	terminals, pageRsp, err := s.terminal.List(ctx, req.GetFilter(), req.GetPagination())
	if err != nil {
		return nil, err
	}
	return &paymentsvcpb.ListTerminalsResponse{
		Terminals:  terminals,
		Pagination: pageRsp,
	}, nil
}
