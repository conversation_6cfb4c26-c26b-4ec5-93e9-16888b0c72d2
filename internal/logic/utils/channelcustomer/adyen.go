package channelcustomer

import (
	"fmt"
	"strconv"
	"strings"

	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
)

const DELIMITER = ":" // TODO: consider using a better delimiter

func GetAdyenShopperReference(user *paymentpb.User) string {
	return fmt.Sprintf("%s%s%d", user.GetEntityType(), DELIMITER, user.GetEntityId())
}

func GetUserFromAdyenShopperReference(shopperReference string) (*paymentpb.User, error) {
	const N = 2

	ss := strings.Split(shopperReference, DELIMITER)
	if len(ss) != N {
		return nil, fmt.Errorf("invalid shopper reference: %s", shopperReference)
	}
	entityType, ok := paymentpb.EntityType_value[ss[0]]
	if !ok {
		return nil, fmt.Errorf("invalid entity type: %s", ss[0])
	}
	entityID, err := strconv.ParseInt(ss[1], 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid entity id: %s", ss[1])
	}
	return &paymentpb.User{
		EntityType: paymentpb.EntityType(entityType),
		EntityId:   entityID,
	}, nil
}
