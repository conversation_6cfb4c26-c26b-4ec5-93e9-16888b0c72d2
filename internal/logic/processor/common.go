package processor

import (
	"context"
	"strconv"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"
	paymentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2"

	"github.com/MoeGolibrary/moego-svc-payment/internal/logic/processor/state"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/payment"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/tx"
)

// Book entry 的逻辑是所有渠道共用的
func (h *handler) handleBookEntry(ctx context.Context,
	paymentDOList []*payment.Payment) (*paymentsvcpb.PayPaymentResponse, error) {
	referenceIDList := make([]string, 0, len(paymentDOList))
	for _, paymentDO := range paymentDOList {
		referenceIDList = append(referenceIDList, strconv.FormatInt(paymentDO.ID, 10))
	}

	// 事务更新 payment
	if err := h.txManager.Tx(func(tx tx.PaymentTX) error {
		if err := state.BatchTransitionStatusWithTx(ctx, tx, paymentDOList, []paymentpb.PaymentModel_PaymentStatus{
			paymentpb.PaymentModel_ACCEPTED,
		}, paymentpb.PaymentModel_SUCCEEDED); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return nil, err
	}

	// 更新了状态，异步触发一下事件
	h.event.AsyncBatchSend(ctx, eventbuspb.EventType_PAYMENT_STATUS_CHANGED, referenceIDList)

	return &paymentsvcpb.PayPaymentResponse{}, nil
}

func IsNonChannelPaymentMethod(pm paymentpb.PaymentMethod_MethodType) bool {
	return pm == paymentpb.PaymentMethod_BOOK_ENTRY
}
