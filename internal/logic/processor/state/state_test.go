package state

import (
	"context"
	"errors"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	paymentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2"

	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/eventbus"
	eventbusmock "github.com/MoeGolibrary/moego-svc-payment/internal/repo/eventbus/mock"
	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/payment"
	paymentmock "github.com/MoeGolibrary/moego-svc-payment/internal/repo/payment/mock"
	txmock "github.com/MoeGolibrary/moego-svc-payment/internal/repo/tx/mock"
)

func TestUpdateByStatusWithTx(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockTx := txmock.NewMockPaymentTX(ctrl)
	mockPaymentRW := paymentmock.NewMockReadWriter(ctrl)
	mockEventBusRW := eventbusmock.NewMockReadWriter(ctrl)

	ctx := context.Background()
	record := &payment.Payment{
		ID:     12345,
		Status: paymentpb.PaymentModel_ACCEPTED,
	}
	srcStatuses := []paymentpb.PaymentModel_PaymentStatus{
		paymentpb.PaymentModel_CREATED,
	}

	tests := []struct {
		name        string
		setupMocks  func()
		expectError bool
	}{
		{
			name: "success",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().UpdateByStatus(ctx, record, srcStatuses).Return(nil)
				mockTx.EXPECT().EventBusRW().Return(mockEventBusRW)
				mockEventBusRW.EXPECT().Insert(ctx, gomock.Any()).Return(nil)
			},
			expectError: false,
		},
		{
			name: "payment update error",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().UpdateByStatus(ctx, record, srcStatuses).
					Return(errors.New("payment update error"))
			},
			expectError: true,
		},
		{
			name: "event insert error",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().UpdateByStatus(ctx, record, srcStatuses).Return(nil)
				mockTx.EXPECT().EventBusRW().Return(mockEventBusRW)
				mockEventBusRW.EXPECT().Insert(ctx, gomock.Any()).
					Return(errors.New("event insert error"))
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			err := UpdateByStatusWithTx(ctx, mockTx, record, srcStatuses)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGetPaymentIDListAndEventList(t *testing.T) {
	records := []*payment.Payment{
		{
			ID:     123,
			Status: paymentpb.PaymentModel_ACCEPTED,
		},
		{
			ID:     456,
			Status: paymentpb.PaymentModel_ACCEPTED,
		},
		{
			ID:     789,
			Status: paymentpb.PaymentModel_ACCEPTED,
		},
	}

	paymentIDList, eventList := getPaymentIDListAndEventList(records)

	// Check payment ID list
	assert.Len(t, paymentIDList, 3)
	assert.Equal(t, int64(123), paymentIDList[0])
	assert.Equal(t, int64(456), paymentIDList[1])
	assert.Equal(t, int64(789), paymentIDList[2])

	// Check event list
	assert.Len(t, eventList, 3)

	for i, event := range eventList {
		assert.Equal(t, eventbuspb.EventType_PAYMENT_STATUS_CHANGED, event.MessageType)
		assert.Equal(t, strconv.FormatInt(records[i].ID, 10), event.ReferenceID)
		assert.Equal(t, eventbus.StatusPending, event.Status)
		assert.Equal(t, 0, event.RetryTimes)
		assert.NotNil(t, event.Payload)
		assert.NotNil(t, event.Payload.GetPaymentEvent())
		assert.NotNil(t, event.Payload.GetPaymentEvent().Payment)
	}

	// Test empty records
	emptyPaymentIDList, emptyEventList := getPaymentIDListAndEventList([]*payment.Payment{})
	assert.Len(t, emptyPaymentIDList, 0)
	assert.Len(t, emptyEventList, 0)
}

func TestBatchTransitToAcceptedWithTx(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockTx := txmock.NewMockPaymentTX(ctrl)
	mockPaymentRW := paymentmock.NewMockReadWriter(ctrl)
	mockEventBusRW := eventbusmock.NewMockReadWriter(ctrl)

	ctx := context.Background()
	records := []*payment.Payment{
		{ID: 123, Status: paymentpb.PaymentModel_ACCEPTED},
		{ID: 456, Status: paymentpb.PaymentModel_ACCEPTED},
	}
	srcStatusList := []paymentpb.PaymentModel_PaymentStatus{
		paymentpb.PaymentModel_CREATED,
		paymentpb.PaymentModel_ACCEPTED,
	}

	tests := []struct {
		name        string
		setupMocks  func()
		expectError bool
	}{
		{
			name: "success",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitToAccepted(ctx, records, srcStatusList).Return(nil)
				mockTx.EXPECT().EventBusRW().Return(mockEventBusRW)
				mockEventBusRW.EXPECT().BatchInsert(ctx, gomock.Any()).Return(nil)
			},
			expectError: false,
		},
		{
			name: "payment batch transit error",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitToAccepted(ctx, records, srcStatusList).
					Return(errors.New("batch transit error"))
			},
			expectError: true,
		},
		{
			name: "event batch insert error",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitToAccepted(ctx, records, srcStatusList).Return(nil)
				mockTx.EXPECT().EventBusRW().Return(mockEventBusRW)
				mockEventBusRW.EXPECT().BatchInsert(ctx, gomock.Any()).
					Return(errors.New("batch insert error"))
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			err := BatchTransitToAcceptedWithTx(ctx, mockTx, records, srcStatusList)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBatchTransitToSubmitWithTx(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockTx := txmock.NewMockPaymentTX(ctrl)
	mockPaymentRW := paymentmock.NewMockReadWriter(ctrl)
	mockEventBusRW := eventbusmock.NewMockReadWriter(ctrl)

	ctx := context.Background()
	records := []*payment.Payment{
		{ID: 123, Status: paymentpb.PaymentModel_ACCEPTED},
		{ID: 456, Status: paymentpb.PaymentModel_ACCEPTED},
	}
	srcStatusList := []paymentpb.PaymentModel_PaymentStatus{
		paymentpb.PaymentModel_CREATED,
		paymentpb.PaymentModel_ACCEPTED,
	}

	tests := []struct {
		name        string
		setupMocks  func()
		expectError bool
	}{
		{
			name: "success",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitToSubmit(ctx, records, srcStatusList).Return(nil)
				mockTx.EXPECT().EventBusRW().Return(mockEventBusRW)
				mockEventBusRW.EXPECT().BatchInsert(ctx, gomock.Any()).Return(nil)
			},
			expectError: false,
		},
		{
			name: "payment batch transit error",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitToSubmit(ctx, records, srcStatusList).
					Return(errors.New("batch transit error"))
			},
			expectError: true,
		},
		{
			name: "event batch insert error",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitToSubmit(ctx, records, srcStatusList).Return(nil)
				mockTx.EXPECT().EventBusRW().Return(mockEventBusRW)
				mockEventBusRW.EXPECT().BatchInsert(ctx, gomock.Any()).
					Return(errors.New("batch insert error"))
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			err := BatchTransitToSubmitWithTx(ctx, mockTx, records, srcStatusList)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBatchTransitionStatusWithTx(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockTx := txmock.NewMockPaymentTX(ctrl)
	mockPaymentRW := paymentmock.NewMockReadWriter(ctrl)
	mockEventBusRW := eventbusmock.NewMockReadWriter(ctrl)

	ctx := context.Background()
	records := []*payment.Payment{
		{ID: 123, Status: paymentpb.PaymentModel_ACCEPTED},
		{ID: 456, Status: paymentpb.PaymentModel_ACCEPTED},
	}
	srcStatusList := []paymentpb.PaymentModel_PaymentStatus{
		paymentpb.PaymentModel_CREATED,
		paymentpb.PaymentModel_ACCEPTED,
	}
	targetStatus := paymentpb.PaymentModel_ACCEPTED

	tests := []struct {
		name        string
		setupMocks  func()
		expectError bool
	}{
		{
			name: "success",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitionStatus(ctx, []int64{123, 456}, srcStatusList, targetStatus).Return(nil)
				mockTx.EXPECT().EventBusRW().Return(mockEventBusRW)
				mockEventBusRW.EXPECT().BatchInsert(ctx, gomock.Any()).Return(nil)
			},
			expectError: false,
		},
		{
			name: "payment batch transition error",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitionStatus(ctx, []int64{123, 456}, srcStatusList, targetStatus).
					Return(errors.New("batch transition error"))
			},
			expectError: true,
		},
		{
			name: "event batch insert error",
			setupMocks: func() {
				mockTx.EXPECT().PaymentRW().Return(mockPaymentRW)
				mockPaymentRW.EXPECT().BatchTransitionStatus(ctx, []int64{123, 456}, srcStatusList, targetStatus).Return(nil)
				mockTx.EXPECT().EventBusRW().Return(mockEventBusRW)
				mockEventBusRW.EXPECT().BatchInsert(ctx, gomock.Any()).
					Return(errors.New("batch insert error"))
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			err := BatchTransitionStatusWithTx(ctx, mockTx, records, srcStatusList, targetStatus)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
