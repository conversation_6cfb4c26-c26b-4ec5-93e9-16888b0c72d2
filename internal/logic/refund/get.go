package refund

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/merror"
	"github.com/MoeGolibrary/go-lib/zlog"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	paymentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/payment/v2"
)

func (r *Refund) GetRefund(ctx context.Context,
	req *paymentsvcpb.GetRefundRequest) (*paymentsvcpb.GetRefundResponse, error) {
	if IsLegacyRefund(req.GetId()) {
		model, err := r.paymentClient.GetRefund(ctx, req.GetId())
		if err != nil {
			zlog.Error(ctx, "select refund by id from server payment error",
				zap.Int64("refundID", req.GetId()), zap.Error(err))
			return nil, merror.NewBizErrorWithStack(errorspb.Code_CODE_PAYMENT_RPC_CALL_ERROR,
				fmt.Sprintf("select refund by id from server payment error, %s", err.Error()))
		}
		return &paymentsvcpb.GetRefundResponse{
			Refund: model,
		}, nil
	}

	refundDO, err := r.refund.GetByID(ctx, req.GetId())
	if err != nil {
		zlog.Error(ctx, "select refund by id error", zap.Int64("refundID", req.GetId()), zap.Error(err))
		return nil, merror.NewBizErrorWithStack(errorspb.Code_CODE_PAYMENT_DATA_ACCESS_ERROR,
			fmt.Sprintf("select refund by id error, %s", err.Error()))
	}
	return &paymentsvcpb.GetRefundResponse{
		Refund: refundDO.ToRefundModel(),
	}, nil
}
