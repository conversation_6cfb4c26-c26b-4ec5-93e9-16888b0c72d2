package stripecustomer

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb"
)

//go:generate mockgen -destination=./mock/stripecustomer_mock.go -package=mock github.com/MoeGolibrary/moego-svc-payment/internal/repo/legacydb/stripecustomer ReadWriter
type ReadWriter interface {
	SelectByCustomerID(ctx context.Context, customerID int64) (*StripeCustomer, error)
}

type impl struct {
	db *gorm.DB
}

func New(db legacydb.LegacyDB) ReadWriter {
	return &impl{
		db: db.DB,
	}
}

func (i *impl) SelectByCustomerID(ctx context.Context, customerID int64) (*StripeCustomer, error) {
	stripeCustomer := &StripeCustomer{}
	if err := i.db.WithContext(ctx).Model(&StripeCustomer{}).Where(
		"customer_id = ?", customerID,
	).First(stripeCustomer).Error; err != nil {
		return nil, err
	}

	return stripeCustomer, nil
}
