/*
Terminal API

Terminal API

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package terminal

import (
	"encoding/json"
)

// checks if the TransactionStatusResponseResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &TransactionStatusResponseResponse{}

// TransactionStatusResponseResponse struct for TransactionStatusResponseResponse
type TransactionStatusResponseResponse struct {
	Result *string `json:"Result,omitempty"`
	ErrorCondition *string `json:"ErrorCondition,omitempty"`
	AdditionalResponse *string `json:"AdditionalResponse,omitempty"`
}

// NewTransactionStatusResponseResponse instantiates a new TransactionStatusResponseResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTransactionStatusResponseResponse() *TransactionStatusResponseResponse {
	this := TransactionStatusResponseResponse{}
	return &this
}

// NewTransactionStatusResponseResponseWithDefaults instantiates a new TransactionStatusResponseResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTransactionStatusResponseResponseWithDefaults() *TransactionStatusResponseResponse {
	this := TransactionStatusResponseResponse{}
	return &this
}

// GetResult returns the Result field value if set, zero value otherwise.
func (o *TransactionStatusResponseResponse) GetResult() string {
	if o == nil || IsNil(o.Result) {
		var ret string
		return ret
	}
	return *o.Result
}

// GetResultOk returns a tuple with the Result field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TransactionStatusResponseResponse) GetResultOk() (*string, bool) {
	if o == nil || IsNil(o.Result) {
		return nil, false
	}
	return o.Result, true
}

// HasResult returns a boolean if a field has been set.
func (o *TransactionStatusResponseResponse) HasResult() bool {
	if o != nil && !IsNil(o.Result) {
		return true
	}

	return false
}

// SetResult gets a reference to the given string and assigns it to the Result field.
func (o *TransactionStatusResponseResponse) SetResult(v string) {
	o.Result = &v
}

// GetErrorCondition returns the ErrorCondition field value if set, zero value otherwise.
func (o *TransactionStatusResponseResponse) GetErrorCondition() string {
	if o == nil || IsNil(o.ErrorCondition) {
		var ret string
		return ret
	}
	return *o.ErrorCondition
}

// GetErrorConditionOk returns a tuple with the ErrorCondition field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TransactionStatusResponseResponse) GetErrorConditionOk() (*string, bool) {
	if o == nil || IsNil(o.ErrorCondition) {
		return nil, false
	}
	return o.ErrorCondition, true
}

// HasErrorCondition returns a boolean if a field has been set.
func (o *TransactionStatusResponseResponse) HasErrorCondition() bool {
	if o != nil && !IsNil(o.ErrorCondition) {
		return true
	}

	return false
}

// SetErrorCondition gets a reference to the given string and assigns it to the ErrorCondition field.
func (o *TransactionStatusResponseResponse) SetErrorCondition(v string) {
	o.ErrorCondition = &v
}

// GetAdditionalResponse returns the AdditionalResponse field value if set, zero value otherwise.
func (o *TransactionStatusResponseResponse) GetAdditionalResponse() string {
	if o == nil || IsNil(o.AdditionalResponse) {
		var ret string
		return ret
	}
	return *o.AdditionalResponse
}

// GetAdditionalResponseOk returns a tuple with the AdditionalResponse field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TransactionStatusResponseResponse) GetAdditionalResponseOk() (*string, bool) {
	if o == nil || IsNil(o.AdditionalResponse) {
		return nil, false
	}
	return o.AdditionalResponse, true
}

// HasAdditionalResponse returns a boolean if a field has been set.
func (o *TransactionStatusResponseResponse) HasAdditionalResponse() bool {
	if o != nil && !IsNil(o.AdditionalResponse) {
		return true
	}

	return false
}

// SetAdditionalResponse gets a reference to the given string and assigns it to the AdditionalResponse field.
func (o *TransactionStatusResponseResponse) SetAdditionalResponse(v string) {
	o.AdditionalResponse = &v
}

func (o TransactionStatusResponseResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o TransactionStatusResponseResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Result) {
		toSerialize["Result"] = o.Result
	}
	if !IsNil(o.ErrorCondition) {
		toSerialize["ErrorCondition"] = o.ErrorCondition
	}
	if !IsNil(o.AdditionalResponse) {
		toSerialize["AdditionalResponse"] = o.AdditionalResponse
	}
	return toSerialize, nil
}

type NullableTransactionStatusResponseResponse struct {
	value *TransactionStatusResponseResponse
	isSet bool
}

func (v NullableTransactionStatusResponseResponse) Get() *TransactionStatusResponseResponse {
	return v.value
}

func (v *NullableTransactionStatusResponseResponse) Set(val *TransactionStatusResponseResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableTransactionStatusResponseResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableTransactionStatusResponseResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTransactionStatusResponseResponse(val *TransactionStatusResponseResponse) *NullableTransactionStatusResponseResponse {
	return &NullableTransactionStatusResponseResponse{value: val, isSet: true}
}

func (v NullableTransactionStatusResponseResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTransactionStatusResponseResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


