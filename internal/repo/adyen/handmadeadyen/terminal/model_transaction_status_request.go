/*
Terminal API

Terminal API

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package terminal

import (
	"encoding/json"
)

// checks if the TransactionStatusRequest type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &TransactionStatusRequest{}

// TransactionStatusRequest struct for TransactionStatusRequest
type TransactionStatusRequest struct {
	ReceiptReprintFlag *bool `json:"ReceiptReprintFlag,omitempty"`
	DocumentQualifier *string `json:"DocumentQualifier,omitempty"`
	MessageReference *MessageReference `json:"MessageReference,omitempty"`
}

// NewTransactionStatusRequest instantiates a new TransactionStatusRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewTransactionStatusRequest() *TransactionStatusRequest {
	this := TransactionStatusRequest{}
	return &this
}

// NewTransactionStatusRequestWithDefaults instantiates a new TransactionStatusRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewTransactionStatusRequestWithDefaults() *TransactionStatusRequest {
	this := TransactionStatusRequest{}
	return &this
}

// GetReceiptReprintFlag returns the ReceiptReprintFlag field value if set, zero value otherwise.
func (o *TransactionStatusRequest) GetReceiptReprintFlag() bool {
	if o == nil || IsNil(o.ReceiptReprintFlag) {
		var ret bool
		return ret
	}
	return *o.ReceiptReprintFlag
}

// GetReceiptReprintFlagOk returns a tuple with the ReceiptReprintFlag field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TransactionStatusRequest) GetReceiptReprintFlagOk() (*bool, bool) {
	if o == nil || IsNil(o.ReceiptReprintFlag) {
		return nil, false
	}
	return o.ReceiptReprintFlag, true
}

// HasReceiptReprintFlag returns a boolean if a field has been set.
func (o *TransactionStatusRequest) HasReceiptReprintFlag() bool {
	if o != nil && !IsNil(o.ReceiptReprintFlag) {
		return true
	}

	return false
}

// SetReceiptReprintFlag gets a reference to the given bool and assigns it to the ReceiptReprintFlag field.
func (o *TransactionStatusRequest) SetReceiptReprintFlag(v bool) {
	o.ReceiptReprintFlag = &v
}

// GetDocumentQualifier returns the DocumentQualifier field value if set, zero value otherwise.
func (o *TransactionStatusRequest) GetDocumentQualifier() string {
	if o == nil || IsNil(o.DocumentQualifier) {
		var ret string
		return ret
	}
	return *o.DocumentQualifier
}

// GetDocumentQualifierOk returns a tuple with the DocumentQualifier field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TransactionStatusRequest) GetDocumentQualifierOk() (*string, bool) {
	if o == nil || IsNil(o.DocumentQualifier) {
		return nil, false
	}
	return o.DocumentQualifier, true
}

// HasDocumentQualifier returns a boolean if a field has been set.
func (o *TransactionStatusRequest) HasDocumentQualifier() bool {
	if o != nil && !IsNil(o.DocumentQualifier) {
		return true
	}

	return false
}

// SetDocumentQualifier gets a reference to the given string and assigns it to the DocumentQualifier field.
func (o *TransactionStatusRequest) SetDocumentQualifier(v string) {
	o.DocumentQualifier = &v
}

// GetMessageReference returns the MessageReference field value if set, zero value otherwise.
func (o *TransactionStatusRequest) GetMessageReference() MessageReference {
	if o == nil || IsNil(o.MessageReference) {
		var ret MessageReference
		return ret
	}
	return *o.MessageReference
}

// GetMessageReferenceOk returns a tuple with the MessageReference field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *TransactionStatusRequest) GetMessageReferenceOk() (*MessageReference, bool) {
	if o == nil || IsNil(o.MessageReference) {
		return nil, false
	}
	return o.MessageReference, true
}

// HasMessageReference returns a boolean if a field has been set.
func (o *TransactionStatusRequest) HasMessageReference() bool {
	if o != nil && !IsNil(o.MessageReference) {
		return true
	}

	return false
}

// SetMessageReference gets a reference to the given MessageReference and assigns it to the MessageReference field.
func (o *TransactionStatusRequest) SetMessageReference(v MessageReference) {
	o.MessageReference = &v
}

func (o TransactionStatusRequest) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o TransactionStatusRequest) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.ReceiptReprintFlag) {
		toSerialize["ReceiptReprintFlag"] = o.ReceiptReprintFlag
	}
	if !IsNil(o.DocumentQualifier) {
		toSerialize["DocumentQualifier"] = o.DocumentQualifier
	}
	if !IsNil(o.MessageReference) {
		toSerialize["MessageReference"] = o.MessageReference
	}
	return toSerialize, nil
}

type NullableTransactionStatusRequest struct {
	value *TransactionStatusRequest
	isSet bool
}

func (v NullableTransactionStatusRequest) Get() *TransactionStatusRequest {
	return v.value
}

func (v *NullableTransactionStatusRequest) Set(val *TransactionStatusRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableTransactionStatusRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableTransactionStatusRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableTransactionStatusRequest(val *TransactionStatusRequest) *NullableTransactionStatusRequest {
	return &NullableTransactionStatusRequest{value: val, isSet: true}
}

func (v NullableTransactionStatusRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableTransactionStatusRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


