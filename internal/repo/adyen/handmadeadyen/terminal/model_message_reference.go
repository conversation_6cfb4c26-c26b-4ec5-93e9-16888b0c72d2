/*
Terminal API

Terminal API

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package terminal

import (
	"encoding/json"
)

// checks if the MessageReference type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &MessageReference{}

// MessageReference struct for MessageReference
type MessageReference struct {
	SaleID *string `json:"SaleID,omitempty"`
	ServiceID *string `json:"ServiceID,omitempty"`
	MessageCategory *string `json:"MessageCategory,omitempty"`
}

// NewMessageReference instantiates a new MessageReference object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewMessageReference() *MessageReference {
	this := MessageReference{}
	return &this
}

// NewMessageReferenceWithDefaults instantiates a new MessageReference object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewMessageReferenceWithDefaults() *MessageReference {
	this := MessageReference{}
	return &this
}

// GetSaleID returns the SaleID field value if set, zero value otherwise.
func (o *MessageReference) GetSaleID() string {
	if o == nil || IsNil(o.SaleID) {
		var ret string
		return ret
	}
	return *o.SaleID
}

// GetSaleIDOk returns a tuple with the SaleID field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *MessageReference) GetSaleIDOk() (*string, bool) {
	if o == nil || IsNil(o.SaleID) {
		return nil, false
	}
	return o.SaleID, true
}

// HasSaleID returns a boolean if a field has been set.
func (o *MessageReference) HasSaleID() bool {
	if o != nil && !IsNil(o.SaleID) {
		return true
	}

	return false
}

// SetSaleID gets a reference to the given string and assigns it to the SaleID field.
func (o *MessageReference) SetSaleID(v string) {
	o.SaleID = &v
}

// GetServiceID returns the ServiceID field value if set, zero value otherwise.
func (o *MessageReference) GetServiceID() string {
	if o == nil || IsNil(o.ServiceID) {
		var ret string
		return ret
	}
	return *o.ServiceID
}

// GetServiceIDOk returns a tuple with the ServiceID field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *MessageReference) GetServiceIDOk() (*string, bool) {
	if o == nil || IsNil(o.ServiceID) {
		return nil, false
	}
	return o.ServiceID, true
}

// HasServiceID returns a boolean if a field has been set.
func (o *MessageReference) HasServiceID() bool {
	if o != nil && !IsNil(o.ServiceID) {
		return true
	}

	return false
}

// SetServiceID gets a reference to the given string and assigns it to the ServiceID field.
func (o *MessageReference) SetServiceID(v string) {
	o.ServiceID = &v
}

// GetMessageCategory returns the MessageCategory field value if set, zero value otherwise.
func (o *MessageReference) GetMessageCategory() string {
	if o == nil || IsNil(o.MessageCategory) {
		var ret string
		return ret
	}
	return *o.MessageCategory
}

// GetMessageCategoryOk returns a tuple with the MessageCategory field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *MessageReference) GetMessageCategoryOk() (*string, bool) {
	if o == nil || IsNil(o.MessageCategory) {
		return nil, false
	}
	return o.MessageCategory, true
}

// HasMessageCategory returns a boolean if a field has been set.
func (o *MessageReference) HasMessageCategory() bool {
	if o != nil && !IsNil(o.MessageCategory) {
		return true
	}

	return false
}

// SetMessageCategory gets a reference to the given string and assigns it to the MessageCategory field.
func (o *MessageReference) SetMessageCategory(v string) {
	o.MessageCategory = &v
}

func (o MessageReference) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o MessageReference) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.SaleID) {
		toSerialize["SaleID"] = o.SaleID
	}
	if !IsNil(o.ServiceID) {
		toSerialize["ServiceID"] = o.ServiceID
	}
	if !IsNil(o.MessageCategory) {
		toSerialize["MessageCategory"] = o.MessageCategory
	}
	return toSerialize, nil
}

type NullableMessageReference struct {
	value *MessageReference
	isSet bool
}

func (v NullableMessageReference) Get() *MessageReference {
	return v.value
}

func (v *NullableMessageReference) Set(val *MessageReference) {
	v.value = val
	v.isSet = true
}

func (v NullableMessageReference) IsSet() bool {
	return v.isSet
}

func (v *NullableMessageReference) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableMessageReference(val *MessageReference) *NullableMessageReference {
	return &NullableMessageReference{value: val, isSet: true}
}

func (v NullableMessageReference) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableMessageReference) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


