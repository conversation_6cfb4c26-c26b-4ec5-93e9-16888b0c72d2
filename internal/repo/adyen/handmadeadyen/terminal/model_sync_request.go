/*
Terminal API

Terminal API

API version: 1.0.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package terminal

import (
	"encoding/json"
)

// checks if the SyncRequest type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &SyncRequest{}

// SyncRequest struct for SyncRequest
type SyncRequest struct {
	SaleToPOIRequest *SaleToPOIRequest `json:"SaleToPOIRequest,omitempty"`
}

// NewSyncRequest instantiates a new SyncRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewSyncRequest() *SyncRequest {
	this := SyncRequest{}
	return &this
}

// NewSyncRequestWithDefaults instantiates a new SyncRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewSyncRequestWithDefaults() *SyncRequest {
	this := SyncRequest{}
	return &this
}

// GetSaleToPOIRequest returns the SaleToPOIRequest field value if set, zero value otherwise.
func (o *SyncRequest) GetSaleToPOIRequest() SaleToPOIRequest {
	if o == nil || IsNil(o.SaleToPOIRequest) {
		var ret SaleToPOIRequest
		return ret
	}
	return *o.SaleToPOIRequest
}

// GetSaleToPOIRequestOk returns a tuple with the SaleToPOIRequest field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *SyncRequest) GetSaleToPOIRequestOk() (*SaleToPOIRequest, bool) {
	if o == nil || IsNil(o.SaleToPOIRequest) {
		return nil, false
	}
	return o.SaleToPOIRequest, true
}

// HasSaleToPOIRequest returns a boolean if a field has been set.
func (o *SyncRequest) HasSaleToPOIRequest() bool {
	if o != nil && !IsNil(o.SaleToPOIRequest) {
		return true
	}

	return false
}

// SetSaleToPOIRequest gets a reference to the given SaleToPOIRequest and assigns it to the SaleToPOIRequest field.
func (o *SyncRequest) SetSaleToPOIRequest(v SaleToPOIRequest) {
	o.SaleToPOIRequest = &v
}

func (o SyncRequest) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o SyncRequest) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.SaleToPOIRequest) {
		toSerialize["SaleToPOIRequest"] = o.SaleToPOIRequest
	}
	return toSerialize, nil
}

type NullableSyncRequest struct {
	value *SyncRequest
	isSet bool
}

func (v NullableSyncRequest) Get() *SyncRequest {
	return v.value
}

func (v *NullableSyncRequest) Set(val *SyncRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableSyncRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableSyncRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableSyncRequest(val *SyncRequest) *NullableSyncRequest {
	return &NullableSyncRequest{value: val, isSet: true}
}

func (v NullableSyncRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableSyncRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


