package com.moego.server.grooming.params;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.lib.common.util.JsonUtil;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2021/1/13 11:18 AM
 */
public class BookOnlineSubmitParamsTest {

    String params = "{\n" + "    \"customerData\": {\n"
            + "        \"firstName\": \"ritchie3\",\n"
            + "        \"lastName\": \"liu3\",\n"
            + "        \"phoneNumber\": \"1101201897\",\n"
            + "        \"email\": \"<EMAIL>\",\n"
            + "        \"custom_1445\": \"Perfect\",\n"
            + "        \"custom_1447\": \"\",\n"
            + "        \"address\": \"\",\n"
            + "        \"lng\": \"\",\n"
            + "        \"lat\": \"\",\n"
            + "        \"charge_token\": \"\",\n"
            + "        \"customerId\":10000219,\n"
            + "        \"birthday\":\"2020-02-22T02:22:22\"\n"
            + "    },\n"
            + "    \"petData\": [\n"
            + "        {\n"
            + "            \"isSelected\": true,\n"
            + "            \"serviceId\": \"1000193\",\n"
            + "            \"addOnIds\": [],\n"
            + "            \"breed\": \"Miniature Littlefield Sheepdog\",\n"
            + "            \"avatarPath\": \"https://moego.s3-us-west-2.amazonaws.com/Public/Uploads/BookOnlinePetPhoto/20201203/5fc995a7634e0.jpg\",\n"
            + "            \"birthday\": null,\n"
            + "            \"gender\": 1,\n"
            + "            \"vaccineList\": [{\"vaccineId\":123,\"expirationDate\":\"2020-12-12\"}],\n"
            + "            \"weight\": \"\",\n"
            + "            \"petName\": \"mikey\",\n"
            + "            \"petTypeId\": 1,\n"
            + "            \"vetName\": \"007x\",\n"
            + "            \"healthIssues\": \"\",\n"
            + "            \"behavior\": \"\",\n"
            + "            \"hairLength\": \"\",\n"
            + "            \"vetPhone\": \"\",\n"
            + "            \"vetAddress\": \"\",\n"
            + "            \"custom_1443\": \"noodle\",\n"
            + "            \"custom_221\": \"one year\"\n"
            + "        }\n"
            + "    ],\n"
            + "    \"appointmentDate\": \"2021-01-09\",\n"
            + "    \"appointmentStartTime\": 810,\n"
            + "    \"staffId\": \"5561\",\n"
            + "    \"isAgreePolicy\": \"0\",\n"
            + "    \"note\": \"test 123 \",\n"
            + "    \"agreementConfirmed\": \"1\",\n"
            + "    \"signature\": \"img_signature\",\n"
            + "    \"agreements\":[{}]\n"
            + "}";

    @Test
    public void checkOBParamStruct() {
        BookOnlineSubmitParams test = JsonUtil.toBean(params, BookOnlineSubmitParams.class);
        System.out.println(JsonUtil.toJson(test));
        assertEquals(
                123,
                test.getPetData().get(0).getVaccineList().get(0).getVaccineId().intValue());
    }
}
