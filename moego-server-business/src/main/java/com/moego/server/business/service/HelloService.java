package com.moego.server.business.service;

import com.moego.lib.common.observability.metrics.prometheus.timer.TimerGroup;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetrics;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Service;

@Service
public class HelloService {

    private static final Random random = new Random();

    @TimerMetrics(group = TimerGroup.DEFAULT)
    public String sayHello(String name) throws InterruptedException {
        // random sleep no more than 3000 ms
        TimeUnit.MILLISECONDS.sleep(random.nextInt(3000));
        return String.format("Hello %s!", name);
    }
}
