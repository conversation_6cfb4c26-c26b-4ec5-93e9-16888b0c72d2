<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeVanSmartScheduleRelationMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeVanSmartScheduleRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="van_id" jdbcType="INTEGER" property="vanId" />
    <result column="driving_rule_id" jdbcType="INTEGER" property="drivingRuleId" />
    <result column="location_id" jdbcType="INTEGER" property="locationId" />
    <result column="created_by" jdbcType="INTEGER" property="createdBy" />
    <result column="updated_by" jdbcType="INTEGER" property="updatedBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, van_id, driving_rule_id, location_id, created_by, updated_by, created_at, 
    updated_at, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_van_smart_schedule_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_van_smart_schedule_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeVanSmartScheduleRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_van_smart_schedule_relation (business_id, van_id, driving_rule_id, 
      location_id, created_by, updated_by, 
      created_at, updated_at, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{vanId,jdbcType=INTEGER}, #{drivingRuleId,jdbcType=INTEGER}, 
      #{locationId,jdbcType=INTEGER}, #{createdBy,jdbcType=INTEGER}, #{updatedBy,jdbcType=INTEGER}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeVanSmartScheduleRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_van_smart_schedule_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="vanId != null">
        van_id,
      </if>
      <if test="drivingRuleId != null">
        driving_rule_id,
      </if>
      <if test="locationId != null">
        location_id,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="vanId != null">
        #{vanId,jdbcType=INTEGER},
      </if>
      <if test="drivingRuleId != null">
        #{drivingRuleId,jdbcType=INTEGER},
      </if>
      <if test="locationId != null">
        #{locationId,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeVanSmartScheduleRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_van_smart_schedule_relation
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="vanId != null">
        van_id = #{vanId,jdbcType=INTEGER},
      </if>
      <if test="drivingRuleId != null">
        driving_rule_id = #{drivingRuleId,jdbcType=INTEGER},
      </if>
      <if test="locationId != null">
        location_id = #{locationId,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeVanSmartScheduleRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_van_smart_schedule_relation
    set business_id = #{businessId,jdbcType=INTEGER},
      van_id = #{vanId,jdbcType=INTEGER},
      driving_rule_id = #{drivingRuleId,jdbcType=INTEGER},
      location_id = #{locationId,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=INTEGER},
      updated_by = #{updatedBy,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByVanIdAndBusinessId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_van_smart_schedule_relation
    where van_id = #{vanId,jdbcType=INTEGER}
    and business_id = #{businessId,jdbcType=INTEGER}
  </select>

  <select id="selectByVanIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_van_smart_schedule_relation
    where van_id in
    <foreach close=")" collection="vanIdList" item="vanId" open="(" separator=",">
      #{vanId,jdbcType=INTEGER}
    </foreach>
    and business_id = #{businessId,jdbcType=INTEGER}
  </select>

  <select id="selectByDrivingRuleIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_van_smart_schedule_relation
    where business_id = #{businessId,jdbcType=INTEGER}
    and driving_rule_id in
    <foreach close=")" collection="drivingRuleIdList" item="drivingRuleId" open="(" separator=",">
      #{drivingRuleId,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="selectByLocationIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_van_smart_schedule_relation
    where business_id = #{businessId,jdbcType=INTEGER}
    and location_id in
    <foreach close=")" collection="locationIdList" item="locationId" open="(" separator=",">
      #{locationId,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="selectByDrivingRuleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_van_smart_schedule_relation
    where business_id = #{businessId,jdbcType=INTEGER}
    and driving_rule_id = #{drivingRuleId,jdbcType=INTEGER}
  </select>

  <select id="selectByLocationId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_van_smart_schedule_relation
    where business_id = #{businessId,jdbcType=INTEGER}
    and location_id = #{locationId,jdbcType=INTEGER}
  </select>

  <insert id="batchInsertForInitialize" keyProperty="id" parameterType="com.moego.server.business.mapperbean.MoeVanSmartScheduleRelation" useGeneratedKeys="true">
    insert into moe_van_smart_schedule_relation
        (company_id,business_id, van_id, driving_rule_id, location_id, created_by)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (#{item.companyId}, #{item.businessId}, #{item.vanId}, #{item.drivingRuleId}, #{item.locationId}, #{item.createdBy})
    </foreach>
    AS new
    on duplicate key update
    driving_rule_id = new.driving_rule_id,
    location_id = new.location_id,
    updated_at = now()
  </insert>

  <update id="batchUpdateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeVanSmartScheduleRelation">
    <foreach collection="list" index="index" item="item" separator=";">
      update moe_van_smart_schedule_relation
      <set>
        <if test="item.businessId != null">
          business_id = #{item.businessId,jdbcType=INTEGER},
        </if>
        <if test="item.vanId != null">
          van_id = #{item.vanId,jdbcType=INTEGER},
        </if>
        <if test="item.drivingRuleId != null">
          driving_rule_id = #{item.drivingRuleId,jdbcType=INTEGER},
        </if>
        <if test="item.locationId != null">
          location_id = #{item.locationId,jdbcType=INTEGER},
        </if>
        <if test="item.createdBy != null">
          created_by = #{item.createdBy,jdbcType=INTEGER},
        </if>
        <if test="item.updatedBy != null">
          updated_by = #{item.updatedBy,jdbcType=INTEGER},
        </if>
        <if test="item.createdAt != null">
          created_at = #{item.createdAt,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updatedAt != null">
          updated_at = #{item.updatedAt,jdbcType=TIMESTAMP},
        </if>
      </set>
    where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>

</mapper>