package com.moego.server.business.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.business.mapperbean.MoeBusinessCloseDate;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeBusinessCloseDateMapper extends DynamicDataSource<MoeBusinessCloseDateMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int insert(MoeBusinessCloseDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessCloseDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    MoeBusinessCloseDate selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessCloseDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBusinessCloseDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_close_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessCloseDate record);

    /**
     * 包含closeDate和holiday close日期
     * @param businessId
     * @return
     */
    List<MoeBusinessCloseDate> selectAllCloseDateByBusinessId(@Param("businessId") Integer businessId);
    /**
     * 只包含closeDate
     * @param businessId
     * @return
     */
    List<MoeBusinessCloseDate> selectCloseDateByBusinessId(@Param("businessId") Integer businessId);

    List<MoeBusinessCloseDate> selectCloseDateByStartDateEndDate(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<MoeBusinessCloseDate> queryHolidayByBusinessIdYear(
            @Param("businessId") Integer businessId, @Param("year") String year);

    List<MoeBusinessCloseDate> queryHolidayByStartEndDate(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    int openHolidayByStartEndDate(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("updateTime") Long updateTime);

    int openHolidayByYear(
            @Param("businessId") Integer businessId, @Param("year") String year, @Param("updateTime") Long updateTime);

    List<MoeBusinessCloseDate> selectHolidayByBusinessId(@Param("businessId") Integer businessId);
}
