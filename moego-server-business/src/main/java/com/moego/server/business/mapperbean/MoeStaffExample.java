package com.moego.server.business.mapperbean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class MoeStaffExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public MoeStaffExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Integer value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Integer value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Integer value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Integer value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Integer> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Integer> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andRoleIdIsNull() {
            addCriterion("role_id is null");
            return (Criteria) this;
        }

        public Criteria andRoleIdIsNotNull() {
            addCriterion("role_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoleIdEqualTo(Integer value) {
            addCriterion("role_id =", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotEqualTo(Integer value) {
            addCriterion("role_id <>", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdGreaterThan(Integer value) {
            addCriterion("role_id >", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("role_id >=", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdLessThan(Integer value) {
            addCriterion("role_id <", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdLessThanOrEqualTo(Integer value) {
            addCriterion("role_id <=", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdIn(List<Integer> values) {
            addCriterion("role_id in", values, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotIn(List<Integer> values) {
            addCriterion("role_id not in", values, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdBetween(Integer value1, Integer value2) {
            addCriterion("role_id between", value1, value2, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("role_id not between", value1, value2, "roleId");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIsNull() {
            addCriterion("avatar_path is null");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIsNotNull() {
            addCriterion("avatar_path is not null");
            return (Criteria) this;
        }

        public Criteria andAvatarPathEqualTo(String value) {
            addCriterion("avatar_path =", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotEqualTo(String value) {
            addCriterion("avatar_path <>", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathGreaterThan(String value) {
            addCriterion("avatar_path >", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathGreaterThanOrEqualTo(String value) {
            addCriterion("avatar_path >=", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLessThan(String value) {
            addCriterion("avatar_path <", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLessThanOrEqualTo(String value) {
            addCriterion("avatar_path <=", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathLike(String value) {
            addCriterion("avatar_path like", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotLike(String value) {
            addCriterion("avatar_path not like", value, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathIn(List<String> values) {
            addCriterion("avatar_path in", values, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotIn(List<String> values) {
            addCriterion("avatar_path not in", values, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathBetween(String value1, String value2) {
            addCriterion("avatar_path between", value1, value2, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andAvatarPathNotBetween(String value1, String value2) {
            addCriterion("avatar_path not between", value1, value2, "avatarPath");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNull() {
            addCriterion("first_name is null");
            return (Criteria) this;
        }

        public Criteria andFirstNameIsNotNull() {
            addCriterion("first_name is not null");
            return (Criteria) this;
        }

        public Criteria andFirstNameEqualTo(String value) {
            addCriterion("first_name =", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotEqualTo(String value) {
            addCriterion("first_name <>", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThan(String value) {
            addCriterion("first_name >", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameGreaterThanOrEqualTo(String value) {
            addCriterion("first_name >=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThan(String value) {
            addCriterion("first_name <", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLessThanOrEqualTo(String value) {
            addCriterion("first_name <=", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameLike(String value) {
            addCriterion("first_name like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotLike(String value) {
            addCriterion("first_name not like", value, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameIn(List<String> values) {
            addCriterion("first_name in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotIn(List<String> values) {
            addCriterion("first_name not in", values, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameBetween(String value1, String value2) {
            addCriterion("first_name between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andFirstNameNotBetween(String value1, String value2) {
            addCriterion("first_name not between", value1, value2, "firstName");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNull() {
            addCriterion("last_name is null");
            return (Criteria) this;
        }

        public Criteria andLastNameIsNotNull() {
            addCriterion("last_name is not null");
            return (Criteria) this;
        }

        public Criteria andLastNameEqualTo(String value) {
            addCriterion("last_name =", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotEqualTo(String value) {
            addCriterion("last_name <>", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThan(String value) {
            addCriterion("last_name >", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameGreaterThanOrEqualTo(String value) {
            addCriterion("last_name >=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThan(String value) {
            addCriterion("last_name <", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLessThanOrEqualTo(String value) {
            addCriterion("last_name <=", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameLike(String value) {
            addCriterion("last_name like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotLike(String value) {
            addCriterion("last_name not like", value, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameIn(List<String> values) {
            addCriterion("last_name in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotIn(List<String> values) {
            addCriterion("last_name not in", values, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameBetween(String value1, String value2) {
            addCriterion("last_name between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andLastNameNotBetween(String value1, String value2) {
            addCriterion("last_name not between", value1, value2, "lastName");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryIsNull() {
            addCriterion("employee_category is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryIsNotNull() {
            addCriterion("employee_category is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryEqualTo(Byte value) {
            addCriterion("employee_category =", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNotEqualTo(Byte value) {
            addCriterion("employee_category <>", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryGreaterThan(Byte value) {
            addCriterion("employee_category >", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryGreaterThanOrEqualTo(Byte value) {
            addCriterion("employee_category >=", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryLessThan(Byte value) {
            addCriterion("employee_category <", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryLessThanOrEqualTo(Byte value) {
            addCriterion("employee_category <=", value, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryIn(List<Byte> values) {
            addCriterion("employee_category in", values, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNotIn(List<Byte> values) {
            addCriterion("employee_category not in", values, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryBetween(Byte value1, Byte value2) {
            addCriterion("employee_category between", value1, value2, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andEmployeeCategoryNotBetween(Byte value1, Byte value2) {
            addCriterion("employee_category not between", value1, value2, "employeeCategory");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNull() {
            addCriterion("phone_number is null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNotNull() {
            addCriterion("phone_number is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberEqualTo(String value) {
            addCriterion("phone_number =", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotEqualTo(String value) {
            addCriterion("phone_number <>", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThan(String value) {
            addCriterion("phone_number >", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThanOrEqualTo(String value) {
            addCriterion("phone_number >=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThan(String value) {
            addCriterion("phone_number <", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThanOrEqualTo(String value) {
            addCriterion("phone_number <=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLike(String value) {
            addCriterion("phone_number like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotLike(String value) {
            addCriterion("phone_number not like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIn(List<String> values) {
            addCriterion("phone_number in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotIn(List<String> values) {
            addCriterion("phone_number not in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberBetween(String value1, String value2) {
            addCriterion("phone_number between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotBetween(String value1, String value2) {
            addCriterion("phone_number not between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andHireDateIsNull() {
            addCriterion("hire_date is null");
            return (Criteria) this;
        }

        public Criteria andHireDateIsNotNull() {
            addCriterion("hire_date is not null");
            return (Criteria) this;
        }

        public Criteria andHireDateEqualTo(Long value) {
            addCriterion("hire_date =", value, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateNotEqualTo(Long value) {
            addCriterion("hire_date <>", value, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateGreaterThan(Long value) {
            addCriterion("hire_date >", value, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateGreaterThanOrEqualTo(Long value) {
            addCriterion("hire_date >=", value, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateLessThan(Long value) {
            addCriterion("hire_date <", value, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateLessThanOrEqualTo(Long value) {
            addCriterion("hire_date <=", value, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateIn(List<Long> values) {
            addCriterion("hire_date in", values, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateNotIn(List<Long> values) {
            addCriterion("hire_date not in", values, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateBetween(Long value1, Long value2) {
            addCriterion("hire_date between", value1, value2, "hireDate");
            return (Criteria) this;
        }

        public Criteria andHireDateNotBetween(Long value1, Long value2) {
            addCriterion("hire_date not between", value1, value2, "hireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateIsNull() {
            addCriterion("fire_date is null");
            return (Criteria) this;
        }

        public Criteria andFireDateIsNotNull() {
            addCriterion("fire_date is not null");
            return (Criteria) this;
        }

        public Criteria andFireDateEqualTo(Long value) {
            addCriterion("fire_date =", value, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateNotEqualTo(Long value) {
            addCriterion("fire_date <>", value, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateGreaterThan(Long value) {
            addCriterion("fire_date >", value, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateGreaterThanOrEqualTo(Long value) {
            addCriterion("fire_date >=", value, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateLessThan(Long value) {
            addCriterion("fire_date <", value, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateLessThanOrEqualTo(Long value) {
            addCriterion("fire_date <=", value, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateIn(List<Long> values) {
            addCriterion("fire_date in", values, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateNotIn(List<Long> values) {
            addCriterion("fire_date not in", values, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateBetween(Long value1, Long value2) {
            addCriterion("fire_date between", value1, value2, "fireDate");
            return (Criteria) this;
        }

        public Criteria andFireDateNotBetween(Long value1, Long value2) {
            addCriterion("fire_date not between", value1, value2, "fireDate");
            return (Criteria) this;
        }

        public Criteria andAllowLoginIsNull() {
            addCriterion("allow_login is null");
            return (Criteria) this;
        }

        public Criteria andAllowLoginIsNotNull() {
            addCriterion("allow_login is not null");
            return (Criteria) this;
        }

        public Criteria andAllowLoginEqualTo(Byte value) {
            addCriterion("allow_login =", value, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginNotEqualTo(Byte value) {
            addCriterion("allow_login <>", value, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginGreaterThan(Byte value) {
            addCriterion("allow_login >", value, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginGreaterThanOrEqualTo(Byte value) {
            addCriterion("allow_login >=", value, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginLessThan(Byte value) {
            addCriterion("allow_login <", value, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginLessThanOrEqualTo(Byte value) {
            addCriterion("allow_login <=", value, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginIn(List<Byte> values) {
            addCriterion("allow_login in", values, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginNotIn(List<Byte> values) {
            addCriterion("allow_login not in", values, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginBetween(Byte value1, Byte value2) {
            addCriterion("allow_login between", value1, value2, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andAllowLoginNotBetween(Byte value1, Byte value2) {
            addCriterion("allow_login not between", value1, value2, "allowLogin");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdIsNull() {
            addCriterion("group_leader_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdIsNotNull() {
            addCriterion("group_leader_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdEqualTo(Integer value) {
            addCriterion("group_leader_id =", value, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdNotEqualTo(Integer value) {
            addCriterion("group_leader_id <>", value, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdGreaterThan(Integer value) {
            addCriterion("group_leader_id >", value, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("group_leader_id >=", value, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdLessThan(Integer value) {
            addCriterion("group_leader_id <", value, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdLessThanOrEqualTo(Integer value) {
            addCriterion("group_leader_id <=", value, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdIn(List<Integer> values) {
            addCriterion("group_leader_id in", values, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdNotIn(List<Integer> values) {
            addCriterion("group_leader_id not in", values, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdBetween(Integer value1, Integer value2) {
            addCriterion("group_leader_id between", value1, value2, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andGroupLeaderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("group_leader_id not between", value1, value2, "groupLeaderId");
            return (Criteria) this;
        }

        public Criteria andNoteIsNull() {
            addCriterion("note is null");
            return (Criteria) this;
        }

        public Criteria andNoteIsNotNull() {
            addCriterion("note is not null");
            return (Criteria) this;
        }

        public Criteria andNoteEqualTo(String value) {
            addCriterion("note =", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteNotEqualTo(String value) {
            addCriterion("note <>", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteGreaterThan(String value) {
            addCriterion("note >", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteGreaterThanOrEqualTo(String value) {
            addCriterion("note >=", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteLessThan(String value) {
            addCriterion("note <", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteLessThanOrEqualTo(String value) {
            addCriterion("note <=", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteLike(String value) {
            addCriterion("note like", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteNotLike(String value) {
            addCriterion("note not like", value, "note");
            return (Criteria) this;
        }

        public Criteria andNoteIn(List<String> values) {
            addCriterion("note in", values, "note");
            return (Criteria) this;
        }

        public Criteria andNoteNotIn(List<String> values) {
            addCriterion("note not in", values, "note");
            return (Criteria) this;
        }

        public Criteria andNoteBetween(String value1, String value2) {
            addCriterion("note between", value1, value2, "note");
            return (Criteria) this;
        }

        public Criteria andNoteNotBetween(String value1, String value2) {
            addCriterion("note not between", value1, value2, "note");
            return (Criteria) this;
        }

        public Criteria andInactiveIsNull() {
            addCriterion("inactive is null");
            return (Criteria) this;
        }

        public Criteria andInactiveIsNotNull() {
            addCriterion("inactive is not null");
            return (Criteria) this;
        }

        public Criteria andInactiveEqualTo(Byte value) {
            addCriterion("inactive =", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveNotEqualTo(Byte value) {
            addCriterion("inactive <>", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveGreaterThan(Byte value) {
            addCriterion("inactive >", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveGreaterThanOrEqualTo(Byte value) {
            addCriterion("inactive >=", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveLessThan(Byte value) {
            addCriterion("inactive <", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveLessThanOrEqualTo(Byte value) {
            addCriterion("inactive <=", value, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveIn(List<Byte> values) {
            addCriterion("inactive in", values, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveNotIn(List<Byte> values) {
            addCriterion("inactive not in", values, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveBetween(Byte value1, Byte value2) {
            addCriterion("inactive between", value1, value2, "inactive");
            return (Criteria) this;
        }

        public Criteria andInactiveNotBetween(Byte value1, Byte value2) {
            addCriterion("inactive not between", value1, value2, "inactive");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNull() {
            addCriterion("create_by_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIsNotNull() {
            addCriterion("create_by_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByIdEqualTo(Integer value) {
            addCriterion("create_by_id =", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotEqualTo(Integer value) {
            addCriterion("create_by_id <>", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThan(Integer value) {
            addCriterion("create_by_id >", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_by_id >=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThan(Integer value) {
            addCriterion("create_by_id <", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdLessThanOrEqualTo(Integer value) {
            addCriterion("create_by_id <=", value, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdIn(List<Integer> values) {
            addCriterion("create_by_id in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotIn(List<Integer> values) {
            addCriterion("create_by_id not in", values, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdBetween(Integer value1, Integer value2) {
            addCriterion("create_by_id between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andCreateByIdNotBetween(Integer value1, Integer value2) {
            addCriterion("create_by_id not between", value1, value2, "createById");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIsNull() {
            addCriterion("book_online_available is null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIsNotNull() {
            addCriterion("book_online_available is not null");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableEqualTo(Byte value) {
            addCriterion("book_online_available =", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotEqualTo(Byte value) {
            addCriterion("book_online_available <>", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableGreaterThan(Byte value) {
            addCriterion("book_online_available >", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableGreaterThanOrEqualTo(Byte value) {
            addCriterion("book_online_available >=", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableLessThan(Byte value) {
            addCriterion("book_online_available <", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableLessThanOrEqualTo(Byte value) {
            addCriterion("book_online_available <=", value, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableIn(List<Byte> values) {
            addCriterion("book_online_available in", values, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotIn(List<Byte> values) {
            addCriterion("book_online_available not in", values, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableBetween(Byte value1, Byte value2) {
            addCriterion("book_online_available between", value1, value2, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andBookOnlineAvailableNotBetween(Byte value1, Byte value2) {
            addCriterion("book_online_available not between", value1, value2, "bookOnlineAvailable");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Long value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Long value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Long value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Long value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Long value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Long> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Long> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Long value1, Long value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Long value1, Long value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Long value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Long value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Long value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Long value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Long value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Long> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Long> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Long value1, Long value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Long value1, Long value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarIsNull() {
            addCriterion("show_on_calendar is null");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarIsNotNull() {
            addCriterion("show_on_calendar is not null");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarEqualTo(Byte value) {
            addCriterion("show_on_calendar =", value, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarNotEqualTo(Byte value) {
            addCriterion("show_on_calendar <>", value, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarGreaterThan(Byte value) {
            addCriterion("show_on_calendar >", value, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarGreaterThanOrEqualTo(Byte value) {
            addCriterion("show_on_calendar >=", value, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarLessThan(Byte value) {
            addCriterion("show_on_calendar <", value, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarLessThanOrEqualTo(Byte value) {
            addCriterion("show_on_calendar <=", value, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarIn(List<Byte> values) {
            addCriterion("show_on_calendar in", values, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarNotIn(List<Byte> values) {
            addCriterion("show_on_calendar not in", values, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarBetween(Byte value1, Byte value2) {
            addCriterion("show_on_calendar between", value1, value2, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowOnCalendarNotBetween(Byte value1, Byte value2) {
            addCriterion("show_on_calendar not between", value1, value2, "showOnCalendar");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllIsNull() {
            addCriterion("show_calendar_staff_all is null");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllIsNotNull() {
            addCriterion("show_calendar_staff_all is not null");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllEqualTo(Byte value) {
            addCriterion("show_calendar_staff_all =", value, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllNotEqualTo(Byte value) {
            addCriterion("show_calendar_staff_all <>", value, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllGreaterThan(Byte value) {
            addCriterion("show_calendar_staff_all >", value, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllGreaterThanOrEqualTo(Byte value) {
            addCriterion("show_calendar_staff_all >=", value, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllLessThan(Byte value) {
            addCriterion("show_calendar_staff_all <", value, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllLessThanOrEqualTo(Byte value) {
            addCriterion("show_calendar_staff_all <=", value, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllIn(List<Byte> values) {
            addCriterion("show_calendar_staff_all in", values, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllNotIn(List<Byte> values) {
            addCriterion("show_calendar_staff_all not in", values, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllBetween(Byte value1, Byte value2) {
            addCriterion("show_calendar_staff_all between", value1, value2, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andShowCalendarStaffAllNotBetween(Byte value1, Byte value2) {
            addCriterion("show_calendar_staff_all not between", value1, value2, "showCalendarStaffAll");
            return (Criteria) this;
        }

        public Criteria andAccessCodeIsNull() {
            addCriterion("access_code is null");
            return (Criteria) this;
        }

        public Criteria andAccessCodeIsNotNull() {
            addCriterion("access_code is not null");
            return (Criteria) this;
        }

        public Criteria andAccessCodeEqualTo(String value) {
            addCriterion("access_code =", value, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeNotEqualTo(String value) {
            addCriterion("access_code <>", value, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeGreaterThan(String value) {
            addCriterion("access_code >", value, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeGreaterThanOrEqualTo(String value) {
            addCriterion("access_code >=", value, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeLessThan(String value) {
            addCriterion("access_code <", value, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeLessThanOrEqualTo(String value) {
            addCriterion("access_code <=", value, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeLike(String value) {
            addCriterion("access_code like", value, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeNotLike(String value) {
            addCriterion("access_code not like", value, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeIn(List<String> values) {
            addCriterion("access_code in", values, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeNotIn(List<String> values) {
            addCriterion("access_code not in", values, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeBetween(String value1, String value2) {
            addCriterion("access_code between", value1, value2, "accessCode");
            return (Criteria) this;
        }

        public Criteria andAccessCodeNotBetween(String value1, String value2) {
            addCriterion("access_code not between", value1, value2, "accessCode");
            return (Criteria) this;
        }

        public Criteria andTokenIsNull() {
            addCriterion("token is null");
            return (Criteria) this;
        }

        public Criteria andTokenIsNotNull() {
            addCriterion("token is not null");
            return (Criteria) this;
        }

        public Criteria andTokenEqualTo(String value) {
            addCriterion("token =", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotEqualTo(String value) {
            addCriterion("token <>", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenGreaterThan(String value) {
            addCriterion("token >", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenGreaterThanOrEqualTo(String value) {
            addCriterion("token >=", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLessThan(String value) {
            addCriterion("token <", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLessThanOrEqualTo(String value) {
            addCriterion("token <=", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLike(String value) {
            addCriterion("token like", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotLike(String value) {
            addCriterion("token not like", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenIn(List<String> values) {
            addCriterion("token in", values, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotIn(List<String> values) {
            addCriterion("token not in", values, "token");
            return (Criteria) this;
        }

        public Criteria andTokenBetween(String value1, String value2) {
            addCriterion("token between", value1, value2, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotBetween(String value1, String value2) {
            addCriterion("token not between", value1, value2, "token");
            return (Criteria) this;
        }

        public Criteria andInviteCodeIsNull() {
            addCriterion("invite_code is null");
            return (Criteria) this;
        }

        public Criteria andInviteCodeIsNotNull() {
            addCriterion("invite_code is not null");
            return (Criteria) this;
        }

        public Criteria andInviteCodeEqualTo(String value) {
            addCriterion("invite_code =", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeNotEqualTo(String value) {
            addCriterion("invite_code <>", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeGreaterThan(String value) {
            addCriterion("invite_code >", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("invite_code >=", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeLessThan(String value) {
            addCriterion("invite_code <", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeLessThanOrEqualTo(String value) {
            addCriterion("invite_code <=", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeLike(String value) {
            addCriterion("invite_code like", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeNotLike(String value) {
            addCriterion("invite_code not like", value, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeIn(List<String> values) {
            addCriterion("invite_code in", values, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeNotIn(List<String> values) {
            addCriterion("invite_code not in", values, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeBetween(String value1, String value2) {
            addCriterion("invite_code between", value1, value2, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andInviteCodeNotBetween(String value1, String value2) {
            addCriterion("invite_code not between", value1, value2, "inviteCode");
            return (Criteria) this;
        }

        public Criteria andPayByIsNull() {
            addCriterion("pay_by is null");
            return (Criteria) this;
        }

        public Criteria andPayByIsNotNull() {
            addCriterion("pay_by is not null");
            return (Criteria) this;
        }

        public Criteria andPayByEqualTo(Byte value) {
            addCriterion("pay_by =", value, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByNotEqualTo(Byte value) {
            addCriterion("pay_by <>", value, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByGreaterThan(Byte value) {
            addCriterion("pay_by >", value, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByGreaterThanOrEqualTo(Byte value) {
            addCriterion("pay_by >=", value, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByLessThan(Byte value) {
            addCriterion("pay_by <", value, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByLessThanOrEqualTo(Byte value) {
            addCriterion("pay_by <=", value, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByIn(List<Byte> values) {
            addCriterion("pay_by in", values, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByNotIn(List<Byte> values) {
            addCriterion("pay_by not in", values, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByBetween(Byte value1, Byte value2) {
            addCriterion("pay_by between", value1, value2, "payBy");
            return (Criteria) this;
        }

        public Criteria andPayByNotBetween(Byte value1, Byte value2) {
            addCriterion("pay_by not between", value1, value2, "payBy");
            return (Criteria) this;
        }

        public Criteria andServicePayRateIsNull() {
            addCriterion("service_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andServicePayRateIsNotNull() {
            addCriterion("service_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andServicePayRateEqualTo(Integer value) {
            addCriterion("service_pay_rate =", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateNotEqualTo(Integer value) {
            addCriterion("service_pay_rate <>", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateGreaterThan(Integer value) {
            addCriterion("service_pay_rate >", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_pay_rate >=", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateLessThan(Integer value) {
            addCriterion("service_pay_rate <", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateLessThanOrEqualTo(Integer value) {
            addCriterion("service_pay_rate <=", value, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateIn(List<Integer> values) {
            addCriterion("service_pay_rate in", values, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateNotIn(List<Integer> values) {
            addCriterion("service_pay_rate not in", values, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateBetween(Integer value1, Integer value2) {
            addCriterion("service_pay_rate between", value1, value2, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andServicePayRateNotBetween(Integer value1, Integer value2) {
            addCriterion("service_pay_rate not between", value1, value2, "servicePayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateIsNull() {
            addCriterion("addon_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateIsNotNull() {
            addCriterion("addon_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateEqualTo(Integer value) {
            addCriterion("addon_pay_rate =", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateNotEqualTo(Integer value) {
            addCriterion("addon_pay_rate <>", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateGreaterThan(Integer value) {
            addCriterion("addon_pay_rate >", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("addon_pay_rate >=", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateLessThan(Integer value) {
            addCriterion("addon_pay_rate <", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateLessThanOrEqualTo(Integer value) {
            addCriterion("addon_pay_rate <=", value, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateIn(List<Integer> values) {
            addCriterion("addon_pay_rate in", values, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateNotIn(List<Integer> values) {
            addCriterion("addon_pay_rate not in", values, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateBetween(Integer value1, Integer value2) {
            addCriterion("addon_pay_rate between", value1, value2, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andAddonPayRateNotBetween(Integer value1, Integer value2) {
            addCriterion("addon_pay_rate not between", value1, value2, "addonPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateIsNull() {
            addCriterion("hourly_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateIsNotNull() {
            addCriterion("hourly_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateEqualTo(BigDecimal value) {
            addCriterion("hourly_pay_rate =", value, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateNotEqualTo(BigDecimal value) {
            addCriterion("hourly_pay_rate <>", value, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateGreaterThan(BigDecimal value) {
            addCriterion("hourly_pay_rate >", value, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("hourly_pay_rate >=", value, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateLessThan(BigDecimal value) {
            addCriterion("hourly_pay_rate <", value, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("hourly_pay_rate <=", value, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateIn(List<BigDecimal> values) {
            addCriterion("hourly_pay_rate in", values, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateNotIn(List<BigDecimal> values) {
            addCriterion("hourly_pay_rate not in", values, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hourly_pay_rate between", value1, value2, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andHourlyPayRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("hourly_pay_rate not between", value1, value2, "hourlyPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateIsNull() {
            addCriterion("tips_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateIsNotNull() {
            addCriterion("tips_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateEqualTo(Integer value) {
            addCriterion("tips_pay_rate =", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateNotEqualTo(Integer value) {
            addCriterion("tips_pay_rate <>", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateGreaterThan(Integer value) {
            addCriterion("tips_pay_rate >", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("tips_pay_rate >=", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateLessThan(Integer value) {
            addCriterion("tips_pay_rate <", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateLessThanOrEqualTo(Integer value) {
            addCriterion("tips_pay_rate <=", value, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateIn(List<Integer> values) {
            addCriterion("tips_pay_rate in", values, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateNotIn(List<Integer> values) {
            addCriterion("tips_pay_rate not in", values, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateBetween(Integer value1, Integer value2) {
            addCriterion("tips_pay_rate between", value1, value2, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andTipsPayRateNotBetween(Integer value1, Integer value2) {
            addCriterion("tips_pay_rate not between", value1, value2, "tipsPayRate");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtIsNull() {
            addCriterion("account_last_visited_at is null");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtIsNotNull() {
            addCriterion("account_last_visited_at is not null");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtEqualTo(Long value) {
            addCriterion("account_last_visited_at =", value, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtNotEqualTo(Long value) {
            addCriterion("account_last_visited_at <>", value, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtGreaterThan(Long value) {
            addCriterion("account_last_visited_at >", value, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtGreaterThanOrEqualTo(Long value) {
            addCriterion("account_last_visited_at >=", value, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtLessThan(Long value) {
            addCriterion("account_last_visited_at <", value, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtLessThanOrEqualTo(Long value) {
            addCriterion("account_last_visited_at <=", value, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtIn(List<Long> values) {
            addCriterion("account_last_visited_at in", values, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtNotIn(List<Long> values) {
            addCriterion("account_last_visited_at not in", values, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtBetween(Long value1, Long value2) {
            addCriterion("account_last_visited_at between", value1, value2, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountLastVisitedAtNotBetween(Long value1, Long value2) {
            addCriterion("account_last_visited_at not between", value1, value2, "accountLastVisitedAt");
            return (Criteria) this;
        }

        public Criteria andAccountSortIsNull() {
            addCriterion("account_sort is null");
            return (Criteria) this;
        }

        public Criteria andAccountSortIsNotNull() {
            addCriterion("account_sort is not null");
            return (Criteria) this;
        }

        public Criteria andAccountSortEqualTo(Integer value) {
            addCriterion("account_sort =", value, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortNotEqualTo(Integer value) {
            addCriterion("account_sort <>", value, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortGreaterThan(Integer value) {
            addCriterion("account_sort >", value, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_sort >=", value, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortLessThan(Integer value) {
            addCriterion("account_sort <", value, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortLessThanOrEqualTo(Integer value) {
            addCriterion("account_sort <=", value, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortIn(List<Integer> values) {
            addCriterion("account_sort in", values, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortNotIn(List<Integer> values) {
            addCriterion("account_sort not in", values, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortBetween(Integer value1, Integer value2) {
            addCriterion("account_sort between", value1, value2, "accountSort");
            return (Criteria) this;
        }

        public Criteria andAccountSortNotBetween(Integer value1, Integer value2) {
            addCriterion("account_sort not between", value1, value2, "accountSort");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNull() {
            addCriterion("enterprise_id is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIsNotNull() {
            addCriterion("enterprise_id is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdEqualTo(Integer value) {
            addCriterion("enterprise_id =", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotEqualTo(Integer value) {
            addCriterion("enterprise_id <>", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThan(Integer value) {
            addCriterion("enterprise_id >", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("enterprise_id >=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThan(Integer value) {
            addCriterion("enterprise_id <", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdLessThanOrEqualTo(Integer value) {
            addCriterion("enterprise_id <=", value, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdIn(List<Integer> values) {
            addCriterion("enterprise_id in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotIn(List<Integer> values) {
            addCriterion("enterprise_id not in", values, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdBetween(Integer value1, Integer value2) {
            addCriterion("enterprise_id between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andEnterpriseIdNotBetween(Integer value1, Integer value2) {
            addCriterion("enterprise_id not between", value1, value2, "enterpriseId");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsIsNull() {
            addCriterion("working_in_all_locations is null");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsIsNotNull() {
            addCriterion("working_in_all_locations is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsEqualTo(Boolean value) {
            addCriterion("working_in_all_locations =", value, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsNotEqualTo(Boolean value) {
            addCriterion("working_in_all_locations <>", value, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsGreaterThan(Boolean value) {
            addCriterion("working_in_all_locations >", value, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("working_in_all_locations >=", value, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsLessThan(Boolean value) {
            addCriterion("working_in_all_locations <", value, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsLessThanOrEqualTo(Boolean value) {
            addCriterion("working_in_all_locations <=", value, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsIn(List<Boolean> values) {
            addCriterion("working_in_all_locations in", values, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsNotIn(List<Boolean> values) {
            addCriterion("working_in_all_locations not in", values, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsBetween(Boolean value1, Boolean value2) {
            addCriterion("working_in_all_locations between", value1, value2, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andWorkingInAllLocationsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("working_in_all_locations not between", value1, value2, "workingInAllLocations");
            return (Criteria) this;
        }

        public Criteria andColorCodeIsNull() {
            addCriterion("color_code is null");
            return (Criteria) this;
        }

        public Criteria andColorCodeIsNotNull() {
            addCriterion("color_code is not null");
            return (Criteria) this;
        }

        public Criteria andColorCodeEqualTo(String value) {
            addCriterion("color_code =", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotEqualTo(String value) {
            addCriterion("color_code <>", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeGreaterThan(String value) {
            addCriterion("color_code >", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeGreaterThanOrEqualTo(String value) {
            addCriterion("color_code >=", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLessThan(String value) {
            addCriterion("color_code <", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLessThanOrEqualTo(String value) {
            addCriterion("color_code <=", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeLike(String value) {
            addCriterion("color_code like", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotLike(String value) {
            addCriterion("color_code not like", value, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeIn(List<String> values) {
            addCriterion("color_code in", values, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotIn(List<String> values) {
            addCriterion("color_code not in", values, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeBetween(String value1, String value2) {
            addCriterion("color_code between", value1, value2, "colorCode");
            return (Criteria) this;
        }

        public Criteria andColorCodeNotBetween(String value1, String value2) {
            addCriterion("color_code not between", value1, value2, "colorCode");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdIsNull() {
            addCriterion("last_visit_business_id is null");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdIsNotNull() {
            addCriterion("last_visit_business_id is not null");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdEqualTo(Integer value) {
            addCriterion("last_visit_business_id =", value, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdNotEqualTo(Integer value) {
            addCriterion("last_visit_business_id <>", value, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdGreaterThan(Integer value) {
            addCriterion("last_visit_business_id >", value, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("last_visit_business_id >=", value, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdLessThan(Integer value) {
            addCriterion("last_visit_business_id <", value, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdLessThanOrEqualTo(Integer value) {
            addCriterion("last_visit_business_id <=", value, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdIn(List<Integer> values) {
            addCriterion("last_visit_business_id in", values, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdNotIn(List<Integer> values) {
            addCriterion("last_visit_business_id not in", values, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdBetween(Integer value1, Integer value2) {
            addCriterion("last_visit_business_id between", value1, value2, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andLastVisitBusinessIdNotBetween(Integer value1, Integer value2) {
            addCriterion("last_visit_business_id not between", value1, value2, "lastVisitBusinessId");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffIsNull() {
            addCriterion("access_all_working_locations_staff is null");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffIsNotNull() {
            addCriterion("access_all_working_locations_staff is not null");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffEqualTo(Byte value) {
            addCriterion("access_all_working_locations_staff =", value, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffNotEqualTo(Byte value) {
            addCriterion("access_all_working_locations_staff <>", value, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffGreaterThan(Byte value) {
            addCriterion("access_all_working_locations_staff >", value, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffGreaterThanOrEqualTo(Byte value) {
            addCriterion("access_all_working_locations_staff >=", value, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffLessThan(Byte value) {
            addCriterion("access_all_working_locations_staff <", value, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffLessThanOrEqualTo(Byte value) {
            addCriterion("access_all_working_locations_staff <=", value, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffIn(List<Byte> values) {
            addCriterion("access_all_working_locations_staff in", values, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffNotIn(List<Byte> values) {
            addCriterion("access_all_working_locations_staff not in", values, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffBetween(Byte value1, Byte value2) {
            addCriterion(
                    "access_all_working_locations_staff between", value1, value2, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andAccessAllWorkingLocationsStaffNotBetween(Byte value1, Byte value2) {
            addCriterion(
                    "access_all_working_locations_staff not between", value1, value2, "accessAllWorkingLocationsStaff");
            return (Criteria) this;
        }

        public Criteria andProfileEmailIsNull() {
            addCriterion("profile_email is null");
            return (Criteria) this;
        }

        public Criteria andProfileEmailIsNotNull() {
            addCriterion("profile_email is not null");
            return (Criteria) this;
        }

        public Criteria andProfileEmailEqualTo(String value) {
            addCriterion("profile_email =", value, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailNotEqualTo(String value) {
            addCriterion("profile_email <>", value, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailGreaterThan(String value) {
            addCriterion("profile_email >", value, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailGreaterThanOrEqualTo(String value) {
            addCriterion("profile_email >=", value, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailLessThan(String value) {
            addCriterion("profile_email <", value, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailLessThanOrEqualTo(String value) {
            addCriterion("profile_email <=", value, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailLike(String value) {
            addCriterion("profile_email like", value, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailNotLike(String value) {
            addCriterion("profile_email not like", value, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailIn(List<String> values) {
            addCriterion("profile_email in", values, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailNotIn(List<String> values) {
            addCriterion("profile_email not in", values, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailBetween(String value1, String value2) {
            addCriterion("profile_email between", value1, value2, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andProfileEmailNotBetween(String value1, String value2) {
            addCriterion("profile_email not between", value1, value2, "profileEmail");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeIsNull() {
            addCriterion("require_access_code is null");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeIsNotNull() {
            addCriterion("require_access_code is not null");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeEqualTo(Boolean value) {
            addCriterion("require_access_code =", value, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeNotEqualTo(Boolean value) {
            addCriterion("require_access_code <>", value, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeGreaterThan(Boolean value) {
            addCriterion("require_access_code >", value, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("require_access_code >=", value, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeLessThan(Boolean value) {
            addCriterion("require_access_code <", value, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeLessThanOrEqualTo(Boolean value) {
            addCriterion("require_access_code <=", value, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeIn(List<Boolean> values) {
            addCriterion("require_access_code in", values, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeNotIn(List<Boolean> values) {
            addCriterion("require_access_code not in", values, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeBetween(Boolean value1, Boolean value2) {
            addCriterion("require_access_code between", value1, value2, "requireAccessCode");
            return (Criteria) this;
        }

        public Criteria andRequireAccessCodeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("require_access_code not between", value1, value2, "requireAccessCode");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table moe_staff
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
