package com.moego.server.business.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.business.mapperbean.MoeStaffAccess;
import com.moego.server.business.mapperbean.MoeStaffAccessExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeStaffAccessMapper extends DynamicDataSource<MoeStaffAccessMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    long countByExample(MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int deleteByExample(MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int insert(MoeStaffAccess record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffAccess record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    List<MoeStaffAccess> selectByExample(MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    MoeStaffAccess selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeStaffAccess record, @Param("example") MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeStaffAccess record, @Param("example") MoeStaffAccessExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffAccess record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_access
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffAccess record);

    int insertOrUpdate(MoeStaffAccess record);
}
