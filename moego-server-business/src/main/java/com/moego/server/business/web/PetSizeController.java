package com.moego.server.business.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.business.service.PetSizeService;
import com.moego.server.business.web.vo.PetSizeRequest;
import com.moego.server.business.web.vo.PetSizeResponse;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(path = "/business/pet")
public class PetSizeController {

    @Autowired
    private PetSizeService petSizeService;

    @Autowired
    private MigrateHelper migrateHelper;

    /**
     * For migrated company, please use moego.api.business_customer.v1.BusinessPetSizeService#ListPetSize
     */
    @Deprecated
    @GetMapping("/size")
    @Auth(AuthType.BUSINESS)
    public PetSizeResponse getPetSize(AuthContext context) {

        migrateHelper.blockMigrated(context);

        List<PetSizeDTO> petSizeDTOList = petSizeService.getPetSize(context.companyId(), context.getBusinessId());
        PetSizeResponse response = new PetSizeResponse();
        response.setPetSizeDTOList(petSizeDTOList);
        return response;
    }

    /**
     * For migrated company, please use moego.api.business_customer.v1.BusinessPetSizeService#BatchUpsertPetSize
     */
    @Deprecated
    @PutMapping("/size")
    @Auth(AuthType.BUSINESS)
    public void savePetSize(AuthContext context, @RequestBody @Valid PetSizeRequest request) {

        migrateHelper.blockMigrated(context);

        petSizeService.savePetSize(context.companyId(), context.getBusinessId(), request.getPetSizeDTOList());
    }
}
