package com.moego.server.business.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.dto.StaffWorkingAreaRangeDto;
import com.moego.server.business.helper.MetadataHelper;
import com.moego.server.business.params.WorkingDailyQueryRangeVo;
import com.moego.server.business.service.WorkingAreaService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/workingArea")
@Slf4j
public class WorkingAreaController {

    @Autowired
    private WorkingAreaService workingAreaService;

    @Autowired
    private MetadataHelper metadataHelper;

    @PostMapping("/shiftManagement")
    @Auth(AuthType.BUSINESS)
    public List<StaffWorkingAreaRangeDto> shiftManagement(
            AuthContext context, @Valid @RequestBody WorkingDailyQueryRangeVo rangeVo) {
        List<StaffWorkingAreaRangeDto> staffWorkingAreaRangeDtos = workingAreaService.queryRangeShiftManagementWorkArea(
                context.getBusinessId(), rangeVo.getStartDate(), rangeVo.getEndDate());
        workingAreaService.areaIdDeleteCheck(
                context.getBusinessId(), workingAreaService.getWorkingAreas(staffWorkingAreaRangeDtos));
        return staffWorkingAreaRangeDtos;
    }

    @PostMapping("/changeShiftManagement")
    @Auth(AuthType.BUSINESS)
    public void transToShiftManagement(AuthContext context) {
        if (metadataHelper.isUseStaffWorkingArea(context.getBusinessId(), false)) {
            return;
        }
        workingAreaService.transToShiftManagement(context.getCompanyId(), context.getBusinessId());
        metadataHelper.setUseStaffWorkingArea(context.accountId(), context.getBusinessId());
    }
}
