package com.moego.server.business.web.vo.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PetReportRecord implements ReportRecord {

    private Integer petId;
    private String petName;
    private String ownerName;
    private String breed;
    private Boolean isMix;
    private String type;
    private String birthday;
    private String gender;
    private String hairLength;
    private String weight;
    private String fixed;
    private String behavior;
    private List<String> petCodes;
    private String vetName;
    private String vetContactNumber;
    private String vetAddress;
    private String emergencyContactName;
    private String emergencyContactNumber;
    private String healthIssue;
    private Boolean isPassAway;

    // pets with expired vaccinations
    private String vaccination;
    private String expirationDate;
    private String primaryNumber;
    private String email;
    private String status;

    private Integer totalPets;

    private String petCode;
}
