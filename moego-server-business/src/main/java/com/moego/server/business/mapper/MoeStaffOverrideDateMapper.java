package com.moego.server.business.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.business.mapperbean.MoeStaffOverrideDate;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeStaffOverrideDateMapper extends DynamicDataSource<MoeStaffOverrideDateMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int insert(MoeStaffOverrideDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int insertSelective(MoeStaffOverrideDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    MoeStaffOverrideDate selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeStaffOverrideDate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_staff_override_date
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeStaffOverrideDate record);

    Integer batchInsertOrUpdate(List<MoeStaffOverrideDate> list);

    List<MoeStaffOverrideDate> selectByBusinessIdAndStaffId(
            @Param("businessId") Integer businessId, @Param("staffId") Integer staffId);

    List<MoeStaffOverrideDate> selectByBusinessIdAndDate(
            @Param("businessId") Integer businessId,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    List<MoeStaffOverrideDate> selectByBusinessIdAndStaffIdAndDate(
            @Param("businessId") Integer businessId,
            @Param("staffIdList") List<Integer> staffIdList,
            @Param("startDate") String startDate,
            @Param("endDate") String endDate);

    int deleteByPrimaryKeyAndBusinessId(@Param("businessId") Integer businessId, @Param("id") Integer id);
}
