package com.moego.server.business.service;

import static com.moego.server.business.service.util.ReportBeanUtil.buildName;
import static com.moego.server.business.service.util.ReportBeanUtil.fillStaffNameForPayrollData;

import com.moego.common.dto.PageDTO;
import com.moego.common.enums.PayrollConst;
import com.moego.common.enums.ReportConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.common.utils.TypeUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.consts.report.ReportFieldsFactory;
import com.moego.server.business.converter.PayrollDataConverter;
import com.moego.server.business.dto.BusinessPayrollSettingDTO;
import com.moego.server.business.dto.PayrollEmployeeDTO;
import com.moego.server.business.dto.PayrollEmployeeDetailDTO;
import com.moego.server.business.dto.StaffPayrollSettingDTO;
import com.moego.server.business.mapper.MoeBusinessClockInOutLogMapper;
import com.moego.server.business.mapperbean.MoeBusinessClockInOutLog;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.PayrollStaffDetailVO;
import com.moego.server.business.service.dto.ClockInOutLogCountDTO;
import com.moego.server.business.service.dto.ClockInOutLogDto;
import com.moego.server.business.service.dto.PayrollExportableDTO;
import com.moego.server.business.service.dto.export.ExportHourlyPayrollData;
import com.moego.server.business.service.dto.export.ExportPayrollDataSummary;
import com.moego.server.business.service.dto.export.ExportPayrollStaffOverviewData;
import com.moego.server.business.service.dto.export.ExportServicePayrollData;
import com.moego.server.business.service.dto.export.ExportTipsPayrollData;
import com.moego.server.business.service.dto.report.PayrollReportData;
import com.moego.server.business.service.params.PayrollExportParams;
import com.moego.server.business.service.params.PayrollExportableParams;
import com.moego.server.business.service.util.ReportBeanUtil;
import com.moego.server.business.web.param.QueryReportParams;
import com.moego.server.business.web.vo.report.EmployeePayrollRecord;
import com.moego.server.business.web.vo.report.EmployeeReportRecord;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.client.IGroomingReportClient;
import com.moego.server.grooming.dto.report.PayrollReportCountDTO;
import com.moego.server.grooming.dto.report.ReportWebEmployee;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class PayrollReportService {

    private static final String defaultExportFileName = "MoeGo Payroll(%s - %s)";

    @Autowired
    private CompanyService companyService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private MoeBusinessClockInOutLogMapper clockInOutMapper;

    @Autowired
    private PayrollSettingService payrollSettingService;

    @Autowired
    private ExportService exportService;

    @Autowired
    private IGroomingReportClient groomingReportClient;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    public List<Integer> getStaffIdsByBusinessId(Integer businessId) {
        return staffService.getStaffListByBusinessId(businessId, true).stream()
                .map(MoeStaff::getId)
                .collect(Collectors.toList());
    }

    /**
     * clock in/out report提取
     *
     * @param businessId businessId
     * @param startDate  查询开始日期
     * @param endDate    查询结束日期
     * @return clock in/out report 列表
     */
    public List<EmployeeReportRecord> queryClockInOutReport(
            Integer businessId,
            String startDate,
            String endDate,
            Map<Integer, StaffPayrollSettingDTO> staffPayrollSettingMap) {
        List<MoeBusinessClockInOutLog> clockLogs =
                clockInOutMapper.queryByBusinessIdAndDateRange(businessId, startDate, endDate);
        Map<Integer, Map<String, Long>> staffClockTimeMap = new HashMap<>();
        Map<Integer, Set<String>> staffToClockDay = new HashMap<>();
        if (!CollectionUtils.isEmpty(clockLogs)) {
            clockLogs.forEach(clockLog -> {
                Long clockInTime = clockLog.getClockInTime();
                if (clockInTime > 0) {
                    Long clockOutTime = clockLog.getClockOutTime();
                    if (PrimitiveTypeUtil.isNumberNullOrZero(clockOutTime)) {
                        // MOE-1990 没有clock out 的staff按未出勤处理
                        return;
                    }
                    Map<String, Long> dateClockTimeMap =
                            staffClockTimeMap.computeIfAbsent(clockLog.getStaffId(), k -> new HashMap<>());
                    if (clockOutTime > clockInTime) {
                        long seconds = dateClockTimeMap.getOrDefault(clockLog.getClockDate(), 0L);
                        seconds += clockOutTime - clockInTime;
                        dateClockTimeMap.put(clockLog.getClockDate(), seconds);
                    }

                    Set<String> days = staffToClockDay.computeIfAbsent(clockLog.getStaffId(), k -> new HashSet<>());
                    days.add(clockLog.getClockDate());
                }
            });
        }

        return calculateClockInOutReport(businessId, staffClockTimeMap, staffToClockDay, staffPayrollSettingMap);
    }

    /**
     * 计算打卡时长、hourlyCommission
     *
     * @param businessId        businessId
     * @param staffClockTimeMap staff打卡时间 map
     * @param staffToClockDay   staff打卡日期 map
     * @return 返回 clock in/out report 列表
     */
    public List<EmployeeReportRecord> calculateClockInOutReport(
            Integer businessId,
            Map<Integer, Map<String, Long>> staffClockTimeMap,
            Map<Integer, Set<String>> staffToClockDay,
            Map<Integer, StaffPayrollSettingDTO> staffPayrollSettingMap) {
        List<MoeStaff> moeStaffs = staffService.getStaffListByBusinessId(businessId, true);

        return moeStaffs.stream()
                .map(staff -> ReportBeanUtil.buildEmployeeReportRecord(
                        staff,
                        staffPayrollSettingMap.get(staff.getId()),
                        staffClockTimeMap.getOrDefault(staff.getId(), Collections.emptyMap()),
                        staffToClockDay.containsKey(staff.getId())
                                ? staffToClockDay.get(staff.getId()).size()
                                : 0))
                .filter(s -> s.getTotalWorkingHour() > 0 || s.getStatus().equals("Normal"))
                .collect(Collectors.toList());
    }

    /**
     * Payroll report 查询所有 staff 的 payroll 数据：service、hourly、tips、total
     *
     * @param params startDate、endDate、businessIds
     * @return staff payroll report 列表
     */
    public List<PayrollEmployeeDTO> getStaffPayrollList(QueryReportParams params, boolean isOnlyTheirOwn) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();

        // 查询staff payroll setting
        Map<Integer, StaffPayrollSettingDTO> staffPayrollSettingMap = getStaffPayrollSettingMap(tokenCompanyId);
        // 查询payroll、clock in/out数据
        PayrollReportData payrollReportData =
                queryPayrollReportData(businessId, startDate, endDate, staffPayrollSettingMap);

        Map<Integer, ReportWebEmployee> apptReportMap = payrollReportData.apptReports().stream()
                .collect(Collectors.toMap(ReportWebEmployee::getStaffId, e -> e));
        Map<Integer, EmployeeReportRecord> clockReportMap = payrollReportData.clockReports().stream()
                .collect(Collectors.toMap(EmployeeReportRecord::getEmployeeId, c -> c));

        // 查找当前 business 的 working staff id
        List<Integer> businessStaffIds = getStaffIdsByBusinessId(businessId);
        List<Integer> staffIds = Stream.of(apptReportMap.keySet(), clockReportMap.keySet(), businessStaffIds)
                .flatMap(Collection::stream)
                .distinct()
                .toList();
        List<MoeStaff> staffs = staffService.getStaffByIds(tokenCompanyId, staffIds);
        return staffs.stream()
                .filter(s -> apptReportMap.containsKey(s.getId())
                        || clockReportMap.containsKey(s.getId())
                        || Objects.equals(s.getStatus(), StaffEnum.STATUS_NORMAL))
                .filter(s -> !isOnlyTheirOwn
                        || s.getId().equals(params.getTokenStaffId().intValue()))
                .map(staff -> ReportBeanUtil.buildPayrollEmployeeDTO(
                        staff,
                        apptReportMap.get(staff.getId()),
                        staffPayrollSettingMap.get(staff.getId()),
                        clockReportMap.get(staff.getId())))
                .collect(Collectors.toList());
    }

    public PayrollReportData queryPayrollReportData(
            Integer businessId,
            String startDate,
            String endDate,
            Map<Integer, StaffPayrollSettingDTO> staffPayrollSettingMap) {
        // 查询payroll、clock in/out数据
        List<ReportWebEmployee> apptReports = groomingReportClient.queryPayrollReport(businessId, startDate, endDate);
        List<EmployeeReportRecord> clockReports =
                queryClockInOutReport(businessId, startDate, endDate, staffPayrollSettingMap);
        return new PayrollReportData(apptReports, clockReports);
    }

    /**
     * 查询某个staff的payroll详情 分页接口
     *
     * @param businessId
     * @param params
     * @return
     */
    public PayrollEmployeeDetailDTO getStaffPayrollDetailPage(PayrollStaffDetailVO params) {
        Integer businessId = params.getBusinessId();
        Long companyId = params.getTokenCompanyId();
        if (!CollectionUtils.isEmpty(params.getBusinessIds())) {
            businessId = params.getBusinessIds().get(0).intValue();
        }

        List<EmployeePayrollRecord> records = null;
        Byte type = params.getType();
        var preferenceSetting = companyService.getCompanyPreferenceSetting(companyId);
        long totalCount = 0L;
        if (params.getStaffId() != null) {
            // staff检查
            MoeStaff staff = staffService.getCompanyStaff(companyId, params.getStaffId());
            if (staff == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "staff not found");
            }
            // 查询 staff payroll setting
            StaffPayrollSettingDTO staffPayrollSetting =
                    payrollSettingService.getStaffPayrollSetting(companyId, businessId, staff.getId());
            // business payroll setting
            BusinessPayrollSettingDTO businessPayrollSetting =
                    payrollSettingService.getBusinessPayrollSetting(businessId);
            boolean isBasedOnCollected = businessPayrollSetting != null
                    && PayrollConst.COMMISSION_BASED_ACTUAL_PAYMENT.equals(
                            businessPayrollSetting.getServiceCommissionBased());

            // 分页参数检查
            if (Objects.isNull(params.getPageNum())) {
                params.setPageNum(1);
            }
            if (Objects.isNull(params.getPageSize())) {
                params.setPageSize(10);
            }

            if (!StringUtils.hasLength(params.getStartDate()) || !StringUtils.hasLength(params.getEndDate())) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "start date or end date is empty");
            }

            switch (type) {
                case ReportConst.PAYROLL_TYPE_HOURLY:
                    boolean hourlyCommissionEnable =
                            Boolean.TRUE.equals(staffPayrollSetting.getHourlyCommissionEnable());
                    if (!hourlyCommissionEnable) {
                        break;
                    }
                    BigDecimal hourlyPay = staffPayrollSetting.getHourlyPay();

                    totalCount = clockInOutMapper.selectByClockDateCount(
                            List.of(businessId), params.getStaffId(), params.getStartDate(), params.getEndDate());
                    List<ClockInOutLogDto> clockLogs = clockInOutMapper.selectByClockDate(
                            List.of(businessId),
                            staff.getId(),
                            params.getStartDate(),
                            params.getEndDate(),
                            CommonUtil.getLimitOffset(params.getPageNum(), params.getPageSize()),
                            params.getPageSize());
                    records = processHourlyCommission(clockLogs, preferenceSetting, hourlyPay);
                    break;
                case ReportConst.PAYROLL_TYPE_SERVICE:
                case ReportConst.PAYROLL_TYPE_TIPS:
                    // 如果当前 commission disable，返回 null
                    boolean serviceCommissionEnable =
                            Boolean.TRUE.equals(staffPayrollSetting.getServiceCommissionEnable());
                    boolean tipsCommissionEnable = Boolean.TRUE.equals(staffPayrollSetting.getTipsCommissionEnable());
                    if ((type == ReportConst.PAYROLL_TYPE_SERVICE && !serviceCommissionEnable)
                            || (type == ReportConst.PAYROLL_TYPE_TIPS && !tipsCommissionEnable)) {
                        break;
                    }
                    PageDTO<ReportWebEmployee> queryPayrollResult = groomingReportClient.queryPayrollReportByPage(
                            businessId,
                            params.getStaffId(),
                            type,
                            params.getStartDate(),
                            params.getEndDate(),
                            params.getPageNum(),
                            params.getPageSize());

                    // 查询 customerName
                    Map<Integer, String> customerNameMap = getCustomerNameMap(queryPayrollResult.getDataList());

                    totalCount = queryPayrollResult.getTotal();
                    records = queryPayrollResult.getDataList().stream()
                            .map(report -> {
                                if (type == ReportConst.PAYROLL_TYPE_SERVICE) {
                                    return ReportBeanUtil.buildServicePayrollReport(
                                            report,
                                            preferenceSetting,
                                            customerNameMap.getOrDefault(report.getCustomerId(), ""),
                                            isBasedOnCollected);
                                } else {
                                    return ReportBeanUtil.buildTipsPayrollReport(
                                            report,
                                            preferenceSetting,
                                            customerNameMap.getOrDefault(report.getCustomerId(), ""),
                                            isBasedOnCollected);
                                }
                            })
                            .collect(Collectors.toList());
                    break;
                default:
                    throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "unknown type");
            }
        }

        // 使用type的int值来构建返回对象
        Integer typeIntVal = type.intValue();
        // 获取单位
        Map<String, String> originalUnits =
                ReportFieldsFactory.PAYROLL_TABLE_FIELD_UNIT_MAP.getOrDefault(typeIntVal, Collections.emptyMap());
        Map<String, String> unitMap = new HashMap<>();
        String currencySymbol = preferenceSetting.getCurrencySymbol();
        if (originalUnits != null && originalUnits.containsValue("$")) {
            originalUnits.forEach((k, v) -> unitMap.put(k, v.equals("$") ? currencySymbol : v));
        }

        return PayrollEmployeeDetailDTO.builder()
                .type(typeIntVal)
                .title(ReportFieldsFactory.PAYROLL_TABLE_TITLE_MAP.get(typeIntVal))
                .fieldHeaders(ReportFieldsFactory.PAYROLL_TABLE_HEADERS_MAP.get(typeIntVal))
                .fieldKeys(ReportFieldsFactory.PAYROLL_TABLE_FIELD_KEY_MAP.get(typeIntVal))
                .fieldUnits(unitMap)
                .records(
                        records != null
                                ? records.stream()
                                        .map(CommonUtil::transBean2Map)
                                        .toList()
                                : null)
                .totalCount(totalCount)
                .build();
    }

    /**
     * 计算 staff hourly payroll
     *
     * @param clockLogs         打卡记录
     * @param preferenceSetting company preference setting
     * @param hourlyPay         staff hourly pay
     * @return 分页 staff payroll report list
     */
    public List<EmployeePayrollRecord> processHourlyCommission(
            List<ClockInOutLogDto> clockLogs, CompanyPreferenceSettingModel preferenceSetting, BigDecimal hourlyPay) {
        Map<String, BigDecimal> dailyClockHoursMap = new HashMap<>();
        var hourlyCommissionList = clockLogs.stream()
                .filter(clockLog -> !(PrimitiveTypeUtil.isNumberNullOrZero(clockLog.getClockInTime())
                        || !StringUtils.hasLength(clockLog.getClockDate())))
                .map(clockLog -> {
                    String clockDate = clockLog.getClockDate();
                    String clockInTime = DateUtil.convertTimeBySeconds(
                            clockLog.getClockInTime(),
                            preferenceSetting.getTimeZone().getName(),
                            (byte) preferenceSetting.getTimeFormatType().getNumber());
                    String clockOutTime = ReportConst.DEFAULT_VALUE;
                    BigDecimal clockHours = null;
                    BigDecimal hourlyCommission = null;
                    String date = DateUtil.dateToBusinessFormat(
                            clockDate,
                            TypeUtil.getDateFormat(
                                    preferenceSetting.getDateFormatType().getNumber(), true));
                    // clock out允许为空
                    if (!PrimitiveTypeUtil.isNumberNullOrZero(clockLog.getClockOutTime())) {
                        clockOutTime = DateUtil.convertTimeBySeconds(
                                clockLog.getClockOutTime(),
                                preferenceSetting.getTimeZone().getName(),
                                (byte) preferenceSetting.getTimeFormatType().getNumber());
                        clockHours = BigDecimal.valueOf(
                                        (clockLog.getClockOutTime() - clockLog.getClockInTime()) / 3600d)
                                .setScale(2, RoundingMode.HALF_UP);
                        hourlyCommission = clockHours
                                .multiply(Objects.nonNull(hourlyPay) ? hourlyPay : BigDecimal.ZERO)
                                .setScale(2, RoundingMode.HALF_UP);

                        BigDecimal dailyClockHours = dailyClockHoursMap.getOrDefault(date, BigDecimal.ZERO);
                        dailyClockHoursMap.put(date, dailyClockHours.add(clockHours));
                    }

                    return EmployeePayrollRecord.builder()
                            .date(date)
                            .clockInTime(clockInTime)
                            .clockOutTime(clockOutTime)
                            .clockHours(clockHours == null ? ReportConst.DEFAULT_VALUE : buildClockHours(clockHours))
                            .clockHoursValue(clockHours == null ? null : clockHours.doubleValue())
                            .hourlyCommission(ReportBeanUtil.formatMoneyNumber(hourlyCommission))
                            .hourlyCommissionValue(hourlyCommission == null ? null : hourlyCommission.doubleValue())
                            .build();
                })
                .toList();
        hourlyCommissionList.forEach(record -> {
            record.setDailyClockHours(
                    buildClockHours(dailyClockHoursMap.getOrDefault(record.getDate(), BigDecimal.ZERO)));
            record.setDailyClockHoursValue(dailyClockHoursMap
                    .getOrDefault(record.getDate(), BigDecimal.ZERO)
                    .doubleValue());
        });
        return hourlyCommissionList;
    }

    /**
     * 查询 customer 名字
     *
     * @param employees employee report 列表
     * @return customerId - customerName map
     */
    private Map<Integer, String> getCustomerNameMap(List<ReportWebEmployee> employees) {
        Set<Integer> customerIds =
                employees.stream().map(ReportWebEmployee::getCustomerId).collect(Collectors.toSet());
        CustomerIdListParams queryParams = new CustomerIdListParams();
        queryParams.setIdList(new ArrayList<>(customerIds));
        List<MoeBusinessCustomerDTO> customers = iCustomerCustomerClient.queryCustomerListWithDeleted(queryParams);
        return customers.stream()
                .collect(Collectors.toMap(
                        MoeBusinessCustomerDTO::getCustomerId, c -> buildName(c.getFirstName(), c.getLastName())));
    }

    /**
     * 查询 staff payroll setting
     *
     * @param businessId businessId
     * @return staffId - staffPayrollSetting map
     */
    public Map<Integer, StaffPayrollSettingDTO> getStaffPayrollSettingMap(Long companyId) {
        List<StaffPayrollSettingDTO> staffPayrollSettingList =
                payrollSettingService.getStaffPayrollSettingListByCompanyId(companyId, null);
        return staffPayrollSettingList.stream()
                .collect(Collectors.toMap(StaffPayrollSettingDTO::getStaffId, Function.identity(), (d1, d2) -> d2));
    }

    /**
     * 查询 staff 是否有数据可导出
     *
     * @param businessId
     * @param params
     * @return
     */
    public List<PayrollExportableDTO> getStaffPayrollExportable(
            PayrollExportableParams params, boolean isOnlyTheirOwn) {
        Integer businessId = params.getBusinessId();
        Long tokenCompanyId = params.getTokenCompanyId();
        Integer staffId = params.getStaffId();
        String startDate = params.getStartDate();
        String endDate = params.getEndDate();

        List<Integer> staffIds;
        if (isOnlyTheirOwn) {
            if (staffId != null && !staffId.equals(params.getTokenStaffId().intValue())) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "No permission to access this staff's payroll");
            }
            staffIds = List.of(params.getTokenStaffId().intValue());
        } else {
            // 如果没有传 staffId，默认查询 business 全部 staff
            staffIds = params.getStaffId() != null ? List.of(staffId) : getStaffIdsByBusinessId(businessId);
        }
        if (CollectionUtils.isEmpty(staffIds)) {
            return List.of();
        }
        var staffPayrollSettingMap = getStaffPayrollSettingMap(tokenCompanyId);
        List<Integer> serviceEnableStaffIds = new ArrayList<>();
        List<Integer> hourlyEnableStaffIds = new ArrayList<>();
        staffPayrollSettingMap.values().forEach(staff -> {
            if (staff.getServiceCommissionEnable() || staff.getTipsCommissionEnable()) {
                serviceEnableStaffIds.add(staff.getStaffId());
            }
            if (staff.getHourlyCommissionEnable()) {
                hourlyEnableStaffIds.add(staff.getStaffId());
            }
        });

        // 查询 service、tips payroll 相关预约数量
        var staffPayrollCountMap = groomingReportClient
                .getStaffPayrollReportCounts(
                        businessId, params.getStartDate(), params.getEndDate(), serviceEnableStaffIds)
                .stream()
                .collect(Collectors.toMap(PayrollReportCountDTO::getStaffId, Function.identity()));

        // 查询 clockInOut log 数量
        var staffClockInOutCountMap =
                clockInOutMapper
                        .selectStaffCountsByClockDate(businessId, hourlyEnableStaffIds, startDate, endDate)
                        .stream()
                        .collect(Collectors.toMap(ClockInOutLogCountDTO::getStaffId, ClockInOutLogCountDTO::getCount));

        return staffIds.parallelStream()
                .map(sId -> {
                    PayrollExportableDTO exportableDTO = new PayrollExportableDTO()
                            .setStaffId(sId)
                            .setServiceCommission(false)
                            .setTipsCommission(false)
                            .setHourlyPay(false);
                    StaffPayrollSettingDTO payrollSetting = staffPayrollSettingMap.get(sId);
                    if (payrollSetting == null) {
                        return exportableDTO;
                    }
                    // 有数据时，对应 exportable 字段设置为true
                    if (payrollSetting.getServiceCommissionEnable() && staffPayrollCountMap.containsKey(sId)) {
                        exportableDTO.setServiceCommission(
                                staffPayrollCountMap.get(sId).getServicePayrollCount() > 0);
                    }
                    if (payrollSetting.getTipsCommissionEnable() && staffPayrollCountMap.containsKey(sId)) {
                        exportableDTO.setTipsCommission(
                                staffPayrollCountMap.get(sId).getTipsPayrollCount() > 0);
                    }
                    if (payrollSetting.getHourlyCommissionEnable() && staffClockInOutCountMap.containsKey(sId)) {
                        exportableDTO.setHourlyPay(staffClockInOutCountMap.get(sId) > 0);
                    }
                    return exportableDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取 Payroll 导出 url
     *
     * @param businessId 商家id
     * @param params     导出参数
     * @return
     */
    public String getStaffPayrollExportUrl(PayrollExportParams params, boolean isOnlyTheirOwn) {
        Integer businessId = params.getBusinessId();
        // 默认导出全部，导出时如无数据会跳过对应的 type
        if (params.getContentType() == null || params.getContentType().length == 0) {
            params.setContentType(new Byte[] {
                ReportConst.PAYROLL_TYPE_SERVICE, ReportConst.PAYROLL_TYPE_HOURLY, ReportConst.PAYROLL_TYPE_TIPS,
            });
        }

        List<Integer> staffIds = new ArrayList<>();
        if (isOnlyTheirOwn) {
            staffIds.add(params.getTokenStaffId().intValue());
        } else {
            staffIds = params.getStaffId() != null ? List.of(params.getStaffId()) : getStaffIdsByBusinessId(businessId);
        }
        if (CollectionUtils.isEmpty(staffIds)) {
            return null;
        }
        // 查询 company preference setting
        var preferenceSetting = companyService.getCompanyPreferenceSetting(params.getTokenCompanyId());
        // 查询 business payroll setting
        BusinessPayrollSettingDTO businessPayrollSetting = payrollSettingService.getBusinessPayrollSetting(businessId);

        // 导出单个 staff 为 xlsx 文件，多个 staff 为 zip 文件
        if (staffIds.size() == 1) {
            params.setStaffId(staffIds.get(0));
            return exportPayrollReportForOneStaff(businessId, preferenceSetting, params, businessPayrollSetting);
        } else {
            return exportPayrollReportForStaffs(
                    businessId, preferenceSetting, staffIds, params, businessPayrollSetting, isOnlyTheirOwn);
        }
    }

    /**
     * 导出一个 staff 的 Payroll 明细
     *
     * @param preferenceSetting      company preference setting
     * @param params                 导出参数
     * @param businessPayrollSetting business payroll 设置
     * @return 导出文件 url
     */
    private String exportPayrollReportForOneStaff(
            Integer businessId,
            CompanyPreferenceSettingModel preferenceSetting,
            PayrollExportParams params,
            BusinessPayrollSettingDTO businessPayrollSetting) {
        Integer companyId = businessPayrollSetting.getCompanyId().intValue();
        Integer staffId = params.getStaffId();

        MoeStaff staff = staffService.getCompanyStaff(companyId.longValue(), staffId);
        if (staff == null) {
            return null;
        }

        // 查询 staff payroll setting
        StaffPayrollSettingDTO staffPayrollSetting =
                payrollSettingService.getStaffPayrollSetting(companyId.longValue(), businessId, staffId);

        ExportPayrollDataSummary exportData = getStaffPayrollExportData(
                businessId, preferenceSetting, staff, params, businessPayrollSetting, staffPayrollSetting);
        fillStaffNameForPayrollData(exportData, buildName(staff.getFirstName(), staff.getLastName()));
        if (exportData == null) {
            return null;
        }
        String fileName = StringUtils.hasText(params.getFileName())
                ? params.getFileName()
                : getDefaultExportFileName(
                        staff, params.getStartDate(), params.getEndDate(), ExportService.XLSX_FILE_SUFFIX);
        return exportService.uploadExportPayrollFile(exportData, fileName);
    }

    /**
     * 查询 staff 要导出的 Payroll 数据
     *
     * @param preferenceSetting      company preference setting
     * @param staff                  staff 信息
     * @param params                 导出参数
     * @param businessPayrollSetting business payroll setting
     * @param staffPayrollSetting    staff payroll setting
     * @return 导出数据的汇总
     */
    public ExportPayrollDataSummary getStaffPayrollExportData(
            Integer businessId,
            CompanyPreferenceSettingModel preferenceSetting,
            MoeStaff staff,
            PayrollExportParams params,
            BusinessPayrollSettingDTO businessPayrollSetting,
            StaffPayrollSettingDTO staffPayrollSetting) {
        if (staffPayrollSetting == null) {
            return null;
        }
        boolean isBasedOnCollected =
                PayrollConst.COMMISSION_BASED_ACTUAL_PAYMENT.equals(businessPayrollSetting.getServiceCommissionBased());
        List<ExportServicePayrollData> servicePayrollList = null;
        List<ExportHourlyPayrollData> hourlyPayrollList = null;
        List<ExportTipsPayrollData> tipsPayrollList = null;

        List<Byte> contentTypes = Arrays.asList(params.getContentType());
        if (contentTypes.contains(ReportConst.PAYROLL_TYPE_SERVICE)
                && staffPayrollSetting.getServiceCommissionEnable()) {
            List<ReportWebEmployee> servicePayrolls = groomingReportClient.queryPayrollReportAll(
                    businessId,
                    staff.getId(),
                    ReportConst.PAYROLL_TYPE_SERVICE,
                    params.getStartDate(),
                    params.getEndDate());
            // 查询 customerName
            Map<Integer, String> customerNameMap = getCustomerNameMap(servicePayrolls);
            servicePayrollList = servicePayrolls.stream()
                    .map(employee -> {
                        EmployeePayrollRecord record = ReportBeanUtil.buildServicePayrollReport(
                                employee,
                                preferenceSetting,
                                customerNameMap.getOrDefault(employee.getCustomerId(), ""),
                                isBasedOnCollected);
                        return PayrollDataConverter.INSTANCE.toServiceData(record);
                    })
                    .collect(Collectors.toList());
        }
        if (contentTypes.contains(ReportConst.PAYROLL_TYPE_TIPS) && staffPayrollSetting.getTipsCommissionEnable()) {
            List<ReportWebEmployee> tipsPayrolls = groomingReportClient.queryPayrollReportAll(
                    businessId,
                    staff.getId(),
                    ReportConst.PAYROLL_TYPE_TIPS,
                    params.getStartDate(),
                    params.getEndDate());
            // 查询 customerName
            Map<Integer, String> customerNameMap = getCustomerNameMap(tipsPayrolls);
            tipsPayrollList = tipsPayrolls.stream()
                    .map(employee -> {
                        EmployeePayrollRecord record = ReportBeanUtil.buildTipsPayrollReport(
                                employee,
                                preferenceSetting,
                                customerNameMap.getOrDefault(employee.getCustomerId(), ""),
                                isBasedOnCollected);
                        return PayrollDataConverter.INSTANCE.toTipsData(record);
                    })
                    .collect(Collectors.toList());
        }
        if (contentTypes.contains(ReportConst.PAYROLL_TYPE_HOURLY) && staffPayrollSetting.getHourlyCommissionEnable()) {
            BigDecimal hourlyPay = staffPayrollSetting.getHourlyPay();

            List<ClockInOutLogDto> clockLogs = clockInOutMapper.selectByClockDate(
                    List.of(businessId), staff.getId(), params.getStartDate(), params.getEndDate(), null, null);
            hourlyPayrollList = processHourlyCommission(clockLogs, preferenceSetting, hourlyPay).stream()
                    .map(PayrollDataConverter.INSTANCE::toHourlyData)
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(servicePayrollList)
                && CollectionUtils.isEmpty(tipsPayrollList)
                && CollectionUtils.isEmpty(hourlyPayrollList)) {
            log.info("Export payroll report#business:{} staff:{} data is empty.", businessId, staff.getId());
            return null;
        }
        return new ExportPayrollDataSummary()
                .setCurrencySymbol(preferenceSetting.getCurrencySymbol())
                .setServicePayrollList(servicePayrollList)
                .setHourlyPayrollList(hourlyPayrollList)
                .setTipsPayrollList(tipsPayrollList);
    }

    private String exportPayrollReportForStaffs(
            Integer businessId,
            CompanyPreferenceSettingModel preferenceSetting,
            List<Integer> staffIds,
            PayrollExportParams params,
            BusinessPayrollSettingDTO businessPayrollSetting,
            boolean isOnlyTheirOwn) {
        Long tokenCompanyId = params.getTokenCompanyId();
        List<MoeStaff> staffs = staffService.getStaffByIds(tokenCompanyId, staffIds);
        if (CollectionUtils.isEmpty(staffs)) {
            return null;
        }

        Map<Integer, StaffPayrollSettingDTO> staffPayrollSettingMap =
                payrollSettingService.getStaffPayrollSettingList(businessId, true).stream()
                        .collect(Collectors.toMap(StaffPayrollSettingDTO::getStaffId, Function.identity()));

        Map<Integer, ExportPayrollDataSummary> exportDataMap = new HashMap<>();
        List<ExportPayrollStaffOverviewData> staffPayrollOverviews = new ArrayList<>();
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        for (MoeStaff staff : staffs) {
            completableFutureList.add(CompletableFuture.runAsync(
                    () -> {
                        ExportPayrollDataSummary exportData = getStaffPayrollExportData(
                                businessId,
                                preferenceSetting,
                                staff,
                                params,
                                businessPayrollSetting,
                                staffPayrollSettingMap.get(staff.getId()));
                        if (exportData == null) {
                            return;
                        }
                        fillStaffNameForPayrollData(exportData, buildName(staff.getFirstName(), staff.getLastName()));
                        exportDataMap.put(staff.getId(), exportData);
                    },
                    ThreadPool.getExecuteExecutor()));
        }
        completableFutureList.add(CompletableFuture.runAsync(
                () -> {
                    // 查询 staff overview
                    staffPayrollOverviews.addAll(getStaffPayrollList(params, isOnlyTheirOwn).stream()
                            .map(PayrollDataConverter.INSTANCE::toOverviewData)
                            .toList());
                },
                ThreadPool.getExecuteExecutor()));

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .join();
        return exportPayrollReportForStaffs(staffs, params, exportDataMap, staffPayrollOverviews);
    }

    public String exportPayrollReportForStaffs(
            List<MoeStaff> staffs,
            PayrollExportParams params,
            Map<Integer, ExportPayrollDataSummary> exportDataMap,
            List<ExportPayrollStaffOverviewData> staffPayrollOverviews) {
        // 按 staff 顺序组装数据
        Map<String, ExportPayrollDataSummary> exportDataMapOrdered = new LinkedHashMap<>();
        staffs.forEach(staff -> {
            if (!exportDataMap.containsKey(staff.getId())) {
                return;
            }
            String fileName = getDefaultExportFileName(
                    staff, params.getStartDate(), params.getEndDate(), ExportService.XLSX_FILE_SUFFIX);
            if (exportDataMapOrdered.containsKey(fileName)) {
                fileName = staff.getId() + " " + fileName;
            }
            exportDataMapOrdered.put(fileName, exportDataMap.get(staff.getId()));
        });

        String fileName;
        if (Boolean.TRUE.equals(params.getExportedByStaff())) {
            fileName = getDefaultExportFileName(
                    null, params.getStartDate(), params.getEndDate(), ExportService.ZIP_FILE_SUFFIX);
            return exportService.compressAndUploadExportPayrollFiles(exportDataMapOrdered, fileName);
        } else {
            var payrollSummaryData =
                    ReportBeanUtil.mergePayrollSummaryData(new ArrayList<>(exportDataMapOrdered.values()));
            payrollSummaryData.setStaffOverviewList(staffPayrollOverviews);
            fileName = getDefaultExportFileName(
                    null, params.getStartDate(), params.getEndDate(), ExportService.XLSX_FILE_SUFFIX);
            return exportService.uploadExportPayrollFile(payrollSummaryData, fileName);
        }
    }

    /**
     * 生成 export fileName
     *
     * @param staff     staff 信息
     * @param startDate 导出查询范围开始日期
     * @param endDate   导出查询范围结束日期
     * @param suffix    文件后缀
     * @return 文件名
     */
    private String getDefaultExportFileName(MoeStaff staff, String startDate, String endDate, String suffix) {
        if (staff != null) {
            String staffName = buildName(staff.getFirstName(), staff.getLastName());
            if (!StaffEnum.STATUS_NORMAL.equals(staff.getStatus())) {
                staffName += "(Deleted)";
            }
            return staffName + " " + String.format(defaultExportFileName, startDate, endDate) + suffix;
        } else {
            return String.format(defaultExportFileName, startDate, endDate) + suffix;
        }
    }

    private String buildClockHours(BigDecimal clockHours) {
        if (clockHours == null) {
            return ReportConst.DEFAULT_VALUE;
        }
        if (clockHours.compareTo(BigDecimal.ZERO) > 1) {
            return clockHours.toString() + " hour";
        }
        return clockHours.toString() + " hours";
    }
}
