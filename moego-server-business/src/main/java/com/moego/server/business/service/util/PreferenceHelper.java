package com.moego.server.business.service.util;

import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class PreferenceHelper {
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;

    public CompanyPreferenceSettingModel getCompanyPreferenceSetting(Long companyId) {
        try {
            return companyServiceBlockingStub
                    .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                            .setCompanyId(companyId)
                            .build())
                    .getPreferenceSetting();
        } catch (Exception e) {
            log.error("getCompanyPreferenceSetting error for company id {}", companyId, e);
            return null;
        }
    }
}
