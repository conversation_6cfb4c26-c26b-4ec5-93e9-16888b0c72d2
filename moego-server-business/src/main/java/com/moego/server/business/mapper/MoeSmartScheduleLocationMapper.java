package com.moego.server.business.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.business.mapperbean.MoeSmartScheduleLocation;
import com.moego.server.business.params.SaveSmartScheduleLocationParams;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeSmartScheduleLocationMapper extends DynamicDataSource<MoeSmartScheduleLocationMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_smart_schedule_location
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_smart_schedule_location
     *
     * @mbg.generated
     */
    int insert(MoeSmartScheduleLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_smart_schedule_location
     *
     * @mbg.generated
     */
    int insertSelective(MoeSmartScheduleLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_smart_schedule_location
     *
     * @mbg.generated
     */
    MoeSmartScheduleLocation selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_smart_schedule_location
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeSmartScheduleLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_smart_schedule_location
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeSmartScheduleLocation record);

    List<MoeSmartScheduleLocation> selectByBusinessId(Integer businessId);

    MoeSmartScheduleLocation selectByIdAndBusinessId(@Param("id") Integer id, @Param("businessId") Integer businessId);

    List<MoeSmartScheduleLocation> selectByIdListAndBusinessId(
            @Param("idList") List<Integer> idList, @Param("businessId") Integer businessId);

    int countByParams(@Param("params") SaveSmartScheduleLocationParams params);
}
