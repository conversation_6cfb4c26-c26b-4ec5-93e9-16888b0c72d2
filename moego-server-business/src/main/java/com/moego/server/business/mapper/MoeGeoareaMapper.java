package com.moego.server.business.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.business.mapperbean.MoeGeoarea;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeGeoareaMapper extends DynamicDataSource<MoeGeoareaMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_geoarea
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_geoarea
     *
     * @mbg.generated
     */
    int insert(MoeGeoarea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_geoarea
     *
     * @mbg.generated
     */
    int insertSelective(MoeGeoarea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_geoarea
     *
     * @mbg.generated
     */
    MoeGeoarea selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_geoarea
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGeoarea record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_geoarea
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGeoarea record);

    List<MoeGeoarea> queryNotDeletedByPrimaryIds(@Param("areaIds") List<Integer> areaIds);

    List<MoeGeoarea> queryAreasByBusinessId(@Param("businessId") Integer businessId);

    int softDeleteAreaWithBusinessAndAreaId(@Param("businessId") Integer businessId, @Param("id") Integer id);

    List<MoeGeoarea> queryAreasNotDelete(
            @Param("businessId") Integer businessId,
            @Param("ids") List<Integer> ids,
            @Param("names") List<String> names);

    List<MoeGeoarea> queryAreasNotDeleteInCompany(
            @Param("companyId") Long companyId, @Param("ids") List<Integer> ids, @Param("names") List<String> names);

    boolean checkAreaExist(@Param("businessId") Integer businessId);
}
