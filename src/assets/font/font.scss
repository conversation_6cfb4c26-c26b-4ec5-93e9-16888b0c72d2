/** 关于 font-face: https://www.zhangxinxu.com/wordpress/2017/03/css3-font-face-src-local/ */

@font-face {
  font-family: 'Nunito';
  src: url('./Nunito-Light.ttf');
  font-weight: 100 300;
}

@font-face {
  font-family: 'Nunito';
  src: url('./Nunito-Regular.ttf');
  font-weight: 400;
}

@font-face {
  font-family: 'Nunito';
  src: url('./Nunito-SemiBold.ttf');
  font-weight: 500 600;
}

@font-face {
  font-family: 'Nunito';
  src: url('./Nunito-Bold.ttf');
  font-weight: 700 900;
}

@font-face {
  font-family: 'Nunito';
  src: url('./Nunito-LightItalic.ttf');
  font-style: italic;
  font-weight: 100 300;
}

@font-face {
  font-family: 'Nunito';
  src: url('./Nunito-Italic.ttf');
  font-style: italic;
  font-weight: 400;
}

@font-face {
  font-family: 'Nunito';
  src: url('./Nunito-SemiBoldItalic.ttf');
  font-style: italic;
  font-weight: 500 600;
}

@font-face {
  font-family: 'Nunito';
  src: url('./Nunito-BoldItalic.ttf');
  font-style: italic;
  font-weight: 700 900;
}

@font-face {
  font-family: 'ProximaNova';
  src: url('./ProximaNova-Regular.otf');
  font-weight: 400;
}

@font-face {
  font-family: 'ProximaNova';
  src: url('./ProximaNova-SemiBold.otf');
  font-weight: 500 600;
}

@font-face {
  font-family: 'ProximaNova';
  src: url('./ProximaNova-Bold.otf');
  font-weight: 700 900;
}
