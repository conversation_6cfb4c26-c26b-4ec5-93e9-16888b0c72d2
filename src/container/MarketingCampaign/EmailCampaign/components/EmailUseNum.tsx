import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { useMemo } from 'react';
import { selectEmailBillingPeriodDetail } from '../../../../store/marketingCampaign/emailCampaign.selectors';

interface CreditValue {
  /** this cycle */
  purchaseEmailAmount: number;
  /** Remaing from last cycle */
  purchaseEmailLeftover: number;
  /** included plan */
  subscriptionEmailAmount: number;
}

export interface EmailUseNumProps {
  billingPeriod?: string;
  className?: string;
  value?: CreditValue;
}

export function EmailUseNum(props: EmailUseNumProps) {
  const { className, billingPeriod } = props;
  const [emailBillingDetail] = useSelector(selectEmailBillingPeriodDetail(billingPeriod));
  const hasValue = !!emailBillingDetail.ownKey;
  const total: string = useMemo(() => {
    if (!hasValue) {
      return '';
    }
    const { purchaseEmailAmount, purchaseEmailLeftover, subscriptionEmailAmount } = emailBillingDetail;
    const all = purchaseEmailAmount + purchaseEmailLeftover + subscriptionEmailAmount;
    return Number.isFinite(all) ? String(all) : '';
  }, [emailBillingDetail, hasValue]);

  return (
    <div className={classNames('!moe-bg-[#F7F8FA] !moe-rounded-[12px] !moe-p-[16px]', className)}>
      <div
        className="!moe-pb-[8px] !moe-mb-[8px] !moe-flex !moe-justify-between !moe-items-center !moe-text-[14px] !moe-text-[#333] !moe-font-bold !moe-leading-[18px]"
        style={{ borderBottom: '1px solid #e6e6e6' }}
      >
        <div>Total campaign credit</div>
        <div>{total}</div>
      </div>
      <div className="!moe-flex !moe-flex-col !moe-gap-y-[4px]">
        <div className="!moe-flex !moe-items-center !moe-justify-between !moe-text-[14px] !moe-text-[#666] !moe-font-medium !moe-leading-[18px]">
          <div>Included in plan</div>
          <div>{hasValue ? emailBillingDetail?.subscriptionEmailAmount : ''}</div>
        </div>
        <div className="!moe-flex !moe-items-center !moe-justify-between !moe-text-[14px] !moe-text-[#666] !moe-font-medium !moe-leading-[18px]">
          <div>Purchased (this cycle)</div>
          <div>{hasValue ? emailBillingDetail?.purchaseEmailAmount : ''}</div>
        </div>
        <div className="!moe-flex !moe-items-center !moe-justify-between !moe-text-[14px] !moe-text-[#666] !moe-font-medium !moe-leading-[18px]">
          <div>Remaining from past purchases</div>
          <div>{hasValue ? emailBillingDetail?.purchaseEmailLeftover : ''}</div>
        </div>
      </div>
    </div>
  );
}
