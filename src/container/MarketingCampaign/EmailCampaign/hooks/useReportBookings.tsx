import { useSelector } from 'amos';
import type { ColumnsType } from 'antd/es/table';
import React, { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type EmailCampaignReportBooking } from '../../../../store/marketingCampaign/emailCampaign.action';
import { useMaybeEmptyDate } from './useMaybeEmptyDate';
import { QuestionTooltip } from '../../../../components/Popup/Tooltip';

interface RecipientListParams {
  activeKey: number;
  getList: () => void;
  onOpenBookingModal?: (id: string) => void;
}

export function useReportBookings(params: RecipientListParams) {
  const { activeKey, onOpenBookingModal } = params;
  const [business] = useSelector(selectCurrentBusiness);
  const formateDate = useMaybeEmptyDate();
  const columns = useMemo(() => {
    const columns: ColumnsType<EmailCampaignReportBooking> = [
      {
        title: 'Name',
        dataIndex: 'customerName',
        className: '!moe-w-[20%]',
      },
      {
        title: 'Email address',
        dataIndex: 'customerEmail',
      },
      {
        title: 'Booking ID',
        dataIndex: 'id',
        render: (id) => {
          return (
            <span
              className="!moe-text-[14px] !moe-text-brand !moe-font-medium"
              onClick={(e) => {
                e.stopPropagation();
                onOpenBookingModal?.(id);
              }}
            >
              #{id}
            </span>
          );
        },
      },
      {
        title: (
          <div className="!moe-text-right !moe-w-[120px] !moe-whitespace-nowrap">
            Gross sales
            <QuestionTooltip
              overlay="The total original price of all services in this booking, before applying any discounts, package deductions, taxes, or tips, whether unpaid or paid."
              placement="bottom"
              size={18}
            />
          </div>
        ),
        dataIndex: 'revenue',
        render: (value) => {
          return (
            <div className="!moe-text-right !moe-w-[120px] !moe-whitespace-nowrap">{business.formatAmount(value)}</div>
          );
        },
      },
      {
        title: 'Create time',
        dataIndex: 'createdTime',
        render: formateDate,
      },
    ];
    return columns;
  }, [activeKey, formateDate]);

  return {
    columns,
  };
}
