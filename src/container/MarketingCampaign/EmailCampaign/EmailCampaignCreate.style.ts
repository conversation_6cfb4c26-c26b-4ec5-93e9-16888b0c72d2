import styled from 'styled-components';

export const StyledSpinView = styled.div`
  // spin class name not working
  width: 100%;
  .ant-spin-nested-loading {
    width: 100%;
  }
`;

export const StyledStepsView = styled.div`
  display: flex;
  width: 100%;
  .ant-steps-vertical {
    display: flex;
    flex-direction: row;
    & > .ant-steps-item {
      & > .ant-steps-item-container {
        & > .ant-steps-item-tail {
          display: none;
        }
      }
      .ant-steps-item-content {
        min-height: auto;
      }
    }
  }
`;
