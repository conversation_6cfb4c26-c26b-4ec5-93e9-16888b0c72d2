/*
 * @since 2023-03-13 22:03:45
 * <AUTHOR> <<EMAIL>>
 */

import styled from 'styled-components';
import { DatePicker, type RangePickerProps } from '../../components/DatePicker/DatePicker';
import { LayoutContainer } from '../../layout/LayoutContainer';
import { paginationStyle } from '../settings/components/StyledTable';

export const ActivityLogView = styled(LayoutContainer)`
  .ant-row.ant-form-item {
    margin-bottom: 20px;
  }

  .ant-col.ant-form-item-label {
    min-width: unset;

    > label {
      min-width: unset;
    }
  }

  .ant-col.ant-form-item-control {
    min-width: 150px;
  }
`;

export const StyledRangePicker: (props: RangePickerProps) => JSX.Element = styled(DatePicker.RangePicker)`
  .ant-picker-active-bar {
    display: none;
  }
  &&&.ant-picker {
    border-radius: 16px;
    &.ant-picker-focused {
      box-shadow: none;
    }
  }
`;

export const ActivityLogTableView = styled.div`
  margin: 0 24px 32px;
  flex: 1;
  /* height: calc(100% - 125px); */
  .ant-spin-nested-loading {
    height: 100%;
  }
  .ant-table-wrapper {
    height: 100%;
  }
  .ant-spin-container,
  .ant-table-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .ant-table {
    line-height: 20px;
    white-space: nowrap;
    flex: 1 1 auto;
  }
  .ant-table-body {
    flex: 1 1 auto;
  }
  .ant-table-placeholder .ant-table-cell {
    border-color: rgba(0, 0, 0, 0);
  }
  .ant-table-cell:first-child {
    padding-left: 16px;
  }
  ${paginationStyle}
`;
