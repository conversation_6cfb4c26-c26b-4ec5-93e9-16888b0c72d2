/*
 * @since 2023-03-17 20:37:12
 * <AUTHOR> <<EMAIL>>
 */

import { useDispatch, useSelector } from 'amos';
import { Row } from 'antd';
import React, { memo, useEffect } from 'react';
import { Loading } from '../../../components/Loading/Loading';
import { type ModalRequiredProps } from '../../../components/Modal/Modal';
import { getActivityLogDetail } from '../../../store/activity_log/activity_log.actions';
import { selectActivityLog } from '../../../store/activity_log/activity_log.selectors';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { ActivityLogDetailModalDrawer, ActivityLogDetailView, Label } from './ActivityLogDetail.style';
import { ActivityLogDetailAccordionItem } from './ActivityLogDetailAccordionItem';
import { ActivityLogDetailCard } from './ActivityLogDetailCard';

export interface ActivityLogDetailProps {
  className?: string;
  id?: string;
}

export const ActivityLogDetail = memo<ActivityLogDetailProps>(({ className, id }) => {
  const [log] = useSelector(selectActivityLog(id || ''));
  const dispatch = useDispatch();
  const load = useSerialCallback((id: string) => dispatch(getActivityLogDetail(id)));
  useEffect(() => {
    if (id) {
      load(id);
    }
  }, [id]);
  if (!id) {
    return null;
  }

  return (
    <ActivityLogDetailView className={className}>
      <Loading loading={load.isBusy()}>
        <ActivityLogDetailCard log={log} />
        <Row>
          <Label span={4}>Affected resources</Label>
        </Row>
        <ul>
          {log.affectedResourceList.map((resource) => (
            <ActivityLogDetailAccordionItem id={resource} key={resource} />
          ))}
        </ul>
      </Loading>
    </ActivityLogDetailView>
  );
});

export const ActivityLogDetailModal = memo<ModalRequiredProps & ActivityLogDetailProps>(({ id, onClose, visible }) => {
  return (
    <ActivityLogDetailModalDrawer
      title={'Activity log #' + id}
      width={880}
      visible={visible}
      onClose={() => onClose(void 0)}
    >
      <ActivityLogDetail id={id} />
    </ActivityLogDetailModalDrawer>
  );
});
