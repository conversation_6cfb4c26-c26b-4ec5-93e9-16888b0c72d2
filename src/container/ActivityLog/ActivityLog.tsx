/*
 * @since 2023-03-13 22:03:46
 * <AUTHOR> <<EMAIL>>
 */

import { type SearchActivityLogPageRequest } from '@moego/api-web/moego/api/activity_log/v1/activity_log_api';
import { type ActivityLogModel } from '@moego/api-web/moego/models/activity_log/v1/activity_log_models';
import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Form } from 'antd';
import FormItem from 'antd/es/form/FormItem';
import { type ColumnsType } from 'antd/es/table';
import dayjs, { type Dayjs } from 'dayjs';
import { type RangeValue } from 'rc-picker/es/interface';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Tooltip } from '../../components/Popup/Tooltip';
import { type PaginationOptions } from '../../components/Table/Table.types';
import { usePaginationOptions } from '../../components/Table/Table.utils';
import { PageTitle } from '../../layout/components/PageTitle';
import { useGetClosestScroller } from '../../layout/components/ScrollerProvider';
import {
  clearActivityLogFilter,
  getActivityLogList,
  searchActivityLogAction,
  searchActivityLogOperator,
  searchActivityLogOwner,
  searchActivityLogResourceType,
} from '../../store/activity_log/activity_log.actions';
import { activityLogMapBox } from '../../store/activity_log/activity_log.boxes';
import { selectBusinessActivityLogList } from '../../store/activity_log/activity_log.selectors';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { StyledTable } from '../settings/components/StyledTable';
import { ActivityLogTableView, ActivityLogView, StyledRangePicker } from './ActivityLog.style';
import { ActivityLogDetailModal } from './components/ActivityLogDetail';
import { SearchInput } from './components/SearchInput';
import { type Pagination, SearchSelect } from './components/SearchSelect';

export interface ActivityLogProps {
  className?: string;
}

export const ActivityLog = memo<ActivityLogProps>(({ className }) => {
  const dispatch = useDispatch();
  const [activityLogList, activityLogMap, business] = useSelector(
    selectBusinessActivityLogList(),
    activityLogMapBox,
    selectCurrentBusiness(),
  );
  const [viewDetail, setViewDetail] = useState<string>();
  const getScroller = useGetClosestScroller();

  const handleUpdateParams = useSerialCallback(async (input: Partial<SearchActivityLogPageRequest>) => {
    const newParams = activityLogList.filter.merge(input);
    await dispatch(getActivityLogList({ ...newParams.toJSON(), clear: true, pageNum: 1 }));
  });

  // 离开时清除 filter
  useEffect(() => {
    return () => {
      dispatch(clearActivityLogFilter());
    };
  }, []);

  const fetchOptions =
    (optionType: 'operatorName' | 'resourceType' | 'action' | 'ownerName') =>
    async (keyword: string, pagination: Omit<Pagination, 'total'> = { pageNum: 1, pageSize: 10 }) => {
      let data;
      let options: Array<{ key: string; value: string; label: string }> = [];
      switch (optionType) {
        case 'operatorName':
          data = await dispatch(searchActivityLogOperator({ operatorName: keyword, pagination }));
          options = data.operators.map((v) => ({ key: v.id, value: v.id, label: v.name }));
          break;
        case 'resourceType':
          data = await dispatch(searchActivityLogResourceType({ resourceType: keyword, pagination }));
          options = data.resourceTypes.map((v) => ({ key: v, value: v, label: v }));
          break;
        case 'action':
          data = await dispatch(searchActivityLogAction({ action: keyword, pagination }));
          options = data.actions.map((v) => ({ key: v, value: v, label: v }));
          break;
        case 'ownerName':
        default:
          data = await dispatch(searchActivityLogOwner({ ownerName: keyword, pagination }));
          options = data.owners.map((v) => ({ key: v.id, value: v.id, label: v.name }));
          break;
      }
      return { options, pagination: data.pagination };
    };

  const selectOption =
    (optionType: 'operatorName' | 'resourceType' | 'action' | 'ownerName' | 'resourceId') => (optionValue?: string) => {
      const params: Partial<SearchActivityLogPageRequest> = {};
      const key = (
        {
          operatorName: 'operatorId',
          resourceType: 'resourceType',
          action: 'action',
          ownerName: 'ownerId',
          resourceId: 'resourceId',
        } as const
      )[optionType];
      params[key] = optionValue ? [optionValue] : undefined;
      handleUpdateParams(params);
    };

  const handleSelectTime = useLatestCallback((dates: RangeValue<Dayjs>) => {
    handleUpdateParams({
      startTime: dates?.[0] ? dates[0].toISOString().split('.')[0] + 'Z' : undefined,
      endTime: dates?.[1] ? dates[1].toISOString().split('.')[0] + 'Z' : undefined,
    });
  });

  useEffect(() => {
    dispatch(getActivityLogList({ pageNum: activityLogList.pageNum, pageSize: activityLogList.pageSize }));
  }, []);

  const dataSource = useMemo(() => {
    return activityLogList.getList().map((id) => activityLogMap.mustGetItem(id));
  }, [activityLogList, activityLogMap]);
  return (
    <ActivityLogView
      scroll
      className={cn('moe-pr-0 moe-bg-white', className)}
      header={<PageTitle title="Activity log" />}
    >
      <Form layout="inline" className="!moe-mx-[32px] !moe-min-w-[732px]">
        <FormItem label="Operator">
          <SearchSelect
            getPopupContainer={getScroller}
            placeholder="Select"
            loadMore={fetchOptions('operatorName')}
            onSelect={selectOption('operatorName')}
          />
        </FormItem>
        <FormItem label="Action">
          <SearchSelect
            getPopupContainer={getScroller}
            placeholder="Select"
            loadMore={fetchOptions('action')}
            onSelect={selectOption('action')}
          />
        </FormItem>
        <FormItem label="Resource Type">
          <SearchSelect
            getPopupContainer={getScroller}
            placeholder="Select"
            loadMore={fetchOptions('resourceType')}
            onSelect={selectOption('resourceType')}
          />
        </FormItem>
        <FormItem label="Resource Id">
          <SearchInput placeholder="Input" onSearch={selectOption('resourceId')} />
        </FormItem>
        <FormItem label="Owner">
          <SearchSelect
            getPopupContainer={getScroller}
            placeholder="Select"
            loadMore={fetchOptions('ownerName')}
            onSelect={selectOption('ownerName')}
          />
        </FormItem>
        <FormItem label="Time">
          <StyledRangePicker showTime onChange={handleSelectTime} />
        </FormItem>
      </Form>
      <ActivityLogDetailModal visible={viewDetail !== void 0} onClose={() => setViewDetail(void 0)} id={viewDetail} />
      <ActivityLogTableView>
        <StyledTable
          tableLayout="auto"
          className="!moe-bg-white"
          rowKey={useCallback((log) => log.id, [])}
          dataSource={dataSource}
          loading={activityLogList.isLoading()}
          pagination={usePaginationOptions(activityLogList.total, activityLogList.pageNum, activityLogList.pageSize)}
          onChange={useLatestCallback((pagination: PaginationOptions) => {
            dispatch(getActivityLogList({ pageNum: pagination.current, pageSize: pagination.pageSize }));
          })}
          columns={useMemo(
            (): ColumnsType<ActivityLogModel> => [
              {
                title: 'Operator',
                render: (_, record) => record.operator.name,
              },
              {
                title: 'Action',
                render: (_, record) => record.action,
              },

              {
                title: 'Resource',
                render: (_, record) => record.resource.type,
              },
              {
                title: 'Resource Name',
                render: (_, record) => record.resource.name,
              },

              {
                title: 'Resource ID',
                render: (_, record) => record.resource.id,
              },
              {
                title: 'Owner',
                render: (_, record) => record.owner?.name,
              },
              {
                title: 'Time',
                render: (_, record) => (
                  <Tooltip width={300} overlay={<div>{`UTC time: ${dayjs(record.time).utc().format()}`}</div>}>
                    <span className="!moe-cursor-pointer">{business.formatDateTime(dayjs(record.time))}</span>
                  </Tooltip>
                ),
              },
              {
                title: 'Caused by',
                render: (_, record) =>
                  record.rootActivityLogId ? (
                    <a onClick={() => setViewDetail(record.rootActivityLogId)}>{'#' + record.rootActivityLogId}</a>
                  ) : null,
              },
              {
                title: '',
                render: (_, record) => <a onClick={() => setViewDetail(record.id)}>View</a>,
              },
            ],
            [business],
          )}
        />
      </ActivityLogTableView>
    </ActivityLogView>
  );
});
