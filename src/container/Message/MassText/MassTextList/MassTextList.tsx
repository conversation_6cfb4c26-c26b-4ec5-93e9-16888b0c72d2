import { T_SECOND } from '@moego/reporting';
import { Table, Tag } from '@moego/ui';
import { type ColumnDef } from '@moego/ui/dist/esm/components/Table/Table.types';
import { useSelector } from 'amos';
import React, { type FC, useCallback, useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router';
import { host } from '../../../../config/host/host';
import { Breadcrumb } from '../../../../layout/Breadcrumb';
import { LayoutContainer } from '../../../../layout/LayoutContainer';
import { http } from '../../../../middleware/api';
import { PATH_MASS_TEXT_DETAIL, PATH_MASS_TEXT_LIST, PATH_MESSAGE_CENTER } from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { methodStore } from '../MassTextDetail/MassTextDetail';
import { MassTextListView } from './MassTextList.style';

interface IRowData {
  batchSize: number;
  id: number;
  messageText: string;
  createTime: number;
  businessId: number;
  method: number;
}

interface MassTextListProps {}

const PAGE_SIZE = 10;

export const MassTextList: FC<MassTextListProps> = (props) => {
  const history = useHistory();
  const [business] = useSelector(selectCurrentBusiness);

  const columns: ColumnDef<IRowData, any>[] = [
    {
      header: 'Message content',
      accessorKey: 'messageText',
      cell: (props) => <div className="!moe-break-all ">{props.getValue()}</div>,
      maxSize: 800,
    },
    {
      header: 'Delivery',
      accessorKey: 'method',
      cell: (props) => <Tag label={methodStore[props.getValue()] || 'Unknown'} />,
      size: 88,
    },
    {
      header: 'Total recipients',
      accessorKey: 'batchSize',
      size: 138,
    },
    {
      header: 'Date',
      accessorKey: 'createTime',
      cell: (props) => <span>{business.formatDate(props.getValue() * T_SECOND)}</span>,
      size: 131,
    },
    {
      header: 'Batch ID',
      accessorKey: 'id',
      size: 88,
    },
  ];

  const dataInitial: () => Array<IRowData> = () => {
    const arr: IRowData[] = [];
    return arr;
  };

  const [data, setData] = useState(dataInitial());

  const [pagination, setPagination] = useState<{
    pageIndex: number;
    pageSize: number;
    totalSize: number;
    pageSizeOptions?: number[];
  }>();
  const [loading, setLoading] = useState(false);

  const noMorePage = useRef(false);

  const loadClients = async (targetPage: number, pageSize: number) => {
    setLoading(true);
    const res = await http.request({
      url: host.message.getSendBatchList(),
      method: 'GET',
      data: {
        pageNo: targetPage,
        pageSize: pageSize,
      },
    });

    setLoading(false);
    const {
      code,
      data: { dataList = [], total, end, pageNo },
    } = res || {};
    if (code !== 200) {
      return;
    }
    setData(dataList);

    if (pageNo === 1 && end) {
      // no pagination
      noMorePage.current = true;
    }

    if (noMorePage.current) {
      return;
    }

    const realPagination = {
      ...(pagination || {}),
      pageIndex: targetPage,
      pageSize: pageSize,
      totalSize: total,
      pageSizeOptions: [PAGE_SIZE],
    };
    setPagination(realPagination);
  };

  const handleTableChange = (params: { pageIndex: number; pageSize: number }) => {
    loadClients(params.pageIndex, params.pageSize);
  };

  // const goToNewMassText = useCallback(() => {
  //   history.push(PATH_MASS_TEXT_CREATE.build({ panel: 'select' }));
  // }, []);

  const goToMassTextDetail = useCallback((id: number) => {
    history.push(
      PATH_MASS_TEXT_DETAIL.build({
        id: `${id}`,
      }),
    );
  }, []);

  useEffect(() => {
    loadClients(1, 10);
  }, []);

  return (
    <MassTextListView>
      <LayoutContainer scroll className="moe-bg-white moe-px-[32px] moe-pt-[16px]">
        <Breadcrumb
          list={[
            {
              label: 'Messages',
              route: PATH_MESSAGE_CENTER.build(),
              RoutePath: PATH_MESSAGE_CENTER,
            },
            {
              label: 'Mass message history',
              route: PATH_MASS_TEXT_LIST.build(),
              RoutePath: PATH_MASS_TEXT_LIST,
            },
          ]}
          className="moe-font-manrope moe-font-normal"
        />
        <div className="moe-h2 moe-mt-[16px]">Mass message history</div>

        <div className="table-wrapper">
          <Table
            columns={columns}
            pagination={pagination}
            data={data}
            getRowId={(record) => `${record.id}`}
            isLoading={loading}
            scrollY="calc(100vh - 336px)"
            onRowClick={(record) => goToMassTextDetail(+record.id)}
            onPaginationChange={handleTableChange}
          />
        </div>
      </LayoutContainer>
    </MassTextListView>
  );
};
