import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { type SearchCustomerResult } from '@moego/api-web/moego/api/engagement/v1/calling_api';
import { SearchSelect } from '@moego/fn-components';
import { Avatar } from '@moego/ui';
import React from 'react';
import { Condition } from '../../../../components/Condition';
import { EngagementCallingLogClient } from '../../../../middleware/clients';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';
import { type FilterBarProps, FilterParamsProperty } from '../constants';
import { useHasPermissionMark } from '../hooks/useHasPermissionMark';
import { ActivityStatus } from './ActivityStatus';
import { CallCategory } from './CallCategory';
import { CallDirection } from './CallDirection';
import { CallRecording } from './CallRecording';
import { CallStatus } from './CallStatus';
import { MarkAllButton } from './MarkAllButton';

interface SearchOption {
  label: string;
  value: string;
  icon: React.ReactNode;
  pets: {
    id: string;
    name: string;
    breed: string;
  }[];
}

export const FilterBar = ({
  value,
  onChange,
  markLogResolveStatus,
  handleRemove,
  confirmUnresolvedRange,
}: FilterBarProps) => {
  const { keywords, callDirection, callStatus, callCategory, recordType, activityStatus } = value;
  const hasMarkPermission = useHasPermissionMark();
  const isAllowMarkAsResolvedEnabled = useFeatureIsOn(GrowthBookFeatureList.AllowMarkAsResolved);

  const transformToSelectOption: (data: SearchCustomerResult) => SearchOption[] = (data) => {
    const { callingClients, customers } = data;
    const clientOptions = callingClients.map((client) => ({
      label: client.name,
      value: JSON.stringify({ clientId: client.id }),
      pets: [],
      icon: (
        <Avatar.Client
          name=""
          src=""
          className="moe-w-8px-300 moe-h-8px-300"
          classNames={{
            name: 'moe-font-[700] moe-text-[12px]',
            icon: 'moe-text-[16px] moe-font-bold',
          }}
          color="#f3f3f3"
        />
      ),
    }));
    const customerOptions = customers.map((customer) => ({
      label: customer.firstName + ' ' + customer.lastName,
      value: JSON.stringify({ customerId: customer.id }),
      pets: customer.pets || [],
      description: (
        <Condition if={(customer.pets || []).length > 0}>
          <div className="moe-line-clamp-2">
            {(customer.pets || []).map((pet, idx) => (
              <span
                key={pet.id}
                className="moe-text-tertiary moe-text-[12px] moe-leading-[16px] moe-tracking-[0.24px] moe-font-[564]"
              >
                {pet.name} ({pet.breed})<Condition if={idx < customer.pets.length - 1}>,&nbsp;</Condition>
              </span>
            ))}
          </div>
        </Condition>
      ),

      icon: (
        <Avatar.Client
          name={customer.firstName + ' ' + customer.lastName}
          src={customer.avatarPath}
          className="moe-w-8px-300 moe-h-8px-300"
          classNames={{
            name: 'moe-font-[700] moe-text-[12px]',
            icon: 'moe-text-[16px] moe-font-bold',
          }}
          color={customer?.colorCode || '#f3f3f3'}
        />
      ),
    }));
    return [...clientOptions, ...customerOptions];
  };

  const searchRequest = async (params: string) => {
    return transformToSelectOption(await EngagementCallingLogClient.searchCustomer({ keyword: params }));
  };

  return (
    <div className="moe-pt-[24px] moe-bg-white moe-flex">
      <div className="moe-flex moe-justify-between moe-items-start moe-rounded-m moe-bg-neutral-sunken-0 moe-p-s moe-pl-m moe-w-full">
        <div className="moe-flex moe-gap-[16px] moe-flex-wrap moe-items-center moe-flex-1">
          <SearchSelect
            className="moe-w-[300px]"
            isClearable
            searchRequest={searchRequest}
            debounceWait={500}
            value={keywords}
            onChange={(v: string) => {
              onChange[FilterParamsProperty.Keywords](v);
            }}
            placeholder="Client name, pet name"
          />
          <div className="moe-flex moe-gap-s moe-flex-wrap moe-mr-[16px]">
            <CallDirection
              value={callDirection}
              onChange={onChange[FilterParamsProperty.CallDirection]}
              handleRemove={handleRemove[FilterParamsProperty.CallDirection]}
            />
            <CallStatus
              value={callStatus}
              onChange={onChange[FilterParamsProperty.CallStatus]}
              handleRemove={handleRemove[FilterParamsProperty.CallStatus]}
            />
            <CallCategory
              value={callCategory}
              onChange={onChange[FilterParamsProperty.CallCategory]}
              handleRemove={handleRemove[FilterParamsProperty.CallCategory]}
            />
            <CallRecording
              value={recordType}
              onChange={onChange[FilterParamsProperty.RecordType]}
              handleRemove={handleRemove[FilterParamsProperty.RecordType]}
            />
            <Condition if={isAllowMarkAsResolvedEnabled}>
              <ActivityStatus
                value={activityStatus}
                onChange={onChange[FilterParamsProperty.ActivityStatus]}
                handleRemove={handleRemove[FilterParamsProperty.ActivityStatus]}
              />
            </Condition>
          </div>
        </div>
        <Condition if={hasMarkPermission && isAllowMarkAsResolvedEnabled}>
          <MarkAllButton confirmUnresolvedRange={confirmUnresolvedRange} markLogResolveStatus={markLogResolveStatus} />
        </Condition>
      </div>
    </div>
  );
};
