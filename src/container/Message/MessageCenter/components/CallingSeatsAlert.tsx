import { <PERSON><PERSON>, But<PERSON> } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { useHistory } from 'react-router-dom';
import { Condition } from '../../../../components/Condition';
import { PATH_SETTING_STAFF } from '../../../../router/paths';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { useBizIdReadyEffect } from '../../../../utils/hooks/useBizIdReadyEffect';
import { getTmpCallingSeats } from '../../../settings/Settings/AutoMessageSetting/components/Calling/callingSettingStore/callingSetting.actions';
import { selectEngagementSetting } from '../../../settings/Settings/AutoMessageSetting/components/Calling/callingSettingStore/callingSetting.selectors';

export interface CallingSeatsAlertProps {}

export const CallingSeatsAlert = memo<CallingSeatsAlertProps>(() => {
  const dispatch = useDispatch();
  const [callingSettings, permissions] = useSelector(selectEngagementSetting(), selectCurrentPermissions());
  const canReceiveCall = permissions.has('receiveCalls');
  const canAccessCallingSetting = permissions.has('accessAutoMessageSettings');
  const history = useHistory();

  useBizIdReadyEffect(() => {
    dispatch(getTmpCallingSeats());
  }, []);

  return (
    <Condition if={callingSettings.isSeatsLimitReached && (canAccessCallingSetting || canReceiveCall)}>
      <Alert
        color="warning"
        description={
          canAccessCallingSetting
            ? `The amount of people with the receive call permission has exceeded the seat limit (${callingSettings.seatsLimit} seats). Please ensure there are only ${callingSettings.seatsLimit} people with this permission.`
            : 'The number of staff with call-receiving permissions has exceeded the limit. Please contact your administrator or the owner to adjust the settings.'
        }
        isCloseable
        action={
          canAccessCallingSetting ? (
            <Button
              size="s"
              variant="secondary"
              onPress={() => history.push(PATH_SETTING_STAFF.build({ panel: 'members' }))}
            >
              View details
            </Button>
          ) : null
        }
      />
    </Condition>
  );
});
