import { useDispatch, useSelector } from 'amos';
import { Steps } from 'antd';
import classNames from 'classnames';
import React, { type ReactNode, memo, useEffect, useMemo } from 'react';
import IconIconCardAuthSucceededSvg from '../../../../assets/icon/icon-card-auth-succeeded.svg';
import SvgIconCloseBannerSvg from '../../../../assets/svg/icon-close-banner.svg';
import { Condition } from '../../../../components/Condition';
import { ImgIcon, SvgIcon } from '../../../../components/Icon/Icon';
import { selectCompany } from '../../../../store/company/company.selectors';
import {
  A2PBannerActionMap,
  A2PBannerStepTitleMap,
  A2PStatus,
  type A2PStatusType,
  A2PStatusTypeIconMap,
} from '../../../../store/message/a2p.config';
import {
  getA2PBannerDescAndAction,
  getA2PStatusType,
  useHandleA2PBannerAction,
} from '../../../../store/message/a2p.utils';
import { getMetadataByKey, updateMetadata } from '../../../../store/metadata/metadata.actions';
import { META_DATA_KEY_LIST } from '../../../../store/metadata/metadata.config';
import { selectMetaDataByKey } from '../../../../store/metadata/metadata.selectors';
import { selectCurrentStaff } from '../../../../store/staff/staff.selectors';
import { A2PRegisterProgressView, StyledSteps } from './A2PRegisterProgress.style';

export interface A2PRegisterProgressCommonProps {
  className?: string;
  justify?: 'left' | 'space-between';
  showSteps?: boolean;
}

export interface A2PRegisterProgressProps extends A2PRegisterProgressCommonProps {
  companyId: number;
}

export interface A2PRegisterProgressUIProps extends A2PRegisterProgressCommonProps {
  statusType: A2PStatusType;
  step?: number;
  showTitle: boolean;
  showClose?: boolean;
  desc?: string;
  actionText: string;
  onClick: () => void;
  onClose?: () => void;
}

export const A2PRegisterProgress = memo<A2PRegisterProgressProps>(
  ({ companyId, className = '', justify = 'left', showSteps = true }) => {
    const [company, a2pBannerVisible, staff] = useSelector(
      selectCompany(companyId),
      selectMetaDataByKey<boolean>(META_DATA_KEY_LIST.A2PBannerVisible),
      selectCurrentStaff,
    );
    const dispatch = useDispatch();
    const handleA2PBannerAction = useHandleA2PBannerAction();

    const { a2pStatus, failReason, step, withEin } = company?.businessLine?.a2p || {};

    // isPending: brand reviewing | campaign review
    const isPending = a2pStatus === A2PStatus.Pending;
    const isNoRequired = a2pStatus === A2PStatus.NoRequired;
    const isVerified = a2pStatus === A2PStatus.Verified;
    const statusType = getA2PStatusType(a2pStatus);
    const { desc, action } = getA2PBannerDescAndAction({
      a2pStatus,
      step,
      withEin,
      failReason,
      isExisting: company.IsExistingA2PCompany,
      isStaff: staff.isStaff(),
    });

    useEffect(() => {
      dispatch(getMetadataByKey(META_DATA_KEY_LIST.A2PBannerVisible));
    }, []);

    const handleCloseBanner = () => {
      dispatch(updateMetadata(META_DATA_KEY_LIST.A2PBannerVisible, false));
    };

    const actionText = useMemo(
      // no role distinction when a2p status is verified
      () => (action && (staff.isOwner() || isVerified) ? A2PBannerActionMap.mapLabels[action] : ''),
      [action, staff, isVerified],
    );

    return (
      <Condition if={!isNoRequired && a2pBannerVisible}>
        <A2PRegisterProgressUI
          className={className}
          statusType={statusType}
          justify={justify}
          step={step}
          showSteps={showSteps}
          showTitle={isPending}
          showClose={isVerified}
          desc={desc}
          actionText={actionText}
          onClick={() => handleA2PBannerAction(action)}
          onClose={handleCloseBanner}
        />
      </Condition>
    );
  },
);

export const A2PRegisterProgressUI = memo<A2PRegisterProgressUIProps>(
  ({
    className,
    statusType,
    justify,
    step = 1,
    showSteps,
    showTitle,
    showClose,
    desc,
    actionText,
    onClick,
    onClose,
  }) => {
    return (
      <A2PRegisterProgressView type={statusType} className={className}>
        <ImgIcon src={A2PStatusTypeIconMap.mapLabels[statusType]} width={16} className="moe-mr-[8px] moe-mt-[1px]" />
        <div
          className={`moe-flex moe-flex-1 moe-leading-[16px] ${
            justify === 'left'
              ? 'moe-items-end'
              : 'moe-justify-between moe-items-center max-[750px]:moe-flex-col max-[750px]:moe-justify-end max-[750px]:moe-items-end'
          }`}
        >
          <div>
            <Condition if={showTitle}>
              <div className="moe-flex">
                <span className="moe-flex-shrink-0 moe-text-xs moe-text-[#333] moe-font-bold">
                  {A2PBannerStepTitleMap.mapLabels[step]}
                </span>
                {showSteps && (
                  <StyledSteps
                    current={step - 1}
                    size="small"
                    className="moe-ml-[4px]"
                    progressDot={(iconDot: ReactNode, { index }: { index: number }) => {
                      if (index < step - 1) {
                        return <ImgIcon src={IconIconCardAuthSucceededSvg} width={12} />;
                      }
                      return iconDot;
                    }}
                  >
                    <Steps.Step />
                    <Steps.Step />
                    <Steps.Step />
                  </StyledSteps>
                )}
              </div>
            </Condition>
            {desc ? <span className="moe-text-[12px] moe-font-medium moe-text-[#333]">{desc}</span> : null}
          </div>
          {actionText ? (
            <span
              className={classNames(
                `moe-flex-shrink-0 moe-ml-[8px] moe-text-[12px] moe-font-bold moe-cursor-pointer ${
                  statusType === 'error' ? 'moe-text-[#d0021b]' : 'moe-text-brand'
                }`,
                {
                  'max-[750px]:moe-mr-[-24px]': showClose,
                  'max-[750px]:moe-mt-[4px]': justify === 'space-between',
                },
              )}
              onClick={onClick}
            >
              {actionText}
            </span>
          ) : null}
        </div>
        {showClose && (
          <SvgIcon
            onClick={onClose}
            src={SvgIconCloseBannerSvg}
            size={16}
            className="moe-ml-[16px] max-[750px]:moe-ml-[12px]"
          />
        )}
      </A2PRegisterProgressView>
    );
  },
);
