import { cn } from '@moego/ui';
import { Divider as AntdDivider } from 'antd';
import React from 'react';

export function Divider({ type = 'horizontal', className }: { type?: 'horizontal' | 'vertical'; className?: string }) {
  return (
    <AntdDivider
      type={type}
      className={cn(
        '!moe-m-0 !moe-border-divider',
        {
          '!moe-h-auto': type === 'vertical',
          '!moe-w-full': type === 'horizontal',
        },
        className,
      )}
    />
  );
}
