import { Condition, Heading, Tabs } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { HeaderBack } from '../../../../../components/Breadcrumb/HeaderBack';
import {
  PATH_PAYMENT_CASH_DRAWER,
  PATH_PAYMENT_CASH_DRAWER_HISTORY_PANEL,
  PaymentCashDrawerHistoryPanel,
} from '../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useRouteQueryV2 } from '../../../../../utils/RoutePath';
import { useCashDrawerBlocked } from '../../hooks/useCashDrawerBlocked';
import { CashAdjustmentHistory } from './components/CashAdjustmentHistory';
import { ReconcileHistory } from './components/ReconcileHistory';

const DEFAULT_PANEL = PaymentCashDrawerHistoryPanel.Reconcile;

export const CashDrawerHistory = memo(() => {
  const history = useHistory();
  const { panel } = useRouteQueryV2(PATH_PAYMENT_CASH_DRAWER_HISTORY_PANEL);
  const [business] = useSelector(selectCurrentBusiness);
  const { value: isCashDrawerBlocked, isLoading: isCashDrawerBlockedLoading } = useCashDrawerBlocked();

  useEffect(() => {
    if (isCashDrawerBlockedLoading) return;
    if (isCashDrawerBlocked) {
      history.replace(PATH_PAYMENT_CASH_DRAWER.build());
      return;
    }
    if (!panel) {
      history.replace(PATH_PAYMENT_CASH_DRAWER_HISTORY_PANEL.build({ panel: DEFAULT_PANEL }));
      return;
    }
  }, [isCashDrawerBlockedLoading, isCashDrawerBlocked, panel]);

  const handleChange = ((key: PaymentCashDrawerHistoryPanel) => {
    history.push(PATH_PAYMENT_CASH_DRAWER_HISTORY_PANEL.build({ panel: key }));
  }) as React.ComponentProps<typeof Tabs>['onChange'];

  return (
    <div className="moe-px-l moe-py-[20px] moe-min-w-[1376px]">
      <HeaderBack
        title={<Heading size="2">Cash drawer history of {business.businessName}</Heading>}
        path={PATH_PAYMENT_CASH_DRAWER.build()}
        className="moe-mb-[20px]"
      />

      <Condition if={!isCashDrawerBlocked}>
        <Tabs onChange={handleChange} selectedKey={panel} defaultSelectedKey={DEFAULT_PANEL}>
          <Tabs.Item key={PaymentCashDrawerHistoryPanel.Reconcile} label="Reconcile history">
            <ReconcileHistory />
          </Tabs.Item>
          <Tabs.Item key={PaymentCashDrawerHistoryPanel.Adjustment} label="Cash adjustment">
            <CashAdjustmentHistory />
          </Tabs.Item>
        </Tabs>
      </Condition>
    </div>
  );
});

CashDrawerHistory.displayName = 'CashDrawerHistory';
