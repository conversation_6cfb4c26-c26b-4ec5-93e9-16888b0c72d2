import ImageThemeDefaultPng from '../../../assets/image/theme-default.png';
import { type CustomThemeProps, type RenderBtnProps, renderDefaultBtn, renderDefaultPreviewBtn } from './common';

export const createDefaultTheme = (): CustomThemeProps => {
  return {
    title: 'Default',
    img: ImageThemeDefaultPng,
    renderBtn: (props: RenderBtnProps) => (props.isPreview ? renderDefaultPreviewBtn(props) : renderDefaultBtn(props)),
  };
};
