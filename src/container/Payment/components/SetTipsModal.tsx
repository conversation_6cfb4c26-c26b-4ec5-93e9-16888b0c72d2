/*
 * @since 2020-12-07 18:51:59
 * <AUTHOR> <<EMAIL>>
 */

import { isNormal } from '@moego/finance-utils';
import { AlertDialog, FocusScope, Markup, Text } from '@moego/ui';
import { captureException } from '@sentry/browser';
import { useAsyncEffect } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import classNames from 'classnames';
import { isNil } from 'lodash';
import React, { memo, useEffect, useState } from 'react';
import { AmountInput } from '../../../components/AmountInput';
import { Button } from '../../../components/Button/Button';
import { toastApi } from '../../../components/Toast/Toast';
import { MAX_STRIPE_PAYMENT_AMOUNT } from '../../../config/host/const';
import { type BusinessRecord } from '../../../store/business/business.boxes';
import { AmountChangeType } from '../../../store/grooming/grooming.boxes';
import { getSmartTipConfig } from '../../../store/payment/actions/private/payment.actions';
import {
  PreferTipType,
  SmartTipEnable,
  TipType,
  type PartialTipMap,
  type SmartTipConfig,
} from '../../../store/payment/payment.boxes';
import { selectBusinessCustomTippingConfig, selectSmartTipConfig } from '../../../store/payment/payment.selectors';
import { useRefObject } from '../../../utils/hooks/hooks';
import { useBool } from '../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import { toFixed } from '../../../utils/utils';
import { CustomTheme, NO_HIGHLIGHT } from '../utils/theme';
import { SetTipsModalView } from './SetTipsModal.style';

export interface SetTipsModalProps {
  className?: string;
  visible: boolean;
  amount: number;
  maxTipsAmount?: number;
  initialTipAmount?: number;
  business: BusinessRecord;
  onSubmit: (amount: number, value: number, type: AmountChangeType) => Promise<void>;
  onClose: () => void;
  /** Whether to also call onClose after calling onSubmit. */
  closeOnSubmit?: boolean;
  zIndex?: number;
}

export interface TipItem {
  value: number;
  percentage?: number;
  label: string;
  prefer: boolean;
}

const getTipItem = (
  baseAmount = 0,
  tipMap: PartialTipMap,
  tipType: number,
  key: 'low' | 'medium' | 'high',
  business: BusinessRecord,
) => {
  const { amountConfig, percentageConfig } = tipMap || {};
  const byPercent = tipType === TipType.PERCENT;
  let value = 0;
  let label = '';
  if (byPercent) {
    if (!percentageConfig) throw new Error('empty percentageConfig');
    value = toFixed((baseAmount * percentageConfig[key]) / 100);
    label = `${business.formatAmount(value)} (${percentageConfig[key]}%)`;
  } else {
    if (!amountConfig) throw new Error('empty amountConfig');
    value = toFixed(amountConfig[key]);
    label = business.formatAmount(value);
  }

  return {
    value,
    percentage: byPercent ? percentageConfig?.[key] : undefined,
    label,
    prefer: false,
  } as TipItem;
};

const calculateSmartTipList = (amount: number, business: BusinessRecord, smartTipConfig: SmartTipConfig) => {
  const stripeEnabled = business.preferStripe();
  const { enable, threshold } = smartTipConfig;
  if (stripeEnabled && !isNil(enable)) {
    try {
      const useUpperSetting =
        (enable === SmartTipEnable.OPEN && amount >= +threshold) || enable === SmartTipEnable.CLOSED;
      if (useUpperSetting) {
        return [
          {
            ...getTipItem(amount, smartTipConfig.upperTip, smartTipConfig.upperTipType, 'low', business),
            prefer: smartTipConfig.upperPreferredTip === PreferTipType.LOW,
          },
          {
            ...getTipItem(amount, smartTipConfig.upperTip, smartTipConfig.upperTipType, 'medium', business),
            prefer: smartTipConfig.upperPreferredTip === PreferTipType.MEDIUM,
          },
          {
            ...getTipItem(amount, smartTipConfig.upperTip, smartTipConfig.upperTipType, 'high', business),
            prefer: smartTipConfig.upperPreferredTip === PreferTipType.HIGH,
          },
        ] as TipItem[];
      } else {
        return [
          {
            ...getTipItem(amount, smartTipConfig.lowerTip, smartTipConfig.lowerTipType, 'low', business),
            prefer: smartTipConfig.lowerPreferredTip === PreferTipType.LOW,
          },
          {
            ...getTipItem(amount, smartTipConfig.lowerTip, smartTipConfig.lowerTipType, 'medium', business),
            prefer: smartTipConfig.lowerPreferredTip === PreferTipType.MEDIUM,
          },
          {
            ...getTipItem(amount, smartTipConfig.lowerTip, smartTipConfig.lowerTipType, 'high', business),
            prefer: smartTipConfig.lowerPreferredTip === PreferTipType.HIGH,
          },
        ] as TipItem[];
      }
    } catch (error) {
      captureException(error);
    }
  }

  // 非 smart tip / smart tip 出错兜底
  return [15, 20, 25].map((p) => ({
    value: toFixed((amount * p) / 100),
    label: `${business.formatAmount((amount * p) / 100)} (${p}%)`,
    prefer: false,
  }));
};

export const SetTipsModal = memo<SetTipsModalProps>(
  ({ className, visible, amount, business, onSubmit, onClose, initialTipAmount, ...rest }) => {
    const { maxTipsAmount = Infinity, closeOnSubmit = true, zIndex } = rest;
    const [customTipAmount, setCustomTipAmount] = useState(initialTipAmount);
    const [tipList, setTipList] = useState([] as TipItem[]);
    const dispatch = useDispatch();
    const [smartTipConfig, customTippingConfig] = useSelector(
      selectSmartTipConfig(),
      selectBusinessCustomTippingConfig(),
    );
    const [highlightKey, setHighlightKey] = useState(NO_HIGHLIGHT);
    const tipMask = useBool(true);
    const showTipMask = tipMask.value && !initialTipAmount;
    const { theme, headerText, buttonColor, buttonBorderColor, noTipColor, bgColor, titleColor, iconColor } =
      customTippingConfig;
    const {
      renderBtn,
      renderBottom,
      mainContainerStyle,
      modalContainerStyle,
      modalContainerClassName,
      getBtnContainerStyle,
      customTipClassName,
      customTipStyle,
      noTipClassName,
      renderInputIcon,
    } = CustomTheme.mapLabels[theme]?.() || {};

    useAsyncEffect(async () => {
      if (!isNormal(smartTipConfig.id)) {
        await dispatch(getSmartTipConfig());
      }
    }, [smartTipConfig]);

    useEffect(() => {
      setCustomTipAmount(initialTipAmount);
      const tipList = calculateSmartTipList(amount, business, smartTipConfig);
      setTipList(tipList);
      // 没有 default custom tip amount 时，才选择 prefer 项。
      if (!initialTipAmount) setHighlightKey(tipList.find((tip) => tip.prefer)?.value ?? NO_HIGHLIGHT);

      if (visible) {
        tipMask.as(true);
      }
    }, [visible, smartTipConfig, business]);

    const handleSubmit = useSerialCallback(async (amount: number, value: number, type: AmountChangeType) => {
      const submit = async () => {
        await onSubmit(amount, value, type);
        closeOnSubmit && onClose();
      };

      if (amount > maxTipsAmount) {
        AlertDialog.open({
          title: 'Confirm tip amount',
          content: (
            <Text variant="regular" className="moe-text-primary">
              Are you sure to tip&nbsp;
              <Markup variant="regular" className="moe-inline-block moe-text-information">
                {business.formatAmount(amount)}
              </Markup>
              {/* @text-lint ignore */}
              &nbsp;to this order?
            </Text>
          ),
          onConfirm: () => submit(),
          confirmText: 'Confirm',
          cancelText: 'Back and edit',
        });

        return;
      }
      await submit();
    });

    const handleSubmitRate = (amount: number) => {
      reportData(ReportActionName.MoeGoPayTippingRate);
      handleSubmit(amount, amount, AmountChangeType.Amount);
    };

    const handleSubmitCustom = () => {
      if (!customTipAmount || customTipAmount < 0) {
        toastApi.error('Please input a valid tip amount!');
        return;
      }
      if (customTipAmount > MAX_STRIPE_PAYMENT_AMOUNT) {
        toastApi.error('Tip amount is too large.');
        return;
      }
      handleSubmit(customTipAmount, customTipAmount, AmountChangeType.Amount);
      reportData(ReportActionName.MoeGoPayTippingCustom);
    };

    const handleNoTip = () => {
      handleSubmit(0, 0, AmountChangeType.Amount);
      reportData(ReportActionName.MoeGoPayTippingNoTip);
    };

    const inputRef = useRefObject<HTMLInputElement>();
    const isInputFocused = useBool(false);
    const btnContainerStyle = getBtnContainerStyle?.(iconColor) || {};

    return (
      <SetTipsModalView
        className={classNames(className, 'set-tip-modal', modalContainerClassName || '')}
        visible={visible}
        width="350px"
        onClose={onClose}
        loading={handleSubmit.isBusy()}
        bodyStyle={{
          backgroundColor: bgColor,
          ...modalContainerStyle,
        }}
        closeColor={iconColor}
        wrapperStyle={zIndex === undefined ? {} : { zIndex }}
      >
        {renderBottom?.()}
        <div className="title" style={{ color: titleColor }}>
          {headerText ?? ''}
        </div>
        <div className="desc" style={{ color: titleColor }}>
          Based on service total: {business.formatAmount(amount)}
        </div>
        <div
          className="content"
          style={{
            marginTop: 24,
            minHeight: 358,
            ...mainContainerStyle,
          }}
        >
          <div className="!moe-w-full !moe-rounded-full" style={btnContainerStyle || {}}>
            {tipList?.map((item) =>
              renderBtn?.({
                key: item.value,
                text: item.label,
                amount: item.value,
                percentage: item.percentage,
                fill: highlightKey === item.value,
                onClick: () => handleSubmitRate(item.value),
                onMouseOver: () => setHighlightKey(item.value),
                onMouseLeave: () => setHighlightKey(NO_HIGHLIGHT),
                buttonBorderColor,
                buttonColor,
                business,
              }),
            )}
          </div>
          <FocusScope contain restoreFocus>
            <div
              className={classNames('custom-tip !moe-bg-no-repeat !moe-bg-contain', customTipClassName)}
              style={customTipStyle || {}}
            >
              {renderInputIcon?.(!showTipMask && isInputFocused.value && highlightKey === NO_HIGHLIGHT)}
              <AmountInput
                ref={inputRef}
                className="value-input"
                business={business}
                value={customTipAmount}
                onChange={(e) => setCustomTipAmount(e)}
                onFocus={isInputFocused.open}
                onBlur={isInputFocused.close}
              />
              <Button
                className="add-btn"
                btnType="primary"
                buttonRadius="circle"
                onClick={handleSubmitCustom}
                style={{
                  backgroundColor: buttonColor,
                }}
              >
                Add
              </Button>
              {showTipMask && (
                <div
                  className="tip-btn-mask"
                  onClick={() => {
                    tipMask.close();
                    setHighlightKey(NO_HIGHLIGHT);
                    inputRef.current?.focus();
                    isInputFocused.open();
                    // 完整选中金额，方便用户快捷输入
                    const value = inputRef.current?.value || '';
                    const endPos = value.length > 1 ? value.length : 1;
                    inputRef.current?.setSelectionRange(1, endPos);
                  }}
                >
                  Custom tip amount
                </div>
              )}
            </div>
            <div className={classNames('no-tip', noTipClassName)} style={{ color: noTipColor }} onClick={handleNoTip}>
              No tip
            </div>
          </FocusScope>
        </div>
      </SetTipsModalView>
    );
  },
);
