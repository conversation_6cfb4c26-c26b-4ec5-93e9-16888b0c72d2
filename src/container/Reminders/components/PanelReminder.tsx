import { Popover, Spin } from 'antd';
import React, { type FC, type ReactNode, useContext, useEffect, useState } from 'react';
import IconIconAlarmGreenSvg from '../../../assets/icon/icon-alarm-green.svg';
import IconIconArrowRightGreySvg from '../../../assets/icon/icon-arrow-right-grey.svg';
import IconIconGiftGreenSvg from '../../../assets/icon/icon-gift-green.svg';
import IconIconRefreshGreenSvg from '../../../assets/icon/icon-refresh-green.svg';
import SvgQuestionCircleOutlinedSvg from '../../../assets/svg/question-circle-outlined.svg';
import { SvgIcon } from '../../../components/Icon/Icon';
import { WithPricingEnableUpgrade } from '../../../components/Pricing/WithPricingComponents';
import { host } from '../../../config/host/host';
import { http } from '../../../middleware/api';
import { type PricingPermissionKey } from '../../../store/company/company.boxes';
import { PanelAppointment } from '../Appointment/Appointment';
import { PanelBirthday } from '../Birthday/Birthday';
import { PanelRebook } from '../Rebook/Rebook';
import { PanelRepeat } from '../Repeat/Repeat';
import { useItemRoute } from '../useItemRoute';
import { ReminderContext } from '../utils';

interface RowReminderItemProps {
  id: string;
  num: number;
  /** 处理点击 Item 方法，返回 true 就跳过后面内容 */
  handleClickReminderItem?: (id: PanelItem['id']) => boolean;
  parentType?: string;
}

export type TPanelComponent = () => ReactNode;
export const UnknownPanel: TPanelComponent = () => <div />;

export interface PanelItem {
  id: 'appointment' | 'rebook' | 'birthday' | 'repeat'; // unique param
  component: TPanelComponent;
  title: string;
  permission?: PricingPermissionKey;
}

export const PANEL_ITEMS: PanelItem[] = [
  { id: 'appointment', component: () => <PanelAppointment />, title: 'Appointment reminder' },
  { id: 'rebook', component: () => <PanelRebook />, title: 'Rebook reminder' },
  { id: 'birthday', component: () => <PanelBirthday />, title: 'Birthday reminder' },
  {
    id: 'repeat',
    component: () => <PanelRepeat />,
    title: 'Repeat expiry reminder',
    permission: 'expiryReminder',
  },
];

export const RowReminderItem: FC<RowReminderItemProps> = (props) => {
  const { id, num, parentType, handleClickReminderItem } = props;
  const reminderContext = useContext(ReminderContext);
  const { handleItemRoute } = useItemRoute();

  interface IRowObj {
    icon: string;
    description: React.ReactNode;
    title: string;
    permission?: PricingPermissionKey;
    tooltip?: React.ReactNode;
  }

  const rowObject: IRowObj = {
    icon: `${IconIconAlarmGreenSvg}`,
    description: <p className="row-reminder-item-desc"></p>,
    title: '',
  };

  const panelItem = PANEL_ITEMS.find((item) => item.id === id);
  if (!panelItem) {
    return <></>;
  }

  switch (panelItem.id) {
    case 'appointment':
      rowObject.title = 'Appointment reminder';
      rowObject.description = (
        <p className="row-reminder-item-desc">
          You have <span>{num}</span> unconfirmed appoinments
        </p>
      );
      break;
    case 'rebook':
      rowObject.title = 'Rebook reminder';
      rowObject.description = (
        <p className="row-reminder-item-desc">
          <span>{num}</span> clients need rebook in the next 30 days
        </p>
      );
      rowObject.tooltip = (
        <Popover
          overlayStyle={{ maxWidth: 300 }}
          placement="top"
          content="Reminders for client who need to rebook based on grooming frequency in pet profile"
        >
          <SvgIcon
            src={SvgQuestionCircleOutlinedSvg}
            className="!moe-w-[16px] !moe-h-[16px] !moe-ml-[6px] !moe-cursor-pointer !moe-mb-[2px]"
          />
        </Popover>
      );
      break;
    case 'birthday':
      rowObject.icon = `${IconIconGiftGreenSvg}`;
      rowObject.title = 'Birthday reminder';
      rowObject.description = (
        <p className="row-reminder-item-desc">
          <span>{num}</span> pets will celebrate birthday in the next 30 days
        </p>
      );
      break;
    case 'repeat':
      rowObject.icon = `${IconIconRefreshGreenSvg}`;
      rowObject.title = 'Repeat expiry reminder';
      rowObject.description = (
        <p className="row-reminder-item-desc">
          You have <span>{num}</span> client's appointment needs to be renewed.
        </p>
      );
      rowObject.permission = panelItem.permission;
      break;
    default:
      break;
  }

  const handleClickRow = () => {
    if (parentType === 'navBar' && handleClickReminderItem) {
      const isReturn = handleClickReminderItem(panelItem.id);
      if (isReturn) {
        return;
      }
    }

    // 去除 appointment 和 birthday 的跳转，年底再优化下线
    if (id === 'appointment' || id === 'birthday') {
      return handleItemRoute(id);
    }
    reminderContext.onToggleTab(id);
  };

  const content = (
    <div className="row-reminder-item" onClick={handleClickRow}>
      <div className="row-reminder-item-icon">
        <img src={rowObject.icon} alt="" />
      </div>
      <div className="row-reminder-item-content">
        <div className="row-reminder-item-title !moe-flex !moe-items-center">
          {rowObject.title}
          {rowObject.tooltip}
        </div>
        {rowObject.description}
      </div>
      <img src={IconIconArrowRightGreySvg} alt="" className="right-arrow" />
    </div>
  );

  return rowObject.permission ? (
    <WithPricingEnableUpgrade permission={rowObject.permission}>{content}</WithPricingEnableUpgrade>
  ) : (
    content
  );
};

interface PanelReminderProps {
  handleClickReminderItem?: RowReminderItemProps['handleClickReminderItem'];
  parentType?: string;
}

export const PanelReminder: FC<PanelReminderProps> = (props) => {
  const { handleClickReminderItem, parentType } = props;

  interface IPanelCell {
    id: string;
    num: number;
  }

  const panelCellsInitial: IPanelCell[] = PANEL_ITEMS.map((item) => ({
    id: item.id,
    num: 0,
  }));
  const [panelCells, setPanelCells] = useState(panelCellsInitial);
  const [loaidng, setLoading] = useState(false);

  const handleSetNums = () => {
    // TODO: api data
    setLoading(true);
    http
      .request({
        url: host.reminders.getSummary(),
        method: 'GET',
        query: {
          appointmentReminderType: 2, // 1 查询最近7天内需要发送reminder的预约 2 最近七天的预约
        },
      })
      .then(({ data }) => {
        const newPanelCells = [...panelCells];
        newPanelCells.forEach((item: IPanelCell) => {
          if (item.id !== 'appointment') {
            item.num = data[item.id];
          } else {
            item.num = data?.unconfirmedAppointment;
          }
        });
        setPanelCells(newPanelCells);
        setLoading(false);
      })
      .catch((err) => {
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    handleSetNums();
  }, []);

  return (
    <Spin tip="Loading..." spinning={loaidng}>
      <div className="panel-reminder-wrapper">
        {panelCells.map((item, index) => (
          <RowReminderItem
            key={index}
            id={item.id}
            num={item.num}
            handleClickReminderItem={handleClickReminderItem}
            parentType={parentType}
          />
        ))}
      </div>
    </Spin>
  );
};
