import { Heading, Text, cn } from '@moego/ui';
import { chunk, debounce } from 'lodash';
import ResizeObserver, { type OnResize } from 'rc-resize-observer';
import React from 'react';
import { Condition } from '../../../components/Condition';
import { useBool } from '../../../utils/hooks/useBool';
import { DraggableContainerProvider } from '../DraggableContainer.context';
import { usePlaygroupContext } from '../Playgroup.context';
import { DayViewSortableContainer } from './components/DayViewSortableContainer';

const MinScreenWidth = 768;
// 一行展示的group数量
const GroupCountPerRow = 2;

export const PlaygroupDayView = () => {
  const filled = useBool(false);
  const { fetchData, playgroupsData } = usePlaygroupContext();

  const handleResize: OnResize = debounce(() => {
    if (filled.value !== window.innerWidth < MinScreenWidth) {
      filled.toggle();
    }
  }, 200);

  return (
    <ResizeObserver onResize={handleResize}>
      <DraggableContainerProvider>
        <div className="moe-relative moe-mt-8px-200">
          <Condition if={fetchData?.isBusy()}>
            <div className="moe-absolute moe-inset-0 moe-w-full moe-h-full moe-opacity-90 moe-z-10 moe-cursor-wait" />
          </Condition>

          {chunk(playgroupsData, GroupCountPerRow).map((arr, index) => (
            <div
              key={`${arr?.[0]?.groupId}-${index}`}
              className={cn('moe-flex moe-flex-wrap', {
                'moe-gap-[24px]': !filled.value,
              })}
            >
              {arr.map(({ petPlaygroups, groupId, playgroupsDetails }) => (
                <div
                  key={groupId}
                  className={cn('moe-flex-1 moe-mb-8px-300', {
                    'moe-max-w-[calc(50%-12px)]': !filled.value,
                    'moe-min-w-[80%]': filled.value,
                  })}
                >
                  <div className="moe-flex moe-mb-[20px]">
                    <Heading size={4}>{playgroupsDetails?.name}</Heading>
                    <Text variant="regular" className="moe-ml-8px-100 moe-flex">
                      Pets:{' '}
                      <Text
                        variant="regular"
                        className={cn({
                          'moe-text-warning moe-font-bold':
                            playgroupsDetails?.petNumber > playgroupsDetails?.maxPetCapacity,
                        })}
                      >
                        {playgroupsDetails?.petNumber}
                      </Text>
                      /{playgroupsDetails?.maxPetCapacity}
                    </Text>
                  </div>
                  <DayViewSortableContainer
                    key={groupId}
                    items={petPlaygroups}
                    groupId={groupId}
                    colorCode={playgroupsDetails?.colorCode}
                  />
                </div>
              ))}
            </div>
          ))}
        </div>
      </DraggableContainerProvider>
    </ResizeObserver>
  );
};
