import { type DragEndEvent, type DragMoveEvent, type DragStartEvent } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { type SetState } from 'ahooks/lib/useSetState';
import { cloneDeep } from 'lodash';
import { useDebounceCallback } from '../../../utils/hooks/useDebounceCallback';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import { ViewType, usePlaygroupContext } from '../Playgroup.context';
import { DEBOUNCE_DELAY } from '../PlaygroupWeekView/dragConfig';
import { type DragState } from '../types';
import { findActiveItem, findContainerIndex } from '../utils';
import { useReschedule } from './useReschedule';

export const useDragHandlers = (setDragState: SetState<DragState>, dragState: DragState) => {
  const { viewType, playgroupsData, setPlaygroupsData } = usePlaygroupContext();
  const { originActiveContainerIndex, initialData } = dragState;
  const handleReschedule = useReschedule();

  const handleDragStart = (event: DragStartEvent) => {
    if (!event) {
      return;
    }

    reportData(
      viewType === ViewType.Day ? ReportActionName.PlaygroupDayViewDrag : ReportActionName.PlaygroupWeekViewDrag,
    );

    setDragState({
      activeItem: findActiveItem(playgroupsData, event.active.id as string),
      originActiveContainerIndex: findContainerIndex(playgroupsData, event.active.id as string),
      initialData: playgroupsData,
    });
  };

  const handleDragOver = useDebounceCallback((event: DragMoveEvent) => {
    const { active, over } = event;
    const overId = over?.id;
    const activeId = active?.id;

    if (!overId) {
      return;
    }

    const activeContainerIndex = findContainerIndex(playgroupsData, activeId);
    let overContainerIndex = findContainerIndex(playgroupsData, overId);

    // 如果容器中没有元素，此时获取到的 over 为容器本身，则通过容器特有的 groupId 来获取容器索引
    if (over?.data.current?.type === 'container') {
      overContainerIndex = playgroupsData.findIndex((item) => item.groupId === overId);
    }

    // handleDragOver 的监听主要针对跨容器的拖拽行为，如果当前没有跨容器，则不进行后面的处理，避免不必要的渲染
    if (activeContainerIndex === overContainerIndex) {
      return;
    }

    const activeItems = playgroupsData[activeContainerIndex].petPlaygroups;
    const overItems = playgroupsData[overContainerIndex].petPlaygroups;
    const activeItem = activeItems.find((item) => item.petPlaygroupId === active.id);
    const overIndex = overItems.findIndex((item) => item.petPlaygroupId === over?.id);
    const newIndex = overIndex >= 0 ? overIndex : overItems.length + 1;

    if (activeItem) {
      // 将activeContainer里删除拖拽元素，在overContainer中添加拖拽元素
      const newGroups = cloneDeep(playgroupsData);
      newGroups[activeContainerIndex] = {
        ...newGroups[activeContainerIndex],
        petPlaygroups: activeItems.filter((item) => {
          return item.petPlaygroupId !== active.id;
        }),
      };
      newGroups[overContainerIndex] = {
        ...newGroups[overContainerIndex],
        petPlaygroups: [
          ...newGroups[overContainerIndex].petPlaygroups.slice(0, newIndex),
          activeItem,
          ...newGroups[overContainerIndex].petPlaygroups.slice(
            newIndex,
            newGroups[overContainerIndex].petPlaygroups.length,
          ),
        ],
      };
      setPlaygroupsData(newGroups);
    }
  }, DEBOUNCE_DELAY);

  const handleDragEnd = (event: DragEndEvent, apptDrawerVisible?: boolean) => {
    const { over, active } = event;
    const overId = over?.id;
    const activeId = active?.id;
    const activeContainerIndex = findContainerIndex(playgroupsData, activeId);
    let overContainerIndex = findContainerIndex(playgroupsData, overId);
    const activeItems = playgroupsData[activeContainerIndex].petPlaygroups;
    const isContainer = over?.data.current?.type === 'container';

    // 如果容器中没有元素，此时获取到的 over 为容器本身，则通过容器特有的 groupId 来获取容器索引
    if (isContainer) {
      overContainerIndex = playgroupsData.findIndex((item) => item.groupId === overId);
    }

    // 没有找到拖拽前后的容器或者拖拽前后的容器没变
    if (
      activeContainerIndex < 0 ||
      overContainerIndex < 0 ||
      !overId ||
      !activeItems ||
      originActiveContainerIndex === overContainerIndex
    ) {
      resetActiveData();
      setPlaygroupsData(initialData);
      return;
    }

    const { petPlaygroups: overItems, groupId } = playgroupsData[overContainerIndex];
    const overIndex = isContainer
      ? overItems.length - 1
      : overItems.findIndex((item) => item.petPlaygroupId === overId);
    const activeIndex = activeItems.findIndex((item) => item.petPlaygroupId === activeId);

    if (!overItems) {
      return;
    }

    const newGroups = cloneDeep(playgroupsData);
    newGroups[overContainerIndex] = {
      ...newGroups[overContainerIndex],
      petPlaygroups: arrayMove(overItems, activeIndex, overIndex),
    };
    setPlaygroupsData(newGroups);
    handleReschedule({
      groupId,
      activeId: activeId as string,
      overIndex,
      activeContainerIndex,
      dragState,
      playgroupsData,
      apptDrawerVisible,
      resetActiveData,
    });
  };

  const handleDragCancel = () => {
    resetActiveData();
  };

  const resetActiveData = () => {
    setDragState({
      activeItem: undefined,
      originActiveContainerIndex: undefined,
    });
  };

  return {
    handleDragStart,
    handleDragOver,
    handleDragEnd,
    handleDragCancel,
  };
};
