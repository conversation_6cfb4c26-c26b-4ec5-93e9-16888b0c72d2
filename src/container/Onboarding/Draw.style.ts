import { Drawer } from 'antd';
import styled from 'styled-components';
import { Z_INDEX_ANTD_POPOVER } from '../../style/_variables';

export const DrawContainer = styled(Drawer)`
  z-index: ${Z_INDEX_ANTD_POPOVER + 1};
  .ant-drawer-content-wrapper {
    width: 480px !important;
  }
`;

export const WelcomeBanner = styled.div`
  display: flex;
  border-radius: 8px;
  background: linear-gradient(111deg, #ffeab5 19.19%, #fe9da2 97.46%);
`;

export const PowerUpBanner = styled(WelcomeBanner)`
  background: linear-gradient(101deg, #90ffaf -26.99%, #aaeefd 94.47%);
`;

export const Section = styled.section`
  margin-top: 32px;
`;

export const SectionTitle = styled.div`
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 700;
  line-height: 22px;
  color: #f96b18;
`;
