import { type FNK_OrderV1Model } from '@moego/finance-web-kit';
import { Condition, Heading, cn } from '@moego/ui';
import React, { memo } from 'react';
import { LayoutDivider } from '../../TakePaymentDrawer/components/Layout/LayoutDivider';
import { LocalServiceItemGroupByPet } from './LocalServiceItemGroupByPet';

interface ServiceListAssignedStaffProps {
  order: FNK_OrderV1Model;
  className?: string;
  staffOptionList: { label: string; value: number }[];
}

export const ServiceListAssignedStaff = memo<ServiceListAssignedStaffProps>((props) => {
  const { order, className, staffOptionList } = props;
  const { orderItemsGroupedByPet } = order.getServiceInfoGroupByPet();

  if (!orderItemsGroupedByPet.length) {
    return null;
  }

  return (
    <div className={cn('moe-flex moe-flex-col', className)}>
      <Heading size="6" className={'moe-text-tertiary moe-mb-m'}>
        Services & add-ons
      </Heading>
      {orderItemsGroupedByPet.map((item, index) => {
        return (
          <div>
            <Condition if={index > 0}>
              <LayoutDivider className="moe-my-m" />
            </Condition>

            <LocalServiceItemGroupByPet
              key={item.petInfo.petId}
              order={order}
              isEditable={false}
              petInfo={item.petInfo}
              serviceList={item.serviceList}
              staffOptionList={staffOptionList}
            />
          </div>
        );
      })}
    </div>
  );
});
