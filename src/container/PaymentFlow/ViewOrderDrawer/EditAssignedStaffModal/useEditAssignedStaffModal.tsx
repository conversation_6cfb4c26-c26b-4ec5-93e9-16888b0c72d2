import { useLatestCallback } from '@moego/finance-utils';
import { type FNK_OrderV1Model } from '@moego/finance-web-kit';
import { toast } from '@moego/ui';
import React, { useCallback, useRef } from 'react';
import { OrderClient } from '../../../../middleware/clients';
import { useFloatableHost } from '../../../../utils/hooks/useFloatableHost';
import { EditAssignedStaffModal } from './EditAssignedStaffModal';
import { type EditStaffContextModel } from './EditStaff.context';
import { getEditStaffParams } from './EditStaff.utils';

export const useEditAssignedStaffModal = (options: { orderId: number }) => {
  const { mountModal } = useFloatableHost<boolean>();
  const optionsRef = useRef<FNK_OrderV1Model | null>(null);
  const handleSave = useLatestCallback(async (staffAssignMap: EditStaffContextModel['staffAssignMap']) => {
    if (!optionsRef.current) {
      return;
    }

    const data = getEditStaffParams(optionsRef.current, staffAssignMap);
    await OrderClient.editStaffCommission({
      orderId: options.orderId + '',
      editStaffCommissionItems: data,
    });
    toast({
      type: 'success',
      title: 'Update successfully.',
    });
  });

  return useCallback(async (order: FNK_OrderV1Model) => {
    optionsRef.current = order;
    const { promise, closeFloatable: closeModal } = mountModal(
      <EditAssignedStaffModal
        order={order}
        onClose={() => {
          closeModal?.();
        }}
        onSave={async (data) => {
          await handleSave(data);
          closeModal?.(true);
        }}
      />,
    );
    return promise;
  }, []);
};
