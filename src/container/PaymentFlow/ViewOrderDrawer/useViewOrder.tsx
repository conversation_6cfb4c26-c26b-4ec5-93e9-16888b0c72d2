import { useSerialCallback } from '@moego/finance-utils';
import React from 'react';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import { type ViewInvoiceDrawerProps, ViewOrderDrawer } from './ViewOrderDrawer';

/**
 * 打开 view receipts 抽屉
 * 这里命名是历史原因，之前还没有明确叫 receipts，所以叫 view order
 */
export const useViewOrder = () => {
  const { mountDrawer } = useFloatableHost();

  const openViewOrderDrawer = useSerialCallback((props: ViewInvoiceDrawerProps) => {
    const { closeFloatable: closeModal, promise } = mountDrawer(({ visible, zIndex }) => {
      return (
        <ViewOrderDrawer
          {...{
            ...props,
            onClose() {
              props.onClose?.();
              closeModal();
            },
            zIndex,
            isOpen: visible,
          }}
        />
      );
    });
    return promise;
  });

  return {
    openViewOrderDrawer,
  };
};
