import { useSerialCallback } from '@moego/finance-utils';
import { Button } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useImperativeHandle } from 'react';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { memoForwardRef } from '../../../../utils/react';
import { PaymentActionName } from '../../../../utils/reportData/payment';
import { type OrderContextModel } from '../../hooks/OrderContext';
import { useInvoiceReinventReport } from '../../hooks/useInvoiceReinvent.report';
import { SplitTipsList } from '../components/SplitTipsEditor/SplitedTipsList';
import { useEditSplitTipsModal } from '../components/SplitTipsEditor/useEditSplitTipsModal';
import { useOrderSplitTipsInfo } from '../components/SplitTipsEditor/useOrderSplitTipsInfo';

export interface TipsSplitedInfoRef {
  refreshSplitTips: () => void;
}

export const TipsSplitedInfo = memoForwardRef<TipsSplitedInfoRef, { order: OrderContextModel['order'] }>(
  (props, outerRef) => {
    const { order } = props;
    const { splitTipsList, isLoading, splitInfo, getSplitTips } = useOrderSplitTipsInfo({
      order,
      mode: 'amount-always',
    });
    const [permissions] = useSelector(selectCurrentPermissions());
    const openSplitTipsModal = useEditSplitTipsModal();
    const reportPaymentData = useInvoiceReinventReport();
    const handleEdit = useSerialCallback(async () => {
      reportPaymentData(PaymentActionName.InvoiceTipSplit, { orderId: order.id, isExtraOrder: order.isExtraOrder });

      const result = await openSplitTipsModal({
        splitTipsInfo: splitInfo!,
        order,
      });

      if (result) {
        getSplitTips();
      }
    });

    useImperativeHandle(outerRef, () => ({
      refreshSplitTips: getSplitTips,
    }));

    if (isLoading || !splitTipsList.length || !permissions.has('manageInvoiceTipSplit')) {
      return null;
    }

    return (
      <div className="moe-bg-neutral-sunken-0 moe-px-s moe-py-xs moe-rounded-m">
        <SplitTipsList bold splitTipsList={splitTipsList} />
        <Button variant="tertiary" className="moe-mt-xs" onPress={handleEdit}>
          Edit tip split
        </Button>
      </div>
    );
  },
);
