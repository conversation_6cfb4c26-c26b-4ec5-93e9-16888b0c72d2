import { AppointmentStatus } from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import { useSerialCallback } from '@moego/finance-utils';
import { type GenericOrderDetail, type IGeneralOrderDetailV2, assertModel, RealmType } from '@moego/finance-web-kit';
import { Alert, Button, Empty } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useEffect } from 'react';
import { type FinanceKit } from '../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { memoForwardRef } from '../../../../utils/react';
import { useTicketActions } from '../../../Appt/modules/ApptDetailDrawer/hooks/useTicketActions';
import { selectApptInfo } from '../../../Appt/store/appt.selectors';
import { useInvoiceReinvent } from '../../hooks/useInvoiceReinvent';
import { ListStyledSpin } from '../components/ListStyledSpin';
import { useViewOrderDrawerContext } from '../ViewOrderDrawer.context';
import { GenericListItem } from './OrderListItem/GenericListItem';
interface OrderListSubpageProps {
  visible: boolean;
  loading: boolean;
  orderList: GenericOrderDetail<typeof FinanceKit.model>[];
}

export interface OrderListSubpageRef {}

export const OrderListSubpage = memoForwardRef<OrderListSubpageRef, OrderListSubpageProps>((props) => {
  const { visible, orderList, loading } = props;
  const { overpaidDepositOrderDetail, onRefundOverpaidDeposit } = useViewOrderDrawerContext();
  const handleRefundDeposit = useSerialCallback(() => onRefundOverpaidDeposit?.());
  const [business] = useSelector(selectCurrentBusiness());
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  const normalOrderV2 = orderList.find((orderDetail) => {
    const { order } = orderDetail;
    return assertModel(order, RealmType.OrderV2);
  }) as IGeneralOrderDetailV2<typeof FinanceKit.model>;
  const sourceId = normalOrderV2?.order.sourceId ?? '0';
  const [apptInfo] = useSelector(selectApptInfo(sourceId));
  const isCancelled = apptInfo?.appointmentStatus === AppointmentStatus.CANCELED;

  const { refreshTicket } = useTicketActions(Number(sourceId));
  useEffect(() => {
    if (isNormal(sourceId)) {
      refreshTicket();
    }
  }, [sourceId]);

  if (!visible) {
    return null;
  }

  if (loading) {
    return <ListStyledSpin isLoading />;
  }

  if (!orderList.length) {
    return <Empty title={isNewOrderV4Flow ? 'No receipt' : 'No invoice'} description="" className="moe-mt-[80px]" />;
  }

  return (
    <div className="moe-h-full moe-flex moe-flex-col moe-gap-m">
      {!!overpaidDepositOrderDetail && (
        <Alert
          isBordered
          isRounded
          isCloseable={false}
          color="warning"
          description={`${business.formatMoney(overpaidDepositOrderDetail?.order.overPaidAmount)} deposit overpaid.`}
          action={
            <Button variant="secondary" size="s" isLoading={handleRefundDeposit.isBusy()} onPress={handleRefundDeposit}>
              Refund overpaid deposit
            </Button>
          }
        />
      )}

      {orderList.map((item) => (
        <GenericListItem key={item.order.id} orderDetail={item} isCancelledAppt={isCancelled} />
      ))}
    </div>
  );
});
