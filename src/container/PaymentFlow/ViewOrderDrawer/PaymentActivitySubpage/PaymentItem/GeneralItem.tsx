import { RefundMode } from '@moego/api-web/moego/models/order/v1/refund_order_enums';
import {
  type FNK_OrderPaymentModel,
  type FNK_OrderPromotionModel,
  type IGeneralOrderDetail,
  type IGeneralOrderDetailV2,
  assertModel,
  FNK_OrderPaymentStatusEnum,
  RealmType,
} from '@moego/finance-web-kit';
import { Heading, Text, Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useMemo } from 'react';
import { type FinanceKit } from '../../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { LayoutCard } from '../../../TakePaymentDrawer/components/Layout/LayoutCard';
import { RefundByPaymentButton } from './RefundByPaymentButton';

export interface IGeneralItemProps {
  payment: FNK_OrderPaymentModel;
  order:
    | IGeneralOrderDetailV2<typeof FinanceKit.model>['order']
    | IGeneralOrderDetail<typeof FinanceKit.model>['order'];
  promotions: FNK_OrderPromotionModel[];
  className?: string;
  handleRefunded: (invoiceId: number) => void;
}

export const GeneralItem = memo((props: IGeneralItemProps) => {
  const { payment, order, handleRefunded } = props;
  const [business, permissions] = useSelector(selectCurrentBusiness(), selectCurrentPermissions());
  const canProcessRefund = permissions.has('canProcessRefund');
  const isRefundVisible = payment.paymentStatus === FNK_OrderPaymentStatusEnum.PAID;
  const isOverpaidDeposit = assertModel(order, RealmType.OrderV2) && order.isOverPaid && order.isDepositOrder;

  const disableReason = useMemo(() => {
    if (!canProcessRefund) {
      return 'Please request “Can process refund” permission from the owner';
    }
    if (payment.refundableTotalAmount.valueOf() <= 0) {
      return 'All payments have already been refunded: No further refunds are allowed';
    }
    if (isOverpaidDeposit) {
      return '';
    }
    if (!order.refundableModes.includes(RefundMode.BY_PAYMENT)) {
      return 'Item has been refunded: Further payment refunds are not allowed.';
    }
    return null;
  }, [payment.refundableTotalAmount, order.refundableModes, isOverpaidDeposit, canProcessRefund]);

  return (
    <LayoutCard border className="moe-px-s moe-py-m">
      <div className={'moe-flex moe-justify-between'}>
        <Heading size={'5'}>
          <span>{FNK_OrderPaymentStatusEnum.mapLabels[payment.paymentStatus]?.label ?? ''}</span>
          <span className={'moe-text-primary'}> by {payment.paymentMethodDisplayName}</span>
        </Heading>
        <Text variant={'regular-short'} className="moe-flex-shrink-0 moe-ml-xs">
          <span className={'moe-text-primary'}>{business.formatMoney(payment.totalAmount)}</span>
        </Text>
      </div>
      <Text variant={'small'} className={'moe-text-tertiary moe-mt-[4px]'}>
        {business.formatDateTime(Number(payment.createTime) * T_SECOND)}
      </Text>
      {isRefundVisible ? (
        <Tooltip isDisabled={!disableReason} content={disableReason} side="bottom">
          <section className="moe-inline-block moe-mt-[8px]">
            <RefundByPaymentButton
              order={order}
              isOverpaidDeposit={isOverpaidDeposit}
              isDisabled={!!disableReason}
              item={payment}
              onRefunded={() => {
                handleRefunded(Number(order.id));
              }}
            />
          </section>
        </Tooltip>
      ) : null}
    </LayoutCard>
  );
});
