import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import { usePermissionCheck } from '../../../../components/GuardRoute/WithPermission';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { selectApptInfo } from '../../../Appt/store/appt.selectors';

export function useAdvancedCapabilities(appointmentId: string, canSetItems: boolean) {
  const [permissions, { serviceItemTypes }] = useSelector(selectCurrentPermissions, selectApptInfo(appointmentId));
  const canEditAppt = permissions.has('canAdvancedEditTicket');
  const canAddProduct = usePermissionCheck({
    permissions: 'viewProductSale',
  });

  const isCareTypeSupportAdd = serviceItemTypes.every((v) => v !== ServiceItemType.EVALUATION);

  const canAdd = canSetItems && canEditAppt && isCareTypeSupportAdd;
  return {
    // 在目前的实现中，service & add-ons 会回写到 appt，因此受 editAppt 权限控制，此处收拢对于 appt 权限的依赖，
    canAddServiceOrAddOns: canAdd,
    canEditServiceOrAddOns: canAdd,
    canAddProduct: canSetItems && canAddProduct,
  };
}
