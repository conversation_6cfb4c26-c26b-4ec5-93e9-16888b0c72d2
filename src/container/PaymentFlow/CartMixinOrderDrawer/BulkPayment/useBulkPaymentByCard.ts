import {
  PaymentMethodMethodType,
  PaymentModelPaymentType,
} from '@moego/api-web/moego/models/payment/v2/payment_models';
import { useDispatch } from 'amos';
import { bulkPayOrder } from '../../../../store/PaymentFlow/order.actions';
import { type FlattenBulkPaymentParams } from './BulkPayment.types';
import { getBulkPaymentParams } from './BulkPayment.utils';

export const useBulkPaymentByCard = () => {
  const dispatch = useDispatch();
  const payByCard = async (params: FlattenBulkPaymentParams<'card'>) => {
    return await dispatch(
      bulkPayOrder(
        getBulkPaymentParams('card', params, PaymentModelPaymentType.STANDARD, PaymentMethodMethodType.CARD),
      ),
    );
  };

  return {
    payByCard,
  };
};
