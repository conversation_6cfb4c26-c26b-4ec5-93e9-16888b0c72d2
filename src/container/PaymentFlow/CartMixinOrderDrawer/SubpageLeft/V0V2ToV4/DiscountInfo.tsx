import { type GetOrderDetailResult } from '@moego/api-web/moego/api/order/v1/order_api';
import { isNormal } from '@moego/finance-utils';
import { usePromotionExtractorForLegacyOrder } from '@moego/finance-web-kit';
import { useSerialCallback } from '@moego/tools';
import { useRequest } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { getAvailableDiscountListForV0V2ToV4 } from '../../../../../store/discount/discount.actions';
import { type OrderLegacyView } from '../../CartMixinOrderDrawer.types';
import { RightColumnPage } from '../../Columns/RightColumnPage';
import { useDrawerScopeContext } from '../../context/DrawerScopeContext';
import { PromotionInfo } from '../components/Promotion/components/PromotionInfo';

export interface DiscountInfoProps {
  className?: string;
  orderAllDetailView: GetOrderDetailResult | null;
  canSetDiscount: boolean;
  orderLegacyView: OrderLegacyView | null;
  isReady: boolean;
}

export const DiscountInfo = memo<DiscountInfoProps>((props) => {
  const { className, orderAllDetailView, orderLegacyView, canSetDiscount, isReady } = props;
  const { rightColumnPageRouter } = useDrawerScopeContext();
  const [business] = useSelector(selectCurrentBusiness);
  const { extractor } = usePromotionExtractorForLegacyOrder(orderLegacyView);
  const dispatch = useDispatch();
  const orderId = orderAllDetailView?.order.orderDetail.order.id;
  const appliedList = useMemo(() => {
    if (!extractor || !orderAllDetailView) {
      return [];
    }

    return extractor.getDiscountListInV4View(business, orderAllDetailView.order.orderDetail);
  }, [business, extractor, orderAllDetailView]);

  const visible = appliedList.length > 0 || canSetDiscount;
  const { data: discountList = [] } = useRequest(
    async () => {
      return await dispatch(
        getAvailableDiscountListForV0V2ToV4({
          invoiceId: orderId!,
          pagination: { pageNum: 1, pageSize: 10 },
        }),
      );
    },
    {
      ready: isReady && visible && isNormal(orderId),
      refreshDeps: [orderLegacyView?.updateTime],
    },
  );

  const handleEdit = useSerialCallback(async () => {
    rightColumnPageRouter.go(RightColumnPage.V0V2_TO_V4_COMPATIBLE_ADD_DISCOUNT);
  });

  const appliedListSatisfyDisplayItem = useMemo(() => {
    return appliedList.map((item) => ({
      ...item,
      subjectId: '',
      appliedAmount: 0,
      appliedIndex: 0,
    }));
  }, [appliedList]);

  if (!visible) {
    return null;
  }

  return (
    <PromotionInfo
      className={className}
      name="Discount"
      appliedList={appliedListSatisfyDisplayItem}
      hasAvailable={discountList.length > 0}
      isEditable={canSetDiscount}
      onEdit={handleEdit}
    />
  );
});
