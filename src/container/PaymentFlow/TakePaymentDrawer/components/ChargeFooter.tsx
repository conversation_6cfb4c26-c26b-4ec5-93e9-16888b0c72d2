import { firstNormal } from '@moego/finance-utils';
import { OrderChargeType, type TypeofOrderChargeType } from '@moego/finance-web-kit';
import { MajorInfoOutlined } from '@moego/icons-react';
import { Button, Checkbox, Heading, Spin, Text, Tooltip, cn, toast } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { Condition } from '../../../../components/Condition';
import { ApptTestIds } from '../../../../config/testIds/apptDrawer';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { PaymentActionName } from '../../../../utils/reportData/payment';
import { useViewOrder } from '../../ViewOrderDrawer/useViewOrder';
import { useInvoiceReinventReport } from '../../hooks/useInvoiceReinvent.report';
import { useOrderContext } from '../../hooks/useOrderContext';
import { PaymentDrawerCloseType } from '../../interface';
import { useHandlePayment } from '../hooks/take-payment/useHandlePayment';
import { CompleteOrderButton } from './CompleteOrderButton';
import { LayoutRightPageFooter } from './Layout/LayoutRightPageFooter';

export interface IChargeFooterProps {
  isDisabled?: boolean;
}

export const ChargeFooter = (props: IChargeFooterProps) => {
  const { isDisabled: propsDisabled } = props;
  const {
    amounts,
    paymentChannels,
    chargeType,
    module,
    order,
    orderId,
    isExtraCharge,
    paymentSubscriber,
    closeDrawer,
    orderUpdating,
    rightSidePageRouter,
    refreshOrder,
  } = useOrderContext();
  const [business, permissions] = useSelector(selectCurrentBusiness, selectCurrentPermissions());
  const convenienceFees = amounts.convenienceFees;
  const { openViewOrderDrawer } = useViewOrder();
  const reportPaymentData = useInvoiceReinventReport();
  const withPaymentChannel = Object.keys(paymentChannels.attachedChannels).length > 0;
  const displayAmount = amounts.chargeAmountWithConvenienceFee;
  const containerBgColor = withPaymentChannel ? 'moe-bg-brand-subtle' : '';

  const canControlProcessingFee = permissions.has('canControlProcessingFeeInInvoice');

  const { takePayment } = useHandlePayment();

  const handleTakePayment = useSerialCallback(async () => {
    reportPaymentData(PaymentActionName.ChargeNowSubmit, {
      orderId: orderId,
      isExtraOrder: isExtraCharge,
      ctaId: 'payment_modal',
    });

    await takePayment({
      onSuccess(options = {}) {
        const { shouldCloseDrawer = true, shouldToast = true, shouldResetRightSidePage = true } = options;
        if (shouldToast) {
          toast({
            title: getOrderChargeSuccessToast(chargeType),
            type: 'success',
          });
        }
        if (shouldCloseDrawer) {
          closeDrawer(PaymentDrawerCloseType.Paid);
          paymentSubscriber.success.notify();
        } else {
          refreshOrder().finally(() => {
            paymentSubscriber.success.notify();
          });
        }
        if (shouldResetRightSidePage) {
          rightSidePageRouter.reset();
        }

        if (shouldCloseDrawer && chargeType === OrderChargeType.Deposit) {
          openViewOrderDrawer({
            orderId: firstNormal(order.orderRefId, orderId)?.toString(),
            module,
          });
        }
      },
      onCancel() {
        paymentSubscriber.cancel.notify();
      },
    });
  });

  const buttonLoading = handleTakePayment.isBusy() || orderUpdating || amounts.convenienceFees.processingFeeLoading;
  const buttonDisabled =
    buttonLoading || !withPaymentChannel || amounts.chargeAmountWithConvenienceFee <= 0 || !!propsDisabled;
  return (
    <LayoutRightPageFooter className={cn(' moe-flex-col moe-gap-m moe-py-[25px]', containerBgColor)}>
      <Condition if={convenienceFees.processingFeeAvailable}>
        <section className="moe-w-full moe-flex moe-items-center moe-justify-start moe-h-5">
          <Condition if={convenienceFees.processingFeeLoading}>
            <Spin className="moe-ml-xs" size="s" />
          </Condition>
          <Condition if={canControlProcessingFee && !convenienceFees.processingFeeLoading}>
            <Checkbox
              isSelected={convenienceFees.processingFeeEnabled}
              onChange={(isSelected) => {
                amounts.convenienceFees.setProcessingFeeEnabled(isSelected);
              }}
              classNames={{
                base: 'moe-flex-row-reverse moe-gap-xs moe-mr-2',
                label: 'moe-mb-0',
              }}
            ></Checkbox>
          </Condition>
          <Condition if={!convenienceFees.processingFeeLoading}>
            <div className="moe-flex moe-gap-xxs">
              <Text variant="regular-short">{amounts.convenienceFees.customizedFeeName}</Text>
              <Tooltip content="Debit card transactions are prohibited from surcharging." side="top">
                <MajorInfoOutlined className="moe-w-[20px] moe-h-[20px] moe-cursor-pointer moe-text-tertiary" />
              </Tooltip>
            </div>
            <Heading size="5" className="moe-ml-xs">
              {business.formatAmount(convenienceFees.processingFee)}
            </Heading>
          </Condition>
        </section>
      </Condition>
      <section className="moe-w-full moe-flex moe-gap-s">
        <CompleteOrderButton />
        <Button
          variant="primary"
          className="moe-flex-1 moe-h-[48px]"
          isDisabled={buttonDisabled}
          isLoading={buttonLoading}
          onPress={handleTakePayment}
          data-testid={ApptTestIds.ApptPaymentPopupChargeBtn}
        >
          {`Charge now ${business.formatAmount(displayAmount)}`}
        </Button>
      </section>
    </LayoutRightPageFooter>
  );
};

function getOrderChargeSuccessToast(chargeType: TypeofOrderChargeType) {
  if (chargeType === OrderChargeType.Deposit) return 'Deposit collected!';
  if (chargeType === OrderChargeType.NoShow) return 'No-show fee charged successfully!';
  return 'Charged successfully!';
}
