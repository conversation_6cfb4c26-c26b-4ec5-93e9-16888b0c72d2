import { Text } from '@moego/ui';
import React from 'react';
import { type OrderDetails } from '../../../../../store/grooming/grooming.boxes';
import { buildLodgingInfoFromOrderPetDetail } from '../../../../Appt/utils/lodgingDisplayHelpers';

interface ServiceItemLodgingProps {
  petDetail: OrderDetails['items'][number]['petDetails'][number];
}

export const ServiceItemLodging = (props: ServiceItemLodgingProps) => {
  const { petDetail } = props;
  const { hasLodgings, getLodgingsInfoText } = buildLodgingInfoFromOrderPetDetail(petDetail);
  if (!hasLodgings) {
    return null;
  }

  return (
    <Text variant={'small'} className={'moe-text-tertiary'}>
      {getLodgingsInfoText()}
    </Text>
  );
};
