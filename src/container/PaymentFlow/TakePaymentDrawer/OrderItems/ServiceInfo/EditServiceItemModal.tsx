import { useSerialCallback } from '@moego/finance-utils';
import { type FNK_ApptBasedOrderModel, type NormalizedOrderItem } from '@moego/finance-web-kit';
import { Condition, Heading, Modal, LegacySelect as Select } from '@moego/ui';
import dayjs from 'dayjs';
import React, { memo, useMemo, useState } from 'react';
import { AvailableStaffPicker } from '../../../../../components/AvailableStaffPicker/AvailableStaffPicker';
import { isMultipleStaffService } from '../../../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import { useBool } from '../../../../../utils/hooks/useBool';
import { ServicePrice } from '../../../../Appt/components/EditPetServiceList/ServicePrice';
import { useBizStaffList } from '../../../hooks/useBizStaffList';
import { LayoutDivider } from '../../components/Layout/LayoutDivider';
import { RemoveServiceItemPopover } from './RemoveServiceItemPopover';

export interface EditServiceItemProps {
  item: NormalizedOrderItem;
  order: FNK_ApptBasedOrderModel;
  onClose?: () => void;
  onConfirm?: (input: {
    servicePrice: number;
    staffId: number;
    origin: {
      servicePrice: number;
      staffId: number;
    };
  }) => void | Promise<void>;
  onRemove?: () => void | Promise<void>;
  isRemovable?: boolean;
}

export const EditServiceItemModal = memo((props: EditServiceItemProps) => {
  const { item, isRemovable, order, onClose, onRemove, onConfirm } = props;
  const [origin] = useState({
    servicePrice: item.unitPrice,
    staffId: item.petDetail.staffId,
  });
  const confirmPopoverVisible = useBool();
  const [servicePrice, setPrice] = useState(item.unitPrice);
  const [staffId, setStaffId] = useState(item.petDetail.staffId);
  const { staffOptionList } = useBizStaffList();
  const startTime = useMemo(() => {
    const date = order.appointmentInfo.appointmentDate;
    const time = order.appointmentInfo.appointmentStartTime ?? 0;
    return dayjs(date).setMinutes(time);
  }, [order.appointmentInfo.appointmentDate, order.appointmentInfo.appointmentStartTime]);

  const handleClose = useSerialCallback(() => {
    onClose?.();
  });

  const handleRemove = useSerialCallback(async () => {
    await onRemove?.();
  });

  const handleConfirm = useSerialCallback(async () => {
    await onConfirm?.({
      servicePrice,
      staffId,
      origin,
    });
  });

  const petDetail = item.petDetail;
  const loading = handleConfirm.isBusy();
  const isMultiStaff = isMultipleStaffService(petDetail);
  const isChanged = servicePrice !== origin.servicePrice || staffId !== origin.staffId;
  return (
    <Modal
      isOpen
      title={`Edit service for ${petDetail.petName}  (${petDetail.petBreed})`}
      showTertiaryButton={isRemovable}
      tertiaryText={
        <div>
          <RemoveServiceItemPopover
            visible={confirmPopoverVisible.value}
            onVisibleChange={confirmPopoverVisible.as}
            onRemove={handleRemove}
          >
            <span>Remove</span>
          </RemoveServiceItemPopover>
        </div>
      }
      confirmText={'Update'}
      size="s"
      onClose={handleClose}
      onConfirm={handleConfirm}
      onCancel={handleClose}
      onTertiary={confirmPopoverVisible.toggle}
      tertiaryButtonProps={{
        isLoading: handleRemove.isBusy(),
      }}
      confirmButtonProps={{
        isLoading: loading,
        isDisabled: !isChanged,
      }}
    >
      <div className={'moe-mb-m'}>
        <Heading size={'4'} className="moe-mb-s">
          {item.name}
        </Heading>
        <LayoutDivider />
      </div>
      <div className={'moe-flex moe-justify-between moe-gap-s'}>
        <ServicePrice className={'moe-w-1/2'} value={servicePrice} onChange={(v) => setPrice(v ?? 0)} />
        <Condition if={isMultiStaff}>
          <Select
            label="Staff"
            isRequired
            isMultiple={true}
            isDisabled={true}
            options={staffOptionList}
            value={item.petDetail.operationList?.map((s) => s.staffId as number)}
          />
        </Condition>
        <Condition if={!isMultiStaff}>
          <AvailableStaffPicker
            label="Staff"
            value={staffId}
            isRequired
            className="moe-w-1/2"
            originStaffId={origin.staffId}
            start={startTime}
            duration={petDetail.serviceTime}
            onChange={(v) => v && setStaffId(v)}
          />
        </Condition>
      </div>
    </Modal>
  );
});
