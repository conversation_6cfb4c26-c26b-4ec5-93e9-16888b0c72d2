import { useLatestCallback } from '@moego/finance-utils';
import { PaymentChannelType, PrefabPaymentChannel, RealmType } from '@moego/finance-web-kit';
import { Form, Input } from '@moego/ui';
import React, { memo, useState } from 'react';
import { useMount, useUnmount } from 'react-use';
import { FinanceKit } from '../../../../../../service/finance-kit';
import { useOrderContext } from '../../../../hooks/useOrderContext';

export const CheckNumberForm = memo(() => {
  const { pay, paymentChannels } = useOrderContext();
  const [checkNumber, setCheckNumber] = useState('');

  const handleCheckNumberChanged = useLatestCallback((checkNumber: string) => {
    pay.set('checkNumber', checkNumber);
    setCheckNumber(checkNumber);
  });

  useMount(() => {
    paymentChannels.attachPaymentChannel(
      PrefabPaymentChannel.Check,
      FinanceKit.buildModel(RealmType.PaymentChannel, {
        type: PaymentChannelType.Prefab,
        id: PrefabPaymentChannel.Check,
        methodId: PrefabPaymentChannel.Check,
      }),
      // v1 未用到 payload，传空对象，但 payment v2 用到了，此处类型会有问题，所以传 any
      {} as any,
    );
  });

  useUnmount(() => {
    paymentChannels.detachPaymentChannel(PrefabPaymentChannel.Check);
    pay.set('checkNumber', '');
  });

  return (
    <>
      <Form.Label label="Check number" />
      <Input value={checkNumber} onChange={handleCheckNumberChanged} placeholder="Check number" />
    </>
  );
});
