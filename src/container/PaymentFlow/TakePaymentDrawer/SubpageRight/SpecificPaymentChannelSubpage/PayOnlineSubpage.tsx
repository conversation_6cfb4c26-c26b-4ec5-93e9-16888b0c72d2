import React, { memo } from 'react';
import { ChargeFooter } from '../../components/ChargeFooter';
import { LayoutRightPageHeader } from '../../components/Layout/LayoutRightPageHeader';
import { LayoutSubpage } from '../../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../../components/Layout/LayoutSubpageScrollBody';
import { PaymentChannelSubpageLayout } from '../components/PaymentChannelSubpageLayout';
import { PayOnlineForm } from './components/PayOnlineForm';

export const PayOnlineSubpage = memo(() => {
  return (
    <LayoutSubpage>
      <LayoutRightPageHeader title={'Send invoice to pay online'} />
      <LayoutSubpageScrollBody>
        <PaymentChannelSubpageLayout>
          <PayOnlineForm />
        </PaymentChannelSubpageLayout>
      </LayoutSubpageScrollBody>
      <ChargeFooter />
    </LayoutSubpage>
  );
});
