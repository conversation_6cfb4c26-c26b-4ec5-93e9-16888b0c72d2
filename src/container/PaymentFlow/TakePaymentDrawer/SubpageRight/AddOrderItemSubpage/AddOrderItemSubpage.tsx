import { useLatestCallback } from '@moego/finance-utils';
import { OrderChargeType, OrderItemType } from '@moego/finance-web-kit';
import { Spin, Tabs, toast } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { useMemo } from 'react';
import { type ServicePriceDurationInfo } from '../../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { useOrderAppointmentInfo } from '../../../hooks/useOrderAppointmentInfo';
import { useOrderContext } from '../../../hooks/useOrderContext';
import { LayoutRightPageHeader } from '../../components/Layout/LayoutRightPageHeader';
import { LayoutSubpage } from '../../components/Layout/LayoutSubpage';
import { useAdvancedCapabilities } from '../../hooks/useAdvancedCapabilities';
import { getPetServiceIdListMap, mergePetServiceIdListMap } from './AddOrderItemSubpage.util';
import { AddOrderItemFooter } from './components/AddOrderItemFooter';
import { type EditServiceForExtraOrderModalProps } from './components/EditServiceForExtraOrderModal';
import { useEditServiceForExtraOrderModal } from './components/useEditServiceForExtraOrderModal';
import { useAddPetServicesInit } from './hooks/useAddPetServicesInit';
import { useAddProductsInit } from './hooks/useAddProductsInit';
import { useBuildPetDetailParams } from './hooks/useBuildPetDetailParams';
import { AddPetServices } from './modules/AddPetServices/AddPetServices';
import {
  AddPetServicesContextProvider,
  useAddPetServicesContext,
} from './modules/AddPetServices/AddPetServices.context';
import { AddProducts } from './modules/AddProducts/AddProducts';
import { AddProductsContextProvider } from './modules/AddProducts/AddProducts.context';
import { type ProductItemInfo } from './modules/AddProducts/AddProducts.types';
import { getProductItemDetail } from './modules/AddProducts/store/orderProducts.actions';
import { productItemInfoMapBox } from './modules/AddProducts/store/orderProducts.boxes';

const AddOrderItemSubpageComponent = () => {
  const dispatch = useDispatch();
  const { customerId, operations, order, orderId, originOrder, chargeType } = useOrderContext();
  const { refreshOrderAppointment } = useOrderAppointmentInfo();
  const { openEditModal } = useEditServiceForExtraOrderModal();
  const { canAddProduct, canAddServiceOrAddOns } = useAdvancedCapabilities();
  const { orderPetList, selectedPetServiceIdList } = useMemo(() => {
    const { serviceListGroupByPet: list2 = [] } = order?.getServiceInfoGroupByPet() || {};
    const { serviceListGroupByPet: list1 = [] } = originOrder?.getServiceInfoGroupByPet() || {};
    const petIdList = [...list1, ...list2].map((s) => {
      return s.petInfo.petId;
    });

    return {
      orderPetList: Array.from(new Set(petIdList)),
      selectedPetServiceIdList: mergePetServiceIdListMap(
        getPetServiceIdListMap(originOrder),
        getPetServiceIdListMap(order),
      ),
    };
  }, [originOrder?.items, order?.items]);

  // init data
  useAddPetServicesInit({
    orderPetList,
    customerId,
  });
  const { loading: productListLoading } = useAddProductsInit();

  /**
   * click event handlers
   */
  const { petId } = useAddPetServicesContext();
  const { buildPetDetailWithNewGroomingService, purePetDetailLeftMatchOneOnly } = useBuildPetDetailParams();
  const save = useLatestCallback(async (serviceId: number, savedInfo: ServicePriceDurationInfo, staffId?: number) => {
    const newPetDetail = await buildPetDetailWithNewGroomingService({
      orderId,
      petId: String(petId),
      serviceId: String(serviceId),
      serviceSavedInfo: savedInfo,
      staffId,
    });

    // TODO: extra charge 不用再计算时间等，这些是否有特殊逻辑需要处理 @GQ
    if (chargeType === OrderChargeType.ExtraCharge) {
      await operations.addServiceAndAddOnItems(chargeType, {
        extraOrderId: orderId + '',
        petDetail: purePetDetailLeftMatchOneOnly(newPetDetail, serviceId),
      });
    } else {
      await operations.addServiceAndAddOnItems(chargeType, newPetDetail);
    }
    // 异步刷新 appt 数据，以备下次
    refreshOrderAppointment(orderId);
  });

  const handlePetServiceClick = useSerialCallback(
    async (
      serviceId: number,
      savedInfo: ServicePriceDurationInfo,
      data: Pick<EditServiceForExtraOrderModalProps, 'pet' | 'service'>,
    ) => {
      if (order.isExtraOrder) {
        openEditModal({
          ...data,
          order,
          onConfirm: async (result) => {
            await save(serviceId, { ...savedInfo, servicePrice: result.servicePrice }, result.staffId);
          },
        });
      } else {
        await save(serviceId, savedInfo);
      }
    },
  );

  const handleProductClick = useSerialCallback(async (productInfo: ProductItemInfo) => {
    // 只能基于当前的单，与origin order单无关
    const existingOrderProductItem = order
      ?.filterItemsByType(OrderItemType.Product)
      ?.find((i) => i.objectId === productInfo.id);

    if (existingOrderProductItem) {
      const newProduct = {
        ...existingOrderProductItem,
        quantity: existingOrderProductItem.quantity + 1,
        sku: productInfo.sku,
        stock: productInfo.stock,
      };
      await operations.updateProductDetail(existingOrderProductItem.id, newProduct);
    } else {
      await operations.addProducts([productInfo]);
    }
    toast({
      type: 'success',
      title: 'Item added',
    });
    // update stock
    dispatch([
      // for instant ui stock update
      productItemInfoMapBox.mergeItem(productInfo.id, {
        stock: productInfo.stock - 1,
      }),
      // fetch the real data asynchrously
      getProductItemDetail(productInfo.id),
    ]);
  });

  // TODO: 增加 barcode scanner 的支持
  return (
    <LayoutSubpage>
      <LayoutRightPageHeader title={'Select item'} />
      <Tabs
        orientation="horizontal"
        className="moe-flex-1"
        classNames={{ panel: 'moe-h-full moe-pt-0', tabList: 'moe-mx-l moe-w-auto' }}
      >
        {canAddServiceOrAddOns ? (
          <Tabs.Item
            label={<div className="moe-text-center">Services & Add-ons</div>}
            isDisabled={!canAddServiceOrAddOns}
          >
            <Spin
              classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
              isLoading={handlePetServiceClick.isBusy()}
            >
              <AddPetServices
                onServiceClick={handlePetServiceClick}
                orderPetList={orderPetList}
                selectedPetServiceIdList={selectedPetServiceIdList}
              />
            </Spin>
          </Tabs.Item>
        ) : (
          (null as any)
        )}
        {canAddProduct ? (
          <Tabs.Item
            label={<div className="moe-text-center">Product</div>}
            classNames={{ base: 'moe-h-full', content: 'moe-h-full' }}
          >
            <Spin
              classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
              isLoading={handleProductClick.isBusy() || productListLoading}
            >
              <AddProducts onProductItemClick={handleProductClick} />
            </Spin>
          </Tabs.Item>
        ) : (
          (null as any)
        )}
      </Tabs>
      <AddOrderItemFooter />
    </LayoutSubpage>
  );
};

export const AddOrderItemSubpage = () => {
  return (
    <AddPetServicesContextProvider>
      <AddProductsContextProvider>
        <AddOrderItemSubpageComponent />
      </AddProductsContextProvider>
    </AddPetServicesContextProvider>
  );
};
