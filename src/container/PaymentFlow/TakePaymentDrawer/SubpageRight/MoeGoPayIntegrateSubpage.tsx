/*
 * @since 2024-11-01 11:41:16
 * <AUTHOR> <EMAIL>
 * @description MoeGo Pay integrate landing page
 */

import { AlertDialog } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React from 'react';
import { useHistory } from 'react-router';
import { EnterMoeGoPaySettingSource, PATH_CREDIT_CARD_SETTING_MOEGO_PAY } from '../../../../router/paths';
import { setApptDetailDrawer } from '../../../../store/calendarLatest/actions/public/calendar.actions';
import { usePaymentVersionInfo } from '../../../../utils/hooks/usePaymentVersionInfo';
import { PaymentActionName } from '../../../../utils/reportData/payment';
import { getPurchaseHardwareLink } from '../../../CardProcessing/CardProcessing.utils';
import { MoeGoPayIntegrateSetup } from '../../../CardProcessing/components/Landing/MoeGoPayIntegrateLanding';
import { MoeGoPayIntegrateSetup as MoeGoPayIntegrateSetupV2 } from '../../../CardProcessingV2/components/Landing/MoeGoPayIntegrateLanding';
import { useInvoiceReinventReport } from '../../hooks/useInvoiceReinvent.report';
import { useOrderContext } from '../../hooks/useOrderContext';
import { PaymentDrawerCloseType } from '../../interface';
import { ChargeFooter } from '../components/ChargeFooter';
import { LayoutRightPageHeader } from '../components/Layout/LayoutRightPageHeader';
import { LayoutSubpage } from '../components/Layout/LayoutSubpage';
import { LayoutSubpageScrollBody } from '../components/Layout/LayoutSubpageScrollBody';
import { selectCurrentStaff } from '../../../../store/staff/staff.selectors';

interface MoeGoPayIntegrateSubpageProps {}

export const MoeGoPayIntegrateSubpage = (props: MoeGoPayIntegrateSubpageProps) => {
  const { orderId, closeDrawer } = useOrderContext();
  const dispatch = useDispatch();
  const history = useHistory();
  const reportPaymentData = useInvoiceReinventReport();
  const { isPaymentV2 } = usePaymentVersionInfo();
  const MoeGoPayIntegrateSetupComp = isPaymentV2 ? MoeGoPayIntegrateSetupV2 : MoeGoPayIntegrateSetup;
  const [staff] = useSelector(selectCurrentStaff());

  return (
    <LayoutSubpage>
      <LayoutRightPageHeader title={'Credit or debit card'} />
      <LayoutSubpageScrollBody className="moe-px-l">
        <MoeGoPayIntegrateSetupComp
          classNames={{
            base: 'moe-pt-8px-400',
          }}
          onPurchaseHardware={() => {
            reportPaymentData(PaymentActionName.PurchaseHardware, {
              orderId,
              cta_id: 'payment_flow',
            });
            window.open(getPurchaseHardwareLink(staff.isEnterpriseRelatedStaff()), '_blank');
          }}
          onStart={() => {
            reportPaymentData(PaymentActionName.MgpIntegrateGetStarted, {
              orderId,
              cta_id: 'payment_flow',
            });

            AlertDialog.open({
              title: 'Set up MoeGo Pay to continue',
              content: 'You’ll be redirected to complete the setup and can return here once it’s done',
              confirmText: 'Set up now',
              cancelText: 'I’ll set up later',
              onClose: () => {
                reportPaymentData(PaymentActionName.MgpIntegrateGetStartedCancel, {
                  orderId,
                });
              },
              onConfirm: () => {
                history.push(
                  PATH_CREDIT_CARD_SETTING_MOEGO_PAY.queried({
                    source: EnterMoeGoPaySettingSource.PaymentFLowIntegrate,
                  }),
                );
                dispatch(setApptDetailDrawer({ visible: false }));
                closeDrawer(PaymentDrawerCloseType.Cancel);
                reportPaymentData(PaymentActionName.MgpIntegrateGetStartedConfirm, {
                  orderId,
                });
              },
            });
          }}
        />
      </LayoutSubpageScrollBody>
      <ChargeFooter />
    </LayoutSubpage>
  );
};
