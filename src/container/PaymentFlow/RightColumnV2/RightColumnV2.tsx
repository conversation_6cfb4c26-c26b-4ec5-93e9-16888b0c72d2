import React, { memo } from 'react';
import { Switch } from '../../../components/SwitchCase';
import { AddOrderItemSubpage } from '../TakePaymentDrawer/SubpageRight/AddOrderItemSubpage/AddOrderItemSubpage';
import { OriginOrderSubpage } from '../TakePaymentDrawer/SubpageRight/OriginOrderSubpage';
import { RefundSubpage } from '../TakePaymentDrawer/SubpageRight/RefundSubpage';
import { SetDiscountSubpage } from '../TakePaymentDrawer/SubpageRight/SetDiscountSubpage';
import { useRefundSubpageSwitch } from '../TakePaymentDrawer/hooks/use-refund-subpage-switch';
import { useOrderContext } from '../hooks/useOrderContext';
import { RightSideSubpage } from '../hooks/useSubpageRouter';
import { MoeGoPayIntegrateSubpage } from './SubpageRight/MoeGoPayIntegrateSubpage';
import { PaymentChannelSubPage } from './SubpageRight/PaymentChannel/PaymentChannelSubpage';
import { CashTenderedSubpage } from './SubpageRight/SpecificPaymentChannelSubpage/CashTenderedSubpage';
import { CheckNumberSubpage } from './SubpageRight/SpecificPaymentChannelSubpage/CheckNumberSubpage';
import { CreditOrDebitCardSubpage } from './SubpageRight/SpecificPaymentChannelSubpage/CreditOrDebitCardSubpage';
import { PayOnlineSubpage } from './SubpageRight/SpecificPaymentChannelSubpage/PayOnlineSubpage';
import { StripeSmartReaderSubpage } from './SubpageRight/SpecificPaymentChannelSubpage/StripeSmartReaderSubpage';
import { SplitPaymentSubpage } from './SubpageRight/SplitPayment';

interface TakePaymentColumnProps {}

export const RightColumnV2 = memo<TakePaymentColumnProps>((props) => {
  const { rightSidePageRouter } = useOrderContext();
  // switch refund subpage logic
  useRefundSubpageSwitch();

  return (
    <div className={'moe-w-[680px] moe-bg-[#fafafa]'}>
      <Switch shortCircuit>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.SET_DISCOUNT)}>
          <SetDiscountSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.DEFAULT)}>
          <PaymentChannelSubPage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.ADD_ORDER_ITEM)}>
          <AddOrderItemSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.CASH_TENDERED)}>
          <CashTenderedSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.CREDIT_OR_DEBIT_CARD)}>
          <CreditOrDebitCardSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.CHECK_NUMBER)}>
          <CheckNumberSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.PAY_ONLINE)}>
          <PayOnlineSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.STRIPE_SMART_READER)}>
          <StripeSmartReaderSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.SPLIT_PAYMENT)}>
          <SplitPaymentSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.REFUND)}>
          <RefundSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.VIEW_ORIGIN_ORDER)}>
          <OriginOrderSubpage />
        </Switch.Case>
        <Switch.Case if={rightSidePageRouter.is(RightSideSubpage.MGP_INTEGRATE_LANDING)}>
          <MoeGoPayIntegrateSubpage />
        </Switch.Case>
      </Switch>
    </div>
  );
});
