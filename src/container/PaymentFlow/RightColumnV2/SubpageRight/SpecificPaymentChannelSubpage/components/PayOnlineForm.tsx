import { useLatestCallback } from '@moego/finance-utils';
import { OrderChargeType, PaymentChannelType, PrefabPaymentChannel, RealmType } from '@moego/finance-web-kit';
import { MajorCopyOutlined, MinorMailOutlined } from '@moego/icons-react';
import { Button, Heading, Input, Spin, Text, cn, toast } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { useMount, useUnmount } from 'react-use';
import PayOnlineEmailChannel from '../../../../../../assets/svg/pay-online-email.svg';
import PayOnlineMessageChannel from '../../../../../../assets/svg/pay-online-message.svg';
import { Condition } from '../../../../../../components/Condition';
import { SvgIcon } from '../../../../../../components/Icon/Icon';
import { ALLOW_TEXT_LENGTH_TWOWAY } from '../../../../../../components/MessageSendBox/components/MessageUtils';
import { FinanceKit } from '../../../../../../service/finance-kit';
import { getGroomingDepositGuid, getGroomingTicketGuid } from '../../../../../../store/grooming/grooming.actions';
import { getTemplateMessagePreview } from '../../../../../../store/message/message.actions';
import { MessageDetailType, TARGET_TYPE_PAY_ONLINE } from '../../../../../../store/message/message.boxes';
import { bodyCount } from '../../../../../settings/Agreement/components/EditTemplate.options';
import { useOrderContext } from '../../../../hooks/useOrderContext';
import { PayOnlineChannelCard } from './PayOnlineChannelCard';

export interface PayOnlineFormRef {
  getPayload: () => {
    message?: string;
    email?: string;
    error?: string;
  };
}

export const PayOnlineForm = memo(() => {
  const { customer, chargeType, order, amounts, paymentChannels } = useOrderContext();
  const dispatch = useDispatch();
  const [selectedChannel, setSelectedChannel] = useState<'message' | 'email'>('message');
  const [messageContent, setMessageContent] = useState('');
  const [email, setEmail] = useState(customer.email);
  const hasProcessingFee = useMemo(
    () => amounts.convenienceFees.processingFeeEnabled,
    [amounts.convenienceFees.processingFeeEnabled],
  );
  const [updatingMessageLink, setUpdatingMessageLink] = useState(false);
  const messageLoading = useMemo(() => {
    return updatingMessageLink && !messageContent;
  }, [updatingMessageLink, messageContent]);

  const getMessageContent = useLatestCallback(async () => {
    setUpdatingMessageLink(true);
    let orderUuid: string;
    if (chargeType === OrderChargeType.Deposit) {
      ({ guid: orderUuid } = await dispatch(
        getGroomingDepositGuid(order.id, amounts.chargeAmountWithoutConvenienceFee, hasProcessingFee),
      ));
    } else {
      ({ guid: orderUuid } = await dispatch(getGroomingTicketGuid(order.id, hasProcessingFee)));
    }
    const content = await dispatch(
      getTemplateMessagePreview(TARGET_TYPE_PAY_ONLINE, order.id, MessageDetailType.Text, {
        groomingId: order.groomingId,
        orderUuid,
      }),
    ).finally(() => {
      setUpdatingMessageLink(false);
    });
    return content;
  });

  const getPayOnlineInfo = useLatestCallback(() => {
    if ((selectedChannel === 'message' && !messageContent) || (selectedChannel === 'email' && !email))
      return {
        error: `Please fill the ${selectedChannel === 'message' ? 'message content' : 'email address'}`,
      };
    return {
      ...(selectedChannel === 'message' ? { message: messageContent } : { email: email }),
    };
  });

  const handleCopyMessageContent = useLatestCallback(() => {
    navigator.clipboard.writeText(messageContent).then(() => {
      toast({
        title: 'Copied to clipboard successfully!',
        type: 'success',
      });
    });
  });

  useEffect(() => {
    getMessageContent().then((content) => {
      if (!messageContent) {
        setMessageContent(content);
      }
    });
  }, [hasProcessingFee]);

  useMount(() => {
    paymentChannels.attachPaymentChannel(
      PrefabPaymentChannel.PayOnline,
      FinanceKit.buildModel(RealmType.PaymentChannel, {
        type: PaymentChannelType.Prefab,
        id: PrefabPaymentChannel.PayOnline,
        methodId: PrefabPaymentChannel.PayOnline,
      }),
      {
        getPayOnlineInfo,
      },
    );
  });

  useUnmount(() => {
    paymentChannels.detachPaymentChannel(PrefabPaymentChannel.PayOnline);
  });

  return (
    <>
      <Heading size="6">Select the channel</Heading>
      <section className="moe-flex moe-gap-s moe-items-center moe-justify-center moe-mt-4">
        <PayOnlineChannelCard
          icon={PayOnlineMessageChannel}
          title="By message"
          className={cn(selectedChannel === 'message' && 'moe-border-brand moe-bg-brand-subtle')}
          onClick={() => setSelectedChannel('message')}
        />
        <PayOnlineChannelCard
          icon={PayOnlineEmailChannel}
          title="By email"
          className={cn(selectedChannel === 'email' && 'moe-border-brand moe-bg-brand-subtle')}
          onClick={() => setSelectedChannel('email')}
        />
      </section>
      <Condition if={selectedChannel === 'message'}>
        <Heading size="6" className="moe-mt-6">
          Send to
        </Heading>
        <section className="moe-flex moe-mt-s moe-h-[48px]">
          <span className="moe-flex moe-items-center moe-justify-center moe-w-[48px] moe-h-[48px] moe-rounded-full moe-bg-neutral-sunken-0">
            <SvgIcon src={PayOnlineMessageChannel} size={24}></SvgIcon>
          </span>
          <section className="moe-flex moe-flex-col moe-flex-1 moe-ml-s moe-justify-between">
            <Heading size="5">{customer.phoneNumber}</Heading>
            <Text variant="regular-short" className="moe-text-tertiary">
              {customer.fullName()}
            </Text>
          </section>
        </section>
        <section className="moe-flex moe-justify-between moe-items-center moe-h-8 moe-mt-6">
          <Heading size="5">Message content</Heading>
          <span className="moe-flex moe-items-center">
            <Button
              variant="tertiary"
              className="moe-ml-xs"
              icon={<MajorCopyOutlined className="moe-h-5 moe-w-5"></MajorCopyOutlined>}
              onPress={handleCopyMessageContent}
            >
              Copy text
            </Button>
          </span>
        </section>
        {messageLoading ? (
          <section className="moe-flex moe-w-full moe-mt-l moe-justify-center moe-items-center">
            <Spin></Spin>
          </section>
        ) : (
          <Input.TextArea
            className="moe-mt-xs"
            value={messageContent}
            onChange={(value) => {
              setMessageContent(value);
            }}
          ></Input.TextArea>
        )}
        <Text variant="small" className="moe-text-tertiary moe-mt-1">
          {bodyCount(messageContent || '', undefined, ALLOW_TEXT_LENGTH_TWOWAY)}
        </Text>
      </Condition>
      <Condition if={selectedChannel === 'email'}>
        <Heading size="6" className="moe-mt-6">
          Send to
        </Heading>
        <section className="moe-flex moe-mt-s moe-h-[48px]">
          <Input
            prefix={<MinorMailOutlined></MinorMailOutlined>}
            placeholder="Email address"
            onChange={(value) => setEmail(value)}
            value={email}
            className="moe-w-full"
          />
        </section>
      </Condition>
    </>
  );
});
