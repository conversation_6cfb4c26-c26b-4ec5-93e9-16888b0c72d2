/*
 * @since 2021-07-19 20:11:50
 * <AUTHOR> <<EMAIL>>
 */

import styled from 'styled-components';

export const TerminalPaymentInfoModalView = styled.section`
  padding-top: 24px;
  padding-bottom: 24px;
  .modal-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      font-weight: 500;
      font-size: 14px;
      line-height: 16px;
      color: #666666;
      .device-name {
        color: #333;
      }
    }
    .title-amount {
      text-align: center;
      font-weight: bold;
      font-size: 24px;
      color: #0091ff;
      margin-top: 4px;
    }
    .prompt {
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
      color: #999999;
      margin-top: 16px;
    }
    .title-count-down {
      display: flex;
      padding: 20px;
      width: 150px;
      height: 80px;
      background: #f2f2f2;
      border-radius: 12px;
      margin-top: 24px;

      font-weight: 700;
      font-size: 36px;
      line-height: 40px;
      color: #666666;
    }
  }
`;
