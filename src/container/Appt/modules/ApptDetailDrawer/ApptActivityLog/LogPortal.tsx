import {
  ComMoegoServerGroomingDtoAppointmentHistoryCancelLogDTOCanceledByType,
  ComMoegoServerGroomingDtoAppointmentHistoryChangeTimeLogDTOChangedByType,
} from '@moego/finance-utils/dist/esm/src/types/openApi/grooming-schema';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useMemo } from 'react';
import SvgAutoAssignSvg from '../../../../../assets/svg/auto-assign.svg';
import { Condition } from '../../../../../components/Condition';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { Switch } from '../../../../../components/SwitchCase';
import { type ActionHistoryDTO } from '../../../../../store/calendarLatest/actions/private/calendar.actions';
import {
  ActivityLogType,
  ActivityLogTypes,
  IncludeTimeChangeType,
} from '../../../../../store/calendarLatest/calendar.types';
import { isValidAutoAssign } from '../../../../../store/createTicket/ticket.utils';
import { customerMapBox } from '../../../../../store/customer/customer.boxes';
import { staffMapBox } from '../../../../../store/staff/staff.boxes';
import { AppointmentStatusLabel } from '../../../../Calendar/latest/ApptCalendar/components/AppointmentStatus';
import { AppointmentStatus } from '../../../../TicketDetail/AppointmentStatus';
import { ClientOperationLog } from './ClientOperationLog';
import { LogDescription } from './LogDescription';
import { LogItem } from './LogItem';
import { SentNotificationStatus } from './SentNotificationStatus';
import { SentViaMethod } from './SentViaMethod';
import { TimeChangeLog } from './TimeChangeLog';

export interface LogPortalProps {
  log: ActionHistoryDTO['actions'][number];
  isLast?: boolean;
}

export const LogPortal = memo<LogPortalProps>(function LogPortal(props) {
  const { log, isLast } = props;
  const actionType = log.actionType as unknown as ActivityLogType;
  const {
    operatorId,
    operateTime,
    changeTimeLog,
    updateStatusLog,
    sendNotificationLog,
    cancelReason,
    notificationUpdateDTO,
    createLogDTO,
    cancelLogDTO,
    clientUpdateLog,
  } = log;
  const [staff, customer] = useSelector(staffMapBox.mustGetItem(operatorId), customerMapBox.mustGetItem(operatorId));
  const config = ActivityLogTypes.mapLabels[actionType];
  const hasTimeChange = IncludeTimeChangeType.includes(actionType);
  const isOnlineBooking = createLogDTO?.isOnlineBooking === true;
  const isAutoAccept = !!createLogDTO?.isAutoAccept;
  const isAutoAssign = isValidAutoAssign(createLogDTO?.autoAssign);
  const isCreate = actionType === ActivityLogType.CREATE;
  const isRepliedByClient = actionType === ActivityLogType.CUSTOMER_REPLY;
  const isSendNotification = actionType === ActivityLogType.SEND_NOTIFICATION;
  const isReschedule = actionType === ActivityLogType.RESCHEDULE;
  const isCheckIn = actionType === ActivityLogType.CHANGE_CHECK_IN_TIME;
  const isCheckOut = actionType === ActivityLogType.CHANGE_CHECK_OUT_TIME;
  const isUpdateStatus = actionType === ActivityLogType.UPDATE_STATUS;
  const isCancelled = actionType === ActivityLogType.CANCEL;
  const isSystemOp = operatorId === 0;
  const isPetAndServiceUpdate = actionType === ActivityLogType.PET_AND_SERVICE_UPDATE;
  const isNotificationStatusUpdate = actionType === ActivityLogType.NOTIFICATION_STATUS_UPDATE;
  const isAutoRollover = actionType === ActivityLogType.AUTO_ROLLOVER;
  const isApptUpdate = actionType === ActivityLogType.APPT_UPDATE;
  const { BY_PET_PARENT_APP, BY_CLIENT_PORTAL } =
    ComMoegoServerGroomingDtoAppointmentHistoryChangeTimeLogDTOChangedByType;
  const { BY_PET_PARENT_APP: CANCELED_BY_PET_PARENT_APP, BY_CLIENT_PORTAL: CANCELED_BY_CLIENT_PORTAL } =
    ComMoegoServerGroomingDtoAppointmentHistoryCancelLogDTOCanceledByType;

  const user = useMemo(() => {
    if (isSystemOp) {
      return {
        firstName: 'System',
        isSystem: true,
      };
    }

    if (isRepliedByClient) {
      return customer;
    }

    if (isCancelled && [CANCELED_BY_PET_PARENT_APP, CANCELED_BY_CLIENT_PORTAL].includes(cancelLogDTO?.canceledByType)) {
      return customer;
    }

    if (isReschedule && [BY_PET_PARENT_APP, BY_CLIENT_PORTAL].includes(changeTimeLog?.changedByType)) {
      return customer;
    }

    if (isApptUpdate) {
      return customer;
    }

    return staff;
  }, [isRepliedByClient, isSystemOp, staff, customer, isCancelled, isReschedule, changeTimeLog, cancelLogDTO]);

  return (
    <Condition if={!!config}>
      {() => (
        <Switch shortCircuit>
          <Switch.Case if={isAutoRollover}>
            <LogItem
              key={operateTime}
              title={
                <Condition if={log?.autoRolloverLog?.target?.name}>
                  <div>Service auto rolled over to {`'${log?.autoRolloverLog?.target.name}'`}</div>
                </Condition>
              }
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              isLast={isLast}
              description={
                <Condition if={log?.autoRolloverLog?.source?.name}>
                  <LogDescription description={`Last service: ${log?.autoRolloverLog?.source.name}`} />
                </Condition>
              }
            />
          </Switch.Case>
          <Switch.Case if={isRepliedByClient}>
            <LogItem
              key={operateTime}
              title={
                <ClientOperationLog
                  messageMethodType={log?.customerReplyLogDTO?.messageMethodType}
                  statusUpdated={log?.customerReplyLogDTO?.statusUpdated}
                  clientReplyTypeEnum={log.customerReplyLogDTO?.clientReplyTypeEnum}
                />
              }
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              followNameExtra=" (Client)"
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isSendNotification}>
            <LogItem
              key={operateTime}
              title={<SentViaMethod sendNotificationLog={sendNotificationLog} />}
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isNotificationStatusUpdate}>
            <LogItem
              key={operateTime}
              title={<SentNotificationStatus notificationUpdateDTO={notificationUpdateDTO} />}
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isUpdateStatus}>
            <LogItem
              key={operateTime}
              title={
                <div className="moe-flex moe-items-center moe-gap-x-[4px]">
                  Status updated to <AppointmentStatusLabel appointmentStatus={updateStatusLog?.newStatus} />
                </div>
              }
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isCheckIn}>
            <LogItem
              key={operateTime}
              title="Check in time changed"
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              description={
                <Condition if={hasTimeChange}>
                  <TimeChangeLog changeTime={changeTimeLog} />
                </Condition>
              }
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isCheckOut}>
            <LogItem
              key={operateTime}
              title="Check out time changed"
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              description={
                <Condition if={hasTimeChange}>
                  <TimeChangeLog changeTime={changeTimeLog} />
                </Condition>
              }
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isReschedule}>
            <LogItem
              key={operateTime}
              title={config.title}
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              description={
                <Condition if={hasTimeChange}>
                  <TimeChangeLog changeTime={changeTimeLog} />
                </Condition>
              }
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isCancelled}>
            <LogItem
              key={operateTime}
              title={
                <div className="moe-flex moe-items-center moe-gap-x-[4px]">
                  Status updated to <AppointmentStatusLabel appointmentStatus={AppointmentStatus.CANCELED} />
                </div>
              }
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              description={<LogDescription description={cancelReason} />}
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isPetAndServiceUpdate}>
            <LogItem
              key={operateTime}
              title={config.title}
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              isLast={isLast}
            />
          </Switch.Case>
          <Switch.Case if={isCreate}>
            <Switch shortCircuit>
              {/* ob auto assign & auto accept 需要自定义 render */}
              <Switch.Case if={isOnlineBooking && isAutoAssign && isAutoAccept}>
                <LogItem
                  key={operateTime}
                  title="Auto accept appointment"
                  date={dayjs(operateTime * T_SECOND)}
                  followNameExtra={
                    <div className="moe-flex moe-items-center">
                      <SvgIcon src={SvgAutoAssignSvg} size={24} color="#9c68ff" />
                      <p className="moe-text-[#333] moe-ml-[4px]">Auto-assign via online booking</p>
                    </div>
                  }
                  isLast={isLast}
                />
              </Switch.Case>
              <Switch.Case else>
                <LogItem
                  key={operateTime}
                  title={config.title}
                  date={dayjs(operateTime * T_SECOND)}
                  followNameExtra={isOnlineBooking ? ' (Online booking)' : undefined}
                  user={isOnlineBooking ? customer : staff}
                  isLast={isLast}
                />
              </Switch.Case>
            </Switch>
          </Switch.Case>
          <Switch.Case if={isApptUpdate}>
            <LogItem
              key={operateTime}
              user={user}
              isLast={isLast}
              title={config.title}
              date={dayjs(operateTime * T_SECOND)}
              description={
                <Condition if={clientUpdateLog?.addedServices?.length}>
                  <LogDescription
                    description={`Service added: ${clientUpdateLog?.addedServices?.map((service) => service.serviceName).join(', ') ?? ''}`}
                  />
                </Condition>
              }
            />
          </Switch.Case>
          <Switch.Case else>
            <LogItem
              key={operateTime}
              title={config.title}
              date={dayjs(operateTime * T_SECOND)}
              user={user}
              description={
                <Condition if={hasTimeChange}>
                  <TimeChangeLog changeTime={changeTimeLog} />
                </Condition>
              }
              isLast={isLast}
            />
          </Switch.Case>
        </Switch>
      )}
    </Condition>
  );
});
