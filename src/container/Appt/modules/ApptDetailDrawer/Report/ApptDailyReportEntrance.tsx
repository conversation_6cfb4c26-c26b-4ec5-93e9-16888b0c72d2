import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { Loading } from '../../../../../components/Loading/Loading';
import { selectAppointmentFeedbackPermission } from '../../../../../store/business/role.selectors';
import { petMapBox } from '../../../../../store/pet/pet.boxes';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { useReportCardUpgrade } from '../../../../settings/GroomingReport/hooks/useReportCardUpgrade';
import { ViewGroomingReportPermissionTip } from '../../../components/ViewGroomingReportPermission';
import { selectApptInfo, selectMainServiceInAppt } from '../../../store/appt.selectors';
import { ReportCardContent } from '../../../../../components/ReportCard/components/ReportCardContent';
import { useDailyReportModal } from '../../../../../components/DailyReport/hook/useReportModal';
import { useLastReport } from './hooks/useLastReport';

interface ApptDailyReportEntranceProps {
  appointmentId: string;
  petId: string;
}

export const ApptDailyReportEntrance = memo((props: ApptDailyReportEntranceProps) => {
  const { appointmentId, petId } = props;
  const { allowReportCardLv1 } = useReportCardUpgrade();
  const isLoading = useBool(false);
  const [hasGroomingReportPermission, pet, mainService, { customerId }] = useSelector(
    selectAppointmentFeedbackPermission(),
    petMapBox.mustGetItem(Number(petId)),
    selectMainServiceInAppt(String(appointmentId)),
    selectApptInfo(String(appointmentId)),
  );

  const openDailyReportModal = useDailyReportModal();

  const { data: lastReport, refetch } = useLastReport({
    petId,
    apptId: appointmentId,
    careType: mainService.serviceItemType,
  });

  const handleOpenModal = useLatestCallback(() => {
    reportData(ReportActionName.CheckDailyReportAtAppointment, {
      mainCareType: mainService.serviceItemType,
    });
    openDailyReportModal({
      show: true,
      petId,
      appointmentId,
      isBoarding: mainService.serviceItemType === ServiceItemType.BOARDING,
      petName: pet.petName,
      customerId,
      afterUpdate: refetch,
    });
  });

  return (
    <ViewGroomingReportPermissionTip>
      {allowReportCardLv1 ? (
        <div onClick={(e) => e.stopPropagation()} className={hasGroomingReportPermission ? '' : 'moe-text-[#ccc]'}>
          <Loading loading={isLoading.value}>
            <div
              onClick={handleOpenModal}
              className="moe-px-[16px] moe-py-[8px] moe-border moe-border-solid moe-border-[#E6E6E6] moe-rounded-[8px] moe-cursor-pointer moe-flex moe-w-full moe-flex-col hover:moe-border-focus-dark moe-relative"
            >
              <div className="moe-flex moe-justify-between moe-w-full moe-items-center">
                <ReportCardContent
                  record={lastReport?.sendRecord?.[0] || lastReport}
                  careType={mainService.serviceItemType}
                />
              </div>
              <div className="!moe-pl-[24px]"></div>
            </div>
          </Loading>
        </div>
      ) : null}
    </ViewGroomingReportPermissionTip>
  );
});
