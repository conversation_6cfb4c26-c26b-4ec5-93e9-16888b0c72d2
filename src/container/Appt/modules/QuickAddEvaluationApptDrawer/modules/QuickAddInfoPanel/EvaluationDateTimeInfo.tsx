import React from 'react';
import { EvaluationDateTime } from '../../components/EvaluationDateTime';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';

export interface EvaluationDateTimeInfoProps {
  className?: string;
  onChange?: (newVal: { date: string; time: number }) => void;
  setIsSaveDisabled?: (v: boolean) => void;
}

export const EvaluationDateTimeInfo: React.FC<EvaluationDateTimeInfoProps> = (props) => {
  const { className, onChange, setIsSaveDisabled } = props;
  const { evaluationInfo } = useEvaluationQuickAdd();

  return (
    <div className={className}>
      <EvaluationDateTime
        value={{
          date: evaluationInfo.startDate,
          time: evaluationInfo.startTime,
        }}
        onChange={onChange}
        setIsSaveDisabled={setIsSaveDisabled}
      />
    </div>
  );
};
