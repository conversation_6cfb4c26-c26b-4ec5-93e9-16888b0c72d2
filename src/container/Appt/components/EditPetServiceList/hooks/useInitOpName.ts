import { useSelector } from 'amos';
import { isString } from 'lodash';
import { type ServiceOperationEntry } from '../../../../../components/ServiceApplicablePicker/types/serviceOperation';
import { selectMultiStaffPreference } from '../../../../../store/createTicket/createTicket.selector';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

export function useResetOpName() {
  const [multiStaffPreference] = useSelector(selectMultiStaffPreference);
  const {
    workPattern: { taskName },
    autoMemorizeWorkPattern,
  } = multiStaffPreference;
  const resetOpName = useLatestCallback(
    (op: ServiceOperationEntry, index: number, serviceName?: string, newServiceName?: string) => {
      const currentOpNameEmpty = !op.operationName?.trim();
      /**
       * serviceName切换的两个原则
       * Memory不生效的情况（默认）: task name = service name (service 切换，task name应该对应切换)
       * Memory生效的情况下
       *  只要 task name = service name, 我们记录的pattern，下次task name应该仍然等于用户当下所选的service name
       *  如果用户编辑过，即task name != service name, 下次task name就完全等于用户这次输入的task name */
      const normalModeRenameTaskName = !autoMemorizeWorkPattern && op.operationName?.trim() === serviceName?.trim();
      let memoModeMatchMemorized = false;
      if (autoMemorizeWorkPattern && taskName[index]) {
        const task = taskName[index];
        memoModeMatchMemorized = isString(task)
          ? // 因为老的数据没有存单个的dirty状态，且有值，说明是自定义的taskName（按之前存的逻辑），则沿用老的taskName，不用更新成新的serviceName
            false
          : task.sameAsServiceName;
      }
      if (normalModeRenameTaskName || memoModeMatchMemorized || currentOpNameEmpty) {
        return {
          ...op,
          operationName: newServiceName?.trim() ?? '',
        };
      }
      // false的话，就是沿用之前填充的taskName，也就是自定义的taskName
      return op;
    },
  );

  return resetOpName;
}
