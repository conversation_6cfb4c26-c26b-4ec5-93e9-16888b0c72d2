import { useSelector } from 'amos';
import { isEmpty, isFinite } from 'lodash';
import { useMemo } from 'react';
import { type ServiceEntry } from '../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { isMultipleStaffService } from '../../../../../components/ServiceApplicablePicker/utils/isMultipleStaffService';
import { selectMultiStaffPreference } from '../../../../../store/createTicket/createTicket.selector';
import { isNormal } from '../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { createBigNum } from '../../../../../utils/utils';
import { ERROR_PRICE_NOT_EQ_ONE, ERROR_PRICE_RATIO_NOT_EQ_ONE } from '../../../../CreateTicket/hooks/useValidate';

export function useCheckServicePrice() {
  const [preference] = useSelector(selectMultiStaffPreference);
  const { isPriceRatio } = preference;

  const checkServicePrice = useLatestCallback((service?: ServiceEntry) => {
    if (!service) {
      return { validate: true, errorMsg: '' };
    }
    const { servicePrice: sPrice, operationList } = service;
    const isMultiStaff = isMultipleStaffService(service);
    if (!isMultiStaff) {
      return { validate: true, errorMsg: '' };
    }
    const servicePrice = createBigNum(sPrice);
    if (isPriceRatio) {
      const someEmpty = operationList!.some((i) => !isFinite(i.priceRatio));
      if (someEmpty) {
        return { validate: false, errorMsg: ERROR_PRICE_RATIO_NOT_EQ_ONE };
      }
      const allPercent = operationList!
        .map((i) => createBigNum(i.priceRatio))
        .reduce((pre, i) => pre.plus(i), createBigNum(0));
      return { validate: allPercent.eq(1), errorMsg: ERROR_PRICE_RATIO_NOT_EQ_ONE };
    }
    const someEmpty = operationList!.some((i) => {
      const price = i.price as string | number;
      return (typeof price === 'string' && price.trim() === '') || (typeof price === 'number' && !isFinite(price));
    });
    if (someEmpty) {
      return { validate: false, errorMsg: ERROR_PRICE_NOT_EQ_ONE };
    }
    const operationCount = operationList!
      .map((i) => createBigNum(i.price))
      .reduce((pre, price) => pre.plus(price), createBigNum(0));
    const validate = operationCount.eq(servicePrice);
    return { validate, errorMsg: ERROR_PRICE_NOT_EQ_ONE };
  });

  return checkServicePrice;
}

export function useCheckEditValidate(petId: number, services: ServiceEntry[]) {
  const checkServicePrice = useCheckServicePrice();

  const disabledSave = useMemo(() => {
    if (!isNormal(petId)) {
      return true;
    }
    if (services.length < 1) {
      return true;
    }
    const someServiceInValidate = services.some((service) => {
      const { serviceId, servicePrice, serviceTime } = service;
      if (!isNormal(serviceId)) {
        return true;
      }
      if (!isFinite(servicePrice) || !isFinite(serviceTime)) {
        return true;
      }
      const priceCheck = checkServicePrice(service);
      if (!priceCheck.validate) {
        return true;
      }
      const isMultiStaff = isMultipleStaffService(service);
      if (isMultiStaff) {
        const { operationList } = service;
        const inValidateOp = operationList.some((operation) => {
          const { staffId, operationName } = operation;
          if (!isNormal(staffId) || isEmpty(operationName)) {
            return true;
          }
          return false;
        });

        return inValidateOp;
      }
      return false;
    });

    if (someServiceInValidate) {
      return true;
    }

    return false;
  }, [petId, services]);

  return {
    disabledSave,
    checkServicePrice,
  };
}
