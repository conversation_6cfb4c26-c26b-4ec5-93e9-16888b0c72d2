import {
  type MultiNightDef,
  type MultiNightDefMultiNightItemDef,
} from '@moego/api-web/moego/models/offering/v1/pricing_rule_defs';
import React from 'react';
import { usePricingRulePriceType } from '../../../../settings/Settings/ServicesSetting/hooks/usePricingRulePriceType';

interface MultiNightItemProps {
  item: MultiNightDefMultiNightItemDef;
}

function MultiNightItem({ item }: MultiNightItemProps) {
  const priceValue = usePricingRulePriceType(item.priceItemDef.priceType, item.priceItemDef.priceValue);

  return (
    <div className="moe-text-small">
      ≥ {item.effectiveNumber} nights, {priceValue} off
    </div>
  );
}

interface MultiNightRuleProps {
  nightDef: MultiNightDef | undefined;
}

export function MultiNightRule({ nightDef }: MultiNightRuleProps) {
  if (!nightDef) {
    return null;
  }
  return (
    <>
      {nightDef.itemDefs.map((item, index) => {
        return <MultiNightItem item={item} key={index} />;
      })}
    </>
  );
}
