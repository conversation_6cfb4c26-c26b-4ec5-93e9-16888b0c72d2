import { useDispatch, useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo } from 'react';
import { useSetState } from 'react-use';
import IconIconBookingCalendarConflictSvg from '../../../../../../assets/icon/icon-booking-calendar-conflict.svg';
import IconIconBookingCalendarSvg from '../../../../../../assets/icon/icon-booking-calendar.svg';
import { Condition } from '../../../../../../components/Condition';
import { Switch } from '../../../../../../components/SwitchCase';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { type BoolState, useBool } from '../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { useUnsavedConfirm } from '../../../../../../utils/hooks/useUnsavedConfirm';
import { type StaffConflictBatchParams, checkStaffConflictBatch } from '../../../../../CreateTicket/hooks/useValidate';
import { ApptEditTime } from '../../../../modules/ApptDetailDrawer/ApptInfoPanel/ApptEditTime/ApptEditTime';
import { StaffSelect } from '../../components/StaffSelect';
import { ticketPreviewInfoMapBox } from '../../store/repeatSeries.boxes';
import { selectTicketRepeatPreviewState } from '../../store/repeatSeries.selectors';
import { type StaffConflictInfoDTO, useMergedConflictStatus } from './RepeatSeriesPreviewItem.options';
import { useCalendarCardControl } from './hooks/useCalendarCardControl';
import { Button, Select, Text, Tooltip } from '@moego/ui';

export interface RepeatSeriesPreviewItemEditRef {
  save: (wait?: boolean) => Promise<boolean>;
  leave: () => Promise<void>;
  close: () => void;
  dirty: boolean;
}

export interface RepeatSeriesPreviewItemEditModeProps {
  itemId: string;
  onClose?: () => void;
  onSave?: () => void;
  hasErrorBool?: BoolState;
  isDaycareMode: boolean;
}

export const RepeatSeriesPreviewItemEditMode = forwardRef<
  RepeatSeriesPreviewItemEditRef,
  RepeatSeriesPreviewItemEditModeProps
>((props, ref) => {
  const { itemId, onClose, hasErrorBool, isDaycareMode } = props;
  const dispatch = useDispatch();

  /**
   * basic state
   */
  const [previewInfo, staffMap, { smartSchedule: isSmartScheduleMode }] = useSelector(
    ticketPreviewInfoMapBox.mustGetItem(itemId),
    staffMapBox,
    selectTicketRepeatPreviewState(),
  );
  const initialEditDate = useMemo(
    () => dayjs(previewInfo.date, DATE_FORMAT_EXCHANGE).setMinutes(previewInfo.startTime),
    [previewInfo.date, previewInfo.startTime],
  );
  const { staffIdList } = previewInfo;
  const isMultiStaff = staffIdList?.length > 1;
  const initStaffId = staffIdList?.[0] || ID_ANONYMOUS;
  const isDirty = useBool();
  const [state, setState] = useSetState<{
    staffConflictInfoList: StaffConflictInfoDTO[];
    editDate: Dayjs;
    staffId: number;
  }>({
    staffConflictInfoList: previewInfo.staffConflictInfoList || [],
    editDate: initialEditDate,
    staffId: initStaffId,
  });
  useEffect(() => {
    const { date, startTime } = previewInfo;
    setState({
      staffConflictInfoList: previewInfo.staffConflictInfoList,
      editDate: dayjs(date, DATE_FORMAT_EXCHANGE).startOf('date').add(startTime, 'minute'),
      staffId: previewInfo.staffIdList?.[0],
    });
  }, [previewInfo.date, previewInfo.startTime, previewInfo.staffConflictInfoList, previewInfo.staffIdList]);

  /**
   * conflict check
   */
  const loading = useBool(false);
  const internalHasErrorBool = useBool();
  const hasError = hasErrorBool || internalHasErrorBool;
  const { getMergedConflictStatus } = useMergedConflictStatus();
  const [isConflict, conflictReason] = useMemo(() => getMergedConflictStatus(state.staffConflictInfoList), [state]);

  useEffect(() => {
    hasError.as(!!isConflict);
  }, [isConflict, hasError]);

  /**
   * calendar control
   */
  const { setDraftPreviewInfo, resetDraftPreviewInfo } = useCalendarCardControl({ itemId, editDate: state.editDate });

  /**
   * user actions
   */
  const checkStaffAndUpdate = useLatestCallback(async (withoutLoading?: boolean) => {
    const { editDate } = state;

    if (!editDate) {
      toastApi.error('Date is required');
      throw new Error('Date is required');
    }

    const staffCheckList = (isMultiStaff ? staffIdList : [state.staffId]).filter(isNormal);

    if (!staffCheckList?.length) {
      // daycare mode 不需要 check staff, 因为么有
      return [];
    }

    const { duration } = previewInfo;
    !withoutLoading && loading.open();
    try {
      const startTime = editDate?.diff(dayjs(editDate).startOf('date'), 'minute');
      const checkStaffConflictParams: StaffConflictBatchParams = {
        appointmentCheckParamList: staffCheckList.map((staffId) => {
          return {
            staffId,
            appointmentTime: editDate?.format(DATE_FORMAT_EXCHANGE),
            startTime,
            duration,
            groomingId: previewInfo.appointmentId || undefined,
          };
        }),
      };
      const conflictResult = await checkStaffConflictBatch(checkStaffConflictParams);
      const staffConflictList: StaffConflictInfoDTO[] = conflictResult.map((conflictInfo) => {
        return {
          startTime,
          duration,
          isNotConflict: conflictInfo.isNotConflict,
          conflictType: conflictInfo.type,
          staffId: conflictInfo.staffId,
        };
      });
      !withoutLoading && loading.close();
      setState({ staffConflictInfoList: staffConflictList });
      return staffConflictList;
    } catch (error) {
      loading.close();
      throw error;
    }
  });

  const handleConfirm = useLatestCallback(async (wait = true) => {
    let r = true;
    const notConflictPromise = (async () => {
      const staffConflictList = await checkStaffAndUpdate();
      const [isConflict] = getMergedConflictStatus(staffConflictList);
      dispatch(
        ticketPreviewInfoMapBox.mergeItem(itemId, {
          date: state.editDate.format(DATE_FORMAT_EXCHANGE),
          startTime: state.editDate.diff(dayjs(state.editDate).startOf('date'), 'minute'),
          staffIdList: isMultiStaff ? undefined : [state.staffId],
          staffConflictInfoList: staffConflictList,
          isResolvedManually: !isConflict,
        }),
      );
      return !isConflict;
    })();
    resetDraftPreviewInfo(state.editDate, isMultiStaff ? undefined : state.staffId);
    if (wait) {
      r = await notConflictPromise;
    }
    dispatch(
      ticketPreviewInfoMapBox.mergeItem(itemId, {
        edited: true,
      }),
    );
    onClose?.();
    return r;
  });

  const handleCancel = useSerialCallback(async () => {
    resetDraftPreviewInfo();
    onClose?.();
  });

  const handleSetFormState = useLatestCallback((params: Partial<typeof state>) => {
    setState(params);
    isDirty.open();
    // 下次 render 之后再 check，才能拉到最新的 state
    setTimeout(() => {
      checkStaffAndUpdate(true);
      setDraftPreviewInfo(params.editDate, isMultiStaff ? undefined : params.staffId);
    }, 0);
  });

  const handleLeaveAction = useUnsavedConfirm(isDirty.value, handleCancel, handleConfirm, {
    okText: 'Save',
  });

  useImperativeHandle(
    ref,
    () => ({
      save: handleConfirm,
      leave: handleLeaveAction,
      close: handleCancel,
      dirty: isDirty.value,
    }),
    [isDirty.value],
  );

  const staffNameStr = staffIdList.map((item) => staffMap.mustGetItem(item).firstName).join(', ');
  return (
    <div className="moe-p-s moe-w-full">
      <div className="moe-flex moe-justify-between moe-gap-[16px]">
        <Switch>
          <Switch.Case if={(!isDaycareMode && isConflict) || previewInfo.isSSNoAvailableResult(isSmartScheduleMode)}>
            <img src={IconIconBookingCalendarConflictSvg} className="moe-w-[44px] moe-h-[44px]" />
          </Switch.Case>
          <Switch.Case else>
            <img src={IconIconBookingCalendarSvg} className="moe-w-[44px] moe-h-[44px]" />
          </Switch.Case>
        </Switch>
        <div className="moe-flex-1 moe-min-w-0">
          <ApptEditTime
            value={state.editDate}
            onChange={(v) => {
              handleSetFormState({ editDate: v || undefined });
            }}
          />
          <div className="moe-mt-[8px]">
            <Switch>
              <Switch.Case if={!isDaycareMode && staffIdList.length > 1}>
                <Tooltip
                  content="Staff selection is unavailable here for multi-staff tickets. Please edit staff in the appointment details."
                  classNames={{ container: 'moe-max-w-[400px]' }}
                  side="top"
                >
                  <Select value="0" isDisabled>
                    <Select.Item key="0" title={staffNameStr} />
                  </Select>
                </Tooltip>
              </Switch.Case>
              <Switch.Case else={!isDaycareMode}>
                <StaffSelect
                  value={String(state.staffId)}
                  onChange={(staffId) => handleSetFormState({ staffId: Number(staffId) })}
                />
              </Switch.Case>
            </Switch>
          </div>
          <Condition if={!isDaycareMode && conflictReason}>
            <Text variant="small" className="moe-mt-xs moe-mb-[-4px] moe-text-warning">
              {conflictReason}
            </Text>
          </Condition>
        </div>
      </div>
      <div className="moe-mt-[16px] moe-flex moe-gap-[16px] moe-justify-between moe-items-center">
        <Button variant="secondary" size="s" className="moe-flex-1" onPress={handleCancel}>
          Cancel
        </Button>
        <Button variant="primary" size="s" className="moe-flex-1" onPress={handleConfirm} isLoading={loading.value}>
          Confirm
        </Button>
      </div>
    </div>
  );
});
