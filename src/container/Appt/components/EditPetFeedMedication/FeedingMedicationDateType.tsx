import { FeedingMedicationScheduleDateType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_feeding_medication_enum';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo } from 'react';
import { Condition } from '../../../../components/Condition';
import {
  SelectFeedingMedicationDateType,
  type SelectFeedingMedicationDateTypeValue,
} from '../../../../components/DateType/SelectFeedingMedicationDateType';
import { usePetDisabledDays } from '../../hooks/usePetDisabledDays';
import { matchApptFlowScene } from '../../store/appt.options';
import { selectMainServiceInPet } from '../../store/appt.selectors';
import { ApptFlowScene } from '../../store/appt.types';
import { type MedicationValue } from '../SelectServiceDetail/components/Medication/Medication.util';
import { useResolveSpecificDates } from './hooks/useResolveSpecificDates';
import { validateFeedingMedicationDate } from './utils';

interface FeedingMedicationDateTypeProps {
  appointmentId: string;
  petId: string;
  value: MedicationValue;
  onChange: (value: MedicationValue) => void;
}

export const FeedingMedicationDateType = memo<FeedingMedicationDateTypeProps>((props) => {
  const { appointmentId, petId, value, onChange } = props;

  const [petMainService] = useSelector(selectMainServiceInPet(petId, appointmentId));
  const { startDate, endDate, serviceItemType } = petMainService;

  const disabledDays = usePetDisabledDays(petId, appointmentId);
  const resolvedSpecificDates = useResolveSpecificDates(petId, appointmentId);

  const enableFeedingMedicationDate = matchApptFlowScene(ApptFlowScene.EnableFeedingMedicationDate, serviceItemType);

  const dateTypeValue = useMemo(
    () => ({
      dateType: value.selectedDate?.dateType,
      specificDates: value.selectedDate?.specificDates?.map((v) => dayjs(v)),
    }),
    [value.selectedDate],
  );

  const handleChange = (newValue: SelectFeedingMedicationDateTypeValue) => {
    const { dateType = FeedingMedicationScheduleDateType.EVERYDAY_INCLUDE_CHECKOUT_DATE } = newValue;
    const newSpecificDates = resolvedSpecificDates(newValue);
    onChange({ ...value, selectedDate: { dateType, specificDates: newSpecificDates } });
  };

  // Filter out disabled specific dates when main service start/end date changes
  useEffect(() => {
    if (value.selectedDate?.specificDates?.some((dateStr) => disabledDays(dayjs(dateStr)))) {
      const filteredDates = value.selectedDate?.specificDates?.filter((dateStr) => !disabledDays(dayjs(dateStr)));
      onChange({ ...value, selectedDate: { ...value.selectedDate, specificDates: filteredDates } });
    }
  }, [startDate, endDate]);

  const errorMessage = validateFeedingMedicationDate(value.selectedDate) ? '' : 'Please select at least one date';

  return (
    <Condition if={enableFeedingMedicationDate}>
      <SelectFeedingMedicationDateType
        value={dateTypeValue}
        onChange={handleChange}
        disabledDays={disabledDays}
        label="Medication dates"
        isRequired
        errorMessage={errorMessage}
      />
    </Condition>
  );
});
