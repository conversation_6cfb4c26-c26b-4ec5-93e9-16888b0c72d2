import { MajorMoreOutlined } from '@moego/icons-react';
import { Dropdown, IconButton } from '@moego/ui';
import React from 'react';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import { type ActionItem } from './types';

export interface CircleMoreButtonProps {
  actions?: ActionItem[];
  disabled?: boolean;
}

export function CircleMoreButton(props: CircleMoreButtonProps) {
  const { actions, disabled } = props;
  const isDisabled = actions?.length === 0 || disabled;

  return (
    <Dropdown
      isDisabled={isDisabled}
      onOpenChange={(visible) => {
        if (visible) {
          reportData(ReportActionName.CalendarExploreMoreOptions);
        }
      }}
    >
      <Dropdown.Trigger>
        <IconButton variant="secondary" size="l" icon={<MajorMoreOutlined />} data-testid={ApptTestIds.ApptMoreBtn} />
      </Dropdown.Trigger>
      <Dropdown.Menu selectionMode="none">
        {(actions || []).map((opt, idx) => (
          <Dropdown.Item
            textValue={`${idx}`}
            title={opt.label}
            key={opt.id ?? idx}
            isDisabled={opt.disabled}
            onAction={opt.onClick}
            data-testid={opt.testId}
          />
        ))}
      </Dropdown.Menu>
    </Dropdown>
  );
}
