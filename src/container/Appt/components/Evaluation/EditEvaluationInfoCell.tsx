import React, { useMemo } from 'react';

import { Form, type UseFormReturn } from '@moego/ui';
import { RE_INPUT_AMOUNT, RE_INPUT_NUMBER } from '../../../../components/form/types';
import { toNumber } from '../../../../store/utils/identifier';
import { EvaluationStaffPicker } from './EvaluationStaffPicker';
import { SelectRoom } from '../SelectServiceDetail/components/SelectRoom/SelectRoom';
import { NumberInputV2 } from '../../../../components/form/NumberInputV2';
import { numberToStringV2, stringToNumberV2 } from '../../../settings/Settings/ServicesSetting/utils/inputTransformer';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { type CombinedEvaluationCellData } from '../../../../store/evaluation/evaluation.types';

export interface EditEvaluationInfoCellProps {
  className?: string;
  form: UseFormReturn<CombinedEvaluationCellData>;
  evaluationId: string;
  petId: number;
  startDate?: string;
  startTime?: number;
  duration: number;
  staffId?: string;
  noPermissionEdit?: boolean;
}

export const EditEvaluationInfoCell = (props: EditEvaluationInfoCellProps) => {
  const { form, evaluationId, petId, startDate, startTime, duration, staffId, noPermissionEdit } = props;
  const [business] = useSelector(selectCurrentBusiness);

  // 避免 SelectRoom 反复渲染，大量触发不必要的 cancelable 请求
  const startEndRange = useMemo(
    () => ({
      startDate: dayjs(startDate),
      endDate: dayjs(startDate),
    }),
    [startDate],
  );

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[24px] moe-flex-1">
      <div className="moe-flex moe-justify-between moe-gap-x-[16px]">
        <div className="moe-w-full">
          <Form.Item
            name="price"
            label="Price"
            transformer={numberToStringV2}
            rules={{
              required: {
                value: true,
                message: 'Price is required',
              },
            }}
          >
            <NumberInputV2
              isDisabled={noPermissionEdit}
              isRequired
              placeholder="Add the price"
              inputFormat={RE_INPUT_AMOUNT}
              prefix={<span className="moe-leading-[20px]">{business.currencySymbol}</span>}
              maxLength={10}
            />
          </Form.Item>
        </div>
        <div className="moe-w-full">
          <Form.Item
            rules={{
              required: {
                value: true,
                message: 'Duration is required',
              },
            }}
            name="duration"
            label="Duration"
            transformer={numberToStringV2}
          >
            <NumberInputV2
              isDisabled={noPermissionEdit}
              isRequired
              className="moe-w-full"
              placeholder="Duration"
              suffix="mins"
              inputFormat={RE_INPUT_NUMBER}
              maxLength={4}
            />
          </Form.Item>
        </div>
      </div>
      <div className="moe-flex moe-justify-between moe-gap-x-[16px]">
        <div className="moe-w-full">
          <Form.Item name="staffId" label="Staff">
            <>
              <EvaluationStaffPicker
                isDisabled={noPermissionEdit}
                label="Staff"
                evaluationId={evaluationId}
                appointmentDate={startDate}
                serviceStartTime={startTime}
                serviceTime={toNumber(duration)}
                value={stringToNumberV2.input(staffId)}
                onChange={(value) => {
                  form.setValue('staffId', stringToNumberV2.output(value), { shouldDirty: true });
                }}
                placeholder="Assign a staff if needed"
              />
            </>
          </Form.Item>
        </div>
        <div className="moe-w-full">
          <Form.Item name="lodgingId" label="Lodging">
            <SelectRoom
              isEvaluation
              containerClassName="moe-justify-normal"
              petId={petId}
              isRequired={false}
              isClearable
              range={startEndRange}
              serviceId={evaluationId}
              isDisabled={!startDate || noPermissionEdit}
              onChange={(v) => {
                form.setValue('lodgingId', v || '', { shouldDirty: true });
              }}
              placeholder="Select a room if needed"
            />
          </Form.Item>
        </div>
      </div>
    </div>
  );
};
