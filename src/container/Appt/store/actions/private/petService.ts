import { action } from 'amos';
import {
  ApptAddOnRecord,
  ApptServiceRecord,
  apptAddOnMapBox,
  apptPetMapBox,
  apptServiceMapBox,
} from '../../appt.boxes';
import { selectPetsInAppt } from '../../appt.selectors';
import {
  type ApptAddOnInfoRecord,
  type ApptPetsListInfo,
  type ApptServiceInfoRecord,
  CreateApptId,
} from '../../appt.types';
import { clearPetsInAppt } from '../public/petService';

/**
 * 删除指定 petId 的 pet
 */
export const removeApptPet = action(
  async (dispatch, select, petId: string | number, appointmentId: string = CreateApptId) => {
    const pets = select(selectPetsInAppt(appointmentId, false));
    const currentPets = pets.filter((v) => v.petId === String(petId));
    dispatch(clearPetsInAppt(currentPets));
  },
);

/**
 * new appt 时，当 client 改变，或者是删除最后一只pet 时，调用此方法
 */
export const clearExistPets = action(async (dispatch, select, appointmentId: string = CreateApptId) => {
  const pets = select(selectPetsInAppt(appointmentId));
  dispatch(clearPetsInAppt(pets));
});

export const setServiceForPet = action(
  async (dispatch, select, appointmentId: string, id: string, input: Partial<ApptServiceInfoRecord>) => {
    const ownerId = ApptServiceRecord.createOwnId(appointmentId, id);
    dispatch(apptServiceMapBox.updateItem(ownerId, (preValue) => preValue.merge(input)));
  },
);

export const setAddonForPet = action(
  async (dispatch, select, appointmentId: string, id: string, input: Partial<ApptAddOnInfoRecord>) => {
    const ownerId = ApptAddOnRecord.createOwnId(appointmentId, id);
    dispatch(apptAddOnMapBox.updateItem(ownerId, (preValue) => preValue.merge(input)));
  },
);

export const setApptDetailInfo = action(
  async (dispatch, select, appointmentId: string, input: Partial<ApptPetsListInfo>) => {
    dispatch(apptPetMapBox.mergeItem(appointmentId, input));
  },
);
