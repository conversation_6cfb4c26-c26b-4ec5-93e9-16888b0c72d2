import { useDispatch, useSelector } from 'amos';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { apptInfoMapBox } from '../store/appt.boxes';
import { useEvaluationTicketActions } from './useEvaluationTicketActions';
import { updatePetEvaluation } from '../../../store/evaluation/evaluation.actions';
import { PetEvaluationHistoryModelActionType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_evaluation_models';
import { type UpdatePetEvaluationResult } from '@moego/api-web/moego/api/business_customer/v1/business_pet_evaluation_api';
import { useConfirmCheckOut } from '../components/ApptCheckInOutAlert/hooks/useConfirmCheckOut';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { getApptInfoEvaluationStatus } from '../store/appt.utils';

interface EvaluationConfirmCheckOutProps {
  appointmentId: string | number;
}

export function useEvaluationConfirmCheckOut(props: EvaluationConfirmCheckOutProps) {
  const { appointmentId } = props;
  const dispatch = useDispatch();
  const [apptInfoMap] = useSelector(apptInfoMapBox, currentBusinessIdBox);
  const apptInfo = apptInfoMap.mustGetItem(appointmentId?.toString());
  const { serviceDetail = [] } = apptInfo;
  const { petEvaluationStatus } = getApptInfoEvaluationStatus({
    serviceItemTypes: apptInfo.serviceItemTypes,
    serviceDetail,
  });
  const emptyEvaluationStatusPetIds = petEvaluationStatus
    ?.filter((item) => !item.evaluationStatus)
    ?.map((item) => item.petId);

  const evaluations = serviceDetail?.map((item) => item.evaluations).flat();
  const { refreshTicket } = useEvaluationTicketActions(Number(appointmentId));
  const handleConfirmCheckout = useConfirmCheckOut({
    appointmentId: String(appointmentId),
    emptyEvaluationStatusPetIds,
  });

  return useLatestCallback(async () => {
    const { next, extra } = await handleConfirmCheckout();

    const { evaluationResult } = extra || {};
    if (next && evaluationResult?.length) {
      const actions: Promise<UpdatePetEvaluationResult>[] = [];
      evaluationResult.forEach(({ petId, evaluationStatus }) => {
        const evaluationId = evaluations.find((i) => i.petId === petId)?.serviceId;

        if (!evaluationId) {
          return;
        }

        actions.push(
          dispatch(
            updatePetEvaluation({
              petId: petId,
              evaluationStatus,
              evaluationId,
              actionType: PetEvaluationHistoryModelActionType.UPDATE_BY_EVALUATION_APPOINTMENT,
            }),
          ),
        );
      });

      await Promise.all(actions);
      refreshTicket();
    }

    return { next, extra };
  });
}
