import dayjs, { type Dayjs } from 'dayjs';
import { useMemo, useState } from 'react';
import { DATE_FORMAT_EXCHANGE, getMonthDateRange } from '../../../utils/DateTimeUtil';
import { useDispatch, useSelector } from 'amos';
import { getAvailableDates, getEvaluationAvailableTime } from '../store/appt.actions';
import { useDebounce } from 'react-use';
import { dateMessageToString, stringToDateMessage } from '../../../utils/utils';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { useBool } from '../../../utils/hooks/useBool';

const inValidTime = (d: Dayjs, validTimes: { startTime: number; endTime: number }[]) => {
  const minutes = d.getMinutes();
  return validTimes.some(({ startTime, endTime }) => {
    return minutes >= startTime && minutes <= endTime;
  });
};

export const useGetEvaluationDateTimeConfig = (onlyShowAvailableTime: boolean, selectedDate: Dayjs) => {
  const [validTimes, setValidTimes] = useState<{ startTime: number; endTime: number }[]>([]);
  const [unavailableDates, setUnavailableDates] = useState<string[]>([]);
  const [currentDateView, setCurrentDateView] = useState<Dayjs>(dayjs());
  const isTimeSelectorOpen = useBool(false);
  const { startDate, endDate } = getMonthDateRange(currentDateView);
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness);

  useDebounce(
    async () => {
      if (!onlyShowAvailableTime) return;
      const res = await dispatch(
        getAvailableDates({
          startDate: stringToDateMessage(startDate),
          endDate: stringToDateMessage(endDate),
          businessId: business.id.toString(),
          petServices: [],
        }),
      );
      setUnavailableDates(res.unavailableDates?.map((date) => dateMessageToString(date)) ?? []);
    },
    300,
    [startDate, endDate, onlyShowAvailableTime],
  );

  useDebounce(
    async () => {
      if (!isTimeSelectorOpen.value || !onlyShowAvailableTime) return;

      const res = await dispatch(
        getEvaluationAvailableTime({
          businessId: business.id.toString(),
          petServices: [],
          date: stringToDateMessage(selectedDate),
        }),
      );
      setValidTimes(
        res.dayTimeRanges
          .map((day) => day.timeRange.map((time) => ({ startTime: time.startTime, endTime: time.endTime })))
          ?.flat() ?? [],
      );
    },
    300,
    [selectedDate, isTimeSelectorOpen.value, onlyShowAvailableTime],
  );

  const DateTimeConfig = useMemo(() => {
    return {
      date: {
        label: 'Date',
        key: 'date',
        onViewDateChange: setCurrentDateView,
        disabledDate: (d: Dayjs) => {
          if (onlyShowAvailableTime) {
            return (
              d.isBefore(dayjs().startOf('day')) ||
              unavailableDates.some((date) => {
                return dayjs(date, DATE_FORMAT_EXCHANGE).isSame(d, 'day');
              })
            );
          }
          return false;
        },
      },
      time: {
        label: 'Time',
        key: 'time',
        placeholder: 'Choose time',
        onOpenChange: isTimeSelectorOpen.as,
        disabledTimes: (d: Dayjs) => {
          if (onlyShowAvailableTime) {
            return !inValidTime(d, validTimes);
          }
          return false;
        },
      },
    };
  }, [onlyShowAvailableTime, validTimes, unavailableDates]);

  return DateTimeConfig;
};
