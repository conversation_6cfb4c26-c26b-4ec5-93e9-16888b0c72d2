import { Form, type useForm } from '@moego/ui';
import React, { memo } from 'react';
import { type MembershipModel } from '../../../store/membership/membership.boxes';
import { BasicInfoForm } from './form/BasicInfoForm';
import { BenefitsForm } from './form/BenefitsForm';
import { EnableOnlineBookingForm } from './form/EnableOnlineBookingForm';
import { PetDetailForm } from './form/PetDetailForm';
import { PriceForm } from './form/PriceForm';
import { TermsAndPolicyForm } from './form/TermsAndPolicyForm';

export interface MembershipFormProps {
  form: ReturnType<typeof useForm<MembershipModel>>;
}

export const MembershipForm = memo(function MembershipForm(props: MembershipFormProps) {
  const { form } = props;
  return (
    <Form form={form} footer={null}>
      <div className="moe-flex moe-flex-col moe-gap-y-xl">
        <BasicInfoForm form={form} />
        <PriceForm form={form} />
        <BenefitsForm form={form} />
        <PetDetailForm form={form} />
        <EnableOnlineBookingForm form={form} />
        <TermsAndPolicyForm form={form} />
      </div>
    </Form>
  );
});
