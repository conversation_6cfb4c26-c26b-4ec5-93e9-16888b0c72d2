import { Form, Link, LegacySelect as Select, Text, useFormContext, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { PATH_SETTING_CLIENTS_AND_PETS } from '../../../../router/paths';
import { type MembershipModel } from '../../../../store/membership/membership.boxes';
import { selectPetSizeList } from '../../../../store/onlineBooking/settings/petSize.selectors';
import { useWeightSuffix } from '../../../../utils/hooks/useWeightSuffix';
import { ClientAndPetsNav } from '../../../settings/Settings/ClientsAndPetsSetting/types';
import { PetSizeFilter } from '../../consts';
export interface PetSizeFormProps {
  isDisabled?: boolean;
}

export const Weight = memo((props: PetSizeFormProps) => {
  const { isDisabled } = props;
  const form = useFormContext<MembershipModel>();

  const [petSizeFilter] = useWatch({
    control: form?.control,
    name: ['petSizeFilter'],
  });
  const [petSizeList = []] = useSelector(selectPetSizeList());
  const weightUnit = useWeightSuffix();

  const availableSizeOption = useMemo(
    () =>
      petSizeList.map((item) => {
        const { name, weightHigh, weightLow, id } = item || {};
        return {
          label: `${name}(${weightLow}-${weightHigh}${weightUnit})`,
          value: String(id),
        };
      }),
    [petSizeList],
  );

  const isSelectedAll = !petSizeFilter;

  const handleChange = (value: string[] | undefined = []) => {
    form?.setValue('customizedPetSizes', value, { shouldDirty: true });
    if (value?.length === availableSizeOption.length) {
      form?.setValue('petSizeFilter', PetSizeFilter.All, { shouldDirty: true });
    } else {
      form?.setValue('petSizeFilter', PetSizeFilter.Customized, { shouldDirty: true });
    }
  };

  const renderMultipleValues = () => <div>All range</div>;

  return (
    <Form.Item
      name="customizedPetSizes"
      label="Weight"
      transformer={{
        input(petSizeTypeList) {
          if (isSelectedAll) {
            return availableSizeOption.map((item) => item.value);
          }
          return petSizeTypeList;
        },
      }}
      rules={{
        validate: (value) => {
          if (!value?.length && !isSelectedAll) {
            return 'Please select at least one pet weight';
          }
          return true;
        },
      }}
    >
      <Select
        isDisabled={isDisabled}
        isRequired
        isMultiple
        showSelectAll
        multipleMode={isSelectedAll ? 'value' : 'tag'}
        placeholder="Select pet weight"
        options={availableSizeOption}
        onChange={handleChange}
        renderMultipleValues={renderMultipleValues}
        description={
          <Text variant="small" className="moe-flex moe-items-center">
            Size options can be modified at&nbsp;
            <Link
              variant="small"
              target="_blank"
              href={PATH_SETTING_CLIENTS_AND_PETS.queried({
                panel: ClientAndPetsNav.PetSize,
              })}
            >
              {'Settings > Clients & Pets > Pet size'}
            </Link>
          </Text>
        }
      />
    </Form.Item>
  );
});
