import React from 'react';
import { useAsyncCallback } from '../../../../utils/hooks/useAsyncCallback';
import { useFloatableHost } from '../../../../utils/hooks/useFloatableHost';
import { PackageDeleteConfirmModal } from './PackageDeleteConfirmModal';

export function usePackageDeleteConfirmModal() {
  const { mountModal } = useFloatableHost<boolean>();

  const modal = useAsyncCallback(() => {
    const { promise, closeFloatable: closeModal } = mountModal(
      <PackageDeleteConfirmModal
        onConfirm={() => {
          closeModal(true);
        }}
        onCancel={() => {
          closeModal(false);
        }}
      />,
    );

    return promise;
  });

  return modal;
}
