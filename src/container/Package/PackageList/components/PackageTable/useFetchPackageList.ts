import { useDispatch } from 'amos';
import { getBusinessPackages } from '../../../../../store/packages/package.actions';
import { type PackageListReqParams } from '../../../../../store/packages/package.boxes';
import { useCancelableCallback } from '../../../../../utils/hooks/useCancelableCallback';

export function useFetchPackageList() {
  const dispatch = useDispatch();
  const fetchData = useCancelableCallback(async (signal, params: PackageListReqParams) => {
    await dispatch(getBusinessPackages(params, undefined, signal));
  });

  return fetchData;
}
