import { Input, LegacySelect as Select, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useRef } from 'react';
import { useDebounce, useSetState } from 'react-use';
import { getBusinessPackages } from '../../../../../store/packages/package.actions';
import { selectBusinessPackageList } from '../../../../../store/packages/package.selector';
import { useFetchPackageList } from './useFetchPackageList';

export enum PackageStatusEnum {
  Active = 'Active',
  Inactive = 'Inactive',
}

export interface PackageTableToolbarProps {
  className?: string;
}

export const PackageTableToolbar = memo<PackageTableToolbarProps>((props) => {
  const { className } = props;
  const [packageList] = useSelector(selectBusinessPackageList());
  const filter = packageList.getFilter({});
  const dispatch = useDispatch();
  const runCount = useRef(0);
  const [state, setState] = useSetState<{ name: string; isActive: boolean }>({
    name: filter.name,
    isActive: filter.isActive,
  });
  const fetchData = useFetchPackageList();

  useDebounce(
    () => {
      if (runCount.current++) {
        fetchData({ name: state.name });
      }
    },
    500,
    [state.name],
  );

  return (
    <div className={cn('moe-bg-neutral-sunken-0 moe-rounded-m moe-p-s', className)}>
      <div className="moe-flex moe-items-center moe-gap-4">
        <div className="moe-w-[300px]">
          <Input.Search
            size="m"
            placeholder="Search by package name"
            value={state.name}
            onChange={(value) => {
              setState({ name: value });
            }}
          />
        </div>
        <Select
          className="moe-w-[209px] moe-inline-block"
          size="m"
          isClearable={false}
          isSearchable={false}
          value={state.isActive ? PackageStatusEnum.Active : PackageStatusEnum.Inactive}
          onChange={(value) => {
            const isActive = value === PackageStatusEnum.Active;
            setState({ isActive });
            dispatch(getBusinessPackages({ isActive, pageNum: 1 }));
          }}
          options={Object.keys(PackageStatusEnum).map((key) => ({
            label: key,
            value: PackageStatusEnum[key as PackageStatusEnum],
          }))}
        />
      </div>
    </div>
  );
});
