import { Alert, Form, Heading, type Option, LegacySelect as Select } from '@moego/ui';
import React from 'react';
import { Condition } from '../../../../components/Condition';
import { PackageExpiresDate } from '../../../../store/packages/utils';

const ExpiresAfterOptions: Option<number>[] = PackageExpiresDate.values.map((value) => {
  return {
    label: PackageExpiresDate.mapLabels[value],
    value,
  };
});

export function PackageValidity({ isOldPackageData }: { isOldPackageData: boolean }) {
  return (
    <div className="moe-flex moe-flex-col moe-gap-y-m">
      <Heading size="3" className="moe-mb-xxs">
        Validity
      </Heading>
      <Form.Item
        rules={{
          required: 'This is a required field',
        }}
        name="expirationDays"
        label="Expires after"
      >
        <Select
          tooltip="Package expiry will start from sale date upon selecting a time frame."
          options={ExpiresAfterOptions}
          isRequired
          placeholder="Select time frame"
          formatOptionLabel={(value) => {
            return <span>{value.label || value.value}</span>;
          }}
        />
      </Form.Item>
      <Condition if={isOldPackageData}>
        <Alert
          color="warning"
          title="Update on package validity"
          description="The package start date is now based on the sale date, which is when you sell the package to a client. You can set various expiration dates. If the package should never expire, please select “Never”."
          isCloseable={false}
          isRounded
          classNames={{ base: 'moe-border-warning moe-border' }}
        />
      </Condition>
    </div>
  );
}
