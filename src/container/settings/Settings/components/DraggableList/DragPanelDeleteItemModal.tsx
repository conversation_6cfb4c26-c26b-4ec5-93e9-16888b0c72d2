import { AlertDialog } from '@moego/ui';
import React, { type ReactNode } from 'react';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { memo } from '../../../../../utils/react';

export interface DragPanelDeleteItemModalProps<T> {
  visible: boolean;
  title: ReactNode;
  value: Partial<T>;
  renderBody?: (value: Partial<T>) => ReactNode;
  onClose: () => void;
  onConfirm: (value: T) => Promise<void>;
}

export const DragPanelDeleteItemModal = memo(function DragPanelDeleteItemModal<T>(
  props: DragPanelDeleteItemModalProps<T>,
) {
  const { title, visible, onClose, onConfirm, renderBody, value } = props;
  const handleConfirm = useSerialCallback(onConfirm);
  return (
    <AlertDialog
      variant="danger"
      size="s"
      title={title}
      isOpen={visible}
      onClose={onClose}
      onConfirm={() => handleConfirm(value as T)}
      confirmText="Delete"
    >
      <div className="moe-font-manrope">{renderBody?.(value)}</div>
    </AlertDialog>
  );
});
