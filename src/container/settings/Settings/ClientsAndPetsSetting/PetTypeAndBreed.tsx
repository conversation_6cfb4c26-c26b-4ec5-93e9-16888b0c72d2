import { type BatchUpsertPetBreedParams } from '@moego/api-web/moego/api/business_customer/v1/business_pet_breed_api';
import { MinorEditOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { AlertDialog, Button, IconButton } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { useSetState } from 'react-use';
import { WithPermission } from '../../../../components/GuardRoute/WithPermission';
import { toastApi } from '../../../../components/Toast/Toast';
import { currentAccountIdBox } from '../../../../store/account/account.boxes';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { addPetTypeAndBreedList, getPetTypeList, updatePetType } from '../../../../store/pet/petType.actions';
import { PetTypeRecord, petTypeBreedCountBox, petTypeMapBox } from '../../../../store/pet/petType.boxes';
import { selectBusinessPetTypes } from '../../../../store/pet/petType.selectors';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { withPl } from '../../../../utils/calculator';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { DragPanel } from '../components/DraggableList/DragPanel';
import { PetTypeAndBreedModal } from './components/PetTypeAndBreed/PetTypeAndBreedModal';
import { ClientAndPetsNav, DragListHandleType, PetTypeAvailable, type PetTypeModel } from './types';

interface ModalState {
  currentData: PetTypeModel;
  type: DragListHandleType;
}

const defaultData = {
  id: ID_ANONYMOUS,
  petTypeId: undefined,
  typeName: '',
  isAvailable: PetTypeAvailable.Unavailable,
};

export const PetTypeAndBreed = memo(function PetTypeAndBreed() {
  const dispatch = useDispatch();
  const isLoading = useBool(true);
  const [business, petTypeIdList, petTypeMap, currentAccountId, petTypeBreedCount] = useSelector(
    selectCurrentBusiness,
    selectBusinessPetTypes,
    petTypeMapBox,
    currentAccountIdBox,
    petTypeBreedCountBox,
  );
  const [modalState, setModalState] = useSetState<ModalState>({
    currentData: defaultData,
    type: DragListHandleType.Add,
  });
  const modalVisible = useBool();
  const removeModalVisible = useBool();
  useEffect(() => {
    if (isNormal(business.id)) {
      getData();
    }
  }, [business.id]);

  const getData = useLatestCallback(async () => {
    isLoading.open();
    await dispatch(getPetTypeList()).finally(() => {
      isLoading.close();
    });
  });

  const petTypeList = useMemo(() => {
    return petTypeIdList.toJSON().map((id) => petTypeMap.mustGetItem(id).toJSON());
  }, [petTypeIdList, petTypeMap]);

  const availablePetTypesList = petTypeList.filter((item) => item.isAvailable && isNormal(item.id));

  type PetType = (typeof petTypeList)[0];

  const handleRemove = useSerialCallback(async (item: Partial<PetType>) => {
    await dispatch(
      updatePetType(
        {
          id: item.id!,
          isAvailable: PetTypeAvailable.Unavailable,
        },
        PetTypeRecord.ownId(item.petTypeId!, currentAccountId),
      ),
    );
    removeModalVisible.close();
    toastApi.success('Pet type deleted!');
  });

  const handleOpenAddModal = useLatestCallback(() => {
    setModalState({ currentData: defaultData, type: DragListHandleType.Add });
    modalVisible.open();
  });

  const handleOpenRemoveModal = useLatestCallback((item: PetType) => {
    setModalState({ currentData: item, type: DragListHandleType.Remove });
    removeModalVisible.open();
  });

  const handleOpenEditModal = useLatestCallback((item: PetType) => {
    setModalState({ currentData: item, type: DragListHandleType.Edit });
    modalVisible.open();
  });

  const handleConfirm = useLatestCallback(async (item: Partial<BatchUpsertPetBreedParams>) => {
    await dispatch(addPetTypeAndBreedList(item));
    modalVisible.close();
  });

  return (
    <>
      <DragPanel
        dragDisabled={true}
        title={ClientAndPetsNav.PetTypeAndBreed}
        isLoading={isLoading.value}
        headerRight={
          <WithPermission permissions="managePetTypeAndBreed">
            <Button className="!moe-font-bold !moe-text-white" onPress={() => handleOpenAddModal()}>
              Add new pet type
            </Button>
          </WithPermission>
        }
        list={availablePetTypesList}
        idKey="id"
        renderDragHandleBar={(item) => (
          <WithPermission permissions={'managePetTypeAndBreed'}>
            <div className="moe-flex moe-gap-x-[16px]">
              <IconButton icon={<MinorEditOutlined />} onPress={() => handleOpenEditModal(item)}></IconButton>
              <IconButton icon={<MinorTrashOutlined />} onPress={() => handleOpenRemoveModal(item)}></IconButton>
            </div>
          </WithPermission>
        )}
        renderDragLabel={(item) => {
          return (
            <div className="moe-flex moe-w-full moe-items-center">
              <div className="moe-text-[16px] moe-text-primary moe-font-regular moe-leading-[20px] moe-font-manrope moe-w-[50%]">
                {item.typeName}
              </div>
              <div className="moe-text-[16px] moe-text-tertiary moe-font-regular moe-leading-[20px] moe-flex">
                {withPl(petTypeBreedCount[item.id] || 0, 'breed')}
              </div>
            </div>
          );
        }}
        permission="managePetTypeAndBreed"
        dragItemClassName="moe-py-[16px] moe-border-0 moe-border-solid moe-border-b moe-border-b-divider"
      />
      <PetTypeAndBreedModal
        visible={modalVisible.value}
        type={modalState.type}
        petTypeId={modalState.currentData.petTypeId}
        typeName={modalState.currentData.typeName}
        onClose={modalVisible.close}
        onConfirm={handleConfirm}
        petTypeList={petTypeList}
        id={modalState.currentData.id}
      />
      <AlertDialog
        isOpen={removeModalVisible.value}
        title="Delete this pet type?"
        variant="danger"
        size="s"
        onClose={removeModalVisible.close}
        onConfirm={() => handleRemove(modalState.currentData)}
        onCancel={removeModalVisible.close}
        confirmText="Delete"
      >
        <div className="moe-text-[#202020] moe-text-[16px] moe-leading-[24px] moe-font-regular moe-font-manrope">
          Are you sure to delete this pet type?
        </div>
      </AlertDialog>
    </>
  );
});
