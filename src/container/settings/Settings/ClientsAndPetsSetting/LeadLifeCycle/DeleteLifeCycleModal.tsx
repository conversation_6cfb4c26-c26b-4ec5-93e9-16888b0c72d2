import { Form, Modal, Select } from '@moego/ui';
import React from 'react';
import { type BaseModalProps } from '../../../../../components/Modal/useModal';
import { type LeadLifeCycleFields } from './CreateOrEditLifeCycleModal';
import { useSetState } from 'react-use';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useSelector } from 'amos';
import { selectLeadLifeCycleIdList } from '../../../../../store/leads/leadLifeCycle.selectors';
import { leadLifeCycleMapBox } from '../../../../../store/leads/leadLifeCycle.boxes';
export interface DeleteLifeCycleModalProps extends BaseModalProps {
  onConfirm: ({
    fromId,
    toId,
  }: {
    fromId: string;
    toId?: string;
  }) => Promise<void>;
  item: LeadLifeCycleFields;
}

export const DeleteLifeCycleModal = (props: DeleteLifeCycleModalProps) => {
  const { onConfirm, item: currentItem, ...modalProps } = props;
  const [lifeCycleIdList, lifeCycleMap] = useSelector(selectLeadLifeCycleIdList(), leadLifeCycleMapBox);
  const [state, setState] = useSetState<{
    toId?: string;
  }>({});

  const handleConfirm = useLatestCallback(async () => {
    await onConfirm({
      fromId: currentItem.id,
      toId: state.toId,
    });
  });

  return (
    <Modal
      {...modalProps}
      onConfirm={handleConfirm}
      title="Delete this action status?"
      confirmText="Delete"
      confirmButtonProps={{
        color: 'danger',
        isDisabled: !state.toId,
      }}
      isOpen
      classNames={{
        container: 'moe-w-[540px]',
      }}
    >
      <div className="moe-text-regular moe-mb-m">
        {`You are about to  delete the '${currentItem.name}' lifecyle. Please select how to handle leads under this lifecyle (if none exist,  the lifecyle will be deleted directly):`}
      </div>
      <Form.Label label="Lifecyle" isRequired className="moe-mb-[12px]" />
      <Select
        isRequired
        className="moe-mt-[-8px]"
        onChange={(v) => {
          v && setState({ toId: v as string });
        }}
      >
        {lifeCycleIdList
          .toJSON()
          .filter((id) => id !== currentItem.id)
          .map((id) => (
            <Select.Item key={id} title={lifeCycleMap.mustGetItem(id).name} />
          ))}
      </Select>
    </Modal>
  );
};
