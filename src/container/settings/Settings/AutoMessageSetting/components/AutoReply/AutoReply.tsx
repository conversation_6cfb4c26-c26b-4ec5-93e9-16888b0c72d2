import { MessageTemplateUseCase } from '@moego/api-web/moego/models/message/v1/message_template_enums';
import { But<PERSON>, DatePicker, Heading, Spin, Switch, Text, useForm, useFormState, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useEffect } from 'react';
import { emojiValidate } from '../../../../../../components/CustomizeForm/HybridEditor/Validator';
import {
  ALLOW_TEXT_LENGTH_REMINDER,
  AUTO_MESSAGE_SMS_MAX_LENGTH,
  getSmsSegments,
} from '../../../../../../components/MessageSendBox/components/MessageUtils';
import { WithPricingEnableUpgrade } from '../../../../../../components/Pricing/WithPricingComponents';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { AutoMessageNavPanel } from '../../../../../../store/autoMessage/autoMessage.boxes';
import { getAutoReply, updateAutoReply } from '../../../../../../store/autoMessage/autoReply.actions';
import { type AutoReplyModel, type AutoReplyRecord } from '../../../../../../store/autoMessage/autoReply.boxes';
import { selectBusinessAutoReply } from '../../../../../../store/autoMessage/autoReply.selectors';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { getMessageTemplatePlaceholders } from '../../../../../../store/message/messageTemplate.actions';
import { selectTemplatePlaceholderList } from '../../../../../../store/message/messageTemplate.selectors';
import { type PartialProps } from '../../../../../../store/utils/RecordMap';
import { LegacyBool } from '../../../../../../store/utils/createEnum';
import { abortNavigation } from '../../../../../../utils/abortNavigation';
import { BoolText } from '../../../../../../utils/boolText';
import { useBizIdReadyEffect } from '../../../../../../utils/hooks/useBizIdReadyEffect';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { useUnsavedConfirmGlobalV2 } from '../../../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { AutoMessageSettingTitle } from '../AutoMessageSettingTitle';
import { TemplateEditor } from '../TemplateEditor/TemplateEditor';
import { TimeSetting } from './TimeSetting';

const AutoMessageType = AutoMessageNavPanel.AutoReply;

export const AutoReply = memo(function AutoReply() {
  const dispatch = useDispatch();
  const [business, autoReply, placeholderList] = useSelector(
    selectCurrentBusiness(),
    selectBusinessAutoReply(),
    selectTemplatePlaceholderList(),
  );
  const form = useForm<AutoReplyModel>({
    defaultValues: autoReply.toJSON(),
  });

  useEffect(() => {
    form.reset(autoReply.toJSON());
  }, [autoReply]);

  const [autoReplyBody, status, startTime, endTime, timeRangeMap] = useWatch({
    control: form.control,
    name: ['autoReplyBody', 'status', 'startTime', 'endTime', 'timeRangeMap'],
  });

  const { isDirty } = useFormState({
    control: form.control,
  });

  const { remainLength: smsRemainLength, smsSegmentLength } = getSmsSegments(
    autoReplyBody?.trim(),
    ALLOW_TEXT_LENGTH_REMINDER,
  );

  const handleUpdateTime = (kind: 'startTime' | 'endTime', value: dayjs.Dayjs | null) => {
    let time = 0;
    if (kind === 'startTime') {
      time = value ? value.startOf('date').unix() : 0;
    } else {
      time = value ? value.endOf('date').unix() : 0;
    }
    return handleUpdateForm({ [kind]: time });
  };

  const handleUpdateForm = useLatestCallback((input: PartialProps<AutoReplyRecord>) => {
    Object.keys(input || {}).forEach((key) => {
      form.setValue(key as keyof AutoReplyModel, input[key as keyof AutoReplyModel]!, {
        shouldDirty: true,
      });
    });
  });

  const handleUpdate = useSerialCallback(async (input: PartialProps<AutoReplyRecord>) => {
    await dispatch(updateAutoReply({ ...autoReply.toJSON(), ...input }));
    toastApi.success('Auto reply settings updated');
  });

  const handleGetData = useSerialCallback(async () => {
    await Promise.all([
      dispatch(getAutoReply()),
      dispatch(getMessageTemplatePlaceholders({ useCase: MessageTemplateUseCase.USE_CASE_AUTO_REPLY })),
    ]);
  });

  useBizIdReadyEffect(() => {
    handleGetData();
  }, []);

  const handleSave = useSerialCallback(async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      abortNavigation('Auto reply form is invalid');
    }
    await form.handleSubmit(async (values) => {
      await handleUpdate(values);
    })();
  });

  const isEnabled = LegacyBool.truly(status);

  useUnsavedConfirmGlobalV2({
    showConfirm: isDirty,
    modalProps: {
      title: 'Unsaved changes',
      content: 'Would you like to save your changes before exiting?',
      onConfirm: handleSave,
    },
  });

  return (
    <div>
      <AutoMessageSettingTitle autoMessageType={AutoMessageType} className="moe-mb-xl" />
      <Spin
        isLoading={handleGetData.isBusy()}
        classNames={{
          base: 'moe-w-full moe-h-full',
          container: 'moe-w-full moe-h-full',
          iconContainer: '!moe-top-[200px]',
        }}
      >
        <div className="moe-flex moe-flex-col moe-gap-y-xl">
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Heading size="6">Status</Heading>
            <WithPricingEnableUpgrade overrideEvent="onChange" permission="autoReply">
              <Switch
                isSelected={isEnabled}
                onChange={(checked) => handleUpdateForm({ status: LegacyBool.get(checked) })}
              >
                {BoolText.getOnOff(isEnabled)}
              </Switch>
            </WithPricingEnableUpgrade>
            <Text variant="caption" className="moe-text-tertiary">
              Once activated, anyone who sends a message to your MoeGo Number will receive this automated reply at the
              specified date and time. (Limited to one message per 24 hours)
            </Text>
          </div>
          <div>
            <TemplateEditor
              form={form}
              label={'Template'}
              textAreaFieldName={`autoReplyBody`}
              isDisabled={!isEnabled}
              textAreaEditorRules={{
                required: 'The SMS body cannot be empty',
                ...emojiValidate,
              }}
              hintBlock={
                autoReplyBody.trim().length ? (
                  <Text variant="regular-short" className="moe-text-s">
                    <strong>{smsSegmentLength}</strong>
                    {autoReplyBody.trim().length >= AUTO_MESSAGE_SMS_MAX_LENGTH
                      ? ` SMS used (${AUTO_MESSAGE_SMS_MAX_LENGTH} characters limit reached)`
                      : ` SMS used（approx. ${smsRemainLength} characters left for ${smsSegmentLength} SMS）`}
                  </Text>
                ) : null
              }
              showCount={false}
              maxLength={AUTO_MESSAGE_SMS_MAX_LENGTH}
              placeholderList={placeholderList}
            />
          </div>
          <div>
            <Heading size="6" className="moe-mb-xxs">
              Date settings
            </Heading>
            <div className="moe-flex moe-gap-x-s moe-items-center">
              <DatePicker
                className="moe-w-[200px]"
                format={business.dateFormat}
                value={startTime === 0 ? null : dayjs(startTime * T_SECOND)}
                disabledDate={(d) => (endTime ? endTime * T_SECOND < d.startOf('date').valueOf() : false)}
                onChange={(value) => handleUpdateTime('startTime', value)}
                isDisabled={!isEnabled}
              />
              <span>-</span>
              <DatePicker
                className="moe-w-[200px]"
                format={business.dateFormat}
                value={endTime === 0 ? null : dayjs(endTime * T_SECOND)}
                disabledDate={(d) => (startTime ? startTime * T_SECOND > d.startOf('date').valueOf() : false)}
                onChange={(value) => handleUpdateTime('endTime', value)}
                isDisabled={!isEnabled}
              />
            </div>
          </div>
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Heading size="6">Time settings</Heading>
            <TimeSetting
              isDisabled={!isEnabled}
              value={timeRangeMap}
              onChange={(newTimeRangeList) => handleUpdateForm({ timeRangeMap: newTimeRangeList })}
            />
          </div>
          <div>
            <Button onPress={handleSave} isLoading={handleSave.isBusy()} isDisabled={!isDirty}>
              Save
            </Button>
          </div>
        </div>
      </Spin>
    </div>
  );
});
