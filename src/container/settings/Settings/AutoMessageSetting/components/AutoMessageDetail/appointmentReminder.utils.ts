import { type AppointmentReminderListView } from '@moego/api-web/moego/models/auto_message/v1/auto_message_models';

// first reminder 互斥逻辑：first reminder 与 second reminder 之间的时间选择不能相同，且 first reminder 不能晚于 second reminder
export const isFirstReminderValid = (
  secondeReminder: AppointmentReminderListView,
  generalReminder: AppointmentReminderListView,
  minutesAt: number,
  daysBefore?: number,
) => {
  const isSecondReminderEnabled = secondeReminder && secondeReminder.isEnabled;
  const isGeneralReminderEnabled = generalReminder && generalReminder.isEnabled;
  let isValid = true;

  if (isSecondReminderEnabled && daysBefore === secondeReminder.daysBefore) {
    isValid = minutesAt < secondeReminder.minutesAt;
  }
  if (isValid && isGeneralReminderEnabled && daysBefore === generalReminder.daysBefore) {
    isValid = minutesAt !== generalReminder.minutesAt;
  }

  return isValid;
};

// second reminder 互斥逻辑：second reminder 与 first reminder、general reminder 之间的时间选择不能相同，且 seconde reminder 不能早于 first reminder
export const isSecondReminderValid = (
  firstReminder: AppointmentReminderListView,
  generalReminder: AppointmentReminderListView,
  minutesAt: number,
  daysBefore?: number,
) => {
  const isFirstReminderEnabled = firstReminder && firstReminder.isEnabled;
  const isGeneralReminderEnabled = generalReminder && generalReminder.isEnabled;
  let isValid = true;

  if (isFirstReminderEnabled && daysBefore === firstReminder.daysBefore) {
    isValid = minutesAt > firstReminder.minutesAt;
  }
  if (isValid && isGeneralReminderEnabled && daysBefore === generalReminder.daysBefore) {
    isValid = minutesAt !== generalReminder.minutesAt;
  }

  return isValid;
};

// general reminder 互斥逻辑：general reminder 与 first reminder、second reminder 之间的时间选择不能相同
export const isGeneralReminderValid = (
  firstReminder: AppointmentReminderListView,
  secondeReminder: AppointmentReminderListView,
  minutesAt: number,
  daysBefore?: number,
) => {
  const isSecondReminderEnabled = secondeReminder && secondeReminder.isEnabled;
  const isFirstReminderEnabled = firstReminder && firstReminder.isEnabled;
  let isValid = true;

  if (isFirstReminderEnabled && daysBefore === firstReminder.daysBefore) {
    isValid = minutesAt !== firstReminder.minutesAt;
  }
  if (isValid && isSecondReminderEnabled && daysBefore === secondeReminder.daysBefore) {
    isValid = minutesAt !== secondeReminder.minutesAt;
  }

  return isValid;
};
