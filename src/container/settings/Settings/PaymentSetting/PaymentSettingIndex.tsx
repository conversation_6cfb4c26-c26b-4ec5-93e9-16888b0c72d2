import React, { memo } from 'react';
import { Redirect, Route, Switch } from 'react-router';
import { PATH_PAYMENT_SETTING } from '../../../../router/paths';
import { useInvoiceReinvent } from '../../../PaymentFlow/hooks/useInvoiceReinvent';
import { PaymentSetting } from './PaymentSetting';
import { PaymentSetting as PaymentSettingInvoiceV4 } from './PaymentSettingInvoiceV4';

export const PaymentSettingIndex = memo(function PaymentSettingIndex() {
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  return (
    <Switch>
      <Route path={PATH_PAYMENT_SETTING.path} component={isNewOrderV4Flow ? PaymentSettingInvoiceV4 : PaymentSetting} />
      <Redirect to={PATH_PAYMENT_SETTING.path} />
    </Switch>
  );
});
