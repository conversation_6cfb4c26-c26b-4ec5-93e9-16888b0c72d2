import { Avatar } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import SvgIconPlusSvg from '../../../../../../assets/svg/icon-plus.svg';
import { SvgIcon } from '../../../../../../components/Icon/Icon';
import { Switch } from '../../../../../../components/SwitchCase';
import { selectStaff } from '../../../../../../store/staff/staff.selectors';
import { AssignStaffBtnView } from '../AssignStaffBtn/AssignStaffBtn.style';

export interface AssignStaffMainForTableProps {
  staffId?: number;
  className?: string;
  style?: React.CSSProperties;
  onOpenDropdown?: () => void;
}

export const AssignStaffMainForTable: React.FC<AssignStaffMainForTableProps> = (props) => {
  const { style, className, staffId, onOpenDropdown } = props;
  const [staff] = useSelector(selectStaff(staffId));

  return (
    <AssignStaffBtnView className={className} onClick={onOpenDropdown} style={style}>
      <Switch>
        <Switch.Case if={staffId}>
          <Avatar.Staff
            className="moe-border-none"
            src={staff.avatarPath}
            name={staff.fullName()}
            size="xs"
            color={staff.colorCode}
          />
          <div className="moe-ml-[4px]">{staff.fullName()}</div>
        </Switch.Case>
        <Switch.Case else>
          <div className="moe-w-[24px] moe-h-[24px] moe-rounded-full moe-bg-white moe-flex moe-justify-center moe-items-center">
            <SvgIcon src={SvgIconPlusSvg} size={12} />
          </div>
          <div className="moe-ml-[4px]">Assign staff</div>
        </Switch.Case>
      </Switch>
    </AssignStaffBtnView>
  );
};
