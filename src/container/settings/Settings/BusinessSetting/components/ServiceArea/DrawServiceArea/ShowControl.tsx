import { useDispatch, useSelector } from 'amos';
import { Checkbox } from 'antd';
import React, { memo, useEffect } from 'react';
import SvgIconTooltipsSvg from '../../../../../../../assets/svg/icon-tooltips.svg';
import { SvgIcon } from '../../../../../../../components/Icon/Icon';
import { Tooltip } from '../../../../../../../components/Popup/Tooltip';
import { useDrawMarkerCluster } from '../../../../../../../components/ServiceArea/hooks/useDrawMap';
import { getNearbyCustomerList } from '../../../../../../../store/serviceArea/serviceArea.actions';
import { selectBusinessNearbyCustomers } from '../../../../../../../store/serviceArea/serviceArea.selectors';
import { useBool } from '../../../../../../../utils/hooks/useBool';
import { type GoogleMapApi } from '../../../../../../../utils/hooks/useGoogleMap';

interface ShowControlProps {
  isPolygonType: boolean;
  googleMapApi: GoogleMapApi;
  showExistArea: boolean;
  onShowExistAreaChange: (v: boolean) => void;
  businessId: number;
}

export const ShowControl = memo((props: ShowControlProps) => {
  const { isPolygonType, googleMapApi, showExistArea, onShowExistAreaChange, businessId } = props;
  const showClient = useBool(false);
  const drawDrawMarkerCluster = useDrawMarkerCluster(businessId, googleMapApi.map);
  const [nearbyCustomerList] = useSelector(selectBusinessNearbyCustomers(businessId));
  const dispatch = useDispatch();

  useEffect(() => {
    const marker = showClient.value && drawDrawMarkerCluster();
    return () => {
      marker && marker.clearMarkers();
    };
  }, [drawDrawMarkerCluster, showClient.value]);

  useEffect(() => {
    !nearbyCustomerList.size && dispatch(getNearbyCustomerList({ businessId }, businessId));
  }, []);

  return (
    <div className="moe-justify-between moe-pb-[16px]">
      <div className="moe-flex moe-justify-between">
        <div className="moe-text-[14px] moe-font-bold moe-text-[#333]">
          {isPolygonType ? (
            'Draw here'
          ) : (
            <Tooltip overlay="Some zipcode areas are currently not visible on the map, but this does not impact the functionality of the settings.">
              <div className="moe-flex moe-items-center">
                <span>Preview</span>
                <SvgIcon src={SvgIconTooltipsSvg} size={18} className="!moe-cursor-pointer" />
              </div>
            </Tooltip>
          )}
        </div>
        <div>
          <Checkbox checked={showExistArea} onChange={(e) => onShowExistAreaChange(e.target.checked)}>
            Show other service areas
          </Checkbox>
          <Checkbox checked={showClient.value} onChange={(e) => showClient.as(e.target.checked)}>
            Show active clients
          </Checkbox>
        </div>
      </div>
    </div>
  );
});
