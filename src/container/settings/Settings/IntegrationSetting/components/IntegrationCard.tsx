import { Button, Text } from '@moego/ui';
import React, { memo } from 'react';
import {
  IntegrationCardContent,
  IntegrationCardFooter,
  IntegrationCardHeader,
  IntegrationCardView,
} from './IntegrationCard.style';

interface IntegrationCardProps {
  icon: string;
  title: string;
  extra?: JSX.Element;
  description: string;
  subDescription: string;
  handleViewDetail: () => void;
}

export const IntegrationCard = memo((props: IntegrationCardProps) => {
  const { icon, title, extra, description, subDescription, handleViewDetail } = props;
  return (
    <IntegrationCardView>
      <IntegrationCardHeader>
        <section className="icon">
          <img src={icon} alt={title} />
        </section>
        <section className="extra">{extra}</section>
      </IntegrationCardHeader>
      <IntegrationCardContent>
        <Text className="title" variant="caption">
          {title}
        </Text>
        <Text className="description" variant="caption" title={description}>
          {description}
        </Text>
        <Text className="sub-description" variant="caption" title={subDescription}>
          {subDescription}
        </Text>
      </IntegrationCardContent>
      <IntegrationCardFooter>
        <Button onPress={handleViewDetail} variant="tertiary-legacy" className="moe-ml-[-20px]">
          View details
        </Button>
      </IntegrationCardFooter>
    </IntegrationCardView>
  );
});
