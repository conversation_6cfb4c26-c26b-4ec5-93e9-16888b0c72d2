import { VisibilityType } from '@moego/api-web/moego/models/organization/v1/camera_enums';
import { Heading, Tag, Text, createColumnHelper } from '@moego/ui';
import React, { useMemo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import {
  CameraType,
  type CompanyCameraRecord,
} from '../../../../../../store/cameraIntegration/cameraIntegration.boxes';
import { CameraActions } from '../CameraList/CameraActions';

export const useCameraListColumns = () => {
  const columnHelper = createColumnHelper<CompanyCameraRecord>();

  return useMemo(() => {
    return [
      columnHelper.display({
        id: 'cameraId',
        header: 'Camera ID',
        size: 143,
        cell: (props) => (
          <Heading size="5" className="moe-text-primary">
            {props.row.original.originCameraId}
          </Heading>
        ),
      }),
      columnHelper.display({
        id: 'name',
        header: 'Camera name',
        cell: (props) => (
          <Text variant="regular-short" className="moe-text-primary">
            {props.row.original.originCameraTitle}
          </Text>
        ),
      }),
      columnHelper.display({
        id: 'type',
        header: 'Camera type',
        size: 234,
        cell: (props) => {
          const { visibilityType, lodgingUnitName } = props.row.original;

          return (
            <div>
              <Text variant="regular-short" className="moe-text-primary">
                {CameraType.mapLabels[visibilityType]}
              </Text>
              <Condition if={visibilityType === VisibilityType.PRIVATE && lodgingUnitName}>
                <Text variant="regular-short" className="moe-mt-xxs moe-text-tertiary">
                  {lodgingUnitName}
                </Text>
              </Condition>
            </div>
          );
        },
      }),
      columnHelper.display({
        id: 'status',
        header: 'Status',
        size: 155,
        cell: (props) =>
          props.row.original.isActive ? (
            <Tag variant="outlined" label="Active" isBordered color="success" />
          ) : (
            <Tag variant="outlined" label="Inactive" color="neutral" />
          ),
      }),
      columnHelper.display({
        id: 'action',
        header: 'Action',
        size: 177,
        cell: (props) => <CameraActions cameraData={props.row.original.toJSON()} />,
      }),
    ];
  }, []);
};
