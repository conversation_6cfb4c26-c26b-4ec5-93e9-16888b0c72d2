import { useSelector } from 'amos';
import { useMemo } from 'react';
import { selectCurrentQuickBookSettingList } from '../../../../../../store/quickbook/quickbook.selectors';
import { type QuickBookCompany } from '../QuickBookSync.options';

export const useActiveQuickBookCompany = () => {
  const [settings] = useSelector(selectCurrentQuickBookSettingList());
  return useMemo(() => {
    const companies: QuickBookCompany[] = [];
    settings.forEach((s) => {
      if (!s.enableSync || companies.some((c) => c.email === s.connectEmail && c.name === s.connectCompanyName)) {
        return;
      }
      companies.push({ name: s.connectCompanyName, email: s.connectEmail });
    });
    return companies;
  }, [settings]);
};
