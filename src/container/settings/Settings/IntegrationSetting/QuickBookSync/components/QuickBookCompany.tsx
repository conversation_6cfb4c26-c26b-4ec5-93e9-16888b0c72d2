import { AlertDialog, DatePicker, Heading, Modal, Table, Text, toast } from '@moego/ui';
import { useDispatch } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import React, { type Key, useCallback, useState } from 'react';
import { updateQuickBookSetting } from '../../../../../../store/quickbook/quickbook.actions';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { type QuickBookConnection, type QuickBookCompany as TQuickBookCompany } from '../QuickBookSync.options';
import { useActiveQuickBookCompanyConnectionData } from '../hooks/useActiveQuickBookCompanyConnectionData';
import {
  ACTION_DISCONNECT,
  ACTION_EDIT_BEGIN_DATE,
  BEGIN_DATE_DISPLAY_FORMAT,
  useQuickBookCompanyConnectionColumns,
} from '../hooks/useQuickBookCompanyConnectionColumns';
import { useStartSyncDisabledDate } from '../hooks/useStartSyncDisabledDate';

interface Props {
  company: TQuickBookCompany;
}

export const QuickBookCompany = ({ company }: Props) => {
  const dispatch = useDispatch();
  const [actionRow, setActionRow] = useState<QuickBookConnection>();
  const editStartTimeModal = useBool();
  const [editingBeginDate, setEditingBeginDate] = useState<Dayjs>();
  const disconnectModal = useBool();
  const onRowAction = useCallback((action: Key, row: QuickBookConnection) => {
    setActionRow(row);
    switch (action) {
      case ACTION_EDIT_BEGIN_DATE:
        setEditingBeginDate(dayjs(row.setting.syncBeginDate, DATE_FORMAT_EXCHANGE));
        editStartTimeModal.open();
        break;
      case ACTION_DISCONNECT:
        disconnectModal.open();
        break;
      default:
        break;
    }
  }, []);
  const columns = useQuickBookCompanyConnectionColumns(onRowAction);
  const data = useActiveQuickBookCompanyConnectionData(company);
  const disabledDate = useStartSyncDisabledDate();

  const handleConfirmEditBeginDate = async () => {
    const { location } = actionRow!;
    await dispatch(
      updateQuickBookSetting({ syncBeginDate: editingBeginDate!.format(DATE_FORMAT_EXCHANGE) }, +location.id),
    );
  };

  const handleConfirmDisconnect = async () => {
    const { location } = actionRow!;
    await dispatch(updateQuickBookSetting({ enableSync: 0 }, +location.id));
    toast({ type: 'success', title: `${location.name} has been disconnected!` });
  };

  return (
    <>
      <div className="moe-mt-l">
        <Heading size="3">QuickBooks: {company.name}</Heading>
        <Text className="moe-text-tertiary moe-mt-xxs" variant="small">
          QuickBooks email: {company.email}
        </Text>
        {/*SettingsV2.tsx 里给 ScrollerProvider 指定了 paddingTop 导致 sticky header 上边会漏出来一点，这里抵消掉；SettingsV2
         指定 paddingTop 的原因不详*/}
        <Table
          className="moe-mt-s"
          columns={columns}
          data={data}
          getRowId={(row) => row.location.id}
          stickyHeaderOffset={-32}
        />
      </div>
      <Modal
        size="s"
        isOpen={editStartTimeModal.value}
        onClose={editStartTimeModal.close}
        title="Edit sync start time"
        confirmText="Save"
        onConfirm={handleConfirmEditBeginDate}
      >
        <DatePicker
          value={editingBeginDate}
          onChange={(v) => setEditingBeginDate(v!)}
          isClearable={false}
          format={BEGIN_DATE_DISPLAY_FORMAT}
          disabledDate={disabledDate}
        />
      </Modal>
      <AlertDialog
        variant="danger"
        isOpen={disconnectModal.value}
        onClose={disconnectModal.close}
        title="Disconnect from QuickBooks"
        confirmText="Disconnect"
        onConfirm={handleConfirmDisconnect}
      >
        <Text variant="regular">
          {actionRow?.location?.name} will be disconnected from QuickBooks, and synced data will be retained. You can
          reconnect it if necessary.
        </Text>
      </AlertDialog>
    </>
  );
};
