/*
 * TODO(Perqin, P2): This file belongs to legacy QuickBook, and should be removed after QuickBook v2 is fully launched.
 */

import { QuestionCircleFilled } from '@ant-design/icons';
import { Button, Heading, Switch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Form, Select } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useEffect } from 'react';
import { SwitchBusinessDropdown } from '../../../../../../components/Business/SwitchBusinessDropdown';
import { Button as OldButton } from '../../../../../../components/Button/Button';
import { DatePicker } from '../../../../../../components/DatePicker/DatePicker';
import { Loading } from '../../../../../../components/Loading/Loading';
import { Modal } from '../../../../../../components/Modal/Modal';
import { QuestionTooltip } from '../../../../../../components/Popup/Tooltip';
import { WithPricingEnableUpgrade } from '../../../../../../components/Pricing/WithPricingComponents';
import { getBusinessOptions } from '../../../../../../store/business/business.actions';
import { businessOptionsBox, currentBusinessIdBox } from '../../../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import {
  getQuickBookAccountInfo,
  getQuickBookNameList,
  updateQuickBookSetting,
} from '../../../../../../store/quickbook/quickbook.actions';
import {
  selectCurrentQuickbookSetting,
  selectQuickbookAccountList,
} from '../../../../../../store/quickbook/quickbook.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { SettingTitle } from '../../../components/SettingTitle';
import { QuickBookSyncPreOnboardingBanner } from '../PreOnboarding/QuickBookSyncPreOnboarding.banner';
import { QuickbookSyncCardView } from '../QuickbookSync.style';

export interface QuickbookSyncCardProps {
  className?: string;
}

export const QuickbookSyncCard = memo<QuickbookSyncCardProps>(({ className }) => {
  const loading = useBool();
  const disconnectPopupFlag = useBool();
  const dispatch = useDispatch();

  const [quickbook, quickbookAccountList, businessId, business, bussinessOptions] = useSelector(
    selectCurrentQuickbookSetting,
    selectQuickbookAccountList(),
    currentBusinessIdBox,
    selectCurrentBusiness,
    businessOptionsBox,
  );
  // console.log(quickbook);
  // console.log(quickbookAccountList?.accountList);
  const dataFormatType = bussinessOptions.dateFormatList[business.dateFormatType - 1]?.label;
  const quickbookSyncTooltip =
    'Once turned off, the data sync process will be paused. To avoid unexpected interruption, it’s not recommended to do so regularly. ';
  const quickbookSyncStausTooltip =
    'Sync status. If it’s your first time to sync up QuickBooks, please expect up to 48 hours to have the data transferred. ';
  const quickbookSyncStartTimeTooltip = (
    <>
      <span>Select the start date of appointments to be synced. The start date can be up to 3 years from now.</span>
      <br />
      <span>
        If it’s your first time to sync up QuickBooks, please expect up to 48 hours to have the data transferred.
      </span>
    </>
  );

  const quickbookSyncAccountTooltip = (
    <>
      <span>
        The destination account will be synced to. Once selected, you cannot change the account unless turn off the sync
        and re-connect.
      </span>
      <br />
      <br />
      <span>Synced QuickBooks account:</span>
      <ol>
        <li>
          One MoeGo business can be synced to only one QuickBooks account, please set up an account in QuickBooks to
          sync your data.
        </li>
        <li>
          If you have multiple businesses, please make sure that your business are been synced to different accounts.
        </li>
      </ol>
    </>
  );

  const onSwitchChange = () => {
    if (quickbook?.enableSync) {
      disconnectPopupFlag.open();
    } else {
      //if we want to reconnect, we have to go through oauth process one more time
      handleConnect();
    }
  };

  const onModalClose = () => {
    disconnectPopupFlag.close();
  };

  const onAccountDisconnect = () => {
    disconnectPopupFlag.close();

    updateSetting('enableSync', 0);
  };

  const updateSetting = useSerialCallback(async (key: 'enableSync' | 'others', value: any) => {
    loading.open();
    const newSetting = {
      accountId: quickbook?.accountId || '',
      accountName: quickbook?.accountName || '',
      syncBeginDate: quickbook?.syncBeginDate || '',
      enableSync: quickbook?.enableSync || 0,
    };
    //update accountName and begin date
    if (key === 'others') {
      const accountId = () => {
        if (value.accountName === '') return quickbook?.accountId;
        else {
          for (const cur of quickbookAccountList?.accountList || []) {
            if (cur.accountName === value.accountName) return cur.accountId;
          }
          return quickbook?.accountId;
        }
      };

      value.accountId = accountId();
      await dispatch(updateQuickBookSetting({ ...newSetting, ...value }));
    } else {
      await dispatch(updateQuickBookSetting({ ...newSetting, [key]: value }));
    }
    loading.close();
  });

  const handleConnect = () => {
    loading.open();
    // seems like this component is deprecated
    window.open(quickbook?.oauthUrl || '', '_self');
  };

  const onSave = (e: { accountName: string; beginDate: any }) => {
    // if account is disconeected, we can not save anything
    if (!quickbook?.enableSync) {
      return;
    }

    const updatedValue = {
      syncBeginDate: dayjs(e.beginDate).format('YYYY-MM-DD'),
      accountName: e.accountName,
    };
    updateSetting('others', updatedValue);
  };

  const initialPage = useSerialCallback(async () => {
    loading.open();
    await dispatch(getQuickBookAccountInfo());
    dispatch(getBusinessOptions());
    loading.close();
    await dispatch(getQuickBookNameList());
  });

  useEffect(() => {
    if (isNormal(businessId)) {
      initialPage();
    }
  }, [businessId]);
  return (
    <QuickbookSyncCardView>
      <Loading loading={loading.value}>
        <SettingTitle
          title={
            <div>
              <div className="moe-flex moe-items-center moe-gap-x-[16px]">
                <Heading size="2">QuickBooks sync</Heading>
                <SwitchBusinessDropdown scene="working" />
              </div>
              <div className="moe-text-regular-short moe-text-tertiary moe-mt-[4px]">
                This integration will automatically sync the data in MoeGo to your QuickBooks account.
              </div>
            </div>
          }
        />
        <div className="card-body">
          {!loading.value && (
            <section className="moe-w-[631px]">
              <QuickBookSyncPreOnboardingBanner scenario="integration" />
            </section>
          )}
          {!quickbook?.connectStatus ? (
            <WithPricingEnableUpgrade permission="quickBook" overrideEvent="onPress">
              <Button onPress={handleConnect}>Connect to QuickBooks</Button>
            </WithPricingEnableUpgrade>
          ) : (
            <>
              <div className="card-body-header">
                <p className="card-body-header-title">Auto Quickbook sync</p>
                <QuestionTooltip overlay={quickbookSyncTooltip} size={16} />
                <Switch
                  isSelected={quickbook?.enableSync === 1}
                  onChange={onSwitchChange}
                  className="quickbook-sync-switch"
                  classNames={{
                    wrapper: 'moe-ml-[12px]',
                  }}
                />
              </div>
              <div className="quickbook-account-infos">
                <div className="quickbook-info-top">
                  <div className="quickbook-info-row">
                    <div className="quickbook-info-title">
                      <p style={{ marginRight: '5px' }}>Synced Status</p>
                      <QuestionTooltip overlay={quickbookSyncStausTooltip} size={16} />
                    </div>

                    {quickbook?.enableSync ? (
                      <span style={{ color: '#F96B18' }} className="quickbook-info-detail connected-and-disconnect">
                        Connected
                      </span>
                    ) : (
                      <span style={{ color: '#F6413C' }} className="quickbook-info-detail connected-and-disconnect">
                        Disconnected
                      </span>
                    )}
                  </div>
                  <div className="quickbook-info-row">
                    <p className="quickbook-info-title">Synced QuickBooks email:</p>
                    <p className="quickbook-info-detail">{quickbook?.connectEmail}</p>
                  </div>
                  <div className="quickbook-info-row">
                    {' '}
                    <p className="quickbook-info-title">Synced QuickBooks company name:</p>
                    <p className="quickbook-info-detail">{quickbook?.connectCompanyName}</p>
                  </div>
                </div>
                <div className="quickbook-info-bottom">
                  <Form
                    name="quickbook-infos"
                    onFinish={onSave}
                    initialValues={{
                      beginDate: quickbook?.syncBeginDate ? dayjs(quickbook?.syncBeginDate) : dayjs(),
                      accountName: quickbook?.accountName.length > 0 ? quickbook?.accountName : void 0,
                    }}
                  >
                    <div className="quickbook-info-row">
                      <div className="quickbook-info-title">
                        <p style={{ marginRight: '7px' }}>Synced start time: </p>
                        <QuestionTooltip overlay={quickbookSyncStartTimeTooltip} size={16} />
                      </div>

                      <Form.Item name="beginDate">
                        <DatePicker
                          size="large"
                          disabled={quickbook?.enableSync === 0}
                          disabledDate={(d) => !d || d.isAfter(new Date())}
                          allowClear={false}
                          format={dataFormatType}
                          style={{ width: '276px', height: '38px', marginLeft: '40px' }}
                        />
                      </Form.Item>
                    </div>
                    <div className="quickbook-info-row">
                      <div className="quickbook-info-title">
                        <p style={{ marginRight: '2px' }}>Synced QuickBooks account: </p>
                        <QuestionTooltip overlay={quickbookSyncAccountTooltip} size={16} />
                      </div>

                      <Form.Item name="accountName">
                        <Select
                          placeholder="quickbook account"
                          size="large"
                          disabled={quickbook?.enableSync === 0}
                          style={{ width: '276px', marginLeft: '40px' }}
                        >
                          {quickbookAccountList?.accountList.map((element, idx) => {
                            return (
                              <Select.Option key={idx} value={element.accountName}>
                                {element.accountName}
                              </Select.Option>
                            );
                          })}
                        </Select>
                      </Form.Item>
                    </div>
                    <OldButton buttonRadius="circle" btnType="primary" htmlType="submit" className="moe-mt-[40px]">
                      Save
                    </OldButton>
                  </Form>
                </div>
              </div>
            </>
          )}
        </div>
      </Loading>
      <Modal
        visible={disconnectPopupFlag.value}
        onClose={onModalClose}
        width="505px"
        style={{ fontSize: '16px', lineHeight: '22px', position: 'relative' }}
        bodyStyle={{ padding: '32px 24px 24px 46px' }}
      >
        <QuestionCircleFilled style={{ color: '#FAAD14', position: 'absolute', left: '24px', fontSize: '14px' }} />
        <p style={{ marginBottom: '16px' }}>
          Once disconnected, you will no longer be able to send invoices and receipts directly to your QuickBooks
          account from MoeGo. Are you sure to proceed?
        </p>
        <p style={{ marginBottom: '38px' }}>Tips: the data that has been synced will not be deleted.</p>
        <div className="moe-flex moe-justify-end moe-gap-x-[16px]">
          <Button variant="secondary" onPress={onModalClose}>
            Cancel
          </Button>
          <Button variant="primary" onPress={onAccountDisconnect}>
            Disconnect
          </Button>
        </div>
      </Modal>
    </QuickbookSyncCardView>
  );
});
