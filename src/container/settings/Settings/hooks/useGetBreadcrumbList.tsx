import { findLastIndex } from 'lodash';
import { useMemo } from 'react';
import { useLocation } from 'react-router';
import { type BreadcrumbItem } from '../../../../layout/Breadcrumb';
import { PATH_SETTING } from '../../../../router/paths';

const BASE_ROUTE: BreadcrumbItem = {
  route: PATH_SETTING.build({ panel: 'landing' }),
  RoutePath: PATH_SETTING,
  label: 'Settings',
};

export const useGetBreadcrumbList = (list: BreadcrumbItem[]) => {
  const location = useLocation();
  return useMemo(() => {
    // 找到最后一个 match route 的 Item
    const index = findLastIndex(list, (item) => {
      return item.RoutePath.match(location.pathname);
    });
    if (index === -1) {
      return [BASE_ROUTE];
    }
    return [BASE_ROUTE, ...list.slice(0, index + 1)];
  }, [list, location.pathname]);
};
