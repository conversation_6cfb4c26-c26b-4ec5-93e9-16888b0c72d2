import { Button, Heading, Radio, RadioGroup } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useEffect, useState } from 'react';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { updateBusinessPayrollSetting } from '../../../../../../store/business/business.actions';
import { PayrollCalculationBase, PayrollCalculationBaseTips } from '../../../../../../store/payment/payment.boxes';
import { type EnumValues } from '../../../../../../store/utils/createEnum';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';

export type PayrollCaculationValue = {
  serviceCommissionBased: EnumValues<typeof PayrollCalculationBase>;
};

export const CalculationBase = memo(
  ({
    className = '',
    value,
    onChange,
  }: {
    className?: string;
    value: PayrollCaculationValue;
    onChange: (v: PayrollCaculationValue) => void;
  }) => {
    const [caculateState, setCaculateState] = useState(value.serviceCommissionBased + '');
    const [initValue, setInitValue] = useState(value.serviceCommissionBased);
    useEffect(() => {
      setInitValue(value.serviceCommissionBased);
      setCaculateState(value.serviceCommissionBased + '');
    }, [value.serviceCommissionBased]);
    const dispatch = useDispatch();
    const updateCaculationBase = useSerialCallback(async () => {
      await dispatch(
        updateBusinessPayrollSetting({
          serviceCommissionBased: Number(caculateState),
        }),
      );
      onChange({ serviceCommissionBased: Number(caculateState) });
      toastApi.success('Payroll settings updated!');
    });
    const renderContent = () => {
      return (
        <div>
          <Heading className="moe-mt-[24px] moe-mb-[16px]" size="6">
            Calculated on
          </Heading>
          <RadioGroup
            value={caculateState}
            onChange={(e) => {
              setCaculateState(e);
            }}
          >
            {PayrollCalculationBase.values.map((item) => {
              return (
                <div key={PayrollCalculationBase.mapLabels[item]} className="!moe-mb-[8px]">
                  <Radio value={item + ''} description={PayrollCalculationBaseTips.mapLabels[item]}>
                    {PayrollCalculationBase.mapLabels[item]}
                  </Radio>
                </div>
              );
            })}
          </RadioGroup>
          <Button className="moe-mt-[24px]" isDisabled={initValue === +caculateState} onPress={updateCaculationBase}>
            Save
          </Button>
        </div>
      );
    };

    return (
      <div>
        <Heading size="3">Payroll calculation base</Heading>
        <div className="!moe-overflow-auto">{renderContent()}</div>
      </div>
    );
  },
);
