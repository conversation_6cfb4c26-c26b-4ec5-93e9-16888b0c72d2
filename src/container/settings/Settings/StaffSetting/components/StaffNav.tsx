import { createEnum } from '../../../../../store/utils/createEnum';
import { type SettingsLeftNavItem } from '../../types';

export enum StaffSettingNav {
  StaffMembers = 'Staff members',
  ShiftManagement = 'Shift management',
  RoleSettings = 'Role permissions',
  PayrollSettings = 'Payroll settings',
  ClockInOut = 'Clock in/out',
}

export type StaffPanel = 'members' | 'roleSetting' | 'payrollSetting' | 'workingHours' | 'clockInout';

export const StaffSettingNavType = createEnum<StaffSettingNav, StaffPanel, string>({
  [StaffSettingNav.StaffMembers]: ['members', 'Staff members'],
  [StaffSettingNav.ShiftManagement]: ['workingHours', 'Shift management'],
  [StaffSettingNav.RoleSettings]: ['roleSetting', 'Role permissions'],
  [StaffSettingNav.PayrollSettings]: ['payrollSetting', 'Payroll settings'],
  [StaffSettingNav.ClockInOut]: ['clockInout', 'Clock in/out'],
});

export const StaffSettingNavList: SettingsLeftNavItem<StaffPanel>[] = [
  {
    title: StaffSettingNav.StaffMembers,
    id: StaffSettingNavType[StaffSettingNav.StaffMembers],
    permissions: ['accessStaffMembers'],
  },
  {
    title: StaffSettingNav.ShiftManagement,
    id: StaffSettingNavType[StaffSettingNav.ShiftManagement],
    permissions: ['accessStaffShift'],
  },
  {
    title: StaffSettingNav.RoleSettings,
    id: StaffSettingNavType[StaffSettingNav.RoleSettings],
    permissions: ['accessRoleSetting'],
  },
  {
    title: StaffSettingNav.PayrollSettings,
    id: StaffSettingNavType[StaffSettingNav.PayrollSettings],
    permissions: ['accessGeneralPayrollSetting'],
  },
  {
    title: StaffSettingNav.ClockInOut,
    id: StaffSettingNavType[StaffSettingNav.ClockInOut],
    permissions: ['clockinOrOut'],
    pricingPermission: 'clockInOut',
  },
];

export enum DragListHandleType {
  Add,
  Edit,
  Remove,
}
