import { type PermissionModel } from '@moego/api-web/moego/models/permission/v1/permission_models';
import { Checkbox, Text } from '@moego/ui';
import { useSelector } from 'amos';
import { cloneDeep, isNil } from 'lodash';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { usePermissionCheck } from '../../../../../../components/GuardRoute/WithPermission';
import { permissionMap, type permissionMapKinds } from '../../../../../../store/business/role.actions';
import { type PermissionKinds } from '../../../../../../store/business/role.boxes';
import { selectCurrentStaff } from '../../../../../../store/staff/staff.selectors';
import { ScopeList } from './ScopeList';

export interface AccessScopeSelector {
  value: string;
  label: string;
}

export const isPermissionInPermissionMap = (permission: PermissionModel): boolean =>
  !isNil(permissionMap[permission.name as keyof typeof permissionMap]);

/**
 * <AUTHOR>
 * 对于 credit 功能没有开白的用户，隐藏 credit 权限
 * 由于后端 permission 还没接 GrowthBook，所以先在前端加上过滤
 * 和后端约定了在2025年2月28日之前接入 GrowthBook，删掉这段逻辑
 */
export const isInWhiteListForCredit = (permission: PermissionModel, isCreditEnabled: boolean): boolean => {
  if (!isCreditEnabled && permission.name === 'accessStoreCredit') return false;
  if (!isCreditEnabled && permission.name === 'applyStoreCreditAtCheckOut') return false;
  return true;
};

export const AccessCheckBox = memo(
  ({
    item,
    handleChange,
    className,
  }: {
    item: PermissionModel;
    handleChange: (value: PermissionModel) => void;
    className?: string;
  }) => {
    const { id, isSelected, subPermissionList, scopeList, selectedScopeIndex, description, displayName } = item;
    const [currentStaff] = useSelector(selectCurrentStaff);
    const ownerHasPermission = usePermissionCheck({
      permissions: permissionMap[item.name as permissionMapKinds] as PermissionKinds[],
      checkCurrentRoleOrOwner: 'owner',
    });

    const permissionEditable = useMemo(
      () => ownerHasPermission || currentStaff.isEnterpriseOwner(),
      [ownerHasPermission, currentStaff],
    );

    return (
      <div className={`moe-flex moe-flex-col ${className}`}>
        <div>
          <Checkbox
            isSelected={isSelected}
            value={id}
            isDisabled={!permissionEditable}
            description={description}
            onChange={(value) => {
              const temp = cloneDeep(item);
              temp.isSelected = value;
              if (temp.scopeList.length > 0) {
                if (temp.name === 'accessClient') {
                  // 因为权限系统用 index 做索引，且默认有索引越大，权限级别越高的隐藏逻辑（且存量数据不能修改），但是 accessClient 权限新增的 scope 权限反而级别更小，所以只能加特殊逻辑
                  temp.selectedScopeIndex = '3';
                } else {
                  temp.selectedScopeIndex = temp.scopeList[0].index;
                }
              }
              handleChange(temp);
            }}
          >
            {displayName}
          </Checkbox>
        </div>
        <Condition if={isSelected}>
          <Condition if={Number(selectedScopeIndex) > 0 && scopeList.length > 0}>
            <ScopeList
              isDisabled={!permissionEditable}
              permissionItem={item}
              onChange={(value) => {
                handleChange({
                  ...item,
                  ...value,
                });
              }}
            />
          </Condition>
          <Condition if={item.name === 'accessClient' && item.selectedScopeIndex === '3'}>
            <Text variant="regular" className="moe-text-caption moe-ml-[28px] moe-mt-[8px] moe-text-tertiary">
              Allow staff member to access specific client detail when viewing appointments, booking requests and
              messages, but not access full client list.
            </Text>
          </Condition>
          <Condition if={subPermissionList && subPermissionList.length > 0}>
            <div className="moe-ml-[25px] moe-mt-[16px] moe-p-[16px] moe-bg-neutral-sunken-0 moe-rounded-[8px] ">
              {subPermissionList.filter(isPermissionInPermissionMap).map((subPermission, index) => {
                return (
                  <AccessCheckBox
                    key={index + subPermission.name}
                    item={subPermission}
                    className="moe-mt-[16px] first:moe-mt-[0px]"
                    handleChange={(value) => {
                      const temp = cloneDeep(item);
                      temp.subPermissionList[index] = value;
                      handleChange(temp);
                    }}
                  ></AccessCheckBox>
                );
              })}
            </div>
          </Condition>
        </Condition>
      </div>
    );
  },
);
