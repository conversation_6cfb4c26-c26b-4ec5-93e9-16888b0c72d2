import React, { Fragment, type ReactElement, type ReactNode, useMemo } from 'react';
import cn from 'classnames';
import { Tag, type TagProps, Text, Tooltip, type TooltipProps } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import { selectPetSizeOptions } from '../../../../../../../store/onlineBooking/settings/petSize.selectors';
import { selectPetBreedOptions } from '../../../../../../../store/pet/petBreed.selectors';
import { selectPetTypeOptions } from '../../../../../../../store/pet/petType.selectors';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { type ServiceLimit, type PetBreedLimit, type PetSizeLimit } from './Limitation.type';
import { selectServiceOptions } from '../../../../../../../store/service/service.selectors';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import type { GroupItemWithOnlyCheck, LimitationFormValue } from './LimitationFormContext';
import { useValidLimitationOptions } from './hooks/useValidLimitationOptions';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type LimitationGroup } from '@moego/api-web/moego/models/organization/v1/staff_availability_models';
import { transformLimitationGroup2FormValue } from '../../../../../../../store/staffSchedule/staffSchedule.utils';

type LimitationRowChildrenProps = {
  limit: LimitationFormValue;
  getServiceGroupTagLabel: (limitGroup: GroupItemWithOnlyCheck<ServiceLimit>, isTooltipContent?: boolean) => ReactNode;
  getPetSizeGroupTagLabel: (limitGroup: GroupItemWithOnlyCheck<PetSizeLimit>, isTooltipContent?: boolean) => ReactNode;
  getPetBreedGroupTagLabel: (
    limitGroup: GroupItemWithOnlyCheck<PetBreedLimit>,
    isTooltipContent?: boolean,
  ) => ReactNode;
};
interface LimitationRowProps {
  className?: string;
  value?: LimitationGroup[];
  serviceItemType?: ServiceItemType;
  children?: (props: LimitationRowChildrenProps) => ReactElement;
}

/** 根据 limit 渲染 limitation 标签 */
export const LimitationRow = (props: LimitationRowProps) => {
  const { className, serviceItemType = ServiceItemType.GROOMING, value = [], children } = props;
  const store = useStore();
  const LimitationFormValue = useMemo(() => transformLimitationGroup2FormValue(value), [value]);
  const limit = useValidLimitationOptions({ value: LimitationFormValue, serviceItemType });
  const hasPetSizeLimit = limit.petSizeLimits.length > 0;
  const hasPetBreedLimit = limit.petBreedLimits.length > 0;
  const hasServiceLimit = limit.serviceLimits.length > 0;
  const isEmptyLimit = !hasPetSizeLimit && !hasPetBreedLimit && !hasServiceLimit;

  const [petSizeOptions, petTypeOptions, serviceOptions] = useSelector(
    selectPetSizeOptions(),
    selectPetTypeOptions(),
    selectServiceOptions(ServiceType.Service),
  );

  /**
   * label 和 tooltip content 的内容是一样的，只是布局不同
   * @param renderItemLabel 单个 tag label 的渲染函数（service、petSize、petBreed）
   * @returns
   */
  const createGroupTagRenderer = <T extends ServiceLimit | PetSizeLimit | PetBreedLimit>(
    renderItemLabel: (limit: T) => ReactNode,
  ) => {
    return (limitGroup: GroupItemWithOnlyCheck<T>, isTooltipContent?: boolean) => {
      const limitContentLabel = limitGroup.value.map((limit, index) => {
        const isLast = index === limitGroup.value.length - 1;
        return (
          <Fragment key={index}>
            {renderItemLabel(limit)}
            {!isLast && <span className="moe-font-regular"> and </span>}
          </Fragment>
        );
      });
      const onlyLabel = limitGroup.isOnlyChecked && <span className="moe-font-regular">{' (Only)'}</span>;

      if (isTooltipContent) {
        return (
          <>
            {limitContentLabel}
            {onlyLabel}
          </>
        );
      }

      // https://www.figma.com/design/7vRqtdhheadrNBUSUzLkEP/Book-by-Slot-Update?node-id=3449-305277&m=dev
      // 在 tag 上展示 label 时需要做省略，并且 (only) 需要单独展示在外面
      return (
        <div className="moe-flex">
          <div className="moe-overflow-hidden moe-text-ellipsis">{limitContentLabel}</div>
          {onlyLabel}
        </div>
      );
    };
  };

  const getPetSizeTagLabel = ({ petSizeIds, isAllSize, capacity }: PetSizeLimit) => {
    const limitNumberLabel = capacity === 0 ? capacity : `Max ${capacity}`;

    const sizeLabels = isAllSize
      ? 'All sizes'
      : petSizeIds
          .map((id) => petSizeOptions.find(({ value }) => +id === +value)?.label)
          .filter(Boolean)
          .join(', ');

    return (
      <>
        <span className="moe-font-bold">{`${limitNumberLabel} `}</span>
        <span className="moe-font-regular">{sizeLabels}</span>
      </>
    );
  };

  const getPetBreedTagLabel = ({ petTypeId, isAllBreed, breedIds, capacity }: PetBreedLimit) => {
    const limitNumberLabel = capacity === 0 ? capacity : `Max ${capacity}`;
    if (!isNormal(petTypeId)) {
      return 'Unknown breed type';
    }

    const breedTypeOptions = store.select(selectPetBreedOptions(+petTypeId));

    const petTypeLabel = petTypeId ? petTypeOptions.find(({ value }) => +petTypeId == +value)?.label : 'unknown type';
    const breedLabels = isAllBreed
      ? 'All breeds'
      : breedIds
          .map((id) => breedTypeOptions.find(({ value }) => +id === +value)?.label)
          .filter(Boolean)
          .join(', ');

    return (
      <>
        <span className="moe-font-bold">{`${limitNumberLabel} `}</span>
        <span className="moe-font-regular">{`${petTypeLabel} - ${breedLabels}`}</span>
      </>
    );
  };

  const getServiceTagLabel = ({ capacity, isAllService, serviceIds }: ServiceLimit) => {
    const limitNumberLabel = capacity === 0 ? capacity : `Max ${capacity}`;

    const serviceLabels = isAllService
      ? ['All services']
      : serviceIds
          .map((id) => serviceOptions.find(({ value }) => id === value)?.label)
          .filter(Boolean)
          .join(', ');

    return (
      <>
        <span className="moe-font-bold">{`${limitNumberLabel} `}</span>
        <span className="moe-font-regular">{`${serviceLabels}`}</span>
      </>
    );
  };

  const getServiceGroupTagLabel = createGroupTagRenderer(getServiceTagLabel);
  const getPetSizeGroupTagLabel = createGroupTagRenderer(getPetSizeTagLabel);
  const getPetBreedGroupTagLabel = createGroupTagRenderer(getPetBreedTagLabel);

  if (isEmptyLimit) return null;

  if (children) {
    return children({
      limit,
      getServiceGroupTagLabel,
      getPetSizeGroupTagLabel,
      getPetBreedGroupTagLabel,
    });
  }

  return (
    <div className={cn('moe-flex moe-gap-[12px]', className)}>
      <div className="moe-flex moe-gap-xs moe-flex-wrap">
        {limit.serviceLimits.map((limitGroup, index) => (
          <LimitationTag
            key={index}
            tooltipProps={{
              title: 'Service limitation',
              content: getServiceGroupTagLabel(limitGroup, true),
            }}
            label={getServiceGroupTagLabel(limitGroup)}
          />
        ))}
        {limit.petSizeLimits.map((limitGroup, index) => (
          <LimitationTag
            key={index}
            tooltipProps={{
              title: 'Pet size limitation',
              content: getPetSizeGroupTagLabel(limitGroup, true),
            }}
            label={getPetSizeGroupTagLabel(limitGroup)}
          />
        ))}
        {limit.petBreedLimits.map((limitGroup, index) => (
          <LimitationTag
            key={index}
            tooltipProps={{
              title: 'Pet breed limitation',
              content: getPetBreedGroupTagLabel(limitGroup, true),
            }}
            label={getPetBreedGroupTagLabel(limitGroup)}
          />
        ))}
      </div>
    </div>
  );
};

type LimitationTagProps = TagProps & {
  tooltipProps: Omit<TooltipProps, 'content' | 'children'> & {
    title: string | null;
    content: React.ReactNode;
  };
};

const LimitationTag = (props: LimitationTagProps) => {
  const { tooltipProps, ...tagProps } = props;
  const { title, content, ...restTooltipProps } = tooltipProps;

  return (
    <Tooltip
      side="top"
      backgroundTheme="light"
      maxContentHeight={240}
      content={
        title ? (
          <div>
            <Text variant="small">{title}</Text>
            <Text variant="small">{content}</Text>
          </div>
        ) : null
      }
      {...restTooltipProps}
    >
      <Tag className="moe-max-w-[240px] moe-cursor-pointer" color="neutral" variant="filled" {...tagProps} />
    </Tooltip>
  );
};

LimitationRow.displayName = 'LimitationRow';
