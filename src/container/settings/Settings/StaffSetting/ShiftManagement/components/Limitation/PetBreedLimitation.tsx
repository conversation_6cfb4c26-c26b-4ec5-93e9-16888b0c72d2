import { MinorErrorFilled, MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import {
  <PERSON><PERSON>,
  Controller,
  Form,
  IconButton,
  Input,
  type Key,
  Overflow,
  Select,
  Tag,
  Text,
  useFieldArray,
} from '@moego/ui';
import { useSelector, useStore } from 'amos';
import React, { isValidElement, useMemo } from 'react';
import { selectPetBreedOptions } from '../../../../../../../store/pet/petBreed.selectors';
import { selectPetTypeOptions } from '../../../../../../../store/pet/petType.selectors';
import { isNormal } from '../../../../../../../store/utils/identifier';
import type { PetBreedLimit } from './Limitation.type';
import { difference, isNil } from 'lodash';
import cn from 'classnames';
import { MINIMUM_PET_SIZE } from '../../../../../../../store/onlineBooking/settings/petSize.selectors';
import { useLimitationFormContext } from './LimitationFormContext';
import { StaffScheduleTestIds } from '../../../../../../../config/testIds/staffSchedule';
import { LimitationMaxMountFormLabel } from './LimitationMaxMountFormLabel';
import { LimitationOnlyAcceptCheckbox } from './LimitationOnlyAcceptCheckbox';

export interface PetBreedLimitationProps {
  index: number;
  onRemove?: () => void;
}

export const defaultPetBreedLimit: PetBreedLimit = {
  petTypeId: null,
  isAllBreed: true,
  breedIds: [],
  capacity: null,
};

const errorKeys: (keyof PetBreedLimit)[] = ['petTypeId', 'capacity'];

export const PetBreedLimitation = (props: PetBreedLimitationProps) => {
  const { index: rowIndex, onRemove } = props;
  const store = useStore();
  const [typeOptions] = useSelector(selectPetTypeOptions());
  const { form } = useLimitationFormContext();

  const value = form?.watch(`petBreedLimits.${rowIndex}.value`);
  const errors = form?.formState?.errors?.petBreedLimits;

  const { fields, append, remove } = useFieldArray({
    control: form?.control,
    name: `petBreedLimits.${rowIndex}.value`,
  });

  const petTypeOptions = useMemo(
    () =>
      typeOptions.map((option) => ({
        ...option,
        value: String(option.value),
      })),
    [typeOptions],
  );

  const allCheckedKeysMap =
    value?.reduce(
      (map, item) => {
        const petType = item.petTypeId;
        if (!isNormal(petType)) return map;

        if (map[petType]) {
          return {
            ...map,
            [petType]: [...map[petType], ...item.breedIds],
          };
        } else {
          map[petType] = item.breedIds;
          return map;
        }
      },
      {} as Record<Key, string[]>,
    ) || {};

  // 对具有相同 petTypeId 的 row，它们的选项是互斥的，要动态计算每一行的 disabledKeys
  const dynamicDisabledKeys = (petTypeId: Key | null, currentValue: string[]) => {
    if (!isNormal(petTypeId)) return [];

    const allCheckedKeys = allCheckedKeysMap[petTypeId];
    return difference(allCheckedKeys, currentValue);
  };

  const getBreedOptions = (petTypeId: Key | null) => {
    if (!isNormal(petTypeId)) return [];

    return store.select(selectPetBreedOptions(+petTypeId))?.map((option) => ({
      ...option,
      value: String(option.value),
    }));
  };

  const onAddLimit = () => {
    append(defaultPetBreedLimit);
  };

  const onRemoveLimit = (index: number) => {
    remove(index);

    if (value?.length === 1) {
      onRemove?.();
    }
  };

  const renderError = (index: number) => {
    const currentRowErrors = errors?.[rowIndex]?.value?.[index];
    if (!currentRowErrors) return null;
    const errorKey = errorKeys.find((key) => currentRowErrors[key]?.message);

    if (!errorKey) return null;

    return (
      <Text className="moe-text-danger moe-mt-xs moe-flex moe-gap-xxs moe-items-center" variant="small">
        <MinorErrorFilled />
        {currentRowErrors[errorKey]!.message}
      </Text>
    );
  };

  return (
    <div className="moe-mt-[12px]">
      <div className="moe-py-xs moe-px-s moe-bg-neutral-sunken-light moe-rounded-s">
        {/* Header */}
        <div
          className="moe-grid moe-grid-cols-4 moe-gap-x-3"
          style={{ gridTemplateColumns: '140px minmax(0, 1fr) 126px 24px' }}
        >
          <Form.Label isRequired>Type</Form.Label>
          <Form.Label>Breed</Form.Label>
          <LimitationMaxMountFormLabel />
          <div></div>
        </div>

        {/* Content */}
        {fields.map((item, index) => {
          const petTypeId = form?.getValues(`petBreedLimits.${rowIndex}.value.${index}.petTypeId`) ?? item.petTypeId;
          const breedIds = form?.getValues(`petBreedLimits.${rowIndex}.value.${index}.breedIds`) ?? item.breedIds;

          const disabledKeys = dynamicDisabledKeys(petTypeId, breedIds);
          const breedOptions = getBreedOptions(petTypeId);
          const checkedKeysWithDisabled = new Set([...disabledKeys, ...breedIds]);

          return (
            <div key={item.id} className="moe-mb-xs last:moe-mb-0">
              <div
                className="moe-grid moe-grid-cols-4 moe-gap-x-[12px] "
                style={{ gridTemplateColumns: '140px minmax(0, 1fr) 126px 24px' }}
              >
                <Controller
                  control={form?.control}
                  name={`petBreedLimits.${rowIndex}.value.${index}.petTypeId`}
                  render={({ field }) => (
                    <Select
                      items={petTypeOptions}
                      value={isNil(field.value) ? field.value : String(field.value)}
                      onChange={(key) => {
                        field.onChange(key);
                        // pet type 变了之后清空 breedIds
                        form?.setValue(`petBreedLimits.${rowIndex}.value.${index}.breedIds`, []);
                        form?.setValue(`petBreedLimits.${rowIndex}.value.${index}.isAllBreed`, true);
                      }}
                      data-testid={`${StaffScheduleTestIds.PetBreedLimitationPetTypeSelector}-${index}`}
                    />
                  )}
                />
                <Controller
                  control={form?.control}
                  name={`petBreedLimits.${rowIndex}.value.${index}.breedIds`}
                  render={({ field }) => (
                    <Select.Multiple
                      showSelectAll
                      enableSelectAllSearch
                      mode="tag"
                      placeholder="All breeds"
                      classNames={{
                        control: 'moe-max-w-full moe-overflow-hidden',
                      }}
                      tagClassNames={{
                        base: cn('moe-max-w-[calc(100%_-_8px)]', {
                          'moe-max-w-[calc(100%_-_40px)]': field.value.length > 1,
                        }),
                      }}
                      items={breedOptions}
                      disabledKeys={disabledKeys}
                      value={checkedKeysWithDisabled}
                      renderValues={(values, _, renderItems) => {
                        // renderItems 是组件库提供的渲染函数，默认情况下，组件库会使用 renderItems(values) 的返回值作为 tag children
                        // 自定义场景：tag 超出一行后使用 +x 表示剩余数量，并且不渲染 disabled tag
                        // petBreedLimitation 和 petSizeLimitation、serviceLimitation 的计算会有略微的区别，因为 petBreed 有二级选项 petType - petBreed(e.g. Cat - 狸花猫)，disable key 只会从同品种中计算
                        const valuesWithoutDisable = values.filter(({ key }) => !disabledKeys.includes(`${key}`));
                        const itemsElement = renderItems(valuesWithoutDisable);

                        if (valuesWithoutDisable.length > 0) {
                          const validItemsElement = isValidElement(itemsElement) ? itemsElement : <>{itemsElement}</>;

                          return (
                            <Overflow
                              key={`${index}-${values.length}`}
                              className="moe-gap-xs moe-my-[-2px]"
                              renderRemaining={(count) => (
                                <Tag
                                  className="moe-shrink-0 moe-whitespace-nowrap"
                                  variant="filled"
                                  color="neutral"
                                  label={`+${count}`}
                                />
                              )}
                            >
                              {validItemsElement?.props?.children}
                            </Overflow>
                          );
                        }

                        return itemsElement;
                      }}
                      onChange={(keys) => {
                        const breedIds = difference(keys, disabledKeys);
                        // 未选择 breed type 时认为是全选
                        const isAllBreed = breedIds.length === 0 || breedIds.length === breedOptions.length;
                        field.onChange(breedIds);

                        form?.setValue(`petBreedLimits.${rowIndex}.value.${index}.isAllBreed`, isAllBreed);
                      }}
                      data-testid={`${StaffScheduleTestIds.PetBreedLimitationPetBreedSelector}-${index}`}
                    />
                  )}
                />
                <Controller
                  control={form?.control}
                  name={`petBreedLimits.${rowIndex}.value.${index}.capacity`}
                  render={({ field }) => (
                    <Input.Number
                      placeholder="Enter…"
                      precision={0}
                      minValue={MINIMUM_PET_SIZE}
                      value={field.value}
                      onChange={field.onChange}
                      data-testid={`${StaffScheduleTestIds.PetBreedLimitationPetCountInput}-${index}`}
                    />
                  )}
                />

                <IconButton
                  icon={<MinorTrashOutlined />}
                  color="transparent"
                  onPress={() => onRemoveLimit(index)}
                  data-testid={`${StaffScheduleTestIds.PetBreedLimitationDeleteBtn}-${index}`}
                />
              </div>
              {renderError(index)}
            </div>
          );
        })}

        <div className="moe-flex moe-justify-between moe-items-center">
          <Button size="s" variant="tertiary" icon={<MinorPlusOutlined />} onPress={onAddLimit}>
            And
          </Button>
          <Controller
            control={form?.control}
            name={`petBreedLimits.${rowIndex}.isOnlyChecked`}
            render={({ field }) => {
              const limit = form?.getValues(`petBreedLimits.${rowIndex}.value`);
              return (
                <LimitationOnlyAcceptCheckbox
                  title="Exclude other breeds"
                  value={field.value}
                  limit={limit}
                  onChange={field.onChange}
                />
              );
            }}
          />
        </div>
      </div>
    </div>
  );
};

PetBreedLimitation.displayName = 'PetBreedLimitation';
