import { Button, Checkbox, type FieldArray, type FieldPathValue, Markup, useFieldArray } from '@moego/ui';
import {
  type GroupItemWithOnlyCheck,
  useLimitationFormContext,
  type LimitationFormValue,
} from './LimitationFormContext';
import React, { type ReactNode } from 'react';
import { StaffScheduleTestIds } from '../../../../../../../config/testIds/staffSchedule';
import { MinorPlusOutlined } from '@moego/icons-react';
import { Divider } from 'antd';

export interface LimitationGroupProps<K extends keyof LimitationFormValue> {
  fieldName: K;
  defaultLimit: FieldArray<LimitationFormValue, K> extends GroupItemWithOnlyCheck<infer T> ? T : never;
  title: string;
  render: (info: {
    index: number;
    onRemoveItem: () => void;
  }) => ReactNode;
}

/**
 * 数据结构：
 * 1. checkbox
 * 2. +or
 */
export const LimitationGroup = <K extends keyof LimitationFormValue>(props: LimitationGroupProps<K>) => {
  const { fieldName, render: renderRow, defaultLimit, title } = props;
  const { form } = useLimitationFormContext();

  const {
    fields: limitationRows,
    append,
    remove,
  } = useFieldArray({
    control: form?.control,
    name: fieldName,
  });

  const hasLimit = !!limitationRows.length;

  const removeAll = () => {
    form?.setValue(fieldName, [] as FieldPathValue<LimitationFormValue, K>);
  };

  const onAddLimit = () => {
    const addedGroupItem = {
      value: [defaultLimit],
      isOnlyChecked: false,
      isOnlyDisabled: true,
    } as FieldArray<LimitationFormValue, K>;

    append(addedGroupItem);
  };

  return (
    <div>
      <Checkbox
        isSelected={hasLimit}
        onChange={(isSelected) => {
          removeAll();
          form?.clearErrors(fieldName);
          if (isSelected) {
            onAddLimit();
          }
        }}
        data-testid={StaffScheduleTestIds.ServiceLimitationCheckbox}
      >
        {title}
      </Checkbox>

      {hasLimit && (
        <div className="moe-ml-m">
          {limitationRows?.map((row, rowIndex) => {
            const isLatest = rowIndex === limitationRows.length - 1;

            return (
              <div key={row.id}>
                {renderRow({
                  index: rowIndex,
                  onRemoveItem: () => remove(rowIndex),
                })}

                {!isLatest && (
                  <Divider plain style={{ margin: '10px 0' }}>
                    <Markup variant="caption" className="moe-text-tertiary">
                      Or
                    </Markup>
                  </Divider>
                )}
              </div>
            );
          })}
          <Button
            className="moe-min-w-[108px] moe-mt-[10px]"
            size="s"
            variant="secondary"
            icon={<MinorPlusOutlined />}
            onPress={onAddLimit}
          >
            Or
          </Button>
        </div>
      )}
    </div>
  );
};
