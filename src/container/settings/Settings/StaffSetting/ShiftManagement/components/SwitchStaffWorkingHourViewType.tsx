import { MinorCalendarOutlined, MinorListOutlined } from '@moego/icons-react';
import { SegmentControl } from '@moego/ui';
import React from 'react';
import { StaffWorkingHourViewType } from '../../../../../../store/staffSchedule/staffSchedule.types';
import { useControllableValue } from '../../../../../../utils/hooks/useControlledValue';
import { SMTestIds } from '../../../../../../config/testIds/shiftManagement';

export interface SwitchStaffWorkingHourViewTypeProps {
  value?: number;
  onChange?: (active: number) => void;
}

export function SwitchStaffWorkingHourViewType(props: SwitchStaffWorkingHourViewTypeProps) {
  const [viewType, setViewType] = useControllableValue(props, { defaultValue: StaffWorkingHourViewType.ListView });

  return (
    <SegmentControl
      data-testid={SMTestIds.SettingViewTypeSwitch}
      onChange={(type) => setViewType(Number(type))}
      value={viewType.toString()}
    >
      {StaffWorkingHourViewType.values.map((value) => {
        const isList = value === StaffWorkingHourViewType.ListView;
        const icon = isList ? <MinorListOutlined /> : <MinorCalendarOutlined />;
        const label = StaffWorkingHourViewType.mapLabels[value];

        return <SegmentControl.Item key={value} value={value.toString()} icon={icon} label={label} />;
      })}
    </SegmentControl>
  );
}
