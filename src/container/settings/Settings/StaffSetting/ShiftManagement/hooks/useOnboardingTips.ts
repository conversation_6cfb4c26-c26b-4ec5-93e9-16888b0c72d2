import { META_DATA_KEY_LIST } from '../../../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../../../store/metadata/metadata.hooks';
import type { BDCommonOnboardingTips } from '../../../../../../store/metadata/metadata.types';

type StaffScheduleBySlotTipsKey =
  | 'hideStaffScheduleBySlotTips'
  | 'hideStaffScheduleBySlotDailySettingTips'
  | 'hideStaffScheduleBySlotHourSettingTips';

// 获取 staff management
export const useOnboardingTips = (key: StaffScheduleBySlotTipsKey) => {
  const [data, setData] = useMetaData<BDCommonOnboardingTips>(META_DATA_KEY_LIST.BDCommonOnboardingTips);

  const hideTips = () => {
    setData({
      ...data,
      [key]: true,
    });
  };

  return {
    isVisible: !data?.[key],
    hideTips,
  };
};
