import React, { memo } from 'react';

export interface QuestionCardProps {
  title?: string;
  headerRight?: React.ReactNode;
  children: React.ReactNode;
}

export const QuestionCard = memo(({ title = '', children, headerRight }: QuestionCardProps) => {
  return (
    <div className="moe-bg-white moe-rounded-[8px] moe-p-[16px] moe-flex moe-flex-col moe-mb-[10px]">
      <div className="moe-flex moe-justify-between moe-items-center">
        <div className="moe-text-[16px] moe-leading-[22px] moe-font-bold moe-text-[#333]">{title}</div>
        {!!headerRight && headerRight}
      </div>
      {children}
    </div>
  );
});
