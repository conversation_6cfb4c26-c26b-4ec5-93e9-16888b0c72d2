import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'amos';
import { toast<PERSON><PERSON> } from '../../../../../components/Toast/Toast';
import { getBusinessEmail } from '../../../../../store/business/defaultSetting.actions';
import { selectBusinessEmail } from '../../../../../store/business/defaultSetting.selectors';
import { Form, Input, Modal, useForm } from '@moego/ui';
import { useSerialCallback } from '@moego/tools';
import { useModal } from '../../../../../components/Modal/useModal';
import { sendReport } from '../../../../../store/reportCard/actions/private/reportCard.actions';
import { SendMethod } from '@moego/bff-openapi/clients/client.fulfillment';

const DefaultEmailSubjectName = "Checkout summary for {PetName}'s day with {MainStaff} at {BusinessName}";

interface PreviewByEmailFormValue {
  emailSubject: string;
  recipientEmail: string;
}

interface PreviewByEmailModalProps extends React.ComponentProps<typeof Modal> {
  fulfillmentReportId: string;
}

const PreviewByEmailModal: React.FC<PreviewByEmailModalProps> = (props) => {
  const { fulfillmentReportId, onClose, ...rest } = props;
  const [businessEmail] = useSelector(selectBusinessEmail);
  const form = useForm<PreviewByEmailFormValue>();
  const dispatch = useDispatch();

  const handleSubmit = useSerialCallback(async () => {
    return form.handleSubmit(async (values) => {
      // @todo @ikun 这里接口参数支持 template preview
      await dispatch(
        sendReport({
          fulfillmentReportId,
          sendMethod: SendMethod.EMAIL,
          recipientEmails: [values.recipientEmail],
          emailSubject: values.emailSubject,
        }),
      );

      toastApi.success(`A test email has been sent to ${values.recipientEmail}`);
      onClose?.();
    })();
  });

  useEffect(() => {
    form.reset({
      emailSubject: DefaultEmailSubjectName,
      recipientEmail: businessEmail.notificationEmail,
    });
    return () => {
      form.reset();
    };
  }, [businessEmail.notificationEmail]);

  useEffect(() => {
    dispatch(getBusinessEmail());
  }, []);

  return (
    <Modal
      isOpen={true}
      title="Send test email"
      size="s"
      confirmText="Send"
      onConfirm={handleSubmit}
      onClose={onClose}
      autoCloseOnConfirm={false}
      {...rest}
    >
      <Form form={form} footer={null}>
        <Form.Item name="emailSubject" label="Email subject">
          <Input.TextArea isDisabled />
        </Form.Item>
        <Form.Item name="recipientEmail" label="Recipient">
          <Input isRequired />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export const usePreviewByEmailModal = () => {
  return useModal(PreviewByEmailModal);
};
