import IconLogoYelpSvg from '../../../../../../../assets/icon/logo-yelp.svg';
import IconLogoGoogleSvg from '../../../../../../../assets/icon/logo-google.svg';
import IconLogoFacebookSvg from '../../../../../../../assets/icon/logo-facebook.svg';
import { createEnum } from '../../../../../../../store/utils/createEnum';

export interface ReviewBoosterFormValues {
  showYelpReview: boolean;
  yelpReviewLink: string;
  showGoogleReview: boolean;
  googleReviewLink: string;
  showFacebookReview: boolean;
  facebookReviewLink: string;
}

export const ReviewBoosterType = createEnum({
  Yelp: [
    1,
    {
      label: 'Yelp',
      visibleFormKey: 'showYelpReview',
      linkFormKey: 'yelpReviewLink',
      placeholder: 'Add Yelp review link',
      icon: IconLogoYelpSvg,
    },
  ],
  Google: [
    2,
    {
      label: 'Google',
      visibleFormKey: 'showGoogleReview',
      linkFormKey: 'googleReviewLink',
      placeholder: 'Add Google review link',
      icon: IconLogoGoogleSvg,
    },
  ],
  Facebook: [
    3,
    {
      label: 'Facebook',
      visibleFormKey: 'showFacebookReview',
      linkFormKey: 'facebookReviewLink',
      placeholder: 'Add Facebook review link',
      icon: IconLogoFacebookSvg,
    },
  ],
} as const);
