import { type EvaluationDef } from '@moego/api-web/moego/models/offering/v1/evaluation_defs';
import { type useForm } from '@moego/ui';
import React from 'react';

interface EvaluationFormContextProps {
  form?: ReturnType<typeof useForm<EvaluationDef>>;
  isEdit: boolean;
}

export const EvaluationFormContext = React.createContext<EvaluationFormContextProps>({
  form: undefined,
  isEdit: false,
});

export const useEvaluationFormContext = () => React.useContext(EvaluationFormContext);
