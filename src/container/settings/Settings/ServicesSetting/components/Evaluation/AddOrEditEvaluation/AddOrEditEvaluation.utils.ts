import { type EvaluationDef } from '@moego/api-web/moego/models/offering/v1/evaluation_defs';
import { type EvaluationRecord } from '../../../../../../../store/evaluation/evaluation.boxes';
import { isNormal } from '../../../../../../../store/utils/identifier';

const EvaluationFormDefaultValues: Partial<EvaluationDef> = {
  name: 'Evaluation',
  aliasForOnlineBooking: 'Meet & greet',
  description:
    'To secure services, we kindly request scheduling an on-site evaluation to determine suitability for our facility.',
  isResettable: false,
  isActive: true,
  colorCode: '#000000',
  availableForAllBusiness: true,
  isAllStaff: true,
  lodgingFilter: false,
};

export const getEvaluationFormInitialValues = (evaluation?: EvaluationRecord, isDuplicate: boolean = false) => {
  if (!isNormal(evaluation?.id)) {
    return EvaluationFormDefaultValues;
  }
  return {
    ...evaluation.toJSON(),
    name: isDuplicate ? `${evaluation.name} (copy)` : evaluation.name,
  };
};
