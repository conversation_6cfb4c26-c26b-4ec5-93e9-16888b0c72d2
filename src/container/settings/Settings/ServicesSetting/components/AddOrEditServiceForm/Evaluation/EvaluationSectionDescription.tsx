import { Link, Text } from '@moego/ui';
import React, { memo } from 'react';
import { PATH_SERVICE_SETTING } from '../../../../../../../router/paths';
import { ServicesNav } from '../../../types';

export const EvaluationSectionDescription = memo(() => {
  return (
    <div className="moe-flex moe-mt-xxs">
      <Text variant="small" className="moe-text-tertiary">
        Evaluation services can be configured at
        <Link
          variant="small"
          className="moe-ml-[2px]"
          target="_blank"
          href={PATH_SERVICE_SETTING.build({ panel: ServicesNav.Evaluation })}
        >
          {'Settings > Services > Evaluation'}
        </Link>
      </Text>
    </div>
  );
});
