import { Form, Radio, RadioGroup, booleanToStringify } from '@moego/ui';
import React, { memo } from 'react';

export const Status = memo(() => {
  return (
    <Form.Item<boolean, string> name="isActive" label="Status" transformer={booleanToStringify}>
      <RadioGroup isRequired orientation="horizontal">
        <Radio value={'true'}>Active</Radio>
        <Radio value={'false'}>Inactive</Radio>
      </RadioGroup>
    </Form.Item>
  );
});
