import { Heading, cn } from '@moego/ui';
import React, { type PropsWithChildren, memo } from 'react';
import { type EnumValues } from '../../../../../../../store/utils/createEnum';
import { PricingRuleServiceFormSections } from '../PricingRules.enum';

export interface FormSectionProps {
  section: EnumValues<typeof PricingRuleServiceFormSections>;
  title?: string;
  titleWrapperClassName?: string;
}

export const FormSection = memo<PropsWithChildren<FormSectionProps>>((props) => {
  const { section, title, children, titleWrapperClassName } = props;
  const { title: configTitle } = PricingRuleServiceFormSections.mapLabels[section];
  return (
    <div className="moe-flex moe-flex-col moe-gap-m" id={section}>
      <div className={cn(titleWrapperClassName)}>
        <Heading size="3">{title ?? configTitle}</Heading>
      </div>
      {children}
    </div>
  );
});
