# BD service charge

- 改版之后的 BD service charge 属于 pricing rule 的一个子类概念 纯 Grooming 的 service charge 和原来一样未作修改
- 目前 service charge 的 add & edit 表单已经重构为了配置型

## 如何新增一类新的 service charge

- 需要确认一下新的 service charge 类型是否需要列表页面；目前 Exceed 24-hour 和 Medication charge 是不需要列表页面的，可以理解为 company 纬度下这个类型的 service charge 只存在一个
- 如果需要加一些通用的数据可以加在 `CommonServiceChargeContext` 中

目前已有的属性如下，最好是页面级别的属性，如果是组件级别的属性进行 if else，其实可以考虑新增组件

```typescript
export interface CommonServiceChargeContextProps {
  isEdit: boolean;
  surchargeType: SurchargeType;
  form?: ServiceChargeForm;
  serviceId?: string;
  isDuplicate: boolean;
  hasAddUpdatePermission: boolean;
  hasAddUpdatePricePermission: boolean;
}
```

### 配置入口卡片

B 端入口路径是 settings -> service -> pricing rule -> 卡片

- 在 `src/container/settings/Settings/ServicesSetting/components/PricingRules/PricingRules.enum.tsx` 文件中的 `PricingRuleTypes` 增加类型
- 在 `src/container/settings/Settings/ServicesSetting/hooks/usePricingRulesEntrySection.tsx` 文件中增加配置，这里的配置较为简单一看就懂不赘述了
- 在 `src/container/settings/Settings/ServicesSetting/PricingRulesSetting.tsx` 中配置入口

### 配置列表页面

如果新增的 charge 类型需要列表页面可以参照 `src/container/settings/Settings/ServicesSetting/components/PricingRules/FeedingCharge/FeedingCharge.tsx` 进行开发

### 配置 add or edit 表单页面

- 在 `src/store/service/category.boxes.ts` 文件中增加新的 charge 类型
- 在 `src/container/settings/Settings/ServicesSetting/utils/AddOrEditServicePortal.tsx` 文件中配置 add & edit 表单入口
- 在 `src/container/settings/Settings/ServicesSetting/components/PricingRules/PricingRules.enum.tsx` 文件中配置 `ServiceChargeConfig` 如下所示

```typescript
export const ServiceChargeConfig = createEnum({
  FEEDING_CHARGE: [
    // 这里是你新增的 charge 类型
    FEEDING_CHARGE_TYPE,
    {
      // 表单 title
      title: "Feeding charge rule",
      // 后端提供的枚举类型
      surchargeType: SurchargeType.FEEDING_FEE,
      // 使用你新增的 PricingRuleTypes 枚举值
      // 这里主要是导航到准确的子使用的
      nav: PricingRuleTypes.FeedingCharge,
      // 如果有 onboarding 的需要则进行配置
      onboarding: {
        key: undefined,
        content: "",
        onboardingTitle: "",
      },
    },
  ],
  ...,
  // Add your config here
});
```

- 在 `src/container/settings/Settings/ServicesSetting/components/PricingRules/AddOrEditCommonServiceCharge/utils` 文件夹下新增 charge 类型的 helper

我这里以 FeedingCharge 举例

```typescript
export class FeedingChargeFormHelper extends BaseChargeFormHelper {
  // 这里应该是后端的枚举值
  protected surchargeType = SurchargeType.FEEDING_FEE;

  // 自定义 useEffect 需要执行的 actions
  // 有时候需要请求一下你这个 charge 页面需要的独有的数据 如果没有就返回空数组
  protected initCustomizedEffect(): Array<Dispatchable> {
    return [getPetMetaDataList([BusinessPetMetadataName.FEEDING_SOURCE])];
  }

  // 自定义新增表单时的字段 因为 charge 新增和编辑的时候有些字段传了后端可能兼容不了，需要前端控制一下，只传当前类型需要的字段不要多传
  protected initAddFormCustomizedValue(): Partial<TransformedServiceChargeRecord> {
    return {
      chargeMethod: ChargeMethod.PER_DAY,
      foodSource: {
        isAllFoodSource: false,
        foodSourceIds: [],
      },
    };
  }

  // 自定义编辑表单时的字段
  protected initEditFormCustomizedValue(
    service: RecordProps<ServiceChargeRecord>,
    isDuplicate?: boolean
  ): Partial<TransformedServiceChargeRecord> {
    return {};
  }
}
```

- 在 `src/container/settings/Settings/ServicesSetting/components/PricingRules/AddOrEditCommonServiceCharge/utils/ServiceChargeFormHelperFactory.ts` 中加入你刚创建的 helper

```typescript
class ServiceChargeFormHelperFactory {
  ... other code

  private constructor() {
    this.helperMap = new Map<SurchargeType, BaseChargeFormHelper>();
    this.helperCreators = new Map<SurchargeType, () => BaseChargeFormHelper>([
      [SurchargeType.FEEDING_FEE, () => new FeedingChargeFormHelper()],
      [SurchargeType.MEDICATION_FEE, () => new MedicationChargeFormHelper()],
      [SurchargeType.CHARGE_24_HOUR, () => new OverTimeChargeFormHelper()],
      // Register your helper here
    ]);
  }

  ... other code
}
```

- 在 `src/container/settings/Settings/ServicesSetting/components/PricingRules/AddOrEditCommonServiceCharge/utils/AddOrEditCommonServiceCharge.config.ts` 中配置你的表单组件

这里的原则是有能复用的直接复用现有的
没有能复用的就新增一个组件，不要对现有的组件进行强行复用，这点很重要强扭的瓜不甜
新增组件就 follow 当前的模式即可，聪明的你一看就懂

## 待优化的点 & TODO

1. 将 list 页面也进行重构，重构为配置型
2. 将存量还未修改为配置型的 add & edit charge 页面改为配置型
3. 目前整体的 service charge 的配置过于松散和琐碎，还有改进的空间最好是 all in one [at least all in one file XD..]

对代码有疑问可以 slack 找 iKun, <EMAIL> 也是当上一回 C 了
