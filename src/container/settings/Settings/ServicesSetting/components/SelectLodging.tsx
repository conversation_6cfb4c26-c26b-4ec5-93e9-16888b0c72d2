import { MinorPlusOutlined } from '@moego/icons-react';
import { Button, Form, LegacySelect as Select, Switch, Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { useBool } from '../../../../../utils/hooks/useBool';
import { AddOrEditLodgingTypeModal } from '../../LodgingSetting/LodgingTypes/components/AddOrEditLodgingTypeModal';

export interface SelectLodgingProps {
  onSelectAllChange: (value: boolean) => void;
  onSelectChange: (value: string[] | undefined) => void;
  customizedLodgings?: string[];
  lodgingFilter?: boolean;
  lodgingsName?: string;
  availableLodgingOption: {
    label: string;
    value: string;
    isDisabled: boolean;
  }[];
  isDisabled?: boolean;
}

export const SelectLodging = memo((props: SelectLodgingProps) => {
  const {
    customizedLodgings,
    lodgingsName,
    lodgingFilter,
    onSelectChange,
    onSelectAllChange,
    availableLodgingOption,
    isDisabled,
  } = props;
  const [permissions] = useSelector(selectCurrentPermissions);
  const addLodgingTypeModalVisible = useBool();
  const canAccessLodgingSetting = permissions.has('accessLodgingSettings');

  const isSelectedAll = !lodgingFilter;

  return (
    <>
      <Form.Item<string[], string[]>
        name={lodgingsName || 'customizedLodgings'}
        label="Eligible lodging type"
        transformer={{
          input(lodgingTypeList) {
            if (isSelectedAll) {
              return availableLodgingOption.map((item) => item.value);
            }
            return lodgingTypeList;
          },
        }}
        rules={{
          validate: () => {
            if (!isSelectedAll && !customizedLodgings?.length) {
              return 'Please select at least one eligible lodging type';
            }
            return true;
          },
        }}
      >
        <Select
          isDisabled={isDisabled}
          isRequired
          isMultiple={true}
          multipleMode={isSelectedAll ? 'value' : 'tag'}
          options={availableLodgingOption}
          onChange={onSelectChange}
          renderMultipleValues={() => <div>All lodging types</div>}
          footer={
            availableLodgingOption?.length ? (
              <Switch isSelected={isSelectedAll} onChange={onSelectAllChange} className="moe-p-[6px]">
                All lodging types (including new types)
              </Switch>
            ) : (
              <Tooltip
                isDisabled={canAccessLodgingSetting}
                side="top"
                content="Please request “Can access lodging settings” permission from the business owner."
              >
                <Button
                  icon={<MinorPlusOutlined />}
                  onPress={addLodgingTypeModalVisible.open}
                  variant="tertiary-legacy"
                  isDisabled={!canAccessLodgingSetting}
                >
                  Add lodging type
                </Button>
              </Tooltip>
            )
          }
        />
      </Form.Item>
      <AddOrEditLodgingTypeModal
        visible={addLodgingTypeModalVisible.value}
        onClose={addLodgingTypeModalVisible.close}
      />
    </>
  );
});
