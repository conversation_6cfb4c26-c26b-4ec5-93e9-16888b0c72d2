import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Alert } from '@moego/ui';
import React, { memo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { PATH_GROUP_ClASS } from '../../../../../../router/paths';
import { META_DATA_KEY_LIST } from '../../../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../../../store/metadata/metadata.hooks';
import { type BDCommonOnboardingTips } from '../../../../../../store/metadata/metadata.types';
import { useServiceSettingContext } from '../../ServicesSettingContext';

export const CareTypeServiceBanner = memo(() => {
  const { serviceItemType } = useServiceSettingContext();
  const isGroupClass = serviceItemType === ServiceItemType.GROUP_CLASS;
  const [data, setData, isLoading] = useMetaData<BDCommonOnboardingTips>(META_DATA_KEY_LIST.BDCommonOnboardingTips);

  const handleClose = () => {
    setData({ ...data, hideGroupClassSettingTips: true });
  };

  return (
    <Condition if={isGroupClass && !data?.hideGroupClassSettingTips && !isLoading}>
      <Alert
        isRounded
        isBordered
        description="You can manage your group class sessions directly in the Training view."
        linkText=" Go to Training Calendar"
        link={PATH_GROUP_ClASS.build()}
        linkProps={{
          onClick: handleClose,
        }}
        classNames={{
          base: 'moe-mb-m',
          link: 'moe-text-primary hover:!moe-text-primary',
        }}
        onClose={handleClose}
      />
    </Condition>
  );
});
