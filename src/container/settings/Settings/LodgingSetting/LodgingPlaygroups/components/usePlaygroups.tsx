import { useDispatch, useSelector } from 'amos';
import React from 'react';
import {
  createPlaygroup,
  deletePlaygroup,
  getPlaygroup,
  sortPlaygroup,
  updatePlaygroup,
} from '../../../../../../store/playgroups/playgroups.actions';
import { playgroupsLoadingBox, playgroupsRecordMapBox } from '../../../../../../store/playgroups/playgroups.box';
import { useFloatableHost } from '../../../../../../utils/hooks/useFloatableHost';
import { type PlaygroupsFormValues } from '../Playgroups.config';
import { DeletePlaygroupsModal } from './DeletePlaygroupsModal';
import { PlaygroupsModal } from './PlaygroupsModal';

export function usePlaygroups() {
  const { mountFloatableAutoClose } = useFloatableHost();
  const dispatch = useDispatch();
  const [playgroupsRecordMap] = useSelector(playgroupsRecordMapBox);

  const openAddPlaygroupsModal = async () => {
    const handleCreateConfirm = async (values: PlaygroupsFormValues) => {
      await createPlaygroup({ playgroup: values });

      dispatch(getPlaygroup());
    };

    return await mountFloatableAutoClose(<PlaygroupsModal onConfirm={handleCreateConfirm} />);
  };

  const openEditPlaygroupsModal = async (playgroupId: string) => {
    const handleEditConfirm = async (values: PlaygroupsFormValues, playgroupId: string) => {
      await updatePlaygroup({ playgroup: { ...values, id: playgroupId } });

      dispatch(getPlaygroup());
    };
    const playgroup = playgroupsRecordMap.mustGetItem(playgroupId);

    return await mountFloatableAutoClose(
      <PlaygroupsModal
        onConfirm={(values) => handleEditConfirm(values, playgroupId)}
        title="Edit playgroup"
        playgroup={playgroup.toJSON()}
      />,
    );
  };

  const handleDeletePlaygroups = async (playgroupId: string) => {
    const handleDeleteConfirm = async (playgroupId: string) => {
      await deletePlaygroup({ id: playgroupId });

      dispatch(getPlaygroup());
    };

    return await mountFloatableAutoClose(<DeletePlaygroupsModal onConfirm={() => handleDeleteConfirm(playgroupId)} />);
  };

  const handleSortPlaygroups = async (playgroupIds: string[]) => {
    dispatch(playgroupsLoadingBox.setState(true));

    try {
      await sortPlaygroup({ ids: playgroupIds });

      dispatch(getPlaygroup());
    } catch {
      dispatch(playgroupsLoadingBox.setState(false));
    }
  };

  return {
    openAddPlaygroupsModal,
    openEditPlaygroupsModal,
    handleDeletePlaygroups,
    handleSortPlaygroups,
  };
}
