import { AvatarVan, Tag, cn } from '@moego/ui';
import { useSelector } from 'amos';
import { Dropdown } from 'antd';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useLayoutEffect, useRef, useState } from 'react';
import { Condition } from '../../../../../components/Condition';
import { vanMapBox } from '../../../../../store/van/van.boxes';
import { useDebounceValue } from '../../../../../utils/hooks/useDebounceValue';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

// classes
const COMMON_CLASS = 'moe-flex moe-justify-start moe-items-center moe-gap-[4px]';
const MEASURE_CONTAINER_CLASS = 'van-container-for-measure';
const MEASURE_ITEM_CLASS = 'van-item-for-measure';

// table cell width
export const CELL_1_WIDTH = 260;
export const CELL_2_WIDTH = 260;
export const CELL_3_WIDTH = 400;
export const CELL_4_WIDTH = 100;
const PADDING_HORIZONTAL = 16;

// item width
const MORE_WIDTH = 72;
const ITEM_GAP = 4;

export interface AssignVansListProps {
  vanIdList: number[];
}

export const AssignedVansList: React.FC<AssignVansListProps> = (props) => {
  const { vanIdList } = props;
  const [vanMap] = useSelector(vanMapBox);
  const [moreList, setMoreList] = useState([] as number[]);
  const [mainList, setMainList] = useState([] as number[]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(CELL_3_WIDTH);

  // 计算各 item 实际占宽度，用来测算应该展示几条
  const widthMapRef = useRef<{ [x: number]: number }>();
  const getWidthMap = useLatestCallback((forceUpdate?: boolean) => {
    if (
      (!widthMapRef.current || !Object.keys(widthMapRef.current || {}).length || forceUpdate) &&
      containerRef.current
    ) {
      widthMapRef.current = [...containerRef.current.querySelectorAll(`.${MEASURE_ITEM_CLASS}`)].reduce(
        (acc, item, index) => {
          acc[vanIdList[index]] = item.clientWidth;
          return acc;
        },
        {} as { [x: number]: number },
      );
    }
    return widthMapRef.current || {};
  });

  const splitVanList = useLatestCallback((availableWidth: number) => {
    const widthMap = getWidthMap();
    let lastIndex = 0;
    let tempWidth = 0;
    while (lastIndex < vanIdList.length) {
      const vanId = vanIdList[lastIndex];
      tempWidth += (widthMap[vanId] && widthMap[vanId] + ITEM_GAP) || 0;
      if (tempWidth >= availableWidth - MORE_WIDTH - ITEM_GAP) break;
      lastIndex++;
    }
    setMainList(vanIdList.slice(0, lastIndex));
    setMoreList(vanIdList.slice(lastIndex));
  });

  const resizeContainer = useLatestCallback(() => {
    const tableWrapper = containerRef.current?.closest('[data-slot="table-table"]');
    if (tableWrapper) {
      const totalWidth = tableWrapper.clientWidth;
      const remainContainerWidth = totalWidth - CELL_1_WIDTH - CELL_2_WIDTH - CELL_4_WIDTH - PADDING_HORIZONTAL * 2;
      setContainerWidth(remainContainerWidth);
      splitVanList(remainContainerWidth);
    }
  });

  const debouncedVanMap = useDebounceValue(vanMap, 200);
  useLayoutEffect(() => {
    const listener = debounce(() => {
      resizeContainer();
    }, T_SECOND * 0.1);
    window.addEventListener('resize', listener);
    // init
    getWidthMap(true);
    resizeContainer();
    return () => {
      window.removeEventListener('resize', listener);
    };
    // 注意初始化时 vanMap 可能没准备好
  }, [vanIdList.join(''), debouncedVanMap]);

  const renderForMeasure = () => (
    <div
      className={classNames(
        COMMON_CLASS,
        MEASURE_CONTAINER_CLASS,
        'moe-fixed moe-left-0 moe-top-0',
        'moe-pointer-events-none moe-invisible',
      )}
    >
      {vanIdList.map((vanId) => (
        <AssignedVanItem className={MEASURE_ITEM_CLASS} key={vanId} vanId={vanId} />
      ))}
    </div>
  );

  const renderMoreList = () => (
    <Dropdown
      overlay={
        <div
          className="moe-flex moe-flex-col moe-gap-[4px] moe-w-[138px] moe-rounded-[8px] moe-bg-white moe-p-[8px]"
          style={{
            boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.12)',
          }}
        >
          {moreList.map((vanId) => (
            <AssignedVanItem key={vanId} vanId={vanId} />
          ))}
        </div>
      }
    >
      <Tag
        variant="filled"
        color="neutral"
        isBordered={false}
        className={cn('moe-h-[32px] moe-max-w-none moe-rounded-[32px]')}
        label={`${moreList.length} more`}
      />
    </Dropdown>
  );
  return (
    <div
      ref={containerRef}
      className={classNames(COMMON_CLASS, 'moe-relative moe-w-full')}
      style={{
        width: containerWidth,
      }}
    >
      {mainList.map((vanId) => (
        <AssignedVanItem key={vanId} vanId={vanId} />
      ))}
      <Condition if={moreList.length}>{renderMoreList()}</Condition>
      {renderForMeasure()}
    </div>
  );
};

export const AssignedVanItem: React.FC<{
  vanId: number;
  className?: string;
}> = memo((props) => {
  const { vanId } = props;
  const [vanData] = useSelector(vanMapBox.mustGetItem(vanId));
  if (!vanData.nickName) return null;
  return (
    <Tag
      variant="filled"
      color="neutral"
      key={vanId}
      isBordered={false}
      label={vanData.nickName}
      icon={<AvatarVan size="xs" />}
      className={cn('moe-h-[32px] moe-max-w-none moe-rounded-[32px] moe-pl-8px-50', props.className)}
    />
  );
});
