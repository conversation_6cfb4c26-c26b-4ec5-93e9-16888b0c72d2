import { Input, type NumberInputProps, Text, Tooltip, cn, mergeClassNames } from '@moego/ui';

import { isUndefined } from 'lodash';
import React, { type FC } from 'react';
import { WithPricingEnableUpgrade } from '../../../../../../components/Pricing/WithPricingComponents';

const BUFFER_TIME_STEP = 1;
const MAX_BUFFER_TIME = 60;
const MIN_BUFFER_TIME = -60;

interface Props extends Omit<NumberInputProps, 'className'> {
  className?: string;
}

export const BufferInput: FC<Props> = (props) => {
  const { classNames, className, isRequired = true, ...rest } = props;
  const hasLabel = !isUndefined(props.label);
  return (
    <WithPricingEnableUpgrade permission="smartSchedulingForBufferTime" newUI>
      {(withoutPermissionCallback) => {
        const hasPermission = !withoutPermissionCallback;
        return (
          <div
            className={cn('moe-flex moe-flex-row moe-items-center', className)}
            onClickCapture={(e) => {
              if (!hasPermission) {
                e.stopPropagation();
                withoutPermissionCallback();
              }
            }}
          >
            <Tooltip
              sideOffset={15}
              isDisabled={hasPermission}
              content="Upgrade plan to the Growth or Ultimate to unlock buffer time settings."
            >
              <Input.Number
                {...rest}
                isRequired={isRequired}
                step={BUFFER_TIME_STEP}
                minValue={MIN_BUFFER_TIME}
                maxValue={MAX_BUFFER_TIME}
                isReadOnly={!hasPermission}
                classNames={mergeClassNames(
                  {
                    base: 'moe-flex-1',
                    inputWrapper: hasPermission && 'moe-bg-white',
                    input: !hasPermission && 'moe-text-disabled',
                  },
                  classNames,
                )}
              />
            </Tooltip>
            <Text
              as="div"
              variant="regular-short"
              className={cn('moe-text-primary moe-ml-xs', { 'moe-mt-[22px]': hasLabel })}
            >
              mins
            </Text>
          </div>
        );
      }}
    </WithPricingEnableUpgrade>
  );
};
