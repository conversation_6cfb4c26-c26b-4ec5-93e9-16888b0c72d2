import { Radio } from 'antd';
import React, { memo, useMemo } from 'react';
import { GroomingReportPetSvg } from '../../../../../../components/ClickableSVG/GroomingReport/GroomingReportPet';
import { Condition } from '../../../../../../components/Condition';
import { EPetType } from '../../../../../../config/interface';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useControllableValue } from '../../../../../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { BodyView, BodyViewChooseList, type BodyViewType } from '../../GroomingReportEdit.options';

export interface PetConditionBodyViewProps {
  id: string;
  activeList: string[]; // L_TAIL / R_HEAD ...
  petTypeId: number;
  // onChange: (value: string[], id: number, triggerDirty?: boolean) => void;
  onChange: (value: string[], type: BodyViewType, id: string) => void;
  required: boolean;
}

export const PetConditionBodyView = memo((props: PetConditionBodyViewProps) => {
  const { petTypeId } = props;
  // cat & dog's svg scale is different
  const svgScale = petTypeId === EPetType.Dog ? 0.73 : 0.81;
  const isMarked = useBool(!!props.required || props.activeList?.length > 0);

  const [activeList, setActiveList] = useControllableValue<string[]>(props, {
    valuePropName: 'activeList',
    defaultValue: [],
  });

  const leftActiveList = useMemo(() => {
    return activeList?.filter((item) => item.startsWith('L')).map((item) => item.replace('L_', '')) || [];
  }, [activeList]);

  const rightActiveList = useMemo(() => {
    return activeList?.filter((item) => item.startsWith('R')).map((item) => item.replace('R_', '')) || [];
  }, [activeList]);

  const handleSetMarked = (value: boolean) => {
    if (value) {
      isMarked.open();
    } else {
      isMarked.close();
      setActiveList([], props.id);
    }
  };

  const handleChange = useLatestCallback((type: BodyViewType, activeList: string[]) => {
    if (type === BodyView.Left) {
      setActiveList(
        [...activeList.map((item) => `L_${item}`), ...rightActiveList.map((item) => `R_${item}`)],
        type,
        props.id,
      );
    } else {
      setActiveList(
        [...leftActiveList.map((item) => `L_${item}`), ...activeList.map((item) => `R_${item}`)],
        type,
        props.id,
      );
    }
  });

  const renderList = useMemo(() => {
    return [
      {
        title: 'Left',
        type: BodyView.Left,
        activeList: leftActiveList,
      },
      {
        title: 'Right',
        type: BodyView.Right,
        activeList: rightActiveList,
      },
    ];
  }, [leftActiveList, rightActiveList]);

  return (
    <div className="grooming-report-body-view">
      <Radio.Group
        value={isMarked.value}
        onChange={(e) => {
          const val = e.target.value;
          handleSetMarked(val);
        }}
      >
        {BodyViewChooseList.keys.map((item, index) => {
          const value = BodyViewChooseList[item];
          const label = BodyViewChooseList.mapLabels[BodyViewChooseList[item]];
          return (
            <Radio key={`${item}_${index}`} value={value}>
              {label}
            </Radio>
          );
        })}
      </Radio.Group>
      <Condition if={isMarked.value}>
        <div className="!moe-flex !moe-gap-x-[16px] !moe-mt-[8px]">
          {renderList.map((item) => {
            return (
              <div
                className="!moe-w-[200px] !moe-rounded-[10px] !moe-bg-[#F7F8FA] !moe-pt-[21px] !moe-pb-[25px] !moe-px-[10px]"
                key={item.type}
              >
                <div className="!moe-flex !moe-justify-between !moe-mb-[21px]">
                  <div className="!moe-text-[14px] !moe-text-[#333]">{item.title}</div>
                  <div
                    className={`!moe-text-[14px] ${
                      item.activeList.length > 0 ? '!moe-text-brand !moe-cursor-pointer' : '!moe-text-[#FFA46B]'
                    }`}
                    onClick={() => handleChange(item.type, [])}
                  >
                    Clear
                  </div>
                </div>
                <GroomingReportPetSvg
                  size={178}
                  horizontalFlip={item.type === BodyView.Right}
                  typeId={petTypeId}
                  activeList={item.activeList}
                  scale={svgScale}
                  onChange={(activeList) => {
                    handleChange(item.type, activeList);
                  }}
                />
              </div>
            );
          })}
        </div>
      </Condition>
    </div>
  );
});
