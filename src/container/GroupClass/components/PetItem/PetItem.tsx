import {
  type GroupClassClientView,
  type GroupClassPetView,
} from '@moego/api-web/moego/api/offering/v1/group_class_api';
import { CompressedAvatar } from '@moego/business-components';
import { Heading, Text, cn } from '@moego/ui';
import { type SizeOptionsXsSML } from '@moego/ui/dist/esm/components/utils/constants';
import React, { type ReactNode, type FC } from 'react';
import { Condition } from '../../../../components/Condition';
import { printFullName } from '../../../../store/customer/customer.boxes';
import { getPetAvatarType } from '../../../../utils/BusinessUtil';
import { printAge } from '../../../../utils/DateTimeUtil';
import { dateMessageToDayjs, isValidDateMessage } from '../../../../utils/utils';

export interface PetItemProps {
  pet: GroupClassPetView;
  client: GroupClassClientView;
  showBreedAge?: boolean;
  avatarSize?: SizeOptionsXsSML;
  prefixNode?: ReactNode;
  classNames?: {
    base?: string;
    petName?: string;
    clientName?: string;
    breedAge?: string;
  };
}

export const PetItem: FC<PetItemProps> = (props) => {
  const { pet, client, showBreedAge, prefixNode, avatarSize = 's', classNames } = props;
  const { petName, petType, birthday, breed, avatarPath } = pet;
  const { firstName, lastName } = client;
  const age = isValidDateMessage(birthday) ? printAge(dateMessageToDayjs(birthday)!) : '';

  return (
    <div className={cn('moe-flex moe-items-center moe-gap-x-xs', classNames?.base)}>
      {prefixNode}
      <CompressedAvatar.Pet src={avatarPath} size={avatarSize} type={getPetAvatarType(petType)} />
      <div>
        <div className="moe-flex moe-items-center moe-flex-wrap moe-gap-[2px]">
          <Heading size="5" className={cn('moe-text-primary', classNames?.petName)}>
            {petName}
          </Heading>
          <Text variant="regular" className={cn('moe-text-primary', classNames?.clientName)}>
            ({printFullName(firstName, lastName)})
          </Text>
        </div>
        <Condition if={showBreedAge}>
          <Text variant="small" className={cn('moe-text-tertiary', classNames?.breedAge)}>
            {[breed, age].filter(Boolean).join(' · ')}
          </Text>
        </Condition>
      </div>
    </div>
  );
};
