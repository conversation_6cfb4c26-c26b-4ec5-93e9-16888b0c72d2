import {
  type GroupClassClientView,
  type GroupClassPetView,
} from '@moego/api-web/moego/api/offering/v1/group_class_api';
import { Checkbox } from '@moego/ui';
import React, { type FC } from 'react';
import { PetItem } from '../PetItem/PetItem';

export interface CheckInPetItemProps {
  pet: GroupClassPetView;
  client: GroupClassClientView;
  isSelected: boolean;
}

export const CheckInPetItem: FC<CheckInPetItemProps> = (props) => {
  const { pet, client, isSelected } = props;

  return <PetItem pet={pet} client={client} showBreedAge prefixNode={<Checkbox isSelected={isSelected} />} />;
};
