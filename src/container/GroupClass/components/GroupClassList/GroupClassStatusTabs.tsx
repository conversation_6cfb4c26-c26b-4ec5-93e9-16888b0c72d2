import { type GroupClassInstanceStatus } from '@moego/api-web/moego/models/offering/v1/group_class_models';
import { type Key, Tabs } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { type FC } from 'react';
import { changeGroupClassStatus, countInstancesGroupByStatus } from '../../../../store/groupClass/groupClass.actions';
import {
  selectGroupClassStatus,
  selectGroupClassStatusCountMap,
  selectGroupClassTrainerIdList,
} from '../../../../store/groupClass/groupClass.selectors';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { GroupClassInstanceStatusMap } from '../../GroupClass.options';
import { GroupClassTemplateTab } from './GroupClassTemplateTab';

export const GroupClassStatusTabs: FC = () => {
  const [groupClassStatusCountMap, status, trainerIdList] = useSelector(
    selectGroupClassStatusCountMap,
    selectGroupClassStatus,
    selectGroupClassTrainerIdList,
  );

  const dispatch = useDispatch();

  const handleLoadData = useSerialCallback(async (statusTab: GroupClassInstanceStatus) => {
    await Promise.all([
      dispatch(countInstancesGroupByStatus({ staffIds: trainerIdList })),
      dispatch(changeGroupClassStatus(statusTab)),
    ]);
  });

  const handleLoadGroupClassInstanceByStatus = (tabValue: Key) => {
    const statusTab = Number(tabValue) as GroupClassInstanceStatus;
    handleLoadData(statusTab);
  };

  return (
    // must use string key for tab selected key, or will cause infinite onChange
    <Tabs onChange={handleLoadGroupClassInstanceByStatus} selectedKey={String(status)}>
      {GroupClassInstanceStatusMap.values.map((status) => {
        const label = GroupClassInstanceStatusMap.mapLabels[status];
        const count = groupClassStatusCountMap.counts?.find((item) => item.status === status)?.count || 0;
        return (
          <Tabs.Item label={`${label} (${count})`} key={status}>
            <GroupClassTemplateTab status={status} isFetching={handleLoadData.isBusy()} />
          </Tabs.Item>
        );
      })}
    </Tabs>
  );
};
