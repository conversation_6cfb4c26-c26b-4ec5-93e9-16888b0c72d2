import { useSelector } from 'amos';
import { Col, Form, InputNumber, Row, Statistic, Tabs } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useState } from 'react';
import { Button } from '../../../components/Button/Button';
import { Card } from '../../../components/Card/Card';
import { toastApi } from '../../../components/Toast/Toast';
import { type OpenApiModels } from '../../../openApi/schema';
import { selectCurrentCompany } from '../../../store/company/company.selectors';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { http } from './../../../middleware/api';

interface TestClockForm {
  companyId: number;
  advanceDays: number;
}
type TestClockStatus = OpenApiModels['GET/payment/subscription/testClock']['Res'];

export const TestApi = memo(() => {
  const [form] = Form.useForm<TestClockForm>();
  const [company] = useSelector(selectCurrentCompany);
  const [testClockStatus, setTestClockStatus] = useState<TestClockStatus | null>(null);

  const handleAdvance = useSerialCallback(async () => {
    const input = form.getFieldsValue();
    await http.open('POST/payment/subscription/advanceClock', input);
    toastApi.success('Advance days successfully');
  });

  const handleGetStatus = useSerialCallback(async () => {
    const input = form.getFieldsValue();
    const status = await http.open('GET/payment/subscription/testClock', input);
    setTestClockStatus(status);
  });

  return (
    <Card title="Test Clock">
      <Form form={form}>
        <Tabs>
          <Tabs.TabPane tab="Advance" key={1}>
            <Form.Item label="Company Id" name="companyId" rules={[{ required: true }]} initialValue={company.id}>
              <InputNumber style={{ width: '120px' }} />
            </Form.Item>
            <Form.Item label="Advance Days" name="advanceDays" rules={[{ required: true }]}>
              <InputNumber style={{ width: '120px' }} />
            </Form.Item>

            <Form.Item>
              <Button btnType="primary" onClick={handleAdvance} loading={handleAdvance.isBusy()}>
                Advance
              </Button>
            </Form.Item>
            <p>* Check latest status on Status tab</p>
          </Tabs.TabPane>
          <Tabs.TabPane tab="Status" key={2}>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="Company Id" name="companyId" rules={[{ required: true }]} initialValue={company.id}>
                  <InputNumber style={{ width: '120px' }} />
                </Form.Item>
                <Form.Item>
                  <Button btnType="primary" onClick={handleGetStatus} loading={handleGetStatus.isBusy()}>
                    Get status
                  </Button>
                </Form.Item>
              </Col>
              {testClockStatus && (
                <Col span={12}>
                  <Statistic title="Status" value={testClockStatus?.status} />
                  <Row gutter={24}>
                    <Col span={8}>
                      <Statistic
                        title="Create Time"
                        value={dayjs.unix(testClockStatus.created as number).format('YYYY-MM-DD')}
                      />
                    </Col>
                    <Col span={16}>
                      <Statistic
                        title="Current Time"
                        value={dayjs.unix(testClockStatus.frozenTime as number).format('YYYY-MM-DD')}
                      />
                    </Col>
                  </Row>
                </Col>
              )}
            </Row>
          </Tabs.TabPane>
        </Tabs>
      </Form>
    </Card>
  );
});
