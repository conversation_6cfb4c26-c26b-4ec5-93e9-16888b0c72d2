/*
 * @since 2020-11-18 16:35:33
 * <AUTHOR> <<EMAIL>>
 */

import { Tabs } from '@moego/ui';
import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { type Key, memo } from 'react';
import { useHistory, useParams } from 'react-router';
import { type DebuggingParams } from '../../router/paths';
import { currentBusinessIdBox } from '../../store/business/business.boxes';
import { isNormal } from '../../store/utils/identifier';
import { AmosReselect } from './features/AmosReselect';
import { AmosSameSelector } from './features/AmosSameSelector';
import { Bimo } from './features/Bimo';
import { Buttons } from './features/Buttons';
import { FloatableHostExample } from './features/FloatableHostExample';
import { Ian } from './features/Ian';
import { <PERSON><PERSON><PERSON> } from './features/<PERSON>anan';
import { <PERSON> } from './features/<PERSON>';
import { <PERSON> } from './features/Leon';
import { Luz } from './features/Luz';
import { MoeGoUI } from './features/MoeGoUI';
import { Nico } from './features/Nico';
import { PiEgg } from './features/PiEgg';
import { Rex } from './features/Rex';
import { RichTextEditor } from './features/RichTextEditor';
import { Scott } from './features/Scott';
import { TestApi } from './features/TestApi';
import { UseActionTodo } from './features/UseActionTodo';
import { TodoView as Doctorwu } from './features/doctorwu-todo/view/todo';
import { Qin } from './features/qin/qin';
import { Simon } from './features/simon/SimonTest';
const problems = {
  AmosReselect,
  AmosSameSelector,
  FloatableHostExample,
  Buttons,
  TestApi,
  PiEgg,
  Jim,
  RichTextEditor,
  MoeGoUI,
  Doctorwu,
  Luz,
  UseActionTodo,
  Nico,
  Leon,
  Qin,
  Ian,
  Jianan,
  Bimo,
  Scott,
  Rex,
  Simon,
};

export interface DebuggingProps {
  className?: string;
}
type DebuggingComponentType = keyof typeof problems;

const DynamicComponent = ({ is }: { is: DebuggingComponentType }) => {
  const RendercComponent = problems[is];
  return <RendercComponent />;
};
export const Debugging = memo<DebuggingProps>(({ className }) => {
  const history = useHistory();
  const { tab } = useParams<DebuggingParams>();
  const [businessId] = useSelector(currentBusinessIdBox);
  if (!isNormal(businessId)) {
    return null;
  }
  const handleChangeTab = (v: Key) => {
    history.replace(`/debugging/${v}`);
  };
  return (
    <div className={classNames(className, 'moe-p-5')}>
      <Tabs className="moe-overflow-x-hidden" onChange={handleChangeTab} defaultSelectedKey={tab} enableMore>
        {(Object.keys(problems) as Array<DebuggingComponentType>).map((k) => (
          <Tabs.Item key={k} label={k}>
            <DynamicComponent is={k} />
          </Tabs.Item>
        ))}
      </Tabs>
    </div>
  );
});
