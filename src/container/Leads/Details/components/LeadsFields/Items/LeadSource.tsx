import { useMount } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import { isNil } from 'lodash';
import React, { type Key, memo } from 'react';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { getReferralSourceList } from '../../../../../../store/business/referralSource.actions';
import { referralSourceMapBox } from '../../../../../../store/business/referralSource.boxes';
import { selectBusinessReferralSources } from '../../../../../../store/business/referralSource.selectors';
import { useMultiSelectOptions } from '../../../../../Client/ClientList/components/hooks/useMultiSelectOptions';
import { SingleSelect } from '../fields/SingleSelect/SingleSelect';
import { type ItemProps } from './types';
const LABEL = 'Source';

interface LeadSourceProps extends ItemProps {}
export const LeadSource = memo<LeadSourceProps>(function Source(props) {
  const {
    customer: { additionalInfo },
    onChange,
  } = props;

  const source = additionalInfo?.referralSourceId;
  const dispatch = useDispatch();
  useMount(() => {
    dispatch(getReferralSourceList());
  });

  const [sourceList, sourceMap] = useSelector(selectBusinessReferralSources(), referralSourceMapBox);

  const options = useMultiSelectOptions(sourceList, sourceMap, 'sourceName');

  const handleChange = async (value: Key | undefined) => {
    if (isNil(value)) return;
    await onChange({
      source: String(value),
    });
    toastApi.success('Source changed successfully');
  };

  return <SingleSelect emptyText="No source" label={LABEL} options={options} value={source} onChange={handleChange} />;
});
