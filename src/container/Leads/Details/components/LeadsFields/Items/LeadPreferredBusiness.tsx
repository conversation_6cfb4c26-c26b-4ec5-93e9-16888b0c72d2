import { isNil } from 'lodash';
import React, { type Key, memo } from 'react';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { useNewAccountStructure } from '../../../../../../components/WithFeature/useNewAccountStructure';
import { RawText } from '../fields/RawText/RawText';
import { SingleSelect } from '../fields/SingleSelect/SingleSelect';
import { type ItemProps } from './types';

const LABEL = 'Preferred business';

interface LeadPreferredBusinessProps extends ItemProps {}

export const LeadPreferredBusiness = memo<LeadPreferredBusinessProps>(function PreferredBusiness(props) {
  const {
    customer: { preferredBusinessId },
    onChange,
  } = props;
  const { locationList, isSingleLocation } = useNewAccountStructure('accessClient');

  const currentLocationName = locationList.find((location) => location.id === preferredBusinessId)?.name;

  if (isSingleLocation) {
    return <RawText label={LABEL} value={currentLocationName} />;
  }

  const options = locationList
    .map((location) => ({
      label: location.name,
      value: location.id,
    }))
    .toArray();
  const handleChange = async (value: Key | undefined) => {
    if (isNil(value)) return;
    await onChange({
      preferredBusinessId: value as string,
    });
    toastApi.success('Preferred business changed successfully');
  };

  return (
    <SingleSelect
      emptyText="No preferred business"
      label={LABEL}
      options={options}
      value={preferredBusinessId}
      onChange={handleChange}
    />
  );
});
