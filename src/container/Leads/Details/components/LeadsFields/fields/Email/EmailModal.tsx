import { validator } from '@moego/tools';
import { Form, Input, Modal, type ModalProps, useForm } from '@moego/ui';
import React, { memo } from 'react';
import { formatStringToSentence } from '../utils';

interface EmailFormFields {
  email: string;
}

interface EmailModalProps extends Omit<ModalProps, 'onConfirm' | 'title' | 'defaultValue' | 'onSubmit'> {
  defaultValue?: string;
  onSubmit?: (value: string) => void;
  label: string;
}
export const EmailModal = memo<EmailModalProps>(function EmailModal(props) {
  const { defaultValue, onSubmit, label, ...restProps } = props;
  const isCreate = !defaultValue;
  const title = formatStringToSentence(`${isCreate ? 'Add' : 'Edit'} ${label}`);

  const form = useForm<EmailFormFields>({
    defaultValues: {
      email: defaultValue || undefined,
    },
  });
  const onConfirm = async () => {
    form.handleSubmit((values) => {
      onSubmit?.(values.email);
    })();
  };

  return (
    <Modal {...restProps} title={title} onConfirm={onConfirm} size="s" autoCloseOnConfirm={false}>
      <Form form={form} footer={null}>
        <Form.Item
          name="email"
          label={formatStringToSentence(label)}
          rules={{
            required: true,
            validate: (v) => {
              if (!validator.isEmail(v)) {
                return 'Please enter a valid email address';
              }
              return true;
            },
          }}
        >
          <Input.Text isRequired />
        </Form.Item>
      </Form>
    </Modal>
  );
});
