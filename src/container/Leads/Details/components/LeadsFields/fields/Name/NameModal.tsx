import { Form, Input, Modal, type ModalProps, useForm } from '@moego/ui';
import React, { memo } from 'react';
import { AvatarUpload } from '../../../../../../../components/Upload/AvatarUpload';
import { formatStringToSentence } from '../utils';
import { type NameParams } from './Name';

interface NameModalProps extends Omit<ModalProps, 'onConfirm' | 'title' | 'defaultValue' | 'onSubmit'> {
  defaultValue?: NameParams;
  onSubmit?: (value: NameParams) => void;
  label: string;
}

export const NameModal = memo<NameModalProps>(function NameModal(props) {
  const { defaultValue, onSubmit, label, ...restProps } = props;
  const isCreate = !defaultValue;
  const title = formatStringToSentence(`${isCreate ? 'Add' : 'Edit'} ${label}`);
  const form = useForm<NameParams>({
    defaultValues: {
      ...defaultValue,
    },
  });

  const onConfirm = async () => {
    form.handleSubmit((values) => {
      onSubmit?.(values);
    })();
  };

  return (
    <Modal {...restProps} title={title} onConfirm={onConfirm} size="s" autoCloseOnConfirm={false}>
      <Form form={form} footer={null}>
        <Form.Item name="avatarPath" valuePropName="src">
          <AvatarUpload />
        </Form.Item>
        <div className="moe-grid moe-grid-cols-2 moe-gap-[12px]">
          <Form.Item name="givenName" label="First name">
            <Input.Text />
          </Form.Item>
          <Form.Item name="familyName" label="Last name">
            <Input.Text />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
});
