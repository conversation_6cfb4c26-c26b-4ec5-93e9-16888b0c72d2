import { MinorEditOutlined, MinorMoreOutlined, MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { Dropdown, Heading, IconButton, cn } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { formatStringToSentence } from './utils';

export enum ActionEnum {
  ADD = 'Add',
  EDIT = 'Edit',
  DELETE = 'Delete',
}

const ActionIconMap = {
  [ActionEnum.ADD]: <MinorPlusOutlined />,
  [ActionEnum.EDIT]: <MinorEditOutlined />,
  [ActionEnum.DELETE]: <MinorTrashOutlined />,
};

interface FieldWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  label?: string;
  children?: React.ReactNode;
  actions?: Array<ActionEnum>;
  showHoverStyle?: boolean;
  onAction?: (action: ActionEnum) => void;
  classNames?: {
    heading?: string;
  };
}

export const FieldWrapper = memo<FieldWrapperProps>(function FieldWrapper(props: FieldWrapperProps) {
  const { actions = [], label, children, className, onAction, showHoverStyle = true, classNames, ...restProps } = props;

  const actionBtn = useMemo(() => {
    const handleAction = (action: ActionEnum) => {
      onAction?.(action);
    };

    if (actions.length === 0) return null;
    if (actions.length === 1) {
      return (
        <IconButton
          variant="primary"
          size="xs"
          icon={ActionIconMap[actions[0]]}
          color="transparent"
          onPress={() => handleAction(actions[0])}
        />
      );
    }
    return (
      <Dropdown>
        <Dropdown.Trigger>
          <IconButton variant="primary" size="xs" icon={<MinorMoreOutlined />} color="transparent" />
        </Dropdown.Trigger>
        <Dropdown.Menu onAction={(key) => handleAction(key as ActionEnum)}>
          {actions.map((action) => {
            return (
              <Dropdown.Item isDestructive={action === ActionEnum.DELETE} key={action}>
                {formatStringToSentence(`${action} ${label}`)}
              </Dropdown.Item>
            );
          })}
        </Dropdown.Menu>
      </Dropdown>
    );
  }, [actions, onAction, label]);

  return (
    <div
      {...restProps}
      className={cn(
        'moe-group moe-w-full moe-p-[12px] moe-rounded-s  moe-flex moe-flex-col moe-gap-xxs',
        { 'hover:moe-bg-neutral-sunken-0': showHoverStyle },
        className,
      )}
    >
      <div className={cn('moe-flex moe-items-center moe-justify-between', classNames?.heading)}>
        <Heading size={6} className="moe-text-tertiary">
          {formatStringToSentence(label)}
        </Heading>
        <div className="moe-invisible group-hover:moe-visible">{actionBtn}</div>
      </div>
      {children}
    </div>
  );
});
