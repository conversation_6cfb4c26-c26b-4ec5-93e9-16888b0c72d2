import { useSelector } from 'amos';
import { calendarSelectedDate } from '../../../store/calendarLatest/calendar.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { useApptDrawerState } from './useApptDrawerState';
import { useQuickAddDrawerState } from './useQuickAddDrawerState';
import { useRepeatSeriesState } from './useRepeatSeriesState';

export const useModeDate = () => {
  const { data: quickAddApptData } = useQuickAddDrawerState();
  const { data: apptData } = useApptDrawerState();
  const { data: repeatApptData } = useRepeatSeriesState();

  const getDefaultDate = () => {
    if (repeatApptData.visible) {
      return repeatApptData.selectedItem?.date || null;
    }
    if (quickAddApptData.visible) {
      return quickAddApptData.date;
    }
    if (apptData.visible) {
      return apptData.date;
    }
    // add ss later

    return null;
  };

  return getDefaultDate();
};

// 用于获取默认的日期，switch 到 Map view 的时候可用。
export const useDefaultDate = () => {
  const date = useModeDate();
  const [calendarDate] = useSelector(calendarSelectedDate);

  // return date ?? dayjs().format(DATE_FORMAT_EXCHANGE);
  return date ?? calendarDate.format(DATE_FORMAT_EXCHANGE);
};
