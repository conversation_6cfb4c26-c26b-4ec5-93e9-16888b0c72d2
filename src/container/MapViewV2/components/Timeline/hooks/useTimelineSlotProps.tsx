import { type CalendarOptions, type SlotLabelContentArg } from '@fullcalendar/react';
import { Text } from '@moego/ui';
import React from 'react';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

export function useTimelineSlotProps(): Pick<CalendarOptions, 'slotLabelContent'> {
  // 列头
  const slotLabelContent = useLatestCallback((arg: SlotLabelContentArg) => {
    const { text } = arg;
    return (
      <Text className="moe-text-secondary" variant="caption">
        {text}
      </Text>
    );
  });

  return {
    slotLabelContent,
  };
}
