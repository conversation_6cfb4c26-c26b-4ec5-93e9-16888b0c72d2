import { Modal, Typography } from '@moego/ui';
import React, { memo } from 'react';

export enum GuideModalType {
  ASSIGN_STAFF_TO_VAN = 'AssignStaff2Van',
  SET_SERVICE_AREA = 'SetServiceArea',
}

export interface GuideModalProps {
  isOpen?: boolean;
  type?: GuideModalType;
  onConfirm?: () => void;
  onCancel?: () => void;
  container?: Element;
}

const guideModalConfig = {
  [GuideModalType.ASSIGN_STAFF_TO_VAN]: {
    title: 'Assign staff to van',
    contentText:
      'Staff assigned to mobile van can access mobile grooming features, including map view, smart scheduling, route optimizations etc.',
  },
  [GuideModalType.SET_SERVICE_AREA]: {
    title: 'Set service area',
    contentText: 'Set up service areas for effortless staff territory management and improved scheduling efficiency.',
  },
};

export const GuideModal = memo<GuideModalProps>((props) => {
  const { type = GuideModalType.ASSIGN_STAFF_TO_VAN, isOpen } = props;
  return (
    <Modal
      isOpen={isOpen}
      title={guideModalConfig[type].title}
      isBlockScroll
      isDismissable
      showCloseButton={false}
      confirmText="Go to settings"
      onConfirm={props.onConfirm}
      onCancel={props.onCancel}
      onClose={props.onCancel}
      portalContainer={props.container}
      classNames={{
        container: 'moe-w-[540px]',
      }}
    >
      <Typography.Text variant="regular">{guideModalConfig[type].contentText}</Typography.Text>
    </Modal>
  );
});
