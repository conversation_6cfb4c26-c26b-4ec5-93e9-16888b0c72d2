import { useSelector } from 'amos';
import { calendarEditTicketInfoBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { type ApptEventCardInfo } from '../../../../../store/calendarLatest/card.types';
import { staffMapBox } from '../../../../../store/staff/staff.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { useApptDrawerState } from '../../../hooks/useApptDrawerState';
import { useMatchRepeatItem } from '../../../hooks/useRepeatSeriesState';
import { useMapApptCards } from './useMapApptCards';
import { isApptSameRepeat } from './utils';

/**
 * 获取需要在 mapview 上绘制的卡片信息
 * 这些数据合并了了外部 detail drawer 以及 repeat series
 * @returns
 */
export function useMapApptCardsWithExternalState() {
  const cards = useMapApptCards();
  const [staffMap, editTicketInfo] = useSelector(staffMapBox, calendarEditTicketInfoBox);
  const repeatItem = useMatchRepeatItem();
  const {
    data: {
      visible: rescheduleVisible,
      date: rescheduleDate,
      editTimeVisible,
      apptId: rescheduleApptId,
      startTime: rescheduleStartTime,
    },
  } = useApptDrawerState();
  const considerReschedule = rescheduleVisible && !!rescheduleDate && editTimeVisible;

  if (repeatItem) {
    const nextCards = cards
      .filter((item) => {
        if (!editTicketInfo.editTimeVisible || !editTicketInfo?.originCard) {
          return true;
        }

        // 非 repeat 的 appt 在 preview repeat 时，需要将其本身也过滤掉，避免出现重复的 marker
        const isRepeatAppt = isNormal(item.repeatId);
        if (!isRepeatAppt && item.appointmentId === editTicketInfo.originCard.appointmentId) {
          return false;
        }

        // 已 repeat 的 appt 在 repeat 编辑状态下，移除自身以及同一 repeat 的其他卡片，避免出现多余重复的 marker
        const isSameCard = item.cardId === editTicketInfo.originCardId;
        return !isSameCard && !isApptSameRepeat(item, editTicketInfo.originCard);
      })
      .map((item) => ({ ...item }));

    // 正在编辑 repeat，拷贝正在编辑的 appt 数据
    if (editTicketInfo.editTimeVisible && editTicketInfo.originCard) {
      const copies = repeatItem.staffIdList.map((staffId) => {
        const item = {
          ...editTicketInfo.originCard,
          cardId: editTicketInfo.originCard!.cardId!,
          orderIndex: editTicketInfo.originCard!.orderIndex!,
          staffId: staffId,
          colorCode: staffMap.mustGetItem(staffId).colorCode,
          startTime: repeatItem.startTime || editTicketInfo.originCard!.startTime,
          date: repeatItem.date,
        } as ApptEventCardInfo;
        return item;
      });

      nextCards.push(...copies);
    }

    return nextCards;
  }

  // 正在 reschedule 时间
  if (considerReschedule && editTimeVisible) {
    const nextCards = cards.map((item) => ({ ...item }));
    const { originCard } = editTicketInfo;
    if (originCard) {
      /**
       * 修改了 date，复制一份 card 用于占位，
       * @see {@link src/store/calendarLatest/calendar.actions.ts#L271}
       * */
      if (!nextCards.find((c) => c.cardId === originCard.cardId && c.appointmentId === originCard.appointmentId)) {
        nextCards.push({ ...originCard, colorCode: staffMap.mustGetItem(originCard.staffId).colorCode });
      }
    }
    nextCards.forEach((item) => {
      if (item.appointmentId === rescheduleApptId) {
        item.startTime = rescheduleStartTime;
        item.date = rescheduleDate;
      }
    });

    return nextCards;
  }

  return cards;
}
