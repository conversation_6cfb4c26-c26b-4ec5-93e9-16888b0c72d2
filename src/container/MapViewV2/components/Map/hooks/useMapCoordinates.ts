import { useMemo } from 'react';
import { truly } from '../../../../../store/utils/utils';
import { useApptDrawerState } from '../../../hooks/useApptDrawerState';
import { useMapViewConfig } from '../../../hooks/useMapViewConfig';
import { useMapViewStaffs } from '../../../hooks/useMapViewStaffs';
import { useQuickAddDrawerState } from '../../../hooks/useQuickAddDrawerState';
import { useMapApptCards } from '../hooks/useMapApptCards';
import { getNumericLatlng, isValidLatlng } from '../utils';
import { useMapTrackingHelpers } from './useMapTrackingHelper';

type GetMapCoordinatesConfig = {
  /** 是否包含 tracking points */
  includeTrackingPoints?: boolean;
};

/** 获取地图上 location 标点，包含正在 schedule 的数据 */
export function useMapCoordinates(
  config: GetMapCoordinatesConfig = { includeTrackingPoints: false },
): Array<CoordinateOnMap> {
  const { includeTrackingPoints } = config;
  const currentCards = useMapApptCards();
  const scheduleCoords = useScheduleCoordinates();
  const trackingCoords = useTrackingStaffCoordinates();
  const cardsCoords = currentCards.map(({ customerInfo: { primaryAddress } }) => ({
    coordinate: {
      lat: Number(primaryAddress?.coordinate?.latitude),
      lng: Number(primaryAddress?.coordinate?.longitude),
    },
  }));
  const coords = [...cardsCoords, ...scheduleCoords]
    .concat(includeTrackingPoints ? trackingCoords : [])
    .filter((item) => isValidLatlng(item.coordinate));
  return coords;
}

export type CoordinateOnMap = { coordinate: google.maps.LatLngLiteral };

/** 获取有数据的 tracking 数据的 staff 位置 */
function useTrackingStaffCoordinates(): Array<CoordinateOnMap> {
  const [{ isEnableTracking }] = useMapViewConfig();
  const { selectedStaffIds } = useMapViewStaffs();
  const { getStaffLatestTrackingPoints } = useMapTrackingHelpers();

  return useMemo(() => {
    const res: Array<CoordinateOnMap> = [];
    if (!isEnableTracking) {
      return res;
    }

    selectedStaffIds.forEach((staffId) => {
      const trackingPoints = getStaffLatestTrackingPoints(staffId);
      trackingPoints.forEach((point) => {
        res.push({ coordinate: getNumericLatlng(point.coordinate) });
      });
    });

    return res;
  }, [isEnableTracking, selectedStaffIds, getStaffLatestTrackingPoints]);
}

/** 获取 repeat 预览时的坐标 */
function useScheduleCoordinates(): Array<CoordinateOnMap> {
  const {
    data: { coordinate: coord1 },
  } = useApptDrawerState();
  const {
    data: { coordinate: coord2 },
  } = useQuickAddDrawerState();

  return useMemo(() => {
    return [coord1, coord2].filter(truly).map((item) => ({ coordinate: item }));
  }, [coord1, coord2]);
}
