export enum MapLayerZIndex {
  ServiceArea = 1000,
  Route = 2000,
  Tracking = 3000,
  Marker = 4000,
  Popper = 9000,
}

export const DEFAULT_MAP_ZOOM = 12;

export const DEFAULT_MAP_CENTER = {
  lat: 34.0522,
  lng: -118.2437,
};

// 适用于 map view v2 的 mapId
export const MAP_VIEW_MAP_ID = '34fb9df97e7be7d5';

// tracking interval: delay 1 min
export const MAP_TRACKING_DELAY = 1 * 60 * 1000;

// tracking timeout: 3 min
export const MAP_TRACKING_TIMEOUT_MINUTE = 3;

export const DEFAULT_MAP_PADDING: google.maps.Padding = { bottom: 54 + 32, left: 54, right: 54 + 48, top: 54 };

export const DEFAULT_MAP_CENTER_OFFSET = { x: 0, y: 0 };

/** staff filter width: large screen */
export const MAP_STAFF_FILTER_WIDTH_L = 280;

/** staff filter width: small screen */
export const MAP_STAFF_FILTER_WIDTH_S = 200;

/** 大屏宽度：1500px */
export const MAP_VIEW_LARGE_MEDIA_SCREEN_WIDTH = 1500;

export const MAP_TRACKING_TRACKING_LEARN_MORE_URL = 'https://wiki.moego.pet/moego-map-view/#track-staff-on-map-view';
