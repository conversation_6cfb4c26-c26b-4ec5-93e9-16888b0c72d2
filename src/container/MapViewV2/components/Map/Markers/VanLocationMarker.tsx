import { isFunction } from 'lodash';
import React from 'react';
import SvgMapPEndMarkerSvg from '../../../../../assets/svg/map-p-end-marker.svg';
import SvgMapPStartMarkerSvg from '../../../../../assets/svg/map-p-start-marker.svg';
import { Condition } from '../../../../../components/Condition';
import { createMapMarker } from '../../../../../components/GoogleMap/Marker';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { StackUp } from './StackUp';
import { withTooltip } from './withTooltip';

export interface RouteMarkerIconProps {
  type: 'start' | 'end';
  onClick?: () => void;
}

// 起点与终点的 marker
export const VanLocationMarkerIcon: React.FC<RouteMarkerIconProps> = React.memo((props) => {
  const { type } = props;

  return (
    <div className="map-route-marker-wrapper">
      <div className="map-route-marker moe-cursor-pointer moe-relative" onClick={props.onClick}>
        <Condition if={type === 'start'}>
          <SvgIcon src={SvgMapPStartMarkerSvg} size={18} />
        </Condition>
        <Condition if={type === 'end'}>
          <SvgIcon src={SvgMapPEndMarkerSvg} size={18} />
        </Condition>
      </div>
    </div>
  );
});

export const VanLocationMarker = createMapMarker(withTooltip(VanLocationMarkerIcon));

export const StackedVanLocationMarker = createMapMarker(
  withTooltip(
    (props: {
      items?: Array<RouteMarkerIconProps & { key?: React.Key }>;
      children?: (item: RouteMarkerIconProps, index: number) => React.ReactNode;
      stackOffset?: number;
      itemWidth?: number;
    }) => {
      const { stackOffset, itemWidth, items = [], children = (item) => <VanLocationMarkerIcon {...item} /> } = props;
      if (!isFunction(children)) {
        return null;
      }
      return (
        <StackUp stackOffset={stackOffset} itemWidth={itemWidth}>
          {items.map(children)}
        </StackUp>
      );
    },
  ),
);
