import { useSelector } from 'amos';
import { useNewFeature } from '../../../components/ColClientSearch/WithNewFeature/WithNewFeature.hooks';
import { newFeatureConfigMap } from '../../../components/ColClientSearch/WithNewFeature/utils';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { selectMetaDataLoaded } from '../../../store/metadata/metadata.selectors';

const ONBOARDING_KEY = 'financeDismissed';

export const useOnboardingCapital = () => {
  const [business, loaded] = useSelector(
    selectCurrentBusiness,
    selectMetaDataLoaded(newFeatureConfigMap[ONBOARDING_KEY].metadataKey),
  );
  const [isNew, dismiss, { newBefore }] = useNewFeature(ONBOARDING_KEY);

  const isLoading = !loaded;
  const isBusinessLoading = !business || !business.createTime;
  const shouldDisplay = !isLoading && !isBusinessLoading && isNew && Number(business.createTime) * 1e3 <= newBefore;
  return {
    shouldDisplay,
    dismiss,
    isLoading,
  };
};
