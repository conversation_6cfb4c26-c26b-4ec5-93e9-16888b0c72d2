import { Button, Heading, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import { appendQuery } from 'monofile-utilities/lib/query-string';
import React, { useMemo } from 'react';
import { useHistory } from 'react-router';
import SvgStarTwinkleSvg from '../../../../assets/svg/star-twinkle.svg';
import { SvgIcon } from '../../../../components/Icon/Icon';
import { ACCOUNTING_SUBSCRIPTION_LANDING_CONTACT_US, WIKI_FINANCE_ACCOUNTING } from '../../../../config/host/const';
import { PATH_FINANCE_SUBSCRIPTION } from '../../../../router/paths';
import { selectCurrentAccount } from '../../../../store/account/account.selectors';
import { ProductBTypeEnum } from '../../../../store/finance/subscription/subscription.enum';
import { selectCurrentStaff } from '../../../../store/staff/staff.selectors';
import { FinanceActionName } from '../../../../utils/reportData/finance';
import { reportData } from '../../../../utils/tracker';
import { AccountingLandingLayout } from './AccountingLandingLayout';

const DescriptionList: Array<{
  title: string;
  description: string;
}> = [
  {
    title: 'Stay on top of your financial health',
    description:
      'Forget relying on bank balances to gauge your business’s health. With a dedicated MoeGo bookkeeper, keep your records accurate and organized.',
  },
  {
    title: 'Real-time financials',
    description:
      'Close a ticket, and it’s instantly recorded in accounting. Access detailed statements and track expenses—all in one place.',
  },
  {
    title: 'Be prepared for tax season',
    description:
      'No more last-minute scrambling. With full-service bookkeeping, your records stay tax-ready all year, making filing seamless and stress-free.',
  },
];

export const SubscriptionLanding = () => {
  const history = useHistory();
  const [account, staff] = useSelector(selectCurrentAccount(), selectCurrentStaff());
  const contactLink = useMemo(() => {
    return appendQuery(ACCOUNTING_SUBSCRIPTION_LANDING_CONTACT_US, {
      firstname: staff.firstName || '',
      lastname: staff.lastName || '',
      email: account.email || '',
    });
  }, [staff.firstName, staff.lastName, account.email]);

  const gotoSubscribePlan = () => {
    history.push(
      PATH_FINANCE_SUBSCRIPTION.queried({
        financeProductType: Number(ProductBTypeEnum.ACCOUNTING),
      }),
    );
  };

  const handleCheckMyPlan = () => {
    reportData(FinanceActionName.AccountingLandingPageStart);
    gotoSubscribePlan();
  };

  const gotoLearnMore = () => {
    reportData(FinanceActionName.AccountingLandingPageLearnMore);

    window.open(WIKI_FINANCE_ACCOUNTING);
  };

  const handleContact = () => {
    reportData(FinanceActionName.AccountingLandingPageContact);
    window.open(contactLink, '_blank');
  };

  return (
    <AccountingLandingLayout title="Accounting" showSwitchBusinessDropdown={false}>
      <div
        className="moe-h-full moe-flex moe-min-w-fit moe-font-manrope"
        style={{
          backgroundImage: `url('https://dcgll7qxxap6x.cloudfront.net/u/0/2024/11/6f6f931c-7032-4307-8f9d-049e724128cd.png')`,
          backgroundSize: 'cover',
        }}
      >
        <div
          className={cn('moe-h-full moe-px-[60px] moe-w-[700px] moe-mt-[90px] moe-flex moe-flex-col', 'moe-relative')}
        >
          <div
            style={{
              zIndex: 0,
              width: '412px',
              height: '194px',
              backgroundImage: `url('https://dcgll7qxxap6x.cloudfront.net/u/0/2024/11/e5e9b6fc-7a3e-484f-94df-2845a60ccd6e.png')`,
              backgroundSize: 'cover',
              position: 'absolute',
              left: '236px',
              top: '-9px',
            }}
          />
          <div style={{ zIndex: 0 }}>
            <p className="moe-text-xl moe-leading-[24px] moe-font-normal">Let MoeGo handle your accounting.</p>
            <Heading size="2" className="moe-text-primary moe-mt-xs">
              Experience{' '}
              <span
                className="moe-bg-gradient-to-r moe-bg-clip-text moe-from-[#29CD57] moe-to-[#0091FF]"
                style={{ color: 'transparent' }}
              >
                stress-free bookkeeping
              </span>{' '}
              <br />
              with MoeGo today!
            </Heading>
            <div className={'moe-flex moe-gap-s moe-flex-col moe-mt-8px-600'}>
              {DescriptionList.map((item) => (
                <div key={item.title} className={'moe-flex moe-gap-xs moe-items-start'}>
                  <SvgIcon className={'moe-text-[34px]'} src={SvgStarTwinkleSvg} color={'#07AB4C'} />
                  <div className={'moe-flex moe-flex-col moe-gap-xxs'}>
                    <Heading size="5" className="moe-text-primary">
                      {item.title}
                    </Heading>
                    <Text variant="small" className="moe-text-secondary">
                      {item.description}
                    </Text>
                  </div>
                </div>
              ))}
            </div>
            <div className="moe-mt-8px-600">
              <div className="moe-flex moe-gap-s moe-items-center">
                <div
                  onClick={handleCheckMyPlan}
                  className={
                    'moe-flex moe-items-center moe-bg-success-bold hover:moe-bg-success-bold moe-h-8px-600 moe-rounded-full moe-px-8px-400 moe-py-8px-150 moe-cursor-pointer'
                  }
                >
                  <Heading size="5" className="moe-text-white">
                    Start now
                  </Heading>
                </div>
                <Button variant="tertiary" onPress={handleContact}>
                  Contact us
                </Button>
              </div>
              <div className="moe-flex moe-items-center moe-gap-xxs moe-mt-xs">
                <Text variant="small" className="moe-text-secondary">
                  To access MoeGo Accounting, you will be upgraded to our new invoice flow.
                </Text>
                <Button variant="tertiary" size="s" className={'moe-text-success-bold'} onPress={gotoLearnMore}>
                  Learn more
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div
          style={{
            width: '662px',
            marginTop: '50px',
            backgroundImage: `url('https://dcgll7qxxap6x.cloudfront.net/u/0/2024/11/76e94227-cae4-437a-a238-51b4cf662fb2.png')`,
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
          }}
        />
      </div>
    </AccountingLandingLayout>
  );
};
