import {
  ExternalProductType,
  KANMON_CONNECT,
  KanmonConnectEnviroment,
  type KanmonConnectParams,
  type OnEventCallbackEvent,
  OnEventCallbackEventType,
} from '@kanmon/web-sdk';
import { useEffect } from 'react';
import { useUnmount } from 'react-use';
import { MOE_ENV } from '../../../../config/host/const';
import { PATH_MOBILE_FINANCE_CAPITAL_KANMON } from '../../../../router/paths';
import { LoanOfferTypeEnum } from '../../../../store/finance/capital/capital.enum';
import { useRouteQueryV2 } from '../../../../utils/RoutePath';

export const Kanmon = () => {
  const { connectToken, offerType, noNeedAttachProductType, returnUrl } = useRouteQueryV2(
    PATH_MOBILE_FINANCE_CAPITAL_KANMON,
  );

  useUnmount(() => {
    KANMON_CONNECT.stop();
  });

  useEffect(() => {
    if (!connectToken) return;

    const { INTEGRATED_MCA, TERM_LOAN } = ExternalProductType;
    let productSubsetDuringOnboarding = null;
    if (noNeedAttachProductType !== 'true') {
      if (Number(offerType) === LoanOfferTypeEnum.MCA) {
        productSubsetDuringOnboarding = [INTEGRATED_MCA];
      } else if (Number(offerType) === LoanOfferTypeEnum.TERM_LOAN) {
        productSubsetDuringOnboarding = [TERM_LOAN];
      }
    }

    const config = {
      connectToken: connectToken,
      environment: MOE_ENV === 'production' ? KanmonConnectEnviroment.PRODUCTION : KanmonConnectEnviroment.SANDBOX,
      onEvent: (event: OnEventCallbackEvent) => {
        switch (event.eventType) {
          case OnEventCallbackEventType.USER_STATE_CHANGED:
            break;
          case OnEventCallbackEventType.HIDE:
            const url = returnUrl;
            window.open(url, '_self');
            break;
        }
      },
    } satisfies KanmonConnectParams;

    KANMON_CONNECT.stop();
    KANMON_CONNECT.start(productSubsetDuringOnboarding ? { ...config, productSubsetDuringOnboarding } : config);
    KANMON_CONNECT.show();
  }, [connectToken, offerType]);

  return null;
};
