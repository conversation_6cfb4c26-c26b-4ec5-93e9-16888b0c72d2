import { useDispatch } from 'amos';
import { pick } from 'lodash';
import React from 'react';
import { useHistory } from 'react-router';
import { useAsync } from 'react-use';
import CapitalOfferMarket from '../../../../../assets/icon/capital-offer-market.png';
import { CapitalSource, PATH_FINANCE_CAPITAL } from '../../../../../router/paths';
import { getListOffers } from '../../../../../store/finance/capital/actions/private/capital.actions';
import { LoanOffer } from '../../../../../store/finance/capital/capital.boxes';
import { useBusinessProfile } from '../../../../../store/profile/profile.hooks';
import { useIsMoeGoPayReady } from '../../../../../utils/hooks/useIsMoeGoPay';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { FinanceActionName } from '../../../../../utils/reportData/finance';
import { reportData } from '../../../../../utils/tracker';
import { useCapitalBlocked } from '../../../hooks/useCapitalBlocked';

export const EligibleMarketBanner = () => {
  const dispatch = useDispatch();
  const history = useHistory();
  const profile = useBusinessProfile();
  const isMoeGoPayReady = useIsMoeGoPayReady();
  const isCapitalBlocked = useCapitalBlocked();
  const { loading, value: shouldDisplay } = useAsync(async () => {
    if (!isMoeGoPayReady || isCapitalBlocked.isLoading || isCapitalBlocked.value) return false;
    const { offers: originOffers = [] } = await dispatch(getListOffers());
    const offers = originOffers.map((offer) => {
      return new LoanOffer(offer);
    });

    const hasCreatedOffer = !!offers.find((offer) => offer.isCreated);
    return hasCreatedOffer;
  }, [isMoeGoPayReady, isCapitalBlocked.isLoading, isCapitalBlocked.value]);

  const handleCheckOffer = useLatestCallback(() => {
    reportData(FinanceActionName.CapitalMktEntryCardProcessing, {
      ...(profile?.tags ? pick(profile.tags, ['tier', 'rank']) : {}),
    });
    history.push(
      PATH_FINANCE_CAPITAL.queried({
        source: CapitalSource.CardProcessing,
      }),
    );
  });

  if (loading || !shouldDisplay) return null;

  return (
    <img
      src={CapitalOfferMarket}
      className="moe-w-full moe-cursor-pointer moe-mt-5 moe-rounded-[5px]"
      alt="Capital Offer Market"
      onClick={handleCheckOffer}
    />
  );
};
