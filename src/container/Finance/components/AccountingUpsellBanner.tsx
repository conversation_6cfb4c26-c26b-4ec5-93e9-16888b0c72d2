import { OnboardingStatus } from '@moego/api-web/moego/models/accounting/v1/accounting_enums';
import { MinorCrown } from '@moego/icons-react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, Text } from '@moego/ui';
import { useRequest } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import { appendQuery } from 'monofile-utilities/lib/query-string';
import React, { memo } from 'react';
import { ACCOUNTING_UPSELL_BANNER_CONTACT_US } from '../../../config/host/const';
import { selectCurrentAccount } from '../../../store/account/account.selectors';
import { selectCurrentCompany } from '../../../store/company/company.selectors';
import { getOnboardingStatus } from '../../../store/finance/accounting/actions/private/accounting.actions';
import { selectCurrentStaff } from '../../../store/staff/staff.selectors';
import { FinanceActionName } from '../../../utils/reportData/finance';
import { reportData } from '../../../utils/tracker';
import { useInAccountingUpSellBannerWhitelist } from '../Accounting/hooks/useInAccountingUpSellBannerWhitelist';

const SubscribedStatus = [OnboardingStatus.NOT_ONBOARDED, OnboardingStatus.ONBOARDED];

interface AccountingUpSellBannerProps {
  from: 'intake-form' | 'reports';
  className?: string;
}

export const AccountingUpSellBanner = memo(({ className, from }: AccountingUpSellBannerProps) => {
  const dispatch = useDispatch();
  const [account, staff, company] = useSelector(selectCurrentAccount(), selectCurrentStaff(), selectCurrentCompany());
  const { value: inWhiteList } = useInAccountingUpSellBannerWhitelist();
  const { data, loading: isOnboardingStatusLoading } = useRequest(() => dispatch(getOnboardingStatus()));

  const isSubscribed = SubscribedStatus.includes(data?.onboardingStatus!);
  const isOwner = staff.isOwner();
  const isUS = company.isUS;

  const hideBannerInPage = !inWhiteList || !isUS || !isOwner || isSubscribed;

  const loading = isOnboardingStatusLoading;

  const handleContactUS = () => {
    reportData(FinanceActionName.AccountingUpSellBannerEnroll, { from });
    const contactLink = appendQuery(ACCOUNTING_UPSELL_BANNER_CONTACT_US, {
      firstname: staff.firstName || '',
      lastname: staff.lastName || '',
      email: account.email || '',
    });
    window.open(contactLink, '_blank');
  };

  if (hideBannerInPage || loading) {
    return null;
  }

  return (
    <Alert
      classNames={{
        base: 'moe-bg-brand-subtle moe-rounded-m',
        icon: 'moe-hidden',
        content: 'moe-flex-1',
        innerContent: 'moe-ml-0',
      }}
      className={className}
      action={
        <Button onPress={handleContactUS} size="s" variant="primary" color="brand">
          Enroll now
        </Button>
      }
      isCloseable={false}
    >
      <section className="moe-flex moe-items-start">
        <MinorCrown className="moe-mr-xs" />
        <Text variant="small">
          Sign up today for reliable, full-service bookkeeping starting at just{' '}
          <Markup variant="regular" className="moe-inline">
            $99 per month.
          </Markup>{' '}
          Let us handle the numbers so you can focus on doing what you love and growing your business! 🧡
        </Text>
      </section>
    </Alert>
  );
});
