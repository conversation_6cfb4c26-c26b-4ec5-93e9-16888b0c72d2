import { MinorInfoFilled } from '@moego/icons-react';
import { Condition, Tag, Text, cn } from '@moego/ui';
import classNames from 'classnames';
import { repeat } from 'lodash';
import React from 'react';
import ImageDefaultCardPng from '../../../../../assets/image/default-card.png';
import ImageMasterCardPng from '../../../../../assets/image/master-card.png';
import ImageVisaCardPng from '../../../../../assets/image/visa-card.png';
import { useMobileLayout } from '../../../../../utils/hooks/useMobileLayout';
import {
  CardImage,
  CardInfoContainer,
  Content,
  CreditCardContainer,
  CreditCardTopContainer,
} from './AccountCreditCard.style';
export const CreditCardContent = ({
  isSelected,
  onSelect,
  showRadio,
  showTips,
  name,
  expMonth,
  expYear,
  brand,
  lastFor,
}: {
  isSelected: boolean;
  onSelect: () => void;
  showRadio: boolean;
  showTips: boolean;
  name?: string;
  expMonth?: number;
  expYear?: number;
  brand?: string;
  lastFor?: string;
}) => {
  const cardDot = repeat('···· ', 3);
  const expiredDate = `${String(expMonth).padStart(2, '0')}/${expYear}`;

  const cardImg: Record<string, string> = {
    Visa: ImageVisaCardPng,
    MasterCard: ImageMasterCardPng,
    Others: ImageDefaultCardPng,
  };
  const img = cardImg[brand ?? 'Others'] ?? ImageDefaultCardPng;

  const isMobile = useMobileLayout();

  return (
    <CreditCardContainer>
      <CreditCardTopContainer onClick={onSelect}>
        <Content
          className={classNames('moe-rounded-m moe-border moe-border-divider', {
            'hover:moe-bg-neutral-sunken-0 active:moe-bg-neutral-sunken-1': !isSelected,
            'moe-bg-brand-subtle !moe-border-brand ': isSelected,
          })}
        >
          <CardImage src={img} />
          <CardInfoContainer>
            {/* <CardNumber> */}
            <div className="moe-flex moe-items-center moe-flex-wrap">
              <span className="moe-text-primary moe-text-[22px] moe-inline-block moe-h-[22px] moe-leading-[22px]">
                {cardDot}
              </span>
              <Text variant="regular-short" as="span" className="moe-ml-[6px]">
                {lastFor}
              </Text>
              {/* break next flex item to new line */}
              <span
                className={cn({
                  'moe-basis-full moe-h-0': isMobile,
                })}
              />
              <Condition if={isSelected}>
                <Tag
                  color="primary"
                  isBordered
                  variant="outlined"
                  label="Primary"
                  className={cn({
                    'moe-ml-xs': !isMobile,
                    'moe-mt-xs': isMobile,
                  })}
                />
              </Condition>
            </div>
            {/* </CardNumber> */}

            <Text variant="regular-short" className="moe-text-tertiary moe-mt-xs">
              Exp. {expiredDate}
              {name ? (
                <span
                  className={cn({
                    'moe-ml-xxs': !isMobile,
                    'moe-block moe-ml-none moe-mt-xs': isMobile,
                  })}
                >
                  ({name})
                </span>
              ) : null}
            </Text>
          </CardInfoContainer>
        </Content>
      </CreditCardTopContainer>
      {showTips ? (
        <div className="moe-flex moe-items-center moe-ml-xxs moe-mt-xs">
          <MinorInfoFilled />
          <Text variant="small" className="moe-ml-xxs">
            Your primary card will be changed to this card.
          </Text>
        </div>
      ) : null}
    </CreditCardContainer>
  );
};
