/*
 * @since 2021-07-19 15:00:03
 * <AUTHOR> <<EMAIL>>
 */

import styled from 'styled-components';
import { Modal } from '../../../components/Modal/Modal';
import { ModalContainerView } from '../../../components/Modal/Modal.style';
import { c_text_secondary_color } from '../../../style/_variables';

export const SquareTerminalListModalView = styled(Modal)`
  ${ModalContainerView} {
    padding: 40px 70px;
  }

  .list {
    min-height: 300px;
  }

  .label {
    text-transform: uppercase;
    font-size: 16px;
    font-weight: bold;
    color: ${c_text_secondary_color};
    padding: 12px 0;
  }

  .hint {
    color: ${c_text_secondary_color};
    font-size: 14px;
    text-align: center;
    margin-top: 40px;
  }

  .actions {
    text-align: center;
    margin-top: 20px;
  }

  .item {
    border: 1px solid #c9d5e1;
    margin-bottom: 20px;
    border-radius: 6px;
    font-size: 18px;
    padding: 12px 20px 12px 8px;
    display: flex;
    align-items: center;

    > img {
      width: 64px;
      height: auto;
    }

    > .info {
      flex: 1;
      margin-left: 12px;

      > .sn {
        font-size: 14px;
        color: ${c_text_secondary_color};
      }
    }

    > .code {
      color: ${c_text_secondary_color};
      letter-spacing: 2px;
      cursor: pointer;
    }
  }
`;
