import { useDispatch, useSelector } from 'amos';
import { Spin, Tooltip } from 'antd';
import { type TooltipProps } from 'antd/lib/tooltip';
import dayjs from 'dayjs';
import { isUndefined } from 'lodash';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useMemo, useRef, useState } from 'react';
import { useAsync } from 'react-use';
import SvgIconTooltipsSvg from '../../../../assets/svg/icon-tooltips.svg';
import { Condition } from '../../../../components/Condition';
import { SvgIcon } from '../../../../components/Icon/Icon';
import { type OpenApiModels } from '../../../../openApi/schema';
import { type BusinessRecord } from '../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getStripePayoutRangeSummary } from '../../../../store/stripe/payout.actions';
import { type EnumLabels, type EnumValues, createEnum } from '../../../../store/utils/createEnum';
import { useBool } from '../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { SUMMARY_SUPPORT_DATE } from '../PayoutSummary/PayoutSummary.utils';
import { SummaryCardView } from './SummaryCard.style';

type SummaryRes = OpenApiModels['POST/payment/stripe/payout/list/summary']['Res'];
type AmountFormatter = BusinessRecord['formatAmountSpecial'];
const AMOUNT_ALT_CHAR = '-';
const REFRESH_INTERVAL = [5, 10, 15, 30, 60];
/**
 * summary status, determines how the card displayed
 */
const StatusEnum = createEnum<
  string,
  number,
  {
    isAlt?: boolean;
    cardTooltip?: TooltipProps & { show: boolean };
    amountTooltip?: TooltipProps & { show: boolean };
    renderAmount: (formatter: AmountFormatter, amount?: number, isAlt?: boolean) => string;
  }
>({
  // fe loading
  loading: [
    1,
    {
      renderAmount: (formatter: AmountFormatter, amount?: number, isAlt?: boolean) => {
        if (isUndefined(amount)) {
          return isAlt ? AMOUNT_ALT_CHAR : '';
        }

        return formatter(amount);
      },
    },
  ],
  notSupportedDate: [
    2,
    {
      isAlt: true,
      renderAmount: () => {
        return AMOUNT_ALT_CHAR;
      },
      // tooltip for the amount
      amountTooltip: {
        show: true,
        title: 'Data unavailable',
        placement: 'topLeft',
      },
    },
  ],
  noData: [
    3,
    {
      renderAmount: (formatter: AmountFormatter) => {
        return formatter(0);
      },
    },
  ],
  // fe loaded, be calculating summary.
  processing: [
    4,
    {
      // tooltip for the card
      isAlt: true,
      cardTooltip: {
        show: true,
        title: 'Data loading, please check later.',
      },
      renderAmount: () => {
        return AMOUNT_ALT_CHAR;
      },
    },
  ],
  normal: [
    5,
    {
      renderAmount: (formatter: AmountFormatter, amount?: number) => {
        return isUndefined(amount) ? '' : formatter(amount);
      },
    },
  ],
});

const amountList: Array<{
  name: string;
  amountProp: 'grossSales' | 'netSales' | 'totalCollected' | 'totalPayout';
  description: string;
}> = [
  {
    name: 'Gross sales',
    amountProp: 'grossSales',
    description: 'Total sales including all services, products, addons and extra fees.',
  },
  {
    name: 'Net sales',
    amountProp: 'netSales',
    description: 'Gross sales minus any deductions like refund and discount.',
  },
  {
    name: 'Total collected',
    amountProp: 'totalCollected',
    description: 'Total earned revenue including tax and tips.',
  },
  {
    name: 'Payout amount',
    amountProp: 'totalPayout',
    description: 'Total collected amount minus processing fees and other adjustments.',
  },
];

const SummaryAmountItem = (props: {
  item: (typeof amountList)[number];
  amount: string;
  statusConfig: EnumLabels<typeof StatusEnum>;
}) => {
  const { item, amount, statusConfig } = props;
  return (
    <div className="summary-item" key={item.name}>
      <div className="moe-flex moe-flex-row moe-items-center">
        <span className="name">{item.name}</span>
        <Tooltip title={item.description}>
          <SvgIcon src={SvgIconTooltipsSvg} size={16} className="!moe-cursor-pointer" />
        </Tooltip>
      </div>
      <Condition if={statusConfig.amountTooltip?.show}>
        <Tooltip title={statusConfig.amountTooltip?.title} placement={statusConfig.amountTooltip?.placement}>
          <span className="value moe-cursor-pointer">{amount}</span>
        </Tooltip>
      </Condition>
      <Condition if={!statusConfig.amountTooltip?.show}>
        <span className="value">{amount}</span>
      </Condition>
    </div>
  );
};
export interface SummaryCardProps {
  isEmpty: boolean;
  listLoading: boolean;
  dateRangeParams: {
    startDate: string;
    endDate: string;
  };
}

export const SummaryCard = memo<SummaryCardProps>((props) => {
  const { dateRangeParams, isEmpty, listLoading } = props;
  const [business] = useSelector(selectCurrentBusiness());
  const dispatch = useDispatch();
  const countRef = useRef(0);
  const tooltipVisible = useBool();
  const timer = useRef<ReturnType<typeof setTimeout>>();
  const [summary, setSummary] = useState<SummaryRes | null>(null);
  const isNotSupportedDate = useMemo(() => {
    return dayjs(dateRangeParams.startDate).isBefore(SUMMARY_SUPPORT_DATE);
  }, [dateRangeParams]);

  // fetch summary, auto polling when summary is null(processing)
  const getPayoutRangeSummary = useSerialCallback(async () => {
    const newSummary = await dispatch(getStripePayoutRangeSummary(dateRangeParams.startDate, dateRangeParams.endDate));
    setSummary(newSummary || null);
    if (!newSummary && countRef.current < REFRESH_INTERVAL.length) {
      timer.current = setTimeout(async () => {
        getPayoutRangeSummary();
      }, REFRESH_INTERVAL[countRef.current] * T_SECOND);
      countRef.current++;
    }
  });

  const anyLoading = listLoading || getPayoutRangeSummary.isBusy();
  useAsync(async () => {
    if (isNotSupportedDate) {
      setSummary(null);
    } else if (dateRangeParams.startDate && dateRangeParams.endDate) {
      await getPayoutRangeSummary();
    }

    return () => {
      countRef.current = 0;
      timer.current && clearTimeout(timer.current);
    };
  }, [dateRangeParams]);

  const statusConfig = useMemo(() => {
    const status: EnumValues<typeof StatusEnum> =
      (anyLoading && StatusEnum.loading) ||
      (isNotSupportedDate && StatusEnum.notSupportedDate) ||
      (isEmpty && StatusEnum.noData) ||
      (!summary && StatusEnum.processing) ||
      StatusEnum.normal;

    return StatusEnum.mapLabels[status];
  }, [anyLoading, isEmpty, isNotSupportedDate, summary]);

  const formatter = business.formatAmountSpecial.bind(business);
  const isAlt = !!statusConfig.isAlt;
  return (
    <Tooltip
      title={statusConfig.cardTooltip?.title}
      visible={statusConfig.cardTooltip?.show && !anyLoading && tooltipVisible.value}
      onVisibleChange={tooltipVisible.as}
    >
      <Spin spinning={anyLoading} wrapperClassName="moe-mt-[20px]">
        <SummaryCardView>
          {amountList.map((item) => {
            const amount = statusConfig.renderAmount(formatter, summary?.[item.amountProp], isAlt);
            return <SummaryAmountItem item={item} key={item.amountProp} amount={amount} statusConfig={statusConfig} />;
          })}
        </SummaryCardView>
      </Spin>
    </Tooltip>
  );
});
