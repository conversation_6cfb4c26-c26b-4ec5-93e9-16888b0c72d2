import styled from 'styled-components';
import IconEasterBtnRabbitPng from '../../../assets/image/easter-btn-rabbit.png';
import IconEasterEggsBgPng from '../../../assets/image/easter-eggs-bg.png';
import IconEasterMoeGoFamilyPng from '../../../assets/image/easter-moego-family.png';
import SmartTipPreviewSmartReaderPng from '../../../assets/image/smart-tip-preview-smart-reader.png';

export const SmartTipSettingPreviewView = styled.div`
  width: 100%;
  height: 100%;
  padding: 20px 20px;
  overflow-y: scroll;

  .preview-title {
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    color: #333;
  }

  .preview-only-invoice {
    margin: 4px 0 0;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: #666;
  }

  .preview-tab {
    /* fixed width */
    width: 288px;
    margin-top: 8px;
    padding: 4px;
    background: #e5e6ec;
    border-radius: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;

    .tab-item {
      height: 24px;
      padding: 2px 11px;
      text-align: center;
      border-radius: 56px;
      white-space: nowrap;
      cursor: pointer;

      &.active {
        background: #fff;
        color: #f96b18;
      }

      &.item--invoice {
        width: 154px;
      }

      &.item--reader {
        width: 128px;
      }
    }
  }

  .tab-invoice {
    margin-top: 20px;
    display: none;
    width: 100%;
    height: 440px;
    overflow: visible;

    &.active {
      display: flex;
      justify-content: center;
      align-items: flex-start;
    }

    .page-phone {
      width: 440px;
      height: 790px;
      transform: scale(${250 / 440});
      transform-origin: center top;

      .phone-screen {
        width: 350px;
        height: 738px;
        position: relative;
        border-radius: 56px;
        padding: 36px 30px 0 30px;
        margin: 10px;
      }
    }
  }

  .tab-smart-reader {
    display: none;
    width: auto;
    height: 517px;
    overflow: visible;

    &.active {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
    }

    .smart-reader-desc {
      align-self: flex-start;
      margin-top: 10px;
      height: 32px;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      color: #333;
    }

    .page-reader {
      margin-top: 20px;
      width: 296px;
      height: 713px;
      padding: 241px 31px 64px;
      background: url(${SmartTipPreviewSmartReaderPng});
      background-size: contain;
      transform: scale(${200 / 296});
      transform-origin: center top;

      .reader-screen {
        width: 234px;
        height: 408px;
        position: relative;
      }
    }
  }
`;

export const PreviewPhoneContentView = styled.div`
  position: relative;
  width: 100%;
  height: 100%;

  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &.easter-bg {
    background: url(${IconEasterEggsBgPng});
    background-size: cover;

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      /* 20px 预览的 padding 偏差；6px 居中的偏差 */
      left: calc(50% - 170px + 20px + 6px);
      width: calc(347px - 40px);
      height: 164px;
      background: url(${IconEasterMoeGoFamilyPng});
      background-size: contain;
    }
  }

  .nav-bar {
    height: 44px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .tip-box {
    .title {
      color: #333;
      font-weight: 700;
      font-size: 22px;
      line-height: 24px;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 24px;
    }

    .desc {
      margin-top: 12px;
      color: #333;
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
      text-align: center;
    }

    .content {
      margin-top: 48px;
      text-align: center;

      .tip-btn-mask {
        margin-top: 20px;
        width: 100%;
        height: 56px;
        border-radius: 56px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 600;
        font-size: 18px;
        line-height: 24px;
        background: #f2f2f2;
        color: #999;
        cursor: pointer;
        user-select: none;

        &:active,
        &:hover {
          color: #333;
        }
      }

      .no-tip {
        width: 100%;
        position: absolute;
        left: 0;
        bottom: 50px;
        padding: 12px 32px;
        /* bottom: 48px; */
        color: #999;
        font-weight: 600;
        font-size: 18px;
        line-height: 24px;
        cursor: pointer;

        &.follow-up-mode {
          margin-top: 28px;
          position: relative;
          left: 0;
          bottom: 0;
        }
      }

      .btn-easter-fill-rabbit {
        position: absolute;
        left: 16px;
        bottom: 0;
        width: 58px;
        height: 27px;
        background: url(${IconEasterBtnRabbitPng});
        background-size: contain;
        z-index: 0;
      }

      .btn-easter-fill-text {
        z-index: 1;
      }
    }
  }
`;

export const PreviewReaderContentView = styled.div`
  position: relative;
  width: 100%;
  height: 100%;

  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    background: linear-gradient(
      163.57deg,
      #a4a4a4 -1.94%,
      rgba(164, 164, 164, 0) 136.92%,
      rgba(164, 164, 164, 0) 162.05%
    );
    opacity: 0.3;
  }

  .tip-box {
    padding-top: 50px;

    .name {
      color: #fff;
      opacity: 0.7;
      font-weight: 700;
      font-size: 14px;
      line-height: 14px;
      text-align: center;
    }

    .amount {
      margin-top: 14px;
      color: #fff;
      font-weight: 700;
      font-size: 28px;
      line-height: 28px;
      text-align: center;
    }

    .desc {
      margin-top: 56px;
      color: #fff;
      font-weight: 400;
      font-size: 12px;
      line-height: 17px;
      text-align: center;
    }

    .content {
      margin-top: 12px;
      padding: 0 32px;
      text-align: center;

      .tip-btn {
        width: 100%;
        height: 30px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 12px;
        line-height: 17px;

        &.btn-secondary {
          border: none;
          background: #2c6dd9;
          color: #fff;
        }

        &.btn-secondary-unfilled {
          border-color: rgba(230, 230, 230, 0.6);
          color: #2c6dd9;
          background: none;
        }

        &:not(:first-child) {
          margin-top: 6px;
        }
      }
    }
  }
`;
