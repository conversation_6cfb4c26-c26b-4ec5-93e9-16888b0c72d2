import { useDispatch, useSelector } from 'amos';
import { Form, Input } from 'antd';
import classnames from 'classnames';
import React, { useEffect } from 'react';
import { useUpdate } from 'react-use';
import validator from 'validator';
import IconColorfulCirclePng from '../../../assets/image/colorful-circle.png';
import SvgPricingIconRightSvg from '../../../assets/svg/pricing-icon-right.svg';
import { Button } from '../../../components/Button/Button';
import { SvgIcon } from '../../../components/Icon/Icon';
import { Loading } from '../../../components/Loading/Loading';
import { type ModalProps, modalApi } from '../../../components/Modal/Modal';
import { toastApi } from '../../../components/Toast/Toast';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { getSmartTipConfig, updatePaymentSettingInfo } from '../../../store/payment/actions/private/payment.actions';
import { getPaymentSettingInfo } from '../../../store/payment/actions/public/payment.actions';
import { selectPaymentSettingInfo, selectSmartTipConfig } from '../../../store/payment/payment.selectors';
import { isNormal } from '../../../store/utils/identifier';
import { useFormRef } from '../../../utils/hooks/hooks';
import { useBool } from '../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { isAfterEasterTheme } from '../../../utils/utils';
import {
  ColorConfigCustomType,
  CustomTheme,
  type CustomTipping,
  WITHOUT_COLOR_CONFIG_LIST,
  customTippingToConfig,
} from '../../Payment/utils/theme';
import { StyledForm, StyledInput, StyledModal } from './CustomTipScreenModal.style';
import { smartTipConfigToFields } from './SmartTipSettingModal.options';
import { SmartTipSettingPreview } from './SmartTipSettingPreview';

const useLoadData = () => {
  const dispatch = useDispatch();
  const init = useSerialCallback(async () => {
    await Promise.all(dispatch([getPaymentSettingInfo(), getSmartTipConfig()]));
  });

  useEffect(() => {
    init();
  }, []);

  return {
    loading: init.isBusy(),
  };
};

const useCustomTippingFormRef = (visible: boolean, afterInit: () => void) => {
  const [paymentSettingInfo] = useSelector(selectPaymentSettingInfo());
  const form = useFormRef<CustomTipping>();

  useEffect(() => {
    if (isNormal(paymentSettingInfo.businessId) && visible) {
      form.current?.setFieldsValue(paymentSettingInfo.getCustomTipping());
      afterInit();
    }
  }, [paymentSettingInfo.businessId, visible]);

  return form;
};

export const CustomTipScreenModal = ({ visible, onClose }: ModalProps) => {
  const dispatch = useDispatch();
  const [business, smartTipConfig] = useSelector(selectCurrentBusiness(), selectSmartTipConfig());

  const update = useUpdate();
  const isDirty = useBool();

  const { loading } = useLoadData();
  const form = useCustomTippingFormRef(visible, update);

  const handleSave = useSerialCallback(async () => {
    const values = form.current?.getFieldsValue();
    if (values) {
      await dispatch(
        updatePaymentSettingInfo({
          customTipping: JSON.stringify(values),
        }),
      );
      toastApi.success('Appearance settings have been saved.');
      isDirty.close();
      onClose();
    }
  });

  const close = () => {
    isDirty.close();
    onClose();
  };

  const handleClose = () => {
    if (isDirty.value) {
      modalApi.confirm({
        icon: null,
        title: 'Unsaved changes',
        content: 'Are you sure you want to leave without saving?',
        onOk: close,
        okText: 'Yes',
        okButtonProps: {
          className: '!moe-rounded-[56px]',
        },
        cancelText: 'No',
        cancelButtonProps: {
          className: '!moe-rounded-[56px]',
        },
      });
      return;
    }
    close();
  };

  const handleFormChange = () => {
    isDirty.open();
    update();
  };

  const values = form.current?.getFieldsValue();

  return (
    <StyledModal
      visible={visible}
      onClose={handleClose}
      width="800px"
      title="Appearance"
      footer={
        <Button
          btnType="primary"
          size="sm"
          buttonRadius="circle"
          onClick={handleSave}
          loading={handleSave.isBusy()}
          disabled={!isDirty.value}
          className="!moe-w-[96px] !moe-h-[32px]"
        >
          Save
        </Button>
      }
    >
      <Loading loading={loading}>
        <div className="!moe-flex !moe-w-[800px]">
          <div className="modal-part-left !moe-w-[436px] !moe-relative !moe-pt-[24px] !moe-px-[32px]">
            <StyledForm ref={form} className="!moe-w-[374px]" onFieldsChange={handleFormChange}>
              <Form.Item name="headerText" label="Header text">
                <HeaderTextInput />
              </Form.Item>
              <Form.Item name="theme" label="Theme">
                <ThemeInput />
              </Form.Item>
              <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => currentValues.theme !== prevValues.theme}>
                {({ getFieldValue }) => {
                  return (
                    <Form.Item
                      name="colorConfig"
                      label="Color code"
                      style={{
                        display: WITHOUT_COLOR_CONFIG_LIST.includes(getFieldValue('theme')) ? 'none' : 'flex',
                      }}
                    >
                      <ColorInput />
                    </Form.Item>
                  );
                }}
              </Form.Item>
              <p className="!moe-text-[14px] !moe-text-[#999] !moe-leading-[18px] !moe-mb-[20px]">
                This will not apply to the tip screen of smart reader due to the third-party technical restrictions.
              </p>
            </StyledForm>
          </div>
          <div className="modal-part-right !moe-w-[364px] !moe-bg-[#F7F8FA]">
            <SmartTipSettingPreview
              className="!moe-overflow-hidden !moe-h-[560px] !moe-w-[364px]"
              business={business}
              forceUseMainUpperConfig
              hasSmartReader={false}
              customTippingConfig={customTippingToConfig(values)}
              smartTipFields={smartTipConfigToFields(smartTipConfig)}
              invoiceTitle="On the invoice page"
            />
          </div>
        </div>
      </Loading>
    </StyledModal>
  );
};

const DEFAULT_HEADER_TEXT = 'Tip for pawsome service!';
// const CHRISTMAS_HEADER_TEXT = 'Furry Christmas!';
const EASTER_HEADER_TEXT = 'Have a pawfect Easter day!';
const HeaderTextInput = ({ value, onChange }: { value?: string; onChange?: (v: string) => void }) => {
  const handleChangeV = (v: string) => {
    onChange?.(v);
  };
  // const templateHeaderText = isAfterChristmasTheme() ? DEFAULT_HEADER_TEXT : CHRISTMAS_HEADER_TEXT;
  // 圣诞已过，现在是复活节
  const templateHeaderText = isAfterEasterTheme() ? DEFAULT_HEADER_TEXT : EASTER_HEADER_TEXT;

  return (
    <div className="!moe-w-full">
      <StyledInput value={value} onChange={(e) => handleChangeV(e.target.value)} allowClear />
      <div className="!moe-mt-[8px] !moe-text-[#999999] !moe-text-[14px]">
        Use template:{' '}
        <span className="!moe-text-brand !moe-cursor-pointer" onClick={() => handleChangeV(templateHeaderText)}>
          [{templateHeaderText}]
        </span>
      </div>
    </div>
  );
};

const ThemeInput = ({ value, onChange }: { value?: string; onChange?: (v: string) => void }) => {
  const handleClickTheme = (v: string) => {
    onChange?.(v);
  };

  return (
    <div className="!moe-w-full !moe-mb-[-20px]">
      <div className="!moe-flex !moe-flex-wrap">
        {CustomTheme.values.map((theme, i, arr) => {
          // if (isAfterChristmasTheme() && theme === CustomTheme.Christmas) return null;
          const { img, title } = CustomTheme.mapLabels[theme]();

          const className = classnames(
            '!moe-w-[86px] !moe-h-[96px] !moe-flex !moe-items-center !moe-justify-center !moe-bg-white !moe-rounded-[8px] !moe-border-[1px] !moe-border-solid !moe-cursor-pointer !moe-overflow-hidden',
            {
              '!moe-border-brand': theme === value,
              '!moe-border-[#e6e6e6]': theme !== value,
            },
          );
          return (
            <div key={theme} className={`${i % 3 !== 0 ? '!moe-ml-[12px] !moe-mb-[20px]' : '!moe-mb-[20px]'}`}>
              <div className={className} onClick={() => handleClickTheme(theme)}>
                <img src={img} className="!moe-h-[96px] !moe-w-full !moe-object-cover" />
              </div>
              <p className="!moe-font-[600] !moe-text-center !moe-mt-[8px]">{title}</p>
            </div>
          );
        })}
      </div>
      {/* 暂时撤回 */}
      {/* {value === CustomTheme.Christmas ? (
        <p className="!moe-text-[#999999] !moe-text-sm !moe-whitespace-normal !moe-mt-[16px]">
          The Christmas theme is available until Dec 31. After that, the theme will revert back to the default one.
        </p>
      ) : null} */}
    </div>
  );
};

const COLOR_1 = ['#FF3B30', '#F96B18', '#FF9500', '#FFCC00', '#34C7BE', '#30B0C7'];
const COLOR_2 = ['#32ADE6', '#007AFF', '#5856D6', '#AF52DE', '#FF2D55', '#A2845E'];
const ColorInput = ({
  value: { presetColor, customColor, isSelectCustom } = {
    presetColor: '#F96B18',
    customColor: '',
    isSelectCustom: false,
    customType: ColorConfigCustomType.HEX,
  },
  onChange,
}: {
  value?: CustomTipping['colorConfig'];
  onChange?: (v: CustomTipping['colorConfig']) => void;
}) => {
  const handleSelectPresetColor = (color: string) => {
    onChange?.({
      presetColor: color,
      customColor,
      isSelectCustom: false,
      customType: ColorConfigCustomType.HEX,
    });
  };

  const handleChangeCustomColor = (color: string) => {
    onChange?.({
      presetColor,
      customColor: `#${color}`,
      isSelectCustom: true,
      customType: ColorConfigCustomType.HEX,
    });
  };

  const renderColorCircle = (color: string) => {
    const useCurrentColor = !isSelectCustom && presetColor === color;
    return (
      <span
        key={color}
        className={`!moe-w-[32px] !moe-h-[32px] !moe-rounded-full !moe-flex !moe-justify-center !moe-items-center !moe-cursor-pointer`}
        style={{ backgroundColor: color }}
        onClick={() => handleSelectPresetColor(color)}
      >
        {useCurrentColor ? <SvgIcon src={SvgPricingIconRightSvg} color="#fff" size={12} /> : null}
      </span>
    );
  };

  return (
    <div className="!moe-w-full">
      <div className="!moe-flex !moe-justify-between">{COLOR_1.map((color) => renderColorCircle(color))}</div>
      <div className="!moe-flex !moe-justify-between !moe-mt-[16px]">
        {COLOR_2.map((color) => renderColorCircle(color))}
      </div>
      <div className="!moe-h-[1px] !moe-bg-[#E6E6E6] !moe-my-[16px]" />
      <div className="!moe-flex !moe-items-center">
        <span
          className="!moe-relative !moe-flex !moe-justify-center !moe-items-center !moe-mr-[22px] !moe-cursor-pointer"
          onClick={() => handleChangeCustomColor(customColor ? customColor.slice(1) : '000000')}
        >
          <img src={IconColorfulCirclePng} className="!moe-w-[32px] !moe-h-[32px]" />
          {isSelectCustom ? (
            <SvgIcon className="!moe-absolute !moe-ml-0" src={SvgPricingIconRightSvg} color="#fff" size={12} />
          ) : null}
        </span>
        <Input
          value={customColor.slice(1)}
          onChange={(e) => handleChangeCustomColor(e.target.value)}
          onFocus={() => handleChangeCustomColor(customColor.slice(1))}
          onBlur={() => {
            if (!validator.isHexColor(customColor)) {
              handleChangeCustomColor('000000');
            }
          }}
          placeholder="000000"
          prefix="#"
          maxLength={6}
        />
      </div>
    </div>
  );
};
