import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../utils/growthBook/growthBook.config';
import { useOpenApptDetailDrawer } from '../../../utils/hooks/useOpenApptDetailDrawer';

/**
 * homepage 特殊的 checkin 逻辑
 */
export const useSpecialCheckInProcess = (appointmentId: number) => {
  const enableCheckInOpenApptDrawer = useFeatureIsOn(GrowthBookFeatureList.EnableCheckInOpenApptDrawer);
  const { openApptDetailDrawer } = useOpenApptDetailDrawer();

  /**
   * @returns false means nothing happens, true means do some special process
   */
  const handleSpecialCheckInProcess = () => {
    // 如果点击 checkin 则直接打开 appt drawer
    if (enableCheckInOpenApptDrawer) {
      openApptDetailDrawer({
        ticketId: appointmentId,
      });
      return true;
    }

    return false;
  };

  return {
    handleSpecialCheckInProcess,
  };
};
