import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type CellContext } from '@moego/ui/dist/esm/components/Table/Table.types';
import { useSelector } from 'amos';
import { type ReactNode, useEffect, useState } from 'react';
import { getMainCareType } from '../../../components/PetAndServicePicker/utils/getMainCareType';
import { apptDetailModalStatusBox } from '../../../store/calendarLatest/calendar.boxes';
import { GroomingTicketNoShowType } from '../../../store/grooming/grooming.boxes';
import { type OverviewRecordModel } from '../../../store/overview/overview.boxes';
import { ID_ANONYMOUS } from '../../../store/utils/identifier';
import { type ActionItem } from '../../Appt/components/types';
import { useEvaluationTicketMoreActionList } from '../../Appt/hooks/useEvaluationTicketMoreActionList';
import { useNoEvaluationApptMoreActionList } from '../../Appt/modules/ApptDetailDrawer/ApptInfoPanel/ApptInfoStatusNext/hooks/useApptMoreActionList';
import { useTicketMoreActionList } from '../../Appt/modules/ApptDetailDrawer/hooks/useTicketMoreActionList';
import { matchApptFlowScene } from '../../Appt/store/appt.options';
import { ApptFlowScene } from '../../Appt/store/appt.types';
import { useInvoiceReinvent } from '../../PaymentFlow/hooks/useInvoiceReinvent';
import { AppointmentStatus } from '../../TicketDetail/AppointmentStatus';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { useReviewBoosterAction } from '../../Appt/modules/ApptDetailDrawer/hooks/useReviewBoosterAction';

export type OverviewActionItem = ActionItem & {
  showTooltip?: boolean;
  tooltipContent?: ReactNode;
};

type UseMoreActionsProps = {
  ctx: CellContext<OverviewRecordModel, unknown>;
  onChange: () => void;
};

export function useMoreActions({ ctx, onChange }: UseMoreActionsProps) {
  const { id: ticketId, status: appointmentStatus, appointmentDate, customerId, noShow } = ctx.row.original.appointment;
  const customerProfile = ctx.row.original.customer.customerProfile;
  const { serviceItemTypes, serviceDetail, waitList, noShowInvoice, invoice } = ctx.row.original;
  const [{ cancelApptModalVisible }, currentBusinessId] = useSelector(apptDetailModalStatusBox, currentBusinessIdBox);
  const isEvaluation =
    !!ctx.row.original.serviceItemTypes?.length &&
    ctx.row.original.serviceItemTypes?.every((item) => item === ServiceItemType.EVALUATION);
  const showEditLodgingAssignment =
    matchApptFlowScene(ApptFlowScene.ApptLodging, getMainCareType(serviceItemTypes)) &&
    appointmentStatus !== AppointmentStatus.FINISHED; // 完成状态不显示
  const petIds = serviceDetail.map((service) => service.pet.id);

  const evaluationOptList = useEvaluationTicketMoreActionList({
    ticketId: Number(ticketId),
    businessId: String(currentBusinessId),
    appointmentStatus,
    customerId: Number(customerId),
    noShowStatus: noShowInvoice.status,
    noShowInvoiceId: Number(noShowInvoice.invoiceId),
    reviewBoosterSent: ctx.row.original.customer.reviewBoosterSent,
    noShow: noShow ? GroomingTicketNoShowType.noShow : GroomingTicketNoShowType.noShowCancel,
    showEditLodgingAssignment,
    petIds,
  }) as OverviewActionItem[];

  const reviewBoosterAction = useReviewBoosterAction({
    ticketId: Number(ticketId),
    appointmentStatus,
    reviewBoosterSent: ctx.row.original.customer.reviewBoosterSent,
    customerId: Number(customerId),
  });

  const apptOptList = useTicketMoreActionList(
    {
      ticketId: Number(ticketId),
      showAdvancedEdit: false,
      showEditLodgingAssignment,
      appointmentStatus,
      groomingCustomerInfoIsDeleted: customerProfile?.deleted ?? false,
      waitListMainStaffId: Number(serviceDetail?.[0]?.services?.[0]?.staffId ?? ID_ANONYMOUS),
      waitListId: waitList.id,
      appointmentDate,
      customerId: Number(customerId),
      noShowStatus: noShowInvoice.status,
      noShowInvoiceId: Number(noShowInvoice.invoiceId),
      reviewBoosterSent: ctx.row.original.customer.reviewBoosterSent,
      noShow: noShow ? GroomingTicketNoShowType.noShow : GroomingTicketNoShowType.noShowCancel,
      serviceItemTypes,
      petIds,
    },
    reviewBoosterAction,
  ) as OverviewActionItem[];

  const newOpList = useNoEvaluationApptMoreActionList(
    {
      appointmentId: Number(ticketId),
      serviceItemTypes,
      appointmentStatus,
      apptCustomerInfoIsDeleted: customerProfile?.deleted ?? false,
      waitListId: waitList.id,
      waitListMainStaffId: Number(serviceDetail?.[0]?.services?.[0]?.staffId ?? ID_ANONYMOUS),
      appointmentDate,
      customerId: Number(customerId),
      noShow: noShow ? GroomingTicketNoShowType.noShow : GroomingTicketNoShowType.noShowCancel,
      noShowStatus: noShowInvoice.status,
      noShowInvoiceId: Number(noShowInvoice.invoiceId),
      reviewBoosterSent: ctx.row.original.customer.reviewBoosterSent,
      showAdvancedEdit: false,
      showEditLodgingAssignment,
      invoiceId: Number(invoice.invoiceId),
      petIds,
    },
    reviewBoosterAction,
  );

  /**
   * invoice reinvent 的用户使用合并后的 action list，
   * 支持 grooming / bd appt 和 evaluation appt，未来全量后下掉老的 action list
   */
  const { isEnableToNewFlow } = useInvoiceReinvent();
  const optList = isEnableToNewFlow ? newOpList : isEvaluation ? evaluationOptList : apptOptList;

  const [buttonOpen, setButtonOpen] = useState(false);
  // button点击之后有些操作会拉起弹窗,流程是割裂的.所以监听弹窗关闭后,触发onChange事件通知刷新
  useEffect(() => {
    if (!buttonOpen) return;
    if (!cancelApptModalVisible) {
      onChange();
      setButtonOpen(false);
    }
  }, [cancelApptModalVisible, buttonOpen]);

  for (const row of optList) {
    const originalOnClick = row.onClick;
    row.onClick = async () => {
      await originalOnClick?.();

      // cancel会有一个二次确认弹窗
      if (row.id === 'cancel') {
        setButtonOpen(true);
      } else {
        onChange();
      }
    };
  }

  return optList;
}
