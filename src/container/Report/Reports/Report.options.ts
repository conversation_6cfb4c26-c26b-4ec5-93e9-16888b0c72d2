import dayjs, { type Dayjs } from 'dayjs';
import { createEnum } from '../../../store/utils/createEnum';

export const PresetDateRanges = createEnum({
  today: [1, 'Today'],
  thisWeek: [2, 'This week'],
  thisMonth: [3, 'This month'],
  thisYear: [4, 'This year'],

  yesterday: [5, 'Yesterday'],
  lastWeek: [6, 'Last week'],
  lastMonth: [7, 'Last month'],
  lastYear: [8, 'Last year'],

  tomorrow: [9, 'Tomorrow'],
  nextWeek: [10, 'Next week'],
  nextMonth: [11, 'Next month'],
  nextYear: [12, 'Next year'],

  customDate: [99, 'Custom date'],
});

const rangeDate: Record<number, () => [Dayjs, Dayjs]> = {
  [PresetDateRanges.today]: () => [dayjs(), dayjs()],
  [PresetDateRanges.thisWeek]: () => [dayjs().startOf('week'), dayjs().endOf('week')],
  [PresetDateRanges.thisMonth]: () => [dayjs().startOf('month'), dayjs().endOf('month')],
  [PresetDateRanges.thisYear]: () => [dayjs().startOf('year'), dayjs().endOf('year')],

  [PresetDateRanges.yesterday]: () => [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')],
  [PresetDateRanges.lastWeek]: () => [
    dayjs().startOf('week').subtract(1, 'week'),
    dayjs().startOf('week').subtract(1, 'day'),
  ],
  [PresetDateRanges.lastMonth]: () => [
    dayjs().startOf('month').subtract(1, 'month'),
    dayjs().startOf('month').subtract(1, 'day'),
  ],
  [PresetDateRanges.lastYear]: () => [
    dayjs().startOf('year').subtract(1, 'year'),
    dayjs().startOf('year').subtract(1, 'day'),
  ],

  [PresetDateRanges.tomorrow]: () => [dayjs().add(1, 'day'), dayjs().add(1, 'day')],
  [PresetDateRanges.nextWeek]: () => [dayjs().startOf('week').add(1, 'week'), dayjs().endOf('week').add(1, 'week')],
  [PresetDateRanges.nextMonth]: () => [
    dayjs().startOf('month').add(1, 'month'),
    dayjs().startOf('month').add(1, 'month').endOf('month'),
  ],
  [PresetDateRanges.nextYear]: () => [
    dayjs().startOf('year').add(1, 'year'),
    dayjs().startOf('year').add(1, 'year').endOf('year'),
  ],
};

export function getDate(range: number): [Dayjs, Dayjs] {
  return rangeDate[range]() ?? [dayjs(), dayjs()];
}

export function getRange(date: [Dayjs, Dayjs]): number {
  const result = Object.entries(rangeDate).find(([_, getDate]) => {
    const [startDate, endDate] = getDate();
    return startDate.isSame(date[0], 'day') && endDate.isSame(date[1], 'day');
  });
  return result ? +result[0] : PresetDateRanges.customDate;
}

export const DateRangeSectionList = [
  [PresetDateRanges.today, PresetDateRanges.thisWeek, PresetDateRanges.thisMonth, PresetDateRanges.thisYear],
  [PresetDateRanges.yesterday, PresetDateRanges.lastWeek, PresetDateRanges.lastMonth, PresetDateRanges.lastYear],
  [PresetDateRanges.customDate],
];
