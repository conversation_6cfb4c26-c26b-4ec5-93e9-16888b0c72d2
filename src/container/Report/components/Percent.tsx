import * as React from 'react';
import IconRatioDown from '../../../assets/icon/icon-ratio-down.svg';
import IconRatioUp from '../../../assets/icon/icon-ratio-up.svg';
import IconRatioZero from '../../../assets/icon/icon-ratio-zero.svg';
import { ImgIcon } from '../../../components/Icon/Icon';
import { Tooltip } from '../../../components/Popup/Tooltip';
import { RatioNumber } from './Percent.style';

export interface PercentProps {
  tip?: string;
  value?: {
    positive: boolean;
    percent: number;
  };
}

export function Percent(props: PercentProps): React.ReactElement {
  const { value, tip } = props;
  const { positive, percent } = value ?? {};
  const isZero = percent === 0;
  const renderChild = () =>
    value !== undefined ? (
      isZero ? (
        <RatioNumber zeroValue>
          0%
          <ImgIcon src={IconRatioZero} width={16} />
        </RatioNumber>
      ) : (
        <RatioNumber positive={positive}>
          {`${Math.abs(percent! * 100).toFixed(0)}%`}
          <ImgIcon src={positive ? IconRatioUp : IconRatioDown} width={16} />
        </RatioNumber>
      )
    ) : (
      <RatioNumber noValue>-%</RatioNumber>
    );

  return tip ? (
    <Tooltip overlay={tip} placement="bottom">
      {renderChild()}
    </Tooltip>
  ) : (
    renderChild()
  );
}
