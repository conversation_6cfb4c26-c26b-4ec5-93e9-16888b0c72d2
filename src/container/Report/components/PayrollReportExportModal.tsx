import { Radio, RadioGroup } from '@moego/ui';
import { useSetState } from 'ahooks';
import { useSelector } from 'amos';
import { Form } from 'antd';
import type dayjs from 'dayjs';
import React, { memo } from 'react';
import { ALL_LOCATIONS } from '../../../components/Business/SingleLocationSelector';
import { Button } from '../../../components/Button/Button';
import { Modal } from '../../../components/Modal/Modal';
import { toastApi } from '../../../components/Toast/Toast';
import { useIsMultiLocation } from '../../../components/WithFeature/useIsMultiLocation';
import { http } from '../../../middleware/api';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { selectStaffRole } from '../../../store/business/role.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { ExportType } from '../../ReportV2/Reports/Payroll/PayrollDownloadModal.types';
import { useBusinessQuotaInfo } from '../../settings/Settings/hooks/useBusinessQuotaInfo';

interface ReportModalProps {
  visible: boolean;
  onClose: () => void;
  date: [dayjs.Dayjs, dayjs.Dayjs];
  locationId: string;
  staffCount: number;
}

export const PayrollReportExportModal = memo(({ visible, onClose, date, staffCount, locationId }: ReportModalProps) => {
  const [business, role] = useSelector(selectCurrentBusiness, selectStaffRole());
  const { businessList } = useBusinessQuotaInfo();
  const showWorkingLocations = role?.onlyHas('viewReport');
  const [{ exportType }, setState] = useSetState({ exportType: ExportType.Combined });
  const isMultiLocation = useIsMultiLocation(showWorkingLocations ? 'working' : 'all');
  const handleSubmit = useSerialCallback(async () => {
    const res = await handleExportReport();
    if (res) {
      onClose();
    }
  });

  const handleExportReport = async () => {
    const exportedByStaff = exportType === ExportType.ByStaff;
    const reportDownloadUrl = await http.open('POST/business/report/payroll/export', {
      startDate: date[0].format(DATE_FORMAT_EXCHANGE),
      endDate: date[1].format(DATE_FORMAT_EXCHANGE),
      businessIds: locationId === ALL_LOCATIONS ? [] : [locationId],
      isAllLocation: locationId === ALL_LOCATIONS,
      exportedByStaff,
    });
    if (reportDownloadUrl) {
      // download file from url
      try {
        window.open(reportDownloadUrl, '_blank');
        toastApi.success('File download complete.');
      } catch (e) {
        console.error(e);
        toastApi.error('File download failed. Please try again.');
        return false;
      }
      return true;
    }
    toastApi.error('No payroll data available for export.');
    return false;
  };

  const businessName =
    locationId === ALL_LOCATIONS ? 'All businesses' : businessList.find((item) => item.id === locationId)?.name;

  return (
    <Modal
      width="480px"
      title="Export report data"
      visible={visible}
      onClose={onClose}
      footer={
        <Button
          size="sm"
          className="!moe-rounded-[20px]"
          btnType="primary"
          onClick={handleSubmit}
          loading={handleSubmit.isBusy()}
        >
          Export
        </Button>
      }
    >
      <Form requiredMark={false}>
        <Form.Item label="Staff count" className="!moe-mb-[14px]" name="fileName">
          <div className="!moe-leading-[29px] !moe-pt-[3px]">{staffCount}</div>
        </Form.Item>
        <div className="!moe-text-[#999] !moe-text-[12px] !moe-mt-[-20px] !moe-mb-[10px]">
          Staff with no payroll data available will be excluded from the export.
        </div>
        <Form.Item label="Time range" className="!moe-mb-[14px]">
          <div className="!moe-leading-[29px] !moe-pt-[3px]">
            {`${business.formatDate(date[0])} - ${business.formatDate(date[1])}`}
          </div>
        </Form.Item>
        {isMultiLocation && (
          <Form.Item label="Location" className="!moe-mb-[14px]">
            <div className="!moe-leading-[29px] !moe-pt-[3px]">{businessName}</div>
          </Form.Item>
        )}

        <Form.Item label="Export type">
          <RadioGroup
            className="moe-mt-[6px]"
            value={exportType}
            onChange={(exportType) => {
              setState({ exportType });
            }}
          >
            <Radio value={ExportType.Combined}>Combined export</Radio>
            <Radio value={ExportType.ByStaff}>Export by staff</Radio>
          </RadioGroup>
        </Form.Item>
      </Form>
    </Modal>
  );
});
