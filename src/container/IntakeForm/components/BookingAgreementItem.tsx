/*
 * @since 2021-01-14 16:43:45
 * <AUTHOR> <<EMAIL>>
 */

import { type Action, type Selector, useDispatch, useSelector } from 'amos';
import { Checkbox } from 'antd';
import React, { memo } from 'react';
import SvgIconPreviewSvg from '../../../assets/svg/icon-preview.svg';
import SvgIconWritePaperSvg from '../../../assets/svg/icon-write-paper.svg';
import { AgreementModal } from '../../../components/Agreement/AgreementModal';
import { SvgIcon } from '../../../components/Icon/Icon';
import { agreementMapBox } from '../../../store/agreement/agreement.boxes';
import { type IntakeFormAgreementRecord } from '../../../store/intakeForm/intakeForm.boxes';
import { useBool } from '../../../utils/hooks/useBool';
import { getSyncedIsShowAndIsRequired } from '../utils';
import { BookingQuestionDisplayView, BookingQuestionItemView } from './BookingQuestionItem.style';

export interface BookingAgreementItemProps {
  className?: string;
  id: number;
  selectIntakeFormAgreement: (id: number) => Selector<IntakeFormAgreementRecord>;
  updateIntakeFormAgreement: (id: number, isShow: boolean, isRequired: boolean) => Action;
}

export const BookingAgreementItem = memo<BookingAgreementItemProps>(
  ({ className, id, selectIntakeFormAgreement, updateIntakeFormAgreement }) => {
    const [intakeFormAgreement, agreement] = useSelector(
      selectIntakeFormAgreement(id),
      agreementMapBox.mustGetItem(id),
    );
    const dispatch = useDispatch();
    const showModal = useBool();
    return (
      <BookingQuestionItemView className={className}>
        <AgreementModal id={showModal.value ? id : void 0} mode="agreement" onClose={showModal.close} />
        <td>
          <div>
            <SvgIcon src={SvgIconWritePaperSvg} />
            <BookingQuestionDisplayView>
              <div className="title">{agreement.agreementHeader}</div>
            </BookingQuestionDisplayView>
          </div>
        </td>
        <td>
          <Checkbox
            checked={intakeFormAgreement.isShow > 0}
            onChange={(e) => {
              const { isShow, isRequired } = getSyncedIsShowAndIsRequired({
                isShow: e.target.checked,
                isRequired: intakeFormAgreement.isRequired > 0,
                baseOn: 'isShow',
              });
              dispatch(updateIntakeFormAgreement(id, isShow, isRequired));
            }}
          />
        </td>
        <td>
          <Checkbox
            checked={intakeFormAgreement.isRequired > 0}
            onChange={(e) => {
              const { isShow, isRequired } = getSyncedIsShowAndIsRequired({
                isShow: intakeFormAgreement.isShow > 0,
                isRequired: e.target.checked,
                baseOn: 'isRequired',
              });
              dispatch(updateIntakeFormAgreement(id, isShow, isRequired));
            }}
          />
        </td>
        <td>
          <SvgIcon src={SvgIconPreviewSvg} onClick={showModal.open} />
        </td>
      </BookingQuestionItemView>
    );
  },
);
