import { type Workflow, WorkflowStatus } from '@moego/api-web/moego/models/automation/v1/workflow';
import { type PaginationResponse } from '@moego/api-web/moego/utils/v2/pagination_messages';
import { Input, type PaginationCommonProps, Table } from '@moego/ui';
import { useBoolean } from 'ahooks';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router';
import { WorkflowClient } from '../../../../middleware/clients';
import {
  PATH_AUTOMATED_WORKFLOW_EDIT,
  PATH_AUTOMATED_WORKFLOW_RECORD,
  PATH_AUTOMATED_WORKFLOW_VIEW,
} from '../../../../router/paths';
import { useDebounceCallback } from '../../../../utils/hooks/useDebounceCallback';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { StatusFilter, type StatusItem } from '../../components/StatusFilter';

import { useInactiveWorkflowModal } from '../../components/InactivateWorkflowConfirmModal/useInactivateWorkflowModal';
import { toggleWorkflowEffectRangeConfirmModal } from '../../utils/toggleWorkflowEffectRangeConfirmModal';
import type { TabKey } from './WorkflowHomePage';
import { useColumnsForWorkflowList } from './useColumnsForWorkflowList';

interface ListTableProps {
  type: TabKey;
}

const statusList: StatusItem[] = [
  {
    label: 'Active',
    value: WorkflowStatus.ACTIVE,
  },
  {
    label: 'Inactive',
    value: WorkflowStatus.INACTIVE,
  },
  {
    label: 'Draft',
    value: WorkflowStatus.DRAFT,
  },
];
const PAGE_SIZE = 10;

export const ListTable = memo<ListTableProps>(function ListTable({ type }) {
  const [searchName, setSearchName] = useState<string>('');
  const [status, setStatus] = useState<WorkflowStatus[]>([]);
  const [isLoading, loadingAction] = useBoolean(false);
  const history = useHistory();
  const [list, setList] = useState<Workflow[]>([]);
  const [pagination, setPagination] = useState<PaginationResponse>({ pageNum: 1, pageSize: PAGE_SIZE, total: 0 });
  const inactivateWorkflow = useInactiveWorkflowModal();
  const activateWorkflow = async (workflow: Workflow) => {
    const isConfirmed = await toggleWorkflowEffectRangeConfirmModal({
      count: workflow?.consumerData?.effectClientNum ?? 0,
      maxMessageTokenCount: workflow?.consumerData?.costSmsTokenNum ?? 0,
    });
    if (!isConfirmed) return false;
    await WorkflowClient.updateWorkflowInfo({ workflowId: workflow.id, status: WorkflowStatus.ACTIVE });
    return true;
  };

  const fetchData = useMemo(() => {
    if (type === 'corporate') {
      return WorkflowClient.listEnterpriseWorkflows;
    }
    return WorkflowClient.listWorkflows;
  }, [type]);

  const getWorkflowList = useDebounceCallback(async (pageNum: number, pageSize: number) => {
    loadingAction.setTrue();
    const data = await fetchData({
      pagination: {
        pageNum,
        pageSize,
      },
      filter: {
        name: searchName,
        status: status.length === 0 ? [WorkflowStatus.ACTIVE, WorkflowStatus.INACTIVE, WorkflowStatus.DRAFT] : status,
      },
    });
    setList(data.workflows);
    data.pagination && setPagination(data.pagination);
    loadingAction.setFalse();
  }, 600);

  const goRecord = (id: string) => {
    history.push(PATH_AUTOMATED_WORKFLOW_RECORD.build({ id }));
  };
  const handleView = (workflow: Workflow) => {
    history.push(PATH_AUTOMATED_WORKFLOW_VIEW.build({ id: workflow.id }));
  };
  const handleEdit = (workflow: Workflow) => {
    history.push(PATH_AUTOMATED_WORKFLOW_EDIT.build({ id: workflow.id }));
  };
  const handleChangeStatus = async (workflow: Workflow, status: WorkflowStatus) => {
    const { id } = workflow;
    let shouldRefetchData = false;
    switch (status) {
      case WorkflowStatus.INACTIVE:
        shouldRefetchData = !!(await inactivateWorkflow(id));
        break;
      case WorkflowStatus.ACTIVE:
        shouldRefetchData = await activateWorkflow(workflow);
        break;
      default:
        await WorkflowClient.updateWorkflowInfo({ workflowId: id, status });
        shouldRefetchData = true;
        break;
    }
    if (shouldRefetchData) {
      getWorkflowList(1, pagination.pageSize);
    }
  };

  const tablePagination = useMemo<PaginationCommonProps>(
    () => ({
      pageIndex: pagination.pageNum,
      pageSize: pagination.pageSize,
      totalSize: pagination.total,
      renderTotal: () => `Total: ${pagination.total}`,
      minJumperPageCount: 1,
    }),
    [pagination.pageNum, pagination.pageSize, pagination.total],
  );

  const columns = useColumnsForWorkflowList({
    handleView,
    handleEdit,
    handleChangeStatus,
    type,
  });

  useEffect(() => {
    getWorkflowList(1, pagination.pageSize);
  }, [searchName, status, type]);

  return (
    <>
      <div className="moe-w-full moe-flex moe-h-[72px] moe-p-[16px] moe-bg-neutral-sunken-0 moe-rounded-spacing-s">
        <Input.Search
          value={searchName}
          onChange={setSearchName}
          className="moe-w-[300px] moe-mr-[16px]"
          placeholder="Search by flow name"
        />
        <StatusFilter
          statusList={statusList}
          statusValues={status}
          updateFilterData={(status) => {
            reportData(ReportActionName.statusClick);
            setStatus(status);
          }}
        />
      </div>
      <Table
        isLoading={isLoading}
        className="moe-mb-[30px]"
        columns={columns}
        onRowClick={(row) => {
          goRecord(row.id);
        }}
        getRowId={(row) => row.id + ''}
        onPaginationChange={({ pageIndex, pageSize }) => {
          setPagination({ pageNum: pageIndex, pageSize, total: 0 });
          if (pageIndex && pageSize) {
            getWorkflowList(pageIndex, pageSize);
          }
        }}
        pagination={tablePagination}
        data={list}
        stickyHeader={true}
      />
    </>
  );
});
