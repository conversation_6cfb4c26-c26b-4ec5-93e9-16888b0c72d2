import { useMemo } from 'react';
import { type WorkflowCanvasProps } from '@moego/workflow';
import { useSelector } from 'amos';
import { selectLeadActionStatusIdList } from '../../../../../store/leads/leadActionStatus.selectors';
import { leadActionStatusMapBox } from '../../../../../store/leads/leadActionStatus.boxes';
import { useQuery } from '../../../../../store/utils/useQuery';
import { getLeadActionStatusList } from '../../../../../store/leads/leadActionStatus.actions';

export function useGetLeadActionStatusList() {
  const [leadActionStatusIdList, leadActionStatusMap] = useSelector(
    selectLeadActionStatusIdList(),
    leadActionStatusMapBox,
  );
  useQuery(getLeadActionStatusList());
  return useMemo<Required<WorkflowCanvasProps>['leadActionStatusList']>(
    () =>
      leadActionStatusIdList.toArray().map((value) => ({
        label: leadActionStatusMap.mustGetItem(value)?.name,
        value: Number(value),
      })),
    [leadActionStatusIdList, leadActionStatusMap],
  );
}
