import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { But<PERSON>, Condition } from '@moego/ui';
import dayjs from 'dayjs';
import React, { memo, useRef } from 'react';
import { PetAndServicePicker } from '../../../../components/PetAndServicePicker/PetAndServicePicker';
import { getMainCareType } from '../../../../components/PetAndServicePicker/utils/getMainCareType';
import { ServiceTypeOptionsEnum } from '../../../../components/ServiceApplicablePicker/utils/options';
import { isNormal } from '../../../../store/utils/identifier';
import { type UpdateWaitListReq } from '../../../../store/waitList/actions/private/waitList.actions';
import { DATE_FORMAT_EXCHANGE } from '../../../../utils/DateTimeUtil';
import { optionalFunction } from '../../../../utils/utils';
import { matchApptServiceScene } from '../../../Appt/store/appt.options';
import { ApptServiceScene } from '../../../Appt/store/appt.types';
import { SelectService } from '../../../CreateWaitList/components/SelectService/SelectService';
import { type SelectServiceRef } from '../../../CreateWaitList/components/SelectService/SelectService.props';
import { type WaitDetailState } from '../../WaitDetail.props';

interface PetAndServiceProps extends WaitDetailState {
  clientId: number;
  onChange: (v: UpdateWaitListReq) => void;
  isStartSameTimeVisible?: boolean;
  serviceItemType?: ServiceItemType;
}

export const PetAndService = memo((props: PetAndServiceProps) => {
  const {
    petAndServices,
    allPetsStartAtSameTime,
    clientId,
    currentSlot,
    detail,
    onChange,
    isStartSameTimeVisible = true,
    serviceItemType,
  } = props;
  const currentPetIds = petAndServices.map((pet) => String(pet.petId));
  const selectServiceRef = useRef<SelectServiceRef>(null);

  const handleServicePicker = async (petId: number, isAdd = false) => {
    const serviceList = petAndServices.flatMap((p) => p.serviceList);

    const newVal = await selectServiceRef.current?.show({
      params: { disabledPetIds: currentPetIds.map(Number), defaultVisible: isAdd },
      clientId,
      petIds: isNormal(petId) ? [Number(petId)] : undefined,
      serviceList: petAndServices.find((p) => p.petId === Number(petId))?.serviceList,
      serviceItemType:
        serviceItemType ??
        (serviceList ? getMainCareType(serviceList.map((s) => s.serviceItemType).filter(isNormal)) : undefined),
      getTabs: (s) => {
        if (!s) return [];
        return matchApptServiceScene(ApptServiceScene.PickServiceSingle, {
          serviceItemType: s,
        })
          ? [
              {
                key: ServiceTypeOptionsEnum.Service,
                label: ServiceTypeOptionsEnum.mapLabels[ServiceTypeOptionsEnum.Service],
                isSingleSelect: true,
              },
            ]
          : undefined;
      },
    });
    if (!newVal) return;

    const newPetAndServices = [...petAndServices];
    const originIndex = newPetAndServices.findIndex((p) => p.petId === Number(petId));
    const nextPetsServiceList = newVal.petIds.map((petId) => ({ petId, serviceList: newVal.serviceList }));
    if (originIndex === -1) {
      newPetAndServices.push(...nextPetsServiceList);
    } else {
      newPetAndServices.splice(originIndex, 1, ...nextPetsServiceList);
    }

    onChange({
      petServices: newPetAndServices,
    });
  };

  const disabled = isNormal(detail?.appointmentId);

  return (
    <>
      <PetAndServicePicker
        disabled={disabled}
        getServiceListByPet={(petId) => petAndServices.find((item) => item.petId === Number(petId))?.serviceList || []}
        clientId={clientId}
        currentPetIds={currentPetIds}
        isStartSameTime={allPetsStartAtSameTime}
        isStartSameTimeVisible={currentPetIds.length > 1 && isStartSameTimeVisible}
        appointmentDate={currentSlot?.date || dayjs().format(DATE_FORMAT_EXCHANGE)}
        onStartSameTimeChange={(v) => {
          onChange({
            allPetsStartAtSameTime: v,
            petServices: petAndServices,
          });
        }}
        onAdd={(payload) => {
          handleServicePicker(Number(payload.petId));
        }}
        onEdit={(petId) => {
          handleServicePicker(Number(petId), false);
        }}
        onDelete={optionalFunction((petId) => {
          onChange({
            petServices: petAndServices.filter((item) => item.petId !== Number(petId)),
          });
        }, petAndServices.length > 1)}
      >
        {(petId) => {
          const pet = petAndServices.find((p) => p.petId === Number(petId));
          return (
            <Condition if={!disabled && !pet?.serviceList?.length}>
              <div>
                <Button
                  variant="tertiary"
                  onPress={() => {
                    handleServicePicker(Number(petId), false);
                  }}
                >
                  Add service
                </Button>
              </div>
            </Condition>
          );
        }}
      </PetAndServicePicker>
      <SelectService ref={selectServiceRef} />
    </>
  );
});
