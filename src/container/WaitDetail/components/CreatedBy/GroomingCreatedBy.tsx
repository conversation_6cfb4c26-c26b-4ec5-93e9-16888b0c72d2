import { Avatar, Text } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo } from 'react';
import { Condition } from '../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { staffMapBox } from '../../../../store/staff/staff.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { usePrintName } from '../../../CreateWaitList/hooks/useAutoName';

interface GroomingCreatedByProps {
  staffId: number;
  time: string;
}
export const GroomingCreatedBy = memo((props: GroomingCreatedByProps) => {
  const { staffId, time } = props;
  const [staffMap, business] = useSelector(staffMapBox, selectCurrentBusiness);
  const staffInfo = staffMap.mustGetItem(staffId);
  const { printStaffName } = usePrintName();

  return (
    <Condition if={isNormal(staffId) && time}>
      {() => (
        <Text
          variant="caption"
          as="div"
          className="moe-flex moe-items-center moe-justify-center moe-gap-[4px] moe-border-divide moe-text-tertiary"
        >
          <span>Created by</span>
          <Avatar.Staff
            size="xs"
            src={staffInfo.avatarPath}
            color={staffInfo.colorCode}
            name={staffInfo.fullName()}
            isStroked={false}
          />
          <span>{printStaffName(staffId, staffInfo.fullName())}</span>
          <span>on {business.formatDateTime(dayjs(time))}</span>
        </Text>
      )}
    </Condition>
  );
});
