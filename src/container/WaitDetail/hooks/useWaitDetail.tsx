import React from 'react';
import { useDrawer } from '../../../utils/Drawer';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import { WaitDetail } from '../WaitDetail';
import { type WaitDetailProps } from '../WaitDetail.props';

export const useWaitDetail = () => {
  const { zIndex } = useDrawer('waitList');
  const { mountDrawer } = useFloatableHost();

  return (props: WaitDetailProps) => {
    const { promise, closeFloatable: closeModal } = mountDrawer(() => {
      return <WaitDetail {...props} zIndex={zIndex} onClose={() => closeModal()} />;
    });

    return promise;
  };
};
