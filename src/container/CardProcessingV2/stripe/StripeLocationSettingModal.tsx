/*
 * @Author: <PERSON>
 * @Date: 2022-06-20 10:32:53
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2022-06-29 11:07:33
 */

import { useDispatch, useSelector } from 'amos';
import { Form, Input } from 'antd';
import React, { memo, useEffect, useMemo } from 'react';
import { useAddressState } from '../../../components/AddressForm/AddressForm';
import { AddressInput } from '../../../components/AddressForm/AddressInput';
import { Button } from '../../../components/Button/Button';
import { type ModalRequiredProps } from '../../../components/Modal/Modal';
import { FormFooter } from '../../../components/Style/Style';
import { toastApi } from '../../../components/Toast/Toast';
import { getBusinessDetail } from '../../../store/business/business.actions';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { loadCountryDataList } from '../../../store/common/common.actions';
import { countryListBox } from '../../../store/common/common.boxes';
import {
  addStripeTerminalLocation,
  getStripeTerminalLocationList,
  omitEmptyString,
  updateStripeTerminalLocation,
} from '../../../store/stripe/stripeTerminal.actions';
import { type StripeLocationAddress } from '../../../store/stripe/stripeTerminal.boxes';
import { selectStripeTerminalLocation } from '../../../store/stripe/stripeTerminal.selectors';
import { formInput } from '../../../utils/form';
import { getAlpha2 } from '../../../utils/geo';
import { useFormRef } from '../../../utils/hooks/hooks';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { StripeLocationSettingModalView } from './StripeLocationSettingModal.style';

export interface StripeLocationSettingModalProps extends ModalRequiredProps {
  className?: string;
}

const locationFormInput = formInput<StripeLocationAddress>().copy(
  'country',
  'line1',
  'line2',
  'city',
  'state',
  'postal_code',
);

export const StripeLocationSettingModal = memo<StripeLocationSettingModalProps>(({ className, visible, onClose }) => {
  const form = useFormRef();
  const [geo, handleAddressSelect] = useAddressState(form);
  const dispatch = useDispatch();

  const [location, business, countryList] = useSelector(
    selectStripeTerminalLocation,
    selectCurrentBusiness,
    countryListBox,
  );

  const fetchData = useSerialCallback(async () => {
    await Promise.all([
      dispatch(getStripeTerminalLocationList()),
      dispatch(getBusinessDetail()),
      dispatch(loadCountryDataList()),
    ]);
  });

  useEffect(() => {
    if (visible) {
      fetchData();
    }
  }, [visible]);

  const countryAlpha2 = useMemo(() => {
    return getAlpha2(countryList, business.country);
  }, [countryList, business.country]);

  const initialLocationInputValue = useMemo(() => {
    return location.id
      ? location.address
      : {
          country: countryAlpha2,
          line1: business.address1,
          line2: business.address2,
          city: business.addressCity,
          state: business.addressState,
          postal_code: business.addressZipcode,
        };
  }, [location, business, countryAlpha2]);

  useEffect(() => {
    locationFormInput.attach(form, initialLocationInputValue);
  }, [initialLocationInputValue]);

  // 处理 useAddressState 数据映射 zipcode => postal_code
  useEffect(() => {
    if (!geo.current?.country) return;
    const countryToAlpha2 = getAlpha2(countryList, geo.current.country);
    locationFormInput.attach(form, {
      ...initialLocationInputValue,
      ...geo.current,
      line1: geo.current?.address?.additional?.mainText || '',
      country: countryToAlpha2 || geo.current.country,
      postal_code: geo?.current.zipcode,
    });
  }, [geo.current]);

  const onAddressSelect = useSerialCallback(async (data) => {
    await handleAddressSelect(data);
  });

  const handleSubmit = useSerialCallback(async () => {
    const values = await locationFormInput.validate(form);

    if (!values) return;

    if (values.country !== initialLocationInputValue.country) {
      toastApi.error('Country cannot be changed');
      return;
    }

    if (location.id) {
      await dispatch(
        updateStripeTerminalLocation({
          locationId: location.id,
          displayName: location.displayName,
          postalCode: values.postal_code,
          ...omitEmptyString(values),
        }),
      );
    } else {
      await dispatch(
        addStripeTerminalLocation({
          displayName: business.businessName,
          postalCode: values.postal_code,
          ...omitEmptyString(values),
        }),
      );
    }

    onClose?.();
  });

  return (
    <StripeLocationSettingModalView
      width="600px"
      className={className}
      visible={visible}
      onClose={onClose}
      title="Reader registration address"
      loading={fetchData.isBusy()}
    >
      <Form
        ref={form}
        initialValues={initialLocationInputValue}
        validateMessages={{
          required: 'Please input the "${label}"',
        }}
      >
        <Form.Item label="Address1" name="line1" rules={[{ required: true }]}>
          <AddressInput placeholder="Address 1" onSelectAddress={onAddressSelect} />
        </Form.Item>

        <Form.Item label="Address2" name="line2">
          <Input maxLength={50} size="large" placeholder="Address2" />
        </Form.Item>

        <Form.Item label="City" name="city" rules={[{ required: true }]}>
          <Input maxLength={50} size="large" placeholder="City" />
        </Form.Item>

        <Form.Item label="State" name="state" rules={[{ required: true }]}>
          <Input maxLength={50} size="large" placeholder="State" />
        </Form.Item>

        <Form.Item label="Zipcode" name="postal_code" rules={[{ required: true }]}>
          <Input maxLength={50} size="large" placeholder="Zipcode" />
        </Form.Item>

        <Form.Item label="Country" name="country" rules={[{ required: true }]}>
          <Input disabled maxLength={50} size="large" placeholder="Country" />
        </Form.Item>

        <FormFooter>
          <Button onClick={handleSubmit} loading={handleSubmit.isBusy()} btnType="primary" buttonRadius="circle">
            Save
          </Button>
        </FormFooter>
      </Form>
    </StripeLocationSettingModalView>
  );
});
