import styled from 'styled-components';
import { Modal } from '../../../components/Modal/Modal';

export const ProcessingFeeSettingModalView = styled(Modal)`
  .modal-container {
    padding: 20px 32px 24px;
  }

  .setting-radio {
    display: flex;
    justify-content: center;
    margin-top: 6px;

    > label:first-child {
      margin-right: 20px;
    }

    .ant-radio-disabled + span {
      color: #333;
    }

    .ant-radio-checked.ant-radio-disabled {
      .ant-radio-inner {
        border-color: #ffa46b !important;
        background-color: #fff;

        &::after {
          content: '';
          background-color: #ffa46b;
        }
      }
    }
  }

  .fee-name {
    .ant-form-item-label {
      display: flex;
      align-items: flex-start;
      flex-direction: flex-start;
      padding-top: 4px;
    }
    .ant-col.ant-form-item-control {
      overflow: visible;
    }
    .fee-name-input {
      height: 38px;
      padding: 9px 16px;
      border-radius: 4px;
      background: #ffffff;
      /* border: 1px solid #dee1e6; */
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: #333;
    }
  }

  .fee-input-title {
    font-family: 'Nunito';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
  }

  .fee-setting-footer {
    text-align: right;
    .confirm-btn {
      padding: 6px 32px;
      width: 96px;
      height: 32px;
    }
  }

  .disable-tips {
    color: #999;

    > a {
      color: #f96b18;
      text-decoration: underline;
    }
  }

  .tips-placeholder {
    height: 44px;
  }

  /* note: xiaoli 说干掉 antd input 的阴影 */
  .ant-form-item-has-error .ant-input-affix-wrapper:focus,
  .ant-input-affix-wrapper-focused,
  .ant-input-affix-wrapper:focus,
  .ant-input:focus {
    box-shadow: none;
  }
`;

export const SignedLink = styled.a`
  cursor: pointer;
  color: #f96b18;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  text-decoration: underline;

  &:hover {
    color: #f96b18;
    text-decoration: underline;
  }
`;

export const RateSettingWrapDiv = styled.div`
  /* 与 form item 的 margin-bottom: 24px 抵消 */
  margin-top: -12px;
  padding: 20px 20px 4px;
  border-radius: 8px;
  color: #666;
  background: #f7f8fa;

  .rate-type {
    margin-bottom: 24px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .input-wrap {
    /* align with the fields above */
    margin-left: -7px;
    width: 286px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .ant-col.ant-form-item-control {
      overflow: visible;
    }

    .ant-form-item-explain {
      overflow: visible;

      > div {
        white-space: nowrap;
      }
    }

    .ant-input-affix-wrapper {
      border-radius: 4px;
      height: 38px;
      padding: 9px 16px;
    }

    .rate-input {
      width: 104px;
    }

    .mid-plus {
      display: flex;
      align-items: center;
      height: 38px;
      margin-bottom: 24px;
    }

    .cents-input {
      width: 125px;
      height: 38px;
    }

    .ant-input,
    .ant-input-suffix {
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: #333;
    }
  }
`;
