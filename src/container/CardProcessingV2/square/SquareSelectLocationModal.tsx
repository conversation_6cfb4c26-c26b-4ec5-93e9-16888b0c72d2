/*
 * @since 2021-06-21 10:58:23
 * <AUTHOR> <<EMAIL>>
 */

import { useAccount } from '@moego/finance-web-kit';
import { useDispatch, useSelector } from 'amos';
import { Form, Select } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import ImageSquareLogoPng from '../../../assets/image/square-logo.png';
import { Button } from '../../../components/Button/Button';
import { type ModalRequiredProps, modalApi } from '../../../components/Modal/Modal';
import { toastApi } from '../../../components/Toast/Toast';
import { FinanceKit } from '../../../service/finance-kit';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { setSquareDefaultLocation } from '../../../store/square/actions/private/square.actions';
import { squareLocationMapBox } from '../../../store/square/square.boxes';
import { selectBusinessSquareLocationList, selectSquareAccount } from '../../../store/square/square.selectors';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { SquareSelectLocationModalView } from './SquareSelectLocationModal.style';

export interface SquareSelectLocationModalProps extends ModalRequiredProps {
  className?: string;
  loading: boolean;
  mode: 'change' | 'connect';
}

export const SquareSelectLocationModal = memo<SquareSelectLocationModalProps>(
  ({ className, visible, onClose, loading, mode }) => {
    const dispatch = useDispatch();
    const [square, locationMap, locationList, businessId] = useSelector(
      selectSquareAccount(),
      squareLocationMapBox,
      selectBusinessSquareLocationList(),
      currentBusinessIdBox,
    );

    const { value: account } = useAccount(FinanceKit, {
      businessId: `${businessId}`,
    });
    const [selected, setSelected] = useState(square.defaultLocationId ?? void 0);

    useEffect(() => {
      if (square.defaultLocationId) {
        setSelected(square.defaultLocationId);
      }
    }, [square.defaultLocationId]);

    const handleConfirm = useSerialCallback(async () => {
      if (selected === square.defaultLocationId && !__DEV__) {
        onClose();
        return;
      }

      async function confirm() {
        await dispatch(setSquareDefaultLocation(selected, businessId, account?.isAvailable));
        toastApi.success('The location is linked successfully.');
        onClose();
      }

      if (selected) {
        if (mode === 'change') {
          modalApi.confirm({
            title: 'Are you sure to change the location?',
            width: '600px',
            content: (
              <ol>
                <li>You have to re-register Square Terminals.</li>
                <li>You have to re-authorize Square Reader SDK in MoeGo mobile app.</li>
              </ol>
            ),
            onOk: confirm,
            okText: 'Yes, change the location',
          });
        } else {
          await confirm();
        }
      }
    });

    return (
      <SquareSelectLocationModalView
        width="600px"
        className={className}
        title="Square integration"
        onClose={onClose}
        visible={visible}
        loading={loading}
      >
        <div className="logo">
          <img src={ImageSquareLogoPng} />
        </div>
        <div className="desc main">
          <div className="text">
            {mode === 'connect'
              ? 'Complete the authorization set up'
              : 'Start taking payments through MoeGo’s trusted payment partner Square.'}
          </div>
          <div className="text f-14-gray" style={{ display: 'block' }}>
            You can manage your locations in{' '}
            <a target="_blank" href="https://squareup.com/dashboard/locations" rel="noreferrer">
              Square dashboard
            </a>
            .
          </div>
        </div>
        <Form.Item label="Location">
          <Select value={selected} onChange={(v) => setSelected(v)} size="large">
            {locationList.map((id) => (
              <Select.Option value={id} key={id}>
                {locationMap.mustGetItem(id).name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <div className="actions">
          <Button
            onClick={handleConfirm}
            btnType="primary"
            buttonRadius="circle"
            loading={handleConfirm.isBusy()}
            disabled={!selected}
          >
            Confirm
          </Button>
        </div>
      </SquareSelectLocationModalView>
    );
  },
);
