import { Form, Input } from 'antd';
import styled, { css } from 'styled-components';
import { AddressInput } from '../../../../../../components/AddressForm/AddressInput';
import { Button } from '../../../../../../components/Button/Button';
import { Modal } from '../../../../../../components/Modal/Modal';
import { c_danger, c_layout_break_point2 } from '../../../../../../style/_variables';

export const ModalA2PRegistrationView = styled(Modal)`
  color: #333;
  .modal {
    display: flex;
    flex-direction: column;
  }
  .modal-header {
    padding: 16px 32px;
  }
  .modal-container {
    flex: 1;
    padding: 18px 32px 24px 32px;
  }
  .a2p-checkbox {
    display: flex;
    font-size: 12px;
    font-weight: 500;
    color: #666;
    white-space: normal;
    .ant-checkbox {
      top: 1px;
      &::after {
        display: none;
      }
    }
    .ant-checkbox-inner {
      border-radius: 4px;
    }
  }
  @media (max-width: ${c_layout_break_point2}px) {
    .modal {
      margin-top: 28px;
    }
    .modal-container {
      padding: 20px 20px 24px;
    }
  }
`;

export const FormItemTitle = styled.div`
  width: 100%;
  margin-bottom: 20px;
  font-weight: 700;
  font-size: 14px;
  line-height: 20px;
  color: #2a2d34;
  @media (max-width: ${c_layout_break_point2}px) {
    margin-bottom: 16px;
  }
`;

export const FormItem = styled(Form.Item)`
  margin: 0 0 20px 0 !important;
  &.ant-form-item-has-error {
    .ant-input {
      border-color: ${c_danger};
    }
    .ant-form-item-explain-error {
      color: ${c_danger};
    }
  }
  .ant-form-item-control {
    display: flex !important;
    justify-content: center;
  }
  .ant-form-item-label label {
    height: 30px;
    min-width: 126px;
    color: #666;
  }
  @media (max-width: ${c_layout_break_point2}px) {
    margin-bottom: 16px !important;
  }
`;

export const UploadIntro = styled.div<{ size?: number; required?: boolean }>`
  font-size: ${({ size = 14 }) => `${size}px`};
  font-weight: 500;
  color: #999;
  ${(props) =>
    props.required
      ? css`
          color: #666;
          &::before {
            display: inline-block;
            margin-right: 4px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: '*';
          }
        `
      : ''};
`;

export const FormInput = styled(Input)`
  height: 30px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #dee1e6;
  border-radius: 4px;
  min-width: 300px;
  box-shadow: none !important;
  &::placeholder {
    color: #999;
    font-weight: 400;
    font-size: 14px;
  }
  input {
    font-size: 14px;
    font-weight: 500;
  }
  @media (max-width: ${c_layout_break_point2}px) {
    min-width: 100%;
  }
`;

export const StyledAddressInput = styled(AddressInput)`
  width: 520px !important;
  input {
    color: #333;
    font-size: 14px;
    font-weight: 500;
  }
  .ant-select-selection-placeholder {
    color: #999;
    font-weight: 400;
    font-size: 14px;
  }
`;

export const RegisterButtonWrap = styled.div`
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  @media (max-width: ${c_layout_break_point2}px) {
    padding-top: 4px;
  }
`;

export const RegisterButton = styled(Button)`
  padding: 6px 20px;
  @media (max-width: ${c_layout_break_point2}px) {
    width: 100%;
    float: none;
  }
`;

export const GreyMediumText = styled.div`
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #666;
`;

export const RegistrationFeeView = styled.div``;

export const FeeTitle = styled.div`
  font-weight: 700;
  font-size: 14px;
  color: #333;
`;

export const FeeContent = styled(GreyMediumText)`
  margin-top: 4px;
`;

export const Link = styled.span<{ color?: string; size?: number }>`
  font-weight: 500;
  font-size: ${({ size = 12 }) => `${size}px`};
  color: ${({ color = '#333' }) => color};
  cursor: pointer;
  text-decoration-line: underline;
`;
