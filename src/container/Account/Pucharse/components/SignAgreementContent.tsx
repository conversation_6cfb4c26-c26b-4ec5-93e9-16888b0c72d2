import { useSelector } from 'amos';
import React, { memo } from 'react';
import { HtmlContent } from '../../../../components/HtmlContent';
import { selectSubscriptionForm } from '../../../../store/company/subscription.selectors';
import { CareContractComposer } from '../../../RichTextTemplate/CareContract/CareContractComposer';
import { SalesContractComposer } from '../../../RichTextTemplate/SalesContract/SalesContractComposer';
import { usePurchaseBasicConfig } from '../hooks/usePurchaseBasicConfig';
import { basicConfigUtils } from '../utils';

export interface SignAgreementContentProps {}

export const SignAgreementContent = memo<SignAgreementContentProps>(() => {
  const basicConfig = usePurchaseBasicConfig();
  const [{ agreementInfo }] = useSelector(selectSubscriptionForm);

  if (basicConfigUtils.isCareProgram(basicConfig)) {
    return (
      <CareContractComposer>
        <HtmlContent content={agreementInfo.agreementContent || ''} options={{ allowedAttrs: ['class'] }} />
      </CareContractComposer>
    );
  } else if (basicConfigUtils.isSalesProgram(basicConfig)) {
    return (
      <SalesContractComposer>
        <HtmlContent content={agreementInfo.agreementContent || ''} options={{ allowedAttrs: ['class'] }} />
      </SalesContractComposer>
    );
  }

  return null;
});
