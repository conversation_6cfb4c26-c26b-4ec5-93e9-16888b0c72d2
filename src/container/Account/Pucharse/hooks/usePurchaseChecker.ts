import { useSelector, useStore } from 'amos';
import { useEffect } from 'react';
import { useHistory } from 'react-router';
import { PATH_ACCOUNT, PATH_BUNDLE_SALE, PATH_GROOMING_CALENDAR, PATH_PROFILE } from '../../../../router/paths';
import { selectCurrentAccount } from '../../../../store/account/account.selectors';
import { isHardwareAvailableCountry } from '../../../../store/care/care.boxes';
import { selectCompany } from '../../../../store/company/company.selectors';
import { selectSubscriptionBasicConfig } from '../../../../store/company/subscription.selectors';
import { selectCurrentStaff } from '../../../../store/staff/staff.selectors';
import { useBool } from '../../../../utils/hooks/useBool';
import { checkBasicCodeMatched } from '../../../Sales/BasicPlanSetup/utils';
import { basicConfigUtils } from '../utils';

/**
 * 用于检测是否有不符合规范的情况进入，如果是的话，应该要跳转到其他页面或者 toast 提示用户。
 * @param canCheck 表示是否可以开始 check
 */
export const usePurchaseChecker = (canCheck: boolean) => {
  const isSuccess = useBool();
  const store = useStore();
  const [basicConfig] = useSelector(selectSubscriptionBasicConfig);
  const history = useHistory();

  useEffect(() => {
    const basicConfig = store.select(selectSubscriptionBasicConfig);
    if (!canCheck || !basicConfig) return;

    const company = store.select(selectCompany(basicConfig.companyId));
    const account = store.select(selectCurrentAccount());
    const staff = store.select(selectCurrentStaff());

    // 容错，避免 charge failed 的用户进入 purchase 页面。
    if (company.isChargeFailedPlan) {
      history.push(staff.isOwner() ? PATH_ACCOUNT.build() : PATH_PROFILE.build());
      return;
    }

    if (basicConfigUtils.isCareProgram(basicConfig)) {
      if (company.companyBillingData.isSubscription) {
        history.push(PATH_GROOMING_CALENDAR.build());
        return;
      }
    }

    if (basicConfigUtils.isBundleSale(basicConfig)) {
      if (!isHardwareAvailableCountry(company.country)) {
        history.push(PATH_BUNDLE_SALE.queried({}));
        return;
      }
    }

    if (basicConfigUtils.isBasicPlanProgram(basicConfig)) {
      const basicCodeMatched = checkBasicCodeMatched({ email: account?.email, code: basicConfig.basicCode });
      if (!basicCodeMatched.isMatched) {
        history.push(PATH_GROOMING_CALENDAR.build());
        return;
      }
    }

    isSuccess.open();
  }, [canCheck, !!basicConfig]);

  return {
    isSuccess: isSuccess.value,
  };
};
