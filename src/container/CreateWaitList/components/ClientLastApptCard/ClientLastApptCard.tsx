import { useSelector, useStore } from 'amos';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { groupBy, uniq } from 'lodash';
import React, { memo, useMemo } from 'react';
import { Avatar } from '../../../../components/Avatar/Avatar';
import { Button } from '../../../../components/Button/Button';
import { useClientPets } from '../../../../components/PetInfo/hooks/useClientPets';
import { useImperativeServicePriceDurationInfo } from '../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { getDefaultService } from '../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type QuickAddPetService } from '../../../../store/calendarLatest/calendar.types';
import { type CustomerWithPetForMessageDto } from '../../../../store/customer/customer.actions';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import { selectAllActiveServiceIdList } from '../../../../store/service/service.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { getPetAvatarType } from '../../../../utils/BusinessUtil';
import { DATE_FORMAT_EXCHANGE } from '../../../../utils/DateTimeUtil';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { ApptServiceActiveInfo } from '../../../Appt/modules/QuickAddAppt/components/ApptServiceActiveInfo';
import { PetName } from '../../../Appt/modules/QuickAddAppt/components/PetName';

export interface ClientLastApptCardProps {
  clientId: number;
  lastAppointment: CustomerWithPetForMessageDto['lastAppointment'];
  className?: string;
  staffId?: number;
  onSelect: (petServices: QuickAddPetService[]) => void;
}

export const ClientLastApptCard = memo<ClientLastApptCardProps>(function ClientLastAppt(props) {
  const { className, staffId, clientId, lastAppointment, onSelect } = props;
  const store = useStore();
  const getSavedServiceInfo = useImperativeServicePriceDurationInfo();

  const [activeServiceIds, business, petMap] = useSelector(
    selectAllActiveServiceIdList(),
    selectCurrentBusiness,
    petMapBox,
  );
  const allAlivePets = useClientPets(clientId);
  const { allPetIds, petServiceMap, petServices, petList } = useMemo(() => {
    const { appointmentDate, appointmentStartTime, appointmentEndTime, petDetails, id: lastApptId } = lastAppointment;
    const date = dayjs(appointmentDate, DATE_FORMAT_EXCHANGE);
    const start = dayjs().startOf('date').add(appointmentStartTime, 'minute');
    const startFmt = business.formatTime(start);
    const end = dayjs().startOf('date').add(appointmentEndTime, 'minute');
    const endFmt = business.formatTime(end);
    const dateFmt = business.formatDate(date);
    const petList = Array.isArray(petDetails) ? petDetails : [];
    const allPetIds = uniq(petList.map((pet) => pet.petId));
    // 这里能保证都是 alive 的 pet
    const petAliveAndUndeletedIds = allPetIds.filter(
      (petId) => allAlivePets.includes(petId) && store.select(petMapBox.mustGetItem(petId)).isLive(),
    );
    const petServiceMap = groupBy(petList, (pet) => pet.petId);
    const petServices = petAliveAndUndeletedIds
      .map((petId) => {
        const serviceList: QuickAddPetService['serviceList'] = petServiceMap[petId].map(getDefaultService);
        const petService: QuickAddPetService = {
          petId,
          serviceList,
        };
        return petService;
      })
      .filter((pet) => pet.serviceList.length > 0);
    return {
      lastApptId,
      dateTimeFmt: `${dateFmt} ${startFmt} - ${endFmt}`,
      /** last appt 里 alive 的 pet ids  */
      petAliveIds: petAliveAndUndeletedIds,
      allPetIds,
      petList,
      petServiceMap,
      petServices,
    };
  }, [lastAppointment]);

  const getNextPetService = useLatestCallback((list: QuickAddPetService[]) => {
    return list
      .map((pet) => {
        const { petId, serviceList } = pet;
        const nextServiceList = serviceList
          .filter((item) => activeServiceIds.includes(item.serviceId))
          .map((service) => {
            const { serviceId } = service;
            const savedInfo = getSavedServiceInfo({ petId, serviceId });
            const nextService = getDefaultService({
              ...service,
              ...savedInfo,
              staffId: isNormal(staffId) ? staffId : service.staffId,
            });
            return nextService;
          });
        return {
          petId,
          serviceList: nextServiceList,
        };
      })
      .filter((pet) => pet.serviceList.length > 0);
  });

  /**
   * auto fill 的时候使用的 service list
   * 已经处理了 inactive 的 service，这里留下的 pet & service list 都是可以用的
   */
  const nextPetServices = useMemo(
    () => getNextPetService(petServices),
    [petServices, activeServiceIds, getSavedServiceInfo, getDefaultService, staffId],
  );

  const nextServiceListIsAllInactive = nextPetServices.length === 0;

  // book again流程
  // 需要满足pet alive & active services
  const onBookAgain = useLatestCallback(() => {
    if (nextServiceListIsAllInactive) {
      return;
    }
    onSelect(getNextPetService(nextPetServices));
  });

  return (
    <div
      className={classNames(
        'moe-rounded-[8px] moe-overflow-hidden moe-flex-shrink-0 moe-p-[16px] moe-flex moe-flex-col moe-gap-y-[16px]',
        className,
      )}
    >
      <div className="moe-text-[#333] moe-text-base moe-font-bold">Last appointment</div>
      {allPetIds.map((petId) => {
        const pet = petMap.mustGetItem(petId);
        const maybeLegacyPet = petList.find((p) => p.petId === petId);
        const services = petServiceMap[petId];
        return (
          <div key={petId} className="moe-flex moe-items-start moe-gap-x-[8px]">
            <Avatar size="36px" src={pet.avatarPath} type={getPetAvatarType(pet.petTypeId)} />
            <div className="moe-flex-1 moe-min-w-0">
              <PetName petId={petId} fallbackPetName={maybeLegacyPet?.petName} />
              {services.map((service) => {
                const { serviceId } = service;
                return (
                  <div key={serviceId} className="moe-text-[14px] moe-leading-[18px] moe-font-medium moe-text-[#999]">
                    {service.serviceName}
                    <ApptServiceActiveInfo
                      serviceId={serviceId}
                      isDeleted={service.serviceDeleted}
                      isInactive={service.serviceInactive}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}
      <div className="moe-flex moe-items-center moe-justify-end moe-gap-x-[16px]">
        <Button
          btnType="white-border"
          buttonRadius="circle"
          className={classNames(
            '!moe-text-[16px] !moe-font-bold !moe-leading-[20px] !moe-py-[5px]',
            nextServiceListIsAllInactive && '!moe-text-[#CCC] !moe-border-[#DEE1E5]',
          )}
          onClick={onBookAgain}
          disabled={nextServiceListIsAllInactive}
        >
          Book again
        </Button>
      </div>
    </div>
  );
});
