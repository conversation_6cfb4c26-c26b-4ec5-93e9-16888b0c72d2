import { Form, type FormItemLabelProps } from '@moego/ui';
import React, { type PropsWithChildren, memo } from 'react';

interface FormLabelProps extends FormItemLabelProps {}

export const FormLabel = memo(function FormLabel(props: PropsWithChildren<FormLabelProps>) {
  const { children, ...rest } = props;

  return (
    <div className={rest.className}>
      <Form.Label {...rest}></Form.Label>
      {children}
    </div>
  );
});
