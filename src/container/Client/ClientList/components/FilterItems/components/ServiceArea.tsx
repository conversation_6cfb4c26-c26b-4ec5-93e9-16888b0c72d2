import { useDispatch, useSelector } from 'amos';
import React, { memo, useCallback, useMemo } from 'react';
import { ServiceAreaItem } from '../../../../../../components/ServiceArea/ServiceAreaItem';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { selectMobileBusinessId } from '../../../../../../store/business/location.selectors';
import { getBusinessServiceArea } from '../../../../../../store/serviceArea/serviceArea.actions';
import { serviceAreaMapBox } from '../../../../../../store/serviceArea/serviceArea.boxes';
import { selectBusinessServiceAreaIdList } from '../../../../../../store/serviceArea/serviceArea.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useBizIdReadyEffect } from '../../../../../../utils/hooks/useBizIdReadyEffect';
import { useMultiSelectOptions } from '../../hooks/useMultiSelectOptions';
import { type SpecificFilterProps } from '../FilterProperty.options';
import { MultiTagsSelect } from './common/MultiTagsSelect';

export const ServiceArea = memo<SpecificFilterProps>(({ type, property }) => {
  const dispatch = useDispatch();
  const [tagMap, business, mobileBusinessId] = useSelector(
    serviceAreaMapBox,
    selectCurrentBusiness,
    selectMobileBusinessId(),
  );
  const [tagList] = useSelector(selectBusinessServiceAreaIdList(mobileBusinessId));
  const selectOptions = useMultiSelectOptions(tagList, tagMap, 'areaName');
  const selectOptionsWithCustom = useMemo(
    () =>
      selectOptions.map((item) => {
        const areaInfo = tagMap.mustGetItem(+item.value);
        return {
          ...item,
          label: item.label,
          colorCode: areaInfo.colorCode,
        };
      }),
    [selectOptions],
  );

  useBizIdReadyEffect(() => {
    if (isNormal(mobileBusinessId)) {
      dispatch(getBusinessServiceArea(mobileBusinessId));
    }
  }, [mobileBusinessId, business.id]);

  const formatOptionTitle = useCallback((option: unknown) => {
    const _option = option as { label?: string; colorCode: string };
    return (
      <ServiceAreaItem
        className="moe-truncate moe-min-h-0"
        colorCode={_option.colorCode}
        areaName={_option?.label || ''}
      />
    );
  }, []);

  // AS 迁移前，如果不是 mobile grooming 商家就不展示
  // AS 迁移后，如果 company 下没有 mobile biz id，就不用展示了
  if (!isNormal(mobileBusinessId)) {
    // 如果不是mobile 商家的话，没有这个filter
    return null;
  }

  return (
    <MultiTagsSelect
      type={type}
      property={property}
      selectOptions={selectOptionsWithCustom}
      formatOptionTitle={formatOptionTitle}
      formatOptionLabel={formatOptionTitle}
    />
  );
});
