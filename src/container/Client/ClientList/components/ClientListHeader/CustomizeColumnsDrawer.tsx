/**
 * @since 2024-01-24
 * <AUTHOR>
 * @description customize columns of client table
 */
import { Checkbox, Drawer, type DrawerProps, Text, cn } from '@moego/ui';
import React, { memo, useEffect, useState } from 'react';
import { Condition } from '../../../../../components/Condition';
import { type FieldName } from '../../../../../components/SmartClientList/SmartClientListTable';
import { SortableList } from '../../../../../components/SortableList/SortableList';
import { toastApi } from '../../../../../components/Toast/Toast';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { toggleItem } from '../../../../../utils/utils';
import {
  CLIENT_TABLE_CUSTOMIZE_CONFIG,
  type FieldItem,
  prePositionList,
  sortSelectedList,
} from '../ClientTable/Columns.utils';
import { useCustomizeSortableFields } from '../ClientTable/useCustomizeFields';
import { useClientViewContext } from '../hooks/useClientViewContext';
import { useViewActions } from '../hooks/useViewActions';

const ColumnItem: React.FC<
  Pick<FieldItem, 'name'> & {
    selectedFields: FieldName[];
    onChange: React.Dispatch<React.SetStateAction<FieldName[]>>;
  }
> = ({ name, selectedFields, onChange }) => {
  const column = CLIENT_TABLE_CUSTOMIZE_CONFIG.fieldMap[name];
  return (
    <div className="moe-flex moe-justify-between moe-mb-xs moe-pl-s moe-rounded-s moe-border moe-border-solid moe-border-divider">
      <Condition if={column.sortable !== false}>
        <SortableList.DragHandle className="moe-cursor-grab moe-mr-xs" />
      </Condition>
      <label
        className={cn(
          'moe-flex moe-justify-between moe-flex-grow moe-py-s moe-pr-s',
          column.checkable === false ? '' : 'moe-cursor-pointer',
        )}
      >
        <Text variant="regular-short">{column.title}</Text>
        <Checkbox
          onChange={() => {
            if (selectedFields) {
              onChange(toggleItem(selectedFields, column.name));
            }
          }}
          isDisabled={column.checkable === false}
          isSelected={selectedFields.includes(column.name)}
        />
      </label>
    </div>
  );
};

export interface CustomizeColumnsDrawerProps extends DrawerProps {}

export const CustomizeColumnsDrawer = memo<CustomizeColumnsDrawerProps>((props) => {
  const { isOpen, onClose } = props;
  const { currentView } = useClientViewContext();
  const customizeSortableFields = useCustomizeSortableFields();
  const [selectedFields, setSelectedFields] = useState<FieldName[]>([]);
  const [sortableFieldList, setSortableFieldList] = useState<FieldName[]>(customizeSortableFields);
  const viewActions = useViewActions();
  const handleConfirm = useSerialCallback(async () => {
    await viewActions.update({
      fields: sortSelectedList(sortableFieldList, selectedFields),
    });
    toastApi.success('Changes saved');
    onClose?.();
  });

  useEffect(() => {
    if (Array.isArray(currentView.fields) && currentView.fields.length) {
      setSelectedFields(currentView.fields);
      setSortableFieldList(
        prePositionList(
          sortableFieldList,
          currentView.fields.filter((v) => customizeSortableFields.includes(v)),
        ),
      );
    }
  }, [currentView.fields, isOpen, customizeSortableFields]);

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      title="Customize columns"
      size="s"
      confirmText="Apply"
      confirmButtonProps={{
        isLoading: handleConfirm.isBusy(),
      }}
      onConfirm={handleConfirm}
      classNames={{
        base: 'moe-overflow-hidden',
        container: '',
      }}
    >
      {CLIENT_TABLE_CUSTOMIZE_CONFIG.fixedFieldList.map((id) => (
        <ColumnItem name={id} key={id} selectedFields={selectedFields} onChange={setSelectedFields} />
      ))}
      <SortableList
        items={sortableFieldList.map((id) => ({ id }))}
        onChange={(itemList) => {
          setSortableFieldList(itemList.map((item) => item.id));
        }}
        renderItem={({ id }) => {
          return (
            <SortableList.Item id={id} key={id}>
              <ColumnItem name={id} selectedFields={selectedFields} onChange={setSelectedFields} />
            </SortableList.Item>
          );
        }}
      />
    </Drawer>
  );
});
