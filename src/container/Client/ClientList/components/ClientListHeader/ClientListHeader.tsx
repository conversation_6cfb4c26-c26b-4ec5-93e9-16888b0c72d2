import { MajorFilterOutlined, MajorSettingOutlined } from '@moego/icons-react';
import { Badge, Button, IconButton, Input, cn } from '@moego/ui';
import React, { type ReactNode, memo, useRef } from 'react';
import { useDebounce } from 'react-use';
import { Condition } from '../../../../../components/Condition';
import { ClientListTestIds } from '../../../../../config/testIds/client';
import { usePropsState } from '../../../../../utils/hooks/usePropsState';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { ClientViewOnboarding } from '../ClientViewOnboarding';
import { useClientViewContext } from '../hooks/useClientViewContext';
import { useFilterActions } from '../hooks/useFilterActions';
import { ModifiedActions } from './ModifiedActions';
import { SelectedFilters } from './SelectedFilters';

export interface ClientListHeaderProps {
  placeholder?: string;
  defaultKeyword?: string;
  className?: string;
  extra?: ReactNode;
  onCustomizeColumns?: () => void;
  onFilterVisibleChange?: (visible: boolean) => void;
  filterVisible?: boolean;
  /**
   * @description 是否显示右上角的自定义列按钮
   * @default true
   */
  showCustomizeColumns?: boolean;

  /**
   * @description 是否显示修改后的保存视图操作
   * @default true
   */
  showModifiedActions?: boolean;

  onClear?: () => void;
}

export const ClientListHeader = memo<ClientListHeaderProps>((props) => {
  const {
    placeholder = 'name, address, number, pet, email',
    className,
    defaultKeyword = '',
    onCustomizeColumns,
    showCustomizeColumns = true,
    showModifiedActions = true,
    onFilterVisibleChange,
    filterVisible,
    onClear,
  } = props;
  const { filtersCount, clientList } = useClientViewContext();
  const prevKeyword = clientList.filter.queries?.keyword;
  const [keyword, setKeyword] = usePropsState(prevKeyword ?? defaultKeyword);
  const filterActions = useFilterActions();
  const handleClear = useSerialCallback(async () => {
    filterActions.clear();
    onClear?.();
    await filterActions.fetch('init');
  });

  const runCount = useRef(0);
  useDebounce(
    () => {
      if (runCount.current++) {
        filterActions.fetch('init', { queries: { keyword } });
      }
    },
    500,
    [keyword],
  );

  const allFilterButton = (
    <Badge.Count
      placement="topRight"
      count={filtersCount}
      classNames={{
        container: ['moe-right-8px-200 moe-top-[-4px]', filtersCount <= 0 ? 'moe-hidden' : ''],
      }}
    >
      <Button
        onPress={() => onFilterVisibleChange?.(!filterVisible)}
        variant="secondary"
        className="moe-transition-all moe-duration-100"
        icon={<MajorFilterOutlined data-action={filterVisible ? 'clientFilter-hide' : 'clientFilter-show'} />}
      >
        {filterVisible ? 'Hide filters' : 'All filters'}
      </Button>
    </Badge.Count>
  );

  return (
    <div className={cn('moe-bg-neutral-sunken-0 moe-rounded-m moe-p-s moe-pb-none', className)}>
      <div className="moe-flex moe-justify-between">
        <div className="moe-flex moe-flex-wrap moe-gap-s moe-items-center">
          <Input.Search
            className="moe-w-[308px]"
            value={keyword}
            onChange={(v: string) => setKeyword(v)}
            placeholder={placeholder}
            data-testid={ClientListTestIds.ClientListKeywordInput}
          />
          <ClientViewOnboarding step={2}>{allFilterButton}</ClientViewOnboarding>
        </div>
        <Condition if={showCustomizeColumns}>
          <ClientViewOnboarding step={4}>
            <div className="moe-relative">
              <IconButton
                variant="secondary"
                size="l"
                icon={<MajorSettingOutlined />}
                onPress={onCustomizeColumns}
                classNames={{
                  base: 'moe-flex-shrink-0',
                }}
                tooltipProps={{
                  side: 'top',
                  closeDelay: 0,
                  align: 'end',
                  content: 'Customize columns',
                  children: <></>,
                }}
              />
            </div>
          </ClientViewOnboarding>
        </Condition>
      </div>
      <div className="moe-flex moe-justify-between moe-items-end moe-mt-s">
        <div className="moe-flex moe-items-end moe-flex-wrap moe-w-full">
          <SelectedFilters className="moe-mr-s" />
          <div
            className={cn(
              'moe-flex moe-items-center moe-mb-s moe-flex-1',
              filtersCount ? 'moe-justify-between' : 'moe-justify-end',
            )}
          >
            <Condition if={!!filtersCount}>
              <Button className="moe-shrink-0" size="s" onPress={handleClear} variant="tertiary">
                Clear filters
              </Button>
            </Condition>

            {showModifiedActions && <ModifiedActions />}
          </div>
        </div>
      </div>
    </div>
  );
});
