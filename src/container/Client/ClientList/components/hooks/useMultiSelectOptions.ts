/**
 * @since 2024-02-21
 * <AUTHOR>
 * @description options for multi select
 */
import { type List } from 'immutable';
import { type ReactNode, useMemo } from 'react';
import { type RecordInstance, type RecordMap, type RecordProps } from '../../../../../store/utils/RecordMap';
import { valueHelpers } from '../FilterItems/FilterProperty.utils';

export function useMultiSelectOptions<V extends RecordInstance<any>, KF extends keyof RecordProps<V>>(
  list: List<V[KF]>,
  map: RecordMap<V, KF>,
  label: keyof V | ((item: V) => ReactNode),
  getValue?: (item: V) => string | number,
): Array<{
  label: string;
  value: string;
}> {
  return useMemo(() => {
    const result = list
      .map((id) => {
        const record = map.mustGetItem(id);
        return {
          label:
            typeof label === 'function'
              ? label(record)
              : typeof record[label] === 'function'
                ? record[label]()
                : record[label],
          value: getValue ? getValue(record) : id,
        };
      })
      .toArray();
    return valueHelpers.stringValueOptions(result);
  }, [list, map, label]);
}
