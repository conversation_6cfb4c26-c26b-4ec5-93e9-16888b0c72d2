import { Modal, Typography } from '@moego/ui';
import React, { memo } from 'react';
import { type ModalProps } from '../../../../../components/Modal/Modal';

// const CLIENT_AND_PET_DATA_TEMPLATE_AS_XLSX =
//   'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/1/client_and_pet_data_template_v2.xlsx';

export const ImportClientModal = memo<ModalProps>(({ visible, onClose }) => {
  // const importing = useBool();
  // const dispatch = useDispatch();
  // const handleImport = async (value: string) => {
  //   importing.open();
  //   try {
  //     await dispatch(importCustomer(value));
  //     modalApi.info({
  //       content:
  //         "We have received your import request. We'll reply to you within the next business day. The response will include the ETA and importable information.",
  //       onOk: () => onClose(),
  //     });
  //   } finally {
  //     importing.close();
  //   }
  // };

  // const TipsItem = ({ seq, text }: { seq: number; text: ReactNode }) => (
  //   <div className="moe-flex moe-mb-l">
  //     <span className="moe-shrink-0 moe-w-[20px] moe-h-[20px] moe-rounded-full moe-text-s moe-border moe-border-brand moe-text-brand moe-text-center moe-mr-s">
  //       {seq}
  //     </span>
  //     <span className="moe-text-base">{text}</span>
  //   </div>
  // );

  return (
    <Modal
      onClose={onClose}
      isOpen={visible}
      title="Data Import"
      cancelButtonProps={{
        style: {
          display: 'none',
        },
      }}
      confirmText="Got it"
      onConfirm={() => {
        onClose();
      }}
    >
      <div>
        {/* <TipsItem seq={1} text="Once uploaded, we will review your file and respond within 2 business days." />
        <TipsItem seq={2} text="Supported file format: .xlsx / .csv / .txt" />
        <TipsItem
          seq={3}
          text={
            <>
              If you move from paper, or prefer to type out your clients, you can start from our data template.{' '}
              <a href={CLIENT_AND_PET_DATA_TEMPLATE_AS_XLSX}>Download template</a>
            </>
          }
        /> */}
        <Typography.Text variant="regular">
          To initiate a data import, kindly contact our
          <b className="moe-text-brand"> Support Team </b>
          thru chat box or your
          <b className="moe-text-brand"> Account Manager </b>
          for guidance.
        </Typography.Text>
      </div>
      {/* <AlignCenter>
        <Upload accept=".xlsx,.csv,.txt" maxSize={10 << 20} onChange={handleImport}>
          {({ latestStatus, select }) => (
            <Button onPress={select} isLoading={latestStatus === 'uploading' || importing.value}>
              {latestStatus === 'uploading' ? 'Uploading ...' : importing.value ? 'Importing...' : 'Upload'}
            </Button>
          )}
        </Upload>
      </AlignCenter> */}
    </Modal>
  );
});
