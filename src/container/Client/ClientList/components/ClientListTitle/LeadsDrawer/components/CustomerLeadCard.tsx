import { MinorPlusOutlined } from '@moego/icons-react';
import { <PERSON><PERSON>, Head<PERSON> } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo } from 'react';
import { useHistory } from 'react-router';
import { PATH_CUSTOMER_ADD } from '../../../../../../../router/paths';
import { dismissUnassignCustomer } from '../../../../../../../store/message/unassignMessage.actions';
import { useAsyncCallback } from '../../../../../../../utils/hooks/useAsyncCallback';
import { type UnassignCustomer } from '../CustomerLeadListDrawer.type';
import { CustomerHistoryRecordList } from './CustomerHistoryRecordList';
import { CustomerLeadMethodTag } from './CustomerLeadMethodTag';

interface CustomerLeadCardProps {
  item: UnassignCustomer;
  onClose?: () => void;
  onDismiss: (item: UnassignCustomer) => void;
}
export const CustomerLeadCard = memo<CustomerLeadCardProps>(({ item, onClose, onDismiss }) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const createClientProfileHandler = () => {
    onClose?.();
    history.push(PATH_CUSTOMER_ADD.stated({ customerInfo: { phoneNumber: item.phoneNumberWithoutCountryCode } }));
  };
  const dismissHandler = useAsyncCallback(async () => {
    const success = await dispatch(dismissUnassignCustomer({ phoneNumber: item.phone }));
    if (success) {
      onDismiss(item);
    }
  });
  return (
    <div className="moe-flex moe-flex-col moe-gap-8px-150 moe-p-s moe-border moe-border-divider moe-rounded-s">
      <div className="moe-flex moe-items-center moe-gap-s">
        <Heading size="5" className="moe-text-primary">{`${item.phone} (${item.records.length})`}</Heading>
        <CustomerLeadMethodTag records={item.records} />
      </div>
      <CustomerHistoryRecordList records={item.records} />
      <div className="moe-flex moe-items-center moe-gap-8px-150">
        <Button icon={<MinorPlusOutlined />} size="s" variant="tertiary" onPress={createClientProfileHandler}>
          Create client profile
        </Button>
        <Button size="s" variant="tertiary" isLoading={dismissHandler.loading} onPress={dismissHandler}>
          Dismiss
        </Button>
      </div>
    </div>
  );
});
