import { Condition, Text } from '@moego/ui';
import { useSelector } from 'amos';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { type UnassignCustomerRecord } from '../CustomerLeadListDrawer.type';
import { isCallRecord, isSmsRecord } from '../CustomerLeadListDrawer.utils';

interface CustomerHistoryRecordListProps {
  records: UnassignCustomerRecord[];
}
export const CustomerHistoryRecordList = memo<CustomerHistoryRecordListProps>(({ records }) => {
  const [business] = useSelector(selectCurrentBusiness);
  return (
    <div className="moe-flex moe-flex-col moe-gap-8px-150 moe-relative moe-pl-s">
      <div className="moe-absolute moe-left-0 moe-top-0 moe-w-[4px] moe-h-full moe-bg-brand-mild moe-rounded-[2px]" />
      {records.map((record, index) => (
        <div key={index} className="moe-flex moe-flex-col moe-gap-8px-50 moe-items-start">
          <Text variant="regular-short" className="moe-text-secondary">
            {business.formatDateTime(record.createTime * T_SECOND)}
          </Text>
          <Condition if={isSmsRecord(record.method)}>
            <Text variant="regular" className="moe-text-primary">
              {record.messageText}
            </Text>
          </Condition>
          <Condition if={isCallRecord(record.method)}>
            <Text variant="regular" className="moe-text-primary">
              phone call
            </Text>
          </Condition>
        </div>
      ))}
    </div>
  );
});
