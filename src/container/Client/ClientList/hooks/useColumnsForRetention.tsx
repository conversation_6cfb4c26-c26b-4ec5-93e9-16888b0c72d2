import { CustomerRetentionDataTableColumn } from '@moego/api-web/moego/models/business_customer/v1/business_customer_retention_defs';
import { type NumberData } from '@moego/api-web/moego/models/reporting/v2/diagram_model';
import { FieldType } from '@moego/api-web/moego/models/reporting/v2/field_model';
import { MinorFilterFilled } from '@moego/icons-react';
import { Text, cn } from '@moego/ui';
import { type CellContext, type ColumnDef } from '@moego/ui/dist/esm/components/Table/Table.types';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';

import SvgIconUpArrowSvg from '../../../../assets/svg/icon-up-arrow.svg';
import { Condition } from '../../../../components/Condition';
import { SvgIcon } from '../../../../components/Icon/Icon';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type CustomerRetentionType } from '../../../../store/clientRetention/clientRetention.actions';
import { clientFiltersVisibleBox } from '../../../../store/customer/clientFilters.boxes';
import { useBoolBox } from '../../../../utils/hooks/useBoolBox';
import { ClientName } from '../components/ClientTable/Columns/ClientName';
import { ClientTags } from '../components/ClientTable/Columns/ClientTags';

export type MonthColumns = Exclude<
  CustomerRetentionDataTableColumn,
  CustomerRetentionDataTableColumn.UNSPECIFIED | CustomerRetentionDataTableColumn.CLIENT_NAME
>;

const EMPTY_OPTION_LABEL = '--';

export const CustomerRetentionDataTableColumnMap: Record<MonthColumns, string> = {
  [CustomerRetentionDataTableColumn.AVERAGE]: 'Avg',
  [CustomerRetentionDataTableColumn.JANUARY]: 'Jan',
  [CustomerRetentionDataTableColumn.FEBRUARY]: 'Feb',
  [CustomerRetentionDataTableColumn.MARCH]: 'Mar',
  [CustomerRetentionDataTableColumn.APRIL]: 'Apr',
  [CustomerRetentionDataTableColumn.MAY]: 'May',
  [CustomerRetentionDataTableColumn.JUNE]: 'Jun',
  [CustomerRetentionDataTableColumn.JULY]: 'Jul',
  [CustomerRetentionDataTableColumn.AUGUST]: 'Aug',
  [CustomerRetentionDataTableColumn.SEPTEMBER]: 'Sep',
  [CustomerRetentionDataTableColumn.OCTOBER]: 'Oct',
  [CustomerRetentionDataTableColumn.NOVEMBER]: 'Nov',
  [CustomerRetentionDataTableColumn.DECEMBER]: 'Dec',
};

export const CustomerRetentionDataTableColumnIdMap: Record<MonthColumns, string> = {
  [CustomerRetentionDataTableColumn.AVERAGE]: 'avg',
  [CustomerRetentionDataTableColumn.JANUARY]: 'january',
  [CustomerRetentionDataTableColumn.FEBRUARY]: 'february',
  [CustomerRetentionDataTableColumn.MARCH]: 'march',
  [CustomerRetentionDataTableColumn.APRIL]: 'april',
  [CustomerRetentionDataTableColumn.MAY]: 'may',
  [CustomerRetentionDataTableColumn.JUNE]: 'june',
  [CustomerRetentionDataTableColumn.JULY]: 'july',
  [CustomerRetentionDataTableColumn.AUGUST]: 'august',
  [CustomerRetentionDataTableColumn.SEPTEMBER]: 'september',
  [CustomerRetentionDataTableColumn.OCTOBER]: 'october',
  [CustomerRetentionDataTableColumn.NOVEMBER]: 'november',
  [CustomerRetentionDataTableColumn.DECEMBER]: 'december',
};

// 定义每个列的数据类型
export interface ColumnData extends Omit<NumberData, 'value'> {
  value: number | string;
  rawRate: number | undefined;
  rateTxt: string;
  arrowPositive: boolean;
  classNames: string;
  color: string;
}

// 使用 Record 类型来创建一个包含所有列的对象类型
export type ColumnsData = Record<MonthColumns, ColumnData>;

// 定义 CustomerRetentionData 接口
export interface CustomerRetentionData {
  customer: CustomerRetentionType;
  monthlyRetention: ColumnsData;
}

export const useColumnsForRetention = () => {
  const [filterDrawerVisible, setFilterDrawerVisible] = useBoolBox(clientFiltersVisibleBox);
  // const [type] = useSelector(clientRetentionTypeStore);
  const baseColumns: ColumnDef<CustomerRetentionData>[] = [
    {
      id: 'clientName',
      minSize: 160,
      header: () => (
        <div
          className="moe-inline-flex moe-items-center moe-cursor-pointer"
          onClick={() => setFilterDrawerVisible(!filterDrawerVisible)}
        >
          <span>Client name</span>
          <MinorFilterFilled className="moe-text-icon-surface" />
        </div>
      ),
      cell: ({ row }: CellContext<CustomerRetentionData, unknown>) => (
        <div className="moe-flex moe-gap-4">
          <ClientName client={row.original.customer} />
          <ClientTags client={row.original.customer} />
        </div>
      ),
      meta: {
        sticky: 'left',
      },
      accessorKey: 'clientName',
    },
  ];

  const dataColumns: ColumnDef<CustomerRetentionData>[] = Object.keys(CustomerRetentionDataTableColumnMap).map(
    (column) => {
      const columnNum: MonthColumns = Number(column);
      return {
        id: CustomerRetentionDataTableColumnIdMap[columnNum],
        header: CustomerRetentionDataTableColumnMap[columnNum],
        accessorKey: `monthlyRetention.${columnNum}.value`,
        cell: ({ row }: CellContext<CustomerRetentionData, unknown>) => {
          return <MonthCell data={row.original.monthlyRetention?.[columnNum] || {}} column={columnNum} />;
        },
        enableSorting: true,
        sortingFn: () => 0,
        minSize: 120,
        meta: {
          className: ['moe-text-right'],
        },
      };
    },
  );

  return [...baseColumns, ...dataColumns];
};

export const MonthCell = ({ data, column }: { data: ColumnData; column: MonthColumns }) => {
  const { arrowPositive, rawRate, rateTxt } = data;
  const [business] = useSelector(selectCurrentBusiness());

  const validateRawRate = Number.isFinite(rawRate);
  const positive = validateRawRate ? arrowPositive : false;

  const rateValue = useMemo(() => {
    if (validateRawRate) {
      return !rawRate ? EMPTY_OPTION_LABEL : rateTxt;
    }
    return EMPTY_OPTION_LABEL;
  }, [validateRawRate, rawRate, rateTxt]);

  const renderArrow = () => {
    if (!validateRawRate || !rawRate) return null;
    if (positive) {
      return <SvgIcon src={SvgIconUpArrowSvg} size={7} className="!moe-text-success" />;
    }
    return <SvgIcon src={SvgIconUpArrowSvg} size={7} className={cn('moe-rotate-180', '!moe-text-danger')} />;
  };
  return (
    <div>
      <div>{data?.fieldType === FieldType.MONEY ? business.formatAmount(Number(data?.value || 0)) : data?.value}</div>
      <Condition if={column !== CustomerRetentionDataTableColumn.AVERAGE}>
        <div className="moe-flex moe-gap-[2px] moe-justify-end moe-items-center">
          {renderArrow()}
          <Text
            variant="caption"
            className={cn(rateValue === EMPTY_OPTION_LABEL ? 'moe-text-disabled' : data.classNames)}
          >
            {rateValue}
          </Text>
        </div>
      </Condition>
    </div>
  );
};
