import { useSelector } from 'amos';
import React, { memo } from 'react';
import { customerTagMapBox } from '../../../../../../store/customer/customerTag.boxes';
import { selectBusinessCustomerTags } from '../../../../../../store/customer/customerTag.selectors';
import { type SpecificFilterProps } from '../ClientFilters.config';
import { useMultiSelectOptions } from '../ClientFilters.hooks';
import { MultiTagsSelect } from './common/MultiTagsSelect';

export const ClientTag = memo<SpecificFilterProps>(({ type, property }) => {
  const [tagList, tagMap] = useSelector(selectBusinessCustomerTags(), customerTagMapBox);
  const selectOptions = useMultiSelectOptions(tagList, tagMap, 'name');

  return <MultiTagsSelect type={type} property={property} selectOptions={selectOptions} />;
});
