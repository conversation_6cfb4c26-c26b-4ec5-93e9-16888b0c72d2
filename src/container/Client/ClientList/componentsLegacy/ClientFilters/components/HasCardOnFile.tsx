import React, { memo } from 'react';
import { useIsMoeGoPayReady } from '../../../../../../utils/hooks/useIsMoeGoPay';
import { HasCOFOptions, type SpecificFilterProps } from '../ClientFilters.config';
import { RadioNormal } from './common/RadioNormal';

export const HasCardOnFile = memo<SpecificFilterProps>((props) => {
  const { type, property } = props;
  const isMoeGoPayReady = useIsMoeGoPayReady();
  if (!isMoeGoPayReady) {
    return null;
  }
  return <RadioNormal type={type} property={property} options={HasCOFOptions} />;
});
