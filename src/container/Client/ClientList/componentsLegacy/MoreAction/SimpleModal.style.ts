import styled from 'styled-components';
import { Modal } from '../../../../../components/Modal/Modal';

export const SimpleModalView = styled(Modal)`
  .modal {
    width: 420px;
  }

  .modal-header {
    color: #333;
    padding: 24px 24px 8px;
    border-bottom: none;
  }

  .modal-container {
    padding: 0px 24px;
  }

  .modal-footer {
    padding: 16px 24px 24px;
    border-top: none;
  }
`;

export const StyledModal = styled(Modal)`
  .modal-header,
  .modal-footer {
    padding: 16px 32px;
  }
  .modal-container {
    padding: 24px 32px;
  }
  .modal-header-close {
    right: 32px;
  }
  .ant-form-item-label > label {
    color: #333;
    font-weight: 700;
  }
  .ant-select-selector {
    min-height: 48px;
  }
`;
