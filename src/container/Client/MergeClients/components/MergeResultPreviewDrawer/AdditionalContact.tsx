import { type PreviewCustomerMergeResultContactView } from '@moego/api-web/moego/api/business_customer/v1/business_customer_api';
import { Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type FC, memo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { printFullName } from '../../../../../store/customer/customer.boxes';
import { PreviewCard } from './PreviewCard';

interface ContactRowProps {
  contact: PreviewCustomerMergeResultContactView;
}

const ContactRow: FC<ContactRowProps> = ({ contact }) => {
  const { firstName, lastName, phoneNumber, email } = contact;
  const [business] = useSelector(selectCurrentBusiness);

  return (
    <div className="moe-flex moe-flex-row moe-gap-[20px] moe-text-primary">
      <Text variant="small" className="moe-w-[100px] moe-flex-shrink-0" ellipsis>
        {printFullName(firstName, lastName)}
      </Text>
      <Text variant="small" className="moe-w-[140px] moe-flex-shrink-0 rr-mask" ellipsis>
        {business.formatPhoneNumber(phoneNumber)}
      </Text>
      <Text variant="small" ellipsis>
        {email}
      </Text>
    </div>
  );
};

export interface AdditionalContactProps {
  contactList: PreviewCustomerMergeResultContactView[];
}

export const AdditionalContact = memo<AdditionalContactProps>(({ contactList }) => {
  return (
    <PreviewCard title="Additional contact">
      <div className="moe-flex moe-flex-col moe-gap-y-[12px] moe-max-h-[150px] moe-overflow-auto">
        {contactList.map((contact) => (
          <ContactRow key={contact.id} contact={contact} />
        ))}
      </div>
    </PreviewCard>
  );
});
