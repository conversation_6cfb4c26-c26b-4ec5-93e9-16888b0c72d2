/*
 * @since 2020-10-16 19:41:55
 * <AUTHOR> <<EMAIL>>
 */

import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { Condition } from '../../../../components/Condition';
import { Fill } from '../../../../components/Style/Style';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { selectPricingPermission } from '../../../../store/company/company.selectors';
import { useEnableFeature } from '../../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../../store/metadata/metadata.config';
import { getSquareAccount } from '../../../../store/square/actions/public/square.actions';
import { selectSquareAccount } from '../../../../store/square/square.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';
import { CreditCardsView } from './CreditCards.style';
import { Credits } from './components/Credits';
import { SquareCardList } from './components/SquareCardList';
import { StripeCardList } from './components/StripeCardList';
import { StripeDebitCardList } from './components/StripeDebitCardList';

export interface CreditCardsProps {
  className?: string;
}

export const CreditCards = memo<CreditCardsProps>(({ className }) => {
  const [business, square, pricingPermission, permissions] = useSelector(
    selectCurrentBusiness(),
    selectSquareAccount(),
    selectPricingPermission(),
    selectCurrentPermissions(),
  );
  const dispatch = useDispatch();
  const { enable: achEnabled } = useEnableFeature(META_DATA_KEY_LIST.ACHEnabled);
  const isCreditEnabled = useFeatureIsOn(GrowthBookFeatureList.Credit);

  const isReadOnly = !permissions.has('viewIndividualClientProfile');

  useEffect(() => {
    isNormal(business.id) && dispatch(getSquareAccount());
  }, [business.id]);

  const getSquareBlock = () => {
    const isSquareBlockHidden = !square.squareConnected && !pricingPermission.enable.has('square');
    if (isSquareBlockHidden) {
      return null;
    }

    return (
      <>
        <SquareCardList isReadOnly={isReadOnly} />
        <Fill height={32} />
      </>
    );
  };

  const getStripeBlock = () => {
    return (
      <>
        <StripeCardList isReadOnly={isReadOnly} />
        <Fill height={32} />
        <StripeDebitCardList isReadOnly={isReadOnly} />
        {achEnabled && <Fill height={32} />}
      </>
    );
  };

  return (
    <CreditCardsView className={cn(className, 'moe-flex moe-gap-10 moe-overflow-auto')}>
      <div>
        {business.preferSquare() ? (
          <>
            {getSquareBlock()}
            {getStripeBlock()}
          </>
        ) : (
          <>
            {getStripeBlock()}
            {getSquareBlock()}
          </>
        )}
      </div>
      <Condition if={isCreditEnabled}>
        <Credits />
      </Condition>
    </CreditCardsView>
  );
});
