import { Text, cn } from '@moego/ui';
import React, { memo, type ReactNode } from 'react';
import { Condition } from '../../../../../../components/Condition';

interface DescriptionProps {
  label: ReactNode;
  content?: ReactNode;
  className?: string;
  children?: ReactNode;
}
export const Description = memo<DescriptionProps>((props) => {
  const { label, content, className, children } = props;
  return (
    <div className={cn('moe-flex moe-flex-col moe-gap-[4px] moe-flex-1', className)}>
      <Text variant="caption" className="moe-text-tertiary">
        {label}
      </Text>
      <Condition if={content}>
        <Text variant="regular-short">{content}</Text>
      </Condition>
      {children}
    </div>
  );
});
