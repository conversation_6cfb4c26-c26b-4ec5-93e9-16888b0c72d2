/*
 * @since 2020-09-02 15:10:12
 * <AUTHOR> <<EMAIL>>
 */

import { MoreOutlined } from '@ant-design/icons';
import { cn } from '@moego/ui';
import { Popover } from 'antd';
import React, { memo } from 'react';
import IconIconVaccineSvg from '../../../../../assets/icon/icon-vaccine.svg';
import { ImgIcon } from '../../../../../components/Icon/Icon';
import { PetCodeGroupTooltip } from '../../../../../components/PetInfo/PetCodeGroupTooltip';
import { Tooltip } from '../../../../../components/Popup/Tooltip';
import { Fill } from '../../../../../components/Style/Style';
import { type PetCodeRecord } from '../../../../../store/pet/petCode.boxes';
import { type PickProps } from '../../../../../store/utils/RecordMap';
import { PetCodeGroupView, PetCodeIconView, PetCodeTagView } from './PetCodeTag.style';

export type PickerUnion = 'id' | 'color' | 'codeNumber' | 'description';
export interface PetCodeTagProps {
  code: Readonly<PickProps<PetCodeRecord, PickerUnion>> | undefined;
  closable?: boolean;
  onClose?: () => void;
  petId?: number;
}

export const PetCodeTag = memo<PetCodeTagProps>(({ petId, code, closable, onClose }) => {
  if (!code) {
    return null;
  }
  return (
    <PetCodeTagView>
      <PetCodeIcon petId={petId} code={code} />
      <span className="name">{code.description}</span>
      {closable && (
        <span className="close" onClick={onClose}>
          &times;
        </span>
      )}
    </PetCodeTagView>
  );
});

export interface PetCodeIconProps {
  code: Readonly<PickProps<PetCodeRecord, 'id' | 'color' | 'codeNumber' | 'description'>> | undefined;
  showTip?: boolean;
  fill?: boolean;
  style?: React.CSSProperties;
  petId?: number;
}

export const PetCodeIcon = memo<PetCodeIconProps>(({ petId, code, fill, style, ...props }) => {
  if (!code) {
    return null;
  }
  let icon = (
    <PetCodeIconView style={{ backgroundColor: code.color, ...style }} {...props}>
      {code.codeNumber}
    </PetCodeIconView>
  );
  if (fill) {
    icon = <Fill width={50}>{icon}</Fill>;
  }
  return icon;
});

export const VaccineExpired = memo(({ className }: { className?: string }) => (
  <Tooltip overlay="Vaccine expired" placement="top">
    <ImgIcon className={cn('vaccine-expired', className)} src={IconIconVaccineSvg} width={23} />
  </Tooltip>
));

export interface PetCodeGroupProps {
  codes: Readonly<PickProps<PetCodeRecord, 'id' | 'color' | 'codeNumber' | 'description'>>[] | undefined;
  maxLength?: number | false;
  className?: string;
  showTip?: boolean;
  fill?: boolean;
  petId?: number;
}

export const PetCodeGroup = memo<PetCodeGroupProps>(
  ({ petId, codes = [], maxLength = false, className, showTip, fill }) => {
    const result = (
      <PetCodeGroupView className={className}>
        {codes
          .filter((_code, index) => (maxLength ? index < maxLength : true))
          .map((code, index) => (
            <PetCodeIcon key={index} code={code} fill={fill} />
          ))}
        {maxLength && codes.length > maxLength ? (
          <Popover
            content={codes
              .filter((_code, index) => index >= maxLength)
              .map((code, index) => <PetCodeIcon petId={petId} key={index} code={code} fill={fill} />)}
          >
            <MoreOutlined className="icon-more" />
          </Popover>
        ) : null}
      </PetCodeGroupView>
    );
    return showTip ? (
      <PetCodeGroupTooltip codesIdList={codes.map((code) => code.id)} petId={petId}>
        {result}
      </PetCodeGroupTooltip>
    ) : (
      result
    );
  },
);
