import {
  Form,
  Input,
  Modal,
  type ModalProps,
  type PressEvent,
  type UploadItem,
  UploadStatus,
  useForm,
} from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { petPhotoMapBox } from '../../../../../store/pet/petPhoto.boxes';
import { useSerialCallback } from '@moego/tools';
import { addPhoto, removePhoto, updatePhoto } from '../../../../../store/pet/petPhoto.actions';
import { toastApi } from '../../../../../components/Toast/Toast';
import { MoeGoUIUpload } from '../../../../../components/Upload/MoeGoUIUpload';
import { ByteUnit } from '../../../../../utils/common';

interface PhoneFormField {
  photo: UploadItem[] | null;
  description?: string;
}

interface PhotoModalProps extends ModalProps {
  petId: number;
  photoId?: number;
}

export const PhotoModal = memo<PhotoModalProps>((props) => {
  const { petId, photoId, onConfirm, ...rest } = props;
  const dispatch = useDispatch();
  const [photoMap] = useSelector(petPhotoMapBox);
  const originPhoto = photoMap.getItem(photoId);
  const form = useForm<PhoneFormField>();
  // 如果有原图片，则photo可以为空，用来做删除操作
  const isPhotoRequired = !originPhoto;

  useEffect(() => {
    form.reset({
      photo: originPhoto
        ? [
            {
              uid: originPhoto?.photoUrl,
              status: UploadStatus.success,
              url: originPhoto?.photoUrl,
            },
          ]
        : [],
      description: originPhoto?.description,
    });
  }, [originPhoto, form]);

  const handleConfirm = useSerialCallback(async (e: PressEvent) => {
    await form.handleSubmit(async (values) => {
      const { photo, description } = values;
      // 如果没有修改图片，只更新了描述，就调用更新接口
      // 如果修改了图片，就删除旧的，添加新的
      if (!form.formState.dirtyFields.photo) {
        await dispatch(
          updatePhoto({
            petPhotoId: originPhoto!.petPhotoId,
            description,
          }),
        );
      } else {
        if (originPhoto) {
          // 更新接口不支持更新图片，只能更新描述，所以只要有旧值就直接删掉，再重新添加
          await dispatch(removePhoto(originPhoto.petPhotoId));
        }
        if (photo?.length) {
          await dispatch(
            addPhoto({
              petId,
              photoUrl: photo[0].url!,
              description,
            }),
          );
        }
      }
      toastApi.success('Pet photo updated successfully!');
      onConfirm?.(e);
    })();
  });

  return (
    <Modal
      title="Upload photo"
      size="s"
      autoCloseOnConfirm={false}
      {...rest}
      onConfirm={handleConfirm}
      confirmButtonProps={{
        isDisabled: !form.formState.isDirty,
      }}
    >
      <Form form={form} footer={null}>
        <Form.Item label="Photo" name="photo" rules={{ required: isPhotoRequired ? 'Please upload a photo' : false }}>
          <MoeGoUIUpload
            maxCount={1}
            maxSize={10 * ByteUnit.MB}
            variant="image-card"
            isRequired={isPhotoRequired}
            placeholderDescription="Max: 10MB"
          />
        </Form.Item>
        <Form.Item label="Description" name="description">
          <Input.TextArea placeholder="Description" />
        </Form.Item>
      </Form>
    </Modal>
  );
});
