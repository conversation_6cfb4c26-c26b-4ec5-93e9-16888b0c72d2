import { cn, Table } from '@moego/ui';
import React from 'react';
import { ReportTableProps, useReportTable } from '../hooks/useReportTable';
import { CategoryCell } from '../ReportTableCell/CategoryCell';
import { useObjectUUID } from '@moego/reporting';

interface PaymentCategoryTableProps extends Pick<ReportTableProps, 'requestParams'> {
  diagramId: string;
  className?: string;
}

export const PaymentCategoryTable = ({ className, diagramId, requestParams }: PaymentCategoryTableProps) => {
  const getRowId = useObjectUUID();
  const { columns, dataSource, sorting, onSortingChange, tableLoading, onRowClick, hasDrillConfig } = useReportTable({
    diagramId,
    requestParams,
    ReportTableCell: CategoryCell,
    getMetaClassName: ({ key }) => {
      const isProportion = key === 'proportion';
      return cn('moe-w-[25%]', isProportion ? 'moe-text-left' : undefined);
    },
  });

  return (
    <div className={className}>
      <Table
        isLoading={tableLoading}
        sorting={sorting}
        onSortingChange={onSortingChange}
        columns={columns}
        data={dataSource}
        getRowId={getRowId}
        onRowClick={hasDrillConfig ? onRowClick : undefined}
        emptyProps={{
          className: 'moe-my-[30px]',
          description: '',
          illustration: null,
          classNames: { title: 'moe-text-tertiary moe-font-normal moe-mb-0', description: 'moe-hidden' },
        }}
      />
    </div>
  );
};
