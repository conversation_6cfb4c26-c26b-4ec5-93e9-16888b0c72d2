import { Checkbox, CheckboxGroup } from '@moego/ui';
import { useControllableValue } from 'ahooks';
import React from 'react';

interface PaymentMethodListProps {
  options: { value: string; label: string }[];
  value?: string[];
  onChange?: (value: string[]) => void;
}

export const PaymentMethodList = (props: PaymentMethodListProps) => {
  const { options = [] } = props;
  const initialValue = options[0]?.value;
  const [selected, setSelected] = useControllableValue<string[]>(props, {
    defaultValue: initialValue ? [initialValue] : [],
  });

  return (
    <CheckboxGroup
      classNames={{ wrapper: 'moe-flex moe-flex-row moe-gap-s moe-flex-wrap' }}
      value={selected}
      onChange={setSelected}
    >
      {options.map(({ value, label }) => (
        <Checkbox key={value} value={value}>
          {label}
        </Checkbox>
      ))}
    </CheckboxGroup>
  );
};
