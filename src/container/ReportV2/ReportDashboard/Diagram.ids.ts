export enum DashboardDiagram {
  DashboardOperationPieTotalAppointmentsByStatus = 'dashboard_operation_pie_total_appointments_by_status',
  DashboardOperationPieTotalBookingsBySource = 'dashboard_operation_pie_total_bookings_by_source',
  DashboardClientsNumArrayClientPetSummary = 'dashboard_clients_num_array_client_pet_summary',
  /** ----- sales overview ----- */
  DashboardSalesGroupSalesBreakdown = 'dashboard_sales_group_sales_breakdown',
  DashboardOverviewNumGrossSales = 'dashboard_sales_num_gross_sales',
  DashboardOverviewNumNetSales = 'dashboard_sales_num_net_sales',
  DashboardSalesNumExpected = 'dashboard_sales_num_expected',
  DashboardOverviewNumCollected = 'dashboard_sales_num_collected',
  DashboardOverviewNumUnpaidInvoice = 'dashboard_sales_num_unpaid_invoice',
  /** ----- sales bar chart ---- */
  DashboardSalesGroupSalesOverview = 'dashboard_sales_group_sales_overview',
  DashboardSalesBarPerformance = 'dashboard_sales_bar_performance',
  DashboardSalesBarPerformanceByCategory = 'dashboard_sales_bar_performance_by_category',
  DashboardSalesBarBreakdownCategory = 'dashboard_sales_bar_breakdown_category',
  DashboardSalesBarBreakdownZipcode = 'dashboard_sales_bar_breakdown_zipcode',
  DashboardSalesBarBreakdownBusinessName = 'dashboard_sales_bar_breakdown_business_name',
  DashboardSalesBarBreakdownCareType = 'dashboard_sales_bar_breakdown_care_type',
  /** ----- sales average earning ---- */
  DashboardSalesGroupAverageEarnings = 'dashboard_sales_group_average_earnings',
  DashboardAveEarningPerStaff = 'dashboard_sales_num_average_earnings_per_staff',
  DashboardAveEarningPerTicket = 'dashboard_sales_num_average_earnings_per_ticket',
  DashboardAveEarningPerPet = 'dashboard_sales_num_average_earnings_per_pet',
  DashboardAveEarningPerHour = 'dashboard_sales_num_average_earnings_per_hour',
  /** ----- sales bar chart ---- */
  DashboardOverviewBarBookedFinished = 'dashboard_overview_bar_booked_finished',
  /** ----- sales detail table ---- */
  DashboardSalesGroupSalesDetails = 'dashboard_sales_group_sales_details',
  DashboardSalesTableService = 'dashboard_sales_table_service',
  DashboardSalesTableAddon = 'dashboard_sales_table_addon',
  DashboardSalesTableProduct = 'dashboard_sales_table_product',

  /** ----- clients & Pets breeds ----- */
  /** ----- clients serviced/dots/cats ----- */
  DashboardClientsNumTotalClients = 'dashboard_clients_num_total_clients',
  DashboardClientsNumTotalDogs = 'dashboard_clients_num_total_dogs',
  DashboardClientsNumTotalCats = 'dashboard_clients_num_total_cats',
  DashboardClientsNumArrayPetSummary = 'dashboard_clients_num_array_pet_summary',
  /** ----- Client overview ----- */
  DashboardClientsGroupClientServiced = 'dashboard_clients_group_client_serviced',
  DashboardClientsPieServicedClients = 'dashboard_clients_pie_serviced_clients',
  DashboardClientsPieServicedPets = 'dashboard_clients_pie_serviced_pets',
  DashboardClientsGroupClientCreated = 'dashboard_clients_group_client_created',
  DashboardClientsPieCreatedClients = 'dashboard_clients_pie_created_clients',
  DashboardClientsProcedureNumberCreatedClients = 'dashboard_clients_procedure_number_created_clients',
  DashboardClientsGroupClientOverview = 'dashboard_clients_group_client_overview',
  DashboardClientsPieClientType = 'dashboard_clients_pie_client_type',
  DashboardOperationPieTotalBookings = 'dashboard_operation_pie_total_bookings',
  DashboardClientsGroupClientRetention = 'dashboard_clients_group_client_retention',
  DashboardClientsGroupOnlineBooking = 'dashboard_clients_group_online_booking',
  DashboardClientsProcedureNumberClientType = 'dashboard_clients_procedure_number_client_type',
  DashboardClientsNumAverageRebookRate = 'dashboard_clients_num_average_rebook_rate',
  DashboardClientsStatusbarClientRetention = 'dashboard_clients_statusbar_client_retention',
  DashboardClientsFunnelOnlineBooking = 'dashboard_clients_funnel_online_booking',
  DashboardClientsGroupPetBreeds = 'dashboard_clients_group_pet_breeds',
  DashboardClientsTablePetBreeds = 'dashboard_clients_table_pet_breeds',
  DashboardOperationNumAvgOnlineRate = 'dashboard_operation_num_avg_online_rate',

  /** ----- staff sales ----- */
  DashboardStaffGroupSalesContribution = 'dashboard_staff_group_sales_contribution',
  DashboardStaffTableSalesContribution = 'dashboard_staff_table_sales_contribution',
  DashboardStaffRankTopSale = 'dashboard_staff_rank_top_sale',

  /** ----- staff productivity ----- */
  DashboardStaffTableProductivity = 'dashboard_staff_table_productivity',
  DashboardStaffGroupProductivity = 'dashboard_staff_group_productivity',
  DashboardStaffNumAverageEarningPerDay = 'dashboard_staff_num_average_earning_per_day',
  DashboardStaffNumUtilization = 'dashboard_staff_num_utilization',
  DashboardStaffNumAverageActualServiceDuration = 'dashboard_staff_num_average_actual_service_duration',

  /** ----- staff customer satisfaction ----- */
  DashboardStaffGroupCustomerSatisfaction = 'dashboard_staff_group_customer_satisfaction',
  DashboardStaffNumGroomingReportReceivedRate = 'dashboard_staff_num_grooming_report_received_rate',
  DashboardStaffNumAverageTipRate = 'dashboard_staff_num_average_tip_rate',
  DashboardStaffNumAverageReviewScore = 'dashboard_staff_num_average_review_score',
  DashboardStaffNumRebookRate = 'dashboard_staff_num_rebook_rate',
  DashboardStaffTableCustomerSatisfaction = 'dashboard_staff_table_customer_satisfaction',

  /** ----- Operation ----- */
  DashboardOperationGroupBookingOverview = 'dashboard_operation_group_booking_overview',
  DashboardOperationPieTotalBookedClients = 'dashboard_operation_pie_total_booked_clients',
  DashboardOperationGroupAvgDrivingDistance = 'dashboard_operation_group_avg_driving_distance',
  DashboardOperationNumAvgDrivingDistancePerAppointment = 'dashboard_operation_num_avg_driving_distance_per_appointment',
  DashboardOperationNumAvgDrivingDistancePerStaff = 'dashboard_operation_num_avg_driving_distance_per_staff',
  DashboardOperationNumAvgDrivingDistancePerServiceArea = 'dashboard_operation_num_avg_driving_distance_per_service_area',
  DashboardOperationGroupTopSchedulers = 'dashboard_operation_group_top_schedulers',
  DashboardOperationStatusbarTopSchedulers = 'dashboard_operation_statusbar_top_schedulers',
  DashboardOperationGroupCancelledBookings = 'dashboard_operation_group_cancelled_bookings',
  DashboardOperationPieCancelledBookings = 'dashboard_operation_pie_cancelled_bookings',
  DashboardOperationGroupBookingCapacity = 'dashboard_operation_group_booking_capacity',
  DashboardOperationNumTotalServiceTime = 'dashboard_operation_num_total_service_time',
  DashboardOperationNumTotalIdleTime = 'dashboard_operation_num_total_idle_time',
  DashboardOperationNumTotalDrivingTime = 'dashboard_operation_num_total_driving_time',
  DashboardOperationLineBookingCapacity = 'dashboard_operation_line_booking_capacity',
  DashboardOperationGroupAppointmentOverview = 'dashboard_operation_group_appointment_overview',
  DashboardOperationLegendAppointmentsByStatus = 'dashboard_operation_legend_appointments_by_status',
  DashboardOperationBarV2AppointmentsByStatus = 'dashboard_operation_bar_v2_appointments_by_status',
  DashboardOperationGroupCreatedAppointment = 'dashboard_operation_group_created_appointment',
  DashboardOperationLegendCreatedAppointmentBySource = 'dashboard_operation_legend_created_appointment_by_source',
  DashboardOperationBarV2CreatedAppointmentBySource = 'dashboard_operation_bar_v2_created_appointment_by_source',
  DashboardOperationGroupClientVisitFrequency = 'dashboard_operation_group_client_visit_frequency',
  DashboardOperationLegendClientVisitFrequency = 'dashboard_operation_legend_client_visit_frequency',
  DashboardOperationBarV2ClientVisitFrequency = 'dashboard_operation_bar_v2_client_visit_frequency',
  DashboardOperationNumListBookingCapacity = 'dashboard_operation_num_list_booking_capacity',
  /** ----- Landing Page Revenue ----- */
  DashboardLandingVirtualEnterpriseOverview = 'virtual_dashboard_enterprise_overview',
  DashboardLandingGroupRevenue = 'enterprise_dashboard_landing_group_revenue',
  DashboardLandingRevenueGroupSalesOverview = 'enterprise_dashboard_landing_revenue_group_sales_overview',
  DashboardLandingRevenueGrossSale = 'enterprise_dashboard_landing_revenue_gross_sale',
  DashboardLandingRevenueNetSale = 'enterprise_dashboard_landing_revenue_net_sale',
  DashboardLandingRevenueTotalAppointment = 'enterprise_dashboard_landing_revenue_total_appointment',
  DashboardLandingRevenueNewClients = 'enterprise_dashboard_landing_revenue_new_clients',
  DashboardLandingRevenueReturningClients = 'enterprise_dashboard_landing_revenue_returning_clients',
  DashboardLandingRevenueAverageSalePerTickets = 'enterprise_dashboard_landing_revenue_average_sale_per_tickets',
  DashboardLandingRevenueTotalClients = 'enterprise_dashboard_landing_revenue_total_clients',
  DashboardLandingRevenueTotalPets = 'enterprise_dashboard_landing_revenue_total_pets',
  DashboardLandingRevenueUnpaid = 'enterprise_dashboard_landing_revenue_unpaid',
  /** ----- Landing Page Ranking ----- */
  DashboardLandingGroupRanking = 'enterprise_dashboard_landing_group_ranking',
  DashboardLandingRankingTopSales = 'enterprise_dashboard_landing_ranking_top_sales',
  DashboardLandingRankingTopMovers = 'enterprise_dashboard_landing_ranking_top_movers',
  DashboardLandingRankingNeedAttention = 'enterprise_dashboard_landing_ranking_need_attention',
  /** Overview */
  DashboardOverviewGroupSalesOverview = 'dashboard_overview_group_sales_overview',
  DashboardOverviewGroupPaymentBreakdown = 'dashboard_overview_group_payment_breakdown',
  DashboardOverviewGroupSalesBreakdown = 'dashboard_overview_group_sales_breakdown',
  DashboardOverviewGroupClientOverview = 'dashboard_overview_group_client_overview',
  DashboardOverviewGroupBookingOverview = 'dashboard_overview_group_booking_overview',
  DashboardOverviewGroupStaffContribution = 'dashboard_overview_group_staff_contribution',
  DashboardOverviewTableSalesContribution = 'dashboard_overview_table_sales_contribution',
  DashboardOverviewTablePaymentBreakdown = 'dashboard_overview_table_payment_breakdown',

  // leads
  DashboardLeadsGroupGoogleAdsOverview = 'dashboard_leads_group_google_ads_overview',
  DashboardLeadsNumListGoogleAdsMetrics = 'dashboard_leads_num_list_google_ads_metrics',
  DashboardLeadsBarGoogleAdsMetrics = 'dashboard_leads_bar_google_ads_metrics',
  DashboardLeadsBarClientMetrics = 'dashboard_leads_bar_client_metrics',
  DashboardLeadsGroupLeadConversion = 'dashboard_leads_group_lead_conversion',
  DashboardLeadsProcedureNumberLeadConversion = 'dashboard_leads_procedure_number_lead_conversion',
  DashboardLeadsTableCampaignTable = 'dashboard_leads_table_campaign_table',
  /** ----- 下面是未分类的id ----- */
  DashboardOverviewGroupClientRetention = 'dashboard_overview_group_client_retention',
  DashboardOverviewGroupTopSaleByStaff = 'dashboard_overview_group_top_sale_by_staff',
  DashboardOverviewGroupCustomerSatisfaction = 'dashboard_overview_group_customer_satisfaction',
  DashboardOverviewGroupCancelledBookings = 'dashboard_overview_group_cancelled_bookings',
  DashboardClientsGroupClientSummary = 'dashboard_clients_group_client_summary',

  DashboardSalesBarPerformanceBreakdown = 'dashboard_sales_bar_performance_breakdown',

  // overview
  // -- Key metrics
  DashboardOverviewGroupKeyMetrics = 'dashboard_overview_group_key_metrics',
  DashboardOverviewNumListKeyMetrics = 'dashboard_overview_num_list_key_metrics',
  DashboardOverviewLegendCollectedAmount = 'dashboard_overview_legend_collected_amount',
  DashboardOverviewGroupCollected = 'dashboard_overview_group_collected',

  // -- Net sales performance
  DashboardOverviewGroupNetSalesPerformance = 'dashboard_overview_group_net_sales_performance',
  DashboardOverviewLineNetSalesByDay = 'dashboard_overview_line_net_sales_by_day',
  DashboardOverviewLineNetSalesByWeek = 'dashboard_overview_line_net_sales_by_week',
  DashboardOverviewLineNetSalesByMonth = 'dashboard_overview_line_net_sales_by_month',

  // -- Revenue by category
  DashboardOverviewGroupRevenue = 'dashboard_overview_group_revenue',
  DashboardOverviewBarRevenueByCategory = 'dashboard_overview_bar_revenue_by_category',

  // -- Clients serviced
  DashboardOverviewGroupClientServiced = 'dashboard_overview_group_client_serviced',
  DashboardOverviewPieUniqClientServiced = 'dashboard_overview_pie_uniq_client_serviced',
  DashboardOverviewNumListCustomerSatisfaction = 'dashboard_overview_num_list_customer_satisfaction',

  // -- Pets serviced
  DashboardOverviewGroupPetServiced = 'dashboard_overview_group_pet_serviced',
  DashboardOverviewPieUniqPetServiced = 'dashboard_overview_pie_uniq_pet_serviced',
  DashboardOverviewStatusBarTopBreedsServiced = 'dashboard_overview_status_bar_top_breeds_serviced',

  // -- Appointment summary
  DashboardOverviewGroupAppointmentSummary = 'dashboard_overview_group_appointment_summary',
  DashboardOverviewPieAppointmentBySource = 'dashboard_overview_pie_appointment_by_source',
  DashboardOverviewStatusBarAppointmentByStatus = 'dashboard_overview_status_bar_appointment_by_status',

  // -- Staff sales breakdown
  DashboardOverviewGroupStaffPaymentBreakdown = 'dashboard_overview_group_staff_payment_breakdown',
  DashboardOverviewTableStaffPaymentBreakdown = 'dashboard_overview_table_staff_payment_breakdown',

  // Daily revenue
  DashboardDailyRevenueGroupReceivableSummary = 'dashboard_daily_revenue_group_receivable_summary',
  DashboardDailyRevenueTableReceivableSummary = 'dashboard_daily_revenue_table_receivable_summary',
  DashboardDailyRevenuePieReceivableAmount = 'dashboard_daily_revenue_pie_receivable_amount',
  DashboardDailyRevenueNumListUnpaidAmount = 'dashboard_daily_revenue_num_list_unpaid_amount',
  DashboardDailyRevenueTableReceivableSummaryByCategory = 'dashboard_daily_revenue_table_receivable_summary_by_category',

  DashboardDailyRevenueGroupNetPaymentSummary = 'dashboard_daily_revenue_group_net_payment_summary',
  DashboardDailyRevenueTablePaymentSummary = 'dashboard_daily_revenue_table_payment_summary',
  DashboardDailyRevenuePiePaymentSummary = 'dashboard_daily_revenue_pie_payment_summary',
  DashboardDailyRevenueTablePaymentSummaryByMethod = 'dashboard_daily_revenue_table_payment_summary_by_method',

  DashboardDailyRevenueGroupRedeemSummary = 'dashboard_daily_revenue_group_redeem_summary',
  DashboardDailyRevenueTableRedeemSummary = 'dashboard_daily_revenue_table_redeem_summary',
}
