import {
  type FetchReportDataRequest,
  type FetchReportDataResponse,
} from '@moego/api-web/moego/api/reporting/v2/reports_api';
import { ReportPane } from '@moego/reporting';
import React from 'react';
import { LeadsConversionFunnelCharts } from '../../components/LeadsConversionFunnelCharts/LeadsConversionFunnelCharts';
import { LeadsConversionFunnelTable } from '../../components/LeadsConversionFunnelTable/LeadsConversionFunnelTable';
export interface LeadsConversionFunnelProps {
  groupDiagramId: string;
  chartDiagramId: string;
  tableDiagramId: string;
  fetchReportData: (
    request: FetchReportDataRequest,
    config?: { signal?: AbortSignal },
  ) => Promise<FetchReportDataResponse>;
}

export function LeadsConversionFunnel(props: LeadsConversionFunnelProps) {
  const { groupDiagramId, chartDiagramId, tableDiagramId } = props;

  return (
    <ReportPane childClassName="moe-flex moe-flex-col moe-gap-y-m" diagramId={groupDiagramId}>
      <LeadsConversionFunnelCharts diagramId={chartDiagramId} />
      <LeadsConversionFunnelTable diagramId={tableDiagramId} />
    </ReportPane>
  );
}
