import { type Value } from '@moego/api-web/moego/models/reporting/v2/common_model';
import { type RankData } from '@moego/api-web/moego/models/reporting/v2/diagram_model';
import { MajorCrown } from '@moego/icons-react';
import { ChartNoDataTip, ReportPane } from '@moego/reporting';
import { Heading, Typography } from '@moego/ui';
import React, { useMemo } from 'react';
import { Condition } from '../../../../components/Condition';
import { DashboardTable } from '../../components/DashboardTable/DashboardTable';
import { ExpandCollapse } from '../../components/ExpandCollapse';
import { type ColumnCustomizedConfig } from '../../components/ReportTable/ReportTable/useColumns';
import { SpinCenter } from '../../components/SpinCenter';
import { StaffAvatar } from '../../components/StaffAvatar/StaffAvatar';
import { useDashboardDataFetch } from '../../hooks/useDashboardDataFetch';
import { useReportFormatValue } from '../../hooks/useReportFormatValue';
import { DashboardDiagram } from '../Diagram.ids';

function PriceWithPercentage(props: { price: Value; percentage: Value }) {
  const formatValue = useReportFormatValue();

  return (
    <div className="moe-text-center moe-mt-[8px]">
      <Heading as="span" size="5">
        {formatValue(props.percentage, { percent: true })}
      </Heading>
      <Typography.Text as="span" variant="regular-short">
        &nbsp;({formatValue(props.price)})
      </Typography.Text>
    </div>
  );
}

function StaffRank() {
  const [data, isLoading] = useDashboardDataFetch(DashboardDiagram.DashboardStaffRankTopSale);

  if (isLoading) {
    return <SpinCenter containerClassName="moe-h-[182px]"></SpinCenter>;
  }
  const rankData = (data?.rankData as RankData) || { ranks: [] };

  if (!rankData.ranks.length) {
    return <ChartNoDataTip className="moe-h-[182px]"></ChartNoDataTip>;
  }

  const [one, two, three] = rankData.ranks;

  return (
    <div className="moe-flex moe-items-end moe-justify-center">
      <Condition if={two}>
        {() => {
          return (
            <div>
              <StaffAvatar
                name={two.label}
                src={two.avatarUrl}
                direction="column"
                headingProps={{
                  size: '4',
                }}
                avatarProps={{
                  classNames: {
                    base: 'moe-w-[81px] moe-h-[81px]',
                  },
                }}
              ></StaffAvatar>
              <PriceWithPercentage price={two.secondaryMetric!} percentage={two.mainMetric}></PriceWithPercentage>
            </div>
          );
        }}
      </Condition>

      <div className="moe-mx-[150px]">
        <StaffAvatar
          name={one.label}
          src={one.avatarUrl}
          direction="column"
          headingProps={{
            size: '4',
          }}
          avatarProps={{
            classNames: {
              base: 'moe-w-[120px] moe-h-[120px] moe-border-[5px]',
            },
            color: '#FFE7B8',
          }}
          icon={<MajorCrown></MajorCrown>}
        ></StaffAvatar>
        <PriceWithPercentage price={one.secondaryMetric!} percentage={one.mainMetric}></PriceWithPercentage>
      </div>
      <Condition if={three}>
        {() => {
          return (
            <div>
              <StaffAvatar
                name={three.label}
                src={three.avatarUrl}
                direction="column"
                headingProps={{
                  size: '4',
                }}
                avatarProps={{
                  classNames: {
                    base: 'moe-w-[81px] moe-h-[81px]',
                  },
                }}
              ></StaffAvatar>
              <PriceWithPercentage price={three.secondaryMetric!} percentage={three.mainMetric}></PriceWithPercentage>
            </div>
          );
        }}
      </Condition>
    </div>
  );
}

interface StaffRankProps {
  defaultExpanded?: boolean;
  groupId: string;
  diagramId: string;
}

export function SalesContribution(props: StaffRankProps) {
  const { defaultExpanded = true, diagramId, groupId } = props;
  const columnCustomizedConfig = useMemo<ColumnCustomizedConfig>(
    () => ({
      proportion_of_appointments_with_addon: {
        meta: {
          className: 'moe-w-[220px]',
        },
      },
    }),
    [],
  );

  return (
    <ReportPane title="Sales contribution" diagramId={groupId}>
      {/* top3 */}
      <StaffRank></StaffRank>
      {/* staff table列表 */}
      <ExpandCollapse defaultExpanded={defaultExpanded} className="moe-mt-s">
        <DashboardTable
          diagramId={diagramId}
          disableRowInteractive
          columnCustomizedConfig={columnCustomizedConfig}
          tableClassNames={{
            table: 'moe-table-fixed moe-w-full',
          }}
        ></DashboardTable>
      </ExpandCollapse>
    </ReportPane>
  );
}
