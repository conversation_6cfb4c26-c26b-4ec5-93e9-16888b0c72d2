import { MajorCloseOutlined } from '@moego/icons-react';
import { Heading, IconButton, Input, cn } from '@moego/ui';
import React, { memo, useState } from 'react';
import { useToggleIntercom } from '../../../../../utils/hooks/useToggleIntercom';
import { useReportTableViewContext } from '../ReportTableContext';
import { DrawerFilterContext, EXPAND_STATUS } from './DrawerFilter.context';
import { DrawerFilterFooter } from './DrawerFilterFooter';
import { InnerDrawerFilter } from './InnerDrawerFilter';

export const ReportDrawerFilter = memo(() => {
  const [allExpandStatus, setAllExpandStatus] = useState(EXPAND_STATUS.default);
  const [searchValue, setSearchValue] = useState<string>('');
  const { reportTableMetaData, setReportTableMetaData } = useReportTableViewContext();

  useToggleIntercom(reportTableMetaData.isShowTableFilter);

  return (
    <div
      className={cn([
        'moe-bg-neutral-default',
        'moe-transition-[width] moe-ease-in-out moe-duration-300',
        'moe-mb-s',
        'moe-font-manrope',
        'moe-relative',
        'moe-sticky moe-top-0 moe-shrink-0',
        reportTableMetaData.isShowTableFilter ? 'moe-w-[360px] moe-mr-l' : 'moe-w-0 moe-overflow-hidden',
      ])}
    >
      <div className="moe-w-[360px] moe-h-full moe-flex moe-flex-col moe-border moe-border-solid moe-border-divider moe-rounded-m moe-overflow-hidden">
        <div className="moe-flex moe-justify-between moe-items-center moe-p-m">
          <Heading size={'3'} className="moe-text-primary">
            All filters
          </Heading>
          <IconButton
            icon={<MajorCloseOutlined />}
            variant="secondary"
            showBorder={false}
            onPress={() => setReportTableMetaData({ ...reportTableMetaData, isShowTableFilter: false })}
          />
        </div>
        <Input.Search
          placeholder="Search"
          className="moe-mx-m moe-mb-xs"
          value={searchValue}
          onChange={setSearchValue}
        />
        <DrawerFilterContext.Provider value={{ allExpandStatus }}>
          <InnerDrawerFilter searchKeyWord={searchValue} />
        </DrawerFilterContext.Provider>
        <DrawerFilterFooter allExpandStatus={allExpandStatus} onExpandChange={setAllExpandStatus} />
      </div>
    </div>
  );
});
