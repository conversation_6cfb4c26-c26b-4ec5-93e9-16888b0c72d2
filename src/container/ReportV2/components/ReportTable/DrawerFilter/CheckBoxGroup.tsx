import { type Value } from '@moego/api-web/moego/models/reporting/v2/common_model';
import { type FilterOption } from '@moego/api-web/moego/models/reporting/v2/filter_model';
import { MajorInfoOutlined } from '@moego/icons-react';
import { Checkbox, Tooltip } from '@moego/ui';
import { Set } from 'immutable';
import React from 'react';
import { Condition } from '../../../../../components/Condition';
import { type FilterObj } from '../ReportTableContext';
import { getCheckboxFilterText } from './checkBoxGroupHelper';

interface CheckBoxGroupProps {
  checkBoxRadio: Set<string> | undefined;
  changeCheckBox: (value: Set<string>, filter: Partial<FilterObj>) => void;
  checkBoxtOptions: FilterOption[];
}

export const CheckBoxGroup = (props: CheckBoxGroupProps) => {
  const { checkBoxtOptions, checkBoxRadio, changeCheckBox } = props;

  return (
    <>
      {checkBoxtOptions.map((options, index) => {
        return (
          <Checkbox
            key={index}
            value={index + ''}
            isSelected={checkBoxRadio ? checkBoxRadio.has(index + '') : false}
            className="moe-mb-[16px]"
            onChange={(value) => {
              let newSet = checkBoxRadio || Set();
              newSet = value ? newSet.add(String(index)) : newSet.delete(String(index));
              const filterTextList: string[] = [];
              const valueList: Value[][] = [];
              newSet.forEach((item) => {
                filterTextList.push(checkBoxtOptions[+item].label + '');
                valueList.push(checkBoxtOptions[Number(item)].values);
              });
              changeCheckBox(newSet, {
                operator: options.operator,
                filterText: getCheckboxFilterText(filterTextList),
                values: valueList,
              });
            }}
          >
            {options.label}
            <Condition if={options.description}>
              <Tooltip>
                <MajorInfoOutlined />
              </Tooltip>
            </Condition>
          </Checkbox>
        );
      })}
    </>
  );
};
