import { Button, Typography } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { clearFilter } from '../../../../../store/report/report.action';
import { selectTableFilter } from '../../../../../store/report/report.selector';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { useReportTableViewContext } from '../ReportTableContext';
import { type AllExpandStatus, EXPAND_STATUS } from './DrawerFilter.context';

interface DrawerFilterFooterProps {
  onExpandChange: (allExpandStatus: AllExpandStatus) => void;
  allExpandStatus: AllExpandStatus;
}

export const DrawerFilterFooter = memo<DrawerFilterFooterProps>((props) => {
  const dispatch = useDispatch();
  const { allExpandStatus, onExpandChange } = props;
  const {
    reportTableMetaData: { diagramId },
  } = useReportTableViewContext();
  const [tableFilter] = useSelector(selectTableFilter(diagramId));
  const toggleExpandStatus = () => {
    onExpandChange(EXPAND_STATUS.mapLabels[allExpandStatus].nextStatus);
  };

  const handleClear = useSerialCallback(async () => {
    await dispatch(clearFilter(diagramId));
  });

  return (
    <div className="moe-flex moe-justify-between moe-items-center moe-pl-m moe-py-s moe-border-t moe-border-divider">
      <div className="moe-flex moe-justify-between moe-items-center">
        <Typography.Text variant="small">
          {tableFilter.size
            ? `${tableFilter.size} filter${tableFilter.size > 1 ? 's' : ''} applied`
            : 'No filters applied'}
        </Typography.Text>
        <Button onPress={handleClear} isDisabled={!tableFilter.size} variant="tertiary-legacy" size="s">
          Clear
        </Button>
      </div>
      <Button onPress={toggleExpandStatus} variant="tertiary-legacy" size="s">
        {EXPAND_STATUS.mapLabels[allExpandStatus].label}
      </Button>
    </div>
  );
});
