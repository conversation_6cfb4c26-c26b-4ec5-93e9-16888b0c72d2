import React, { memo, useMemo } from 'react';
import { PATH_REPORT_INSIGHTS_REPORTS } from '../../../router/paths';
import { useRouteQueryV2 } from '../../../utils/RoutePath';
import { type ReportTableProps } from '../components/ReportTable/ReportTableContext';
import { DashboardFilterCtxProvider } from './DashboardFilterCtxProvider';
import { ReportFrontPage } from './ReportFrontPage';
import { ReportTablePage } from './ReportTablePage';

export const Reports = memo(() => {
  const query = useRouteQueryV2(PATH_REPORT_INSIGHTS_REPORTS);
  const { dateRange, filters, businessId, diagramId, active } = query;
  const { dates, filterList } = useMemo(() => {
    const dates = dateRange ? (JSON.parse(dateRange) as ReportTableProps['dateRange']) : undefined;
    const filterList = filters ? (JSON.parse(filters) as ReportTableProps['filters']) : undefined;
    return {
      dates,
      filterList,
    };
  }, [dateRange, filters]);

  return diagramId ? (
    <ReportTablePage
      diagramId={diagramId}
      dateRange={dates}
      filters={filterList}
      active={active}
      businessId={businessId}
    />
  ) : (
    <DashboardFilterCtxProvider>
      <ReportFrontPage />
    </DashboardFilterCtxProvider>
  );
});
