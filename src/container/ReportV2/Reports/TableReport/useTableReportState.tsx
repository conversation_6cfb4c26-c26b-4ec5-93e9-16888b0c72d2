import {
  type DrillConfig,
  type FilterRequest,
  InsightsTab,
} from '@moego/api-web/moego/models/reporting/v2/common_model';
import {
  CreateCustomReportModeDiagramId,
  type CustomReportTableContext,
  PreviousType,
  TableTreeRootRowId,
  calcPreDates,
  getDefaultGroupByValue,
  usePresetDates,
} from '@moego/reporting';
import { useLatest, useMemoizedFn, useSetState } from 'ahooks';
import { useDispatch } from 'amos';
import { useEffect } from 'react';
import { useHistory } from 'react-router';
import { toastApi } from '../../../../components/Toast/Toast';
import { useScroller } from '../../../../layout/components/ScrollerProvider';
import { PATH_REPORT_INSIGHTS_CUSTOM_REPORTS_LIST, PATH_REPORT_INSIGHTS_REPORTS } from '../../../../router/paths';
import {
  customReportExportData,
  customReportFetchData,
  customReportGetDimensions,
  customReportGetMetricsCategories,
  customReportQueryFile,
  customReportQueryMetas,
  customReportSaveCustomizeReportV1Config,
  customReportSaveCustomReport,
  modifyCustomDiagram,
} from '../../../../store/report/customReport.action';
import { toggleIntercom } from '../../../../utils/hooks/useToggleIntercom';
import { useCreatedOnBoardingMeta } from '../../CustomReports/hooks/useCreatedOnBoardingMeta';
import { TableCell } from './components/TableCell';
import { BusinessSelector } from './components/BusinessSelector';
import { ScopeFilterScopeType } from '@moego/api-web/moego/models/reporting/v2/report_def';
import { useRedirectDrillConfig } from '../../components/ReportTable/hooks/useRedirectDrillConfig';
import { type Dayjs } from 'dayjs';
import { useCustomizedReportsInfo } from '../../hooks/useReportV2Info';
import { TableHeaderPermission } from './components/TableHeaderPermission';

export interface CustomReportsTableParams {
  diagramId: string;
  createWithPreset: boolean;
  active?: string;
  initBusinessId?: string;
  initDates?: [Dayjs, Dayjs];
  initFilterValues?: FilterRequest[];
  initGroupByDimensions?: CustomReportTableContext['defaultGroupByDimensions'];
}

export function useTableReportState(params: CustomReportsTableParams) {
  const { diagramId, createWithPreset, active, initDates, initFilterValues, initGroupByDimensions, initBusinessId } =
    params;
  const { customReportMeta, onChangeCustomReportMeta } = useCreatedOnBoardingMeta();
  const redirectDrillConfig = useRedirectDrillConfig({ active });
  const customReportMetaRef = useLatest(customReportMeta);
  const isCreate = diagramId === CreateCustomReportModeDiagramId;
  const defaultSelectMetricsModalVisible = isCreate && !createWithPreset;
  const presetDates = usePresetDates();
  const last7Days = presetDates.find((i) => i.key === 'last7Days') ?? presetDates[0];
  const history = useHistory();
  const { allowCustomizedReports } = useCustomizedReportsInfo();
  const onGo2ReportList = useMemoizedFn(() => {
    if (allowCustomizedReports) {
      history.push(
        PATH_REPORT_INSIGHTS_CUSTOM_REPORTS_LIST.queried({ active: active || String(InsightsTab.CUSTOMIZED) }),
      );
    } else {
      history.push(PATH_REPORT_INSIGHTS_REPORTS.queried({ active: active || String(InsightsTab.ALL) }));
    }
  });
  const dispatch = useDispatch();
  const initDateRanges = initDates ?? last7Days.value;
  const initPreviousType: PreviousType | undefined = PreviousType.PREVIOUS_PERIOD;
  const hasInitBusinessId = !!initBusinessId;
  const [profileCtxValue, setProfileValue] = useSetState<CustomReportTableContext>(() => ({
    initFilterValues,
    isReportingV1: true,
    TableHeader: TableHeaderPermission,
    TableCell,
    ScopeSelector: BusinessSelector,
    /** 搭配tableMeta.showScopeSelector使用 */
    scope: {
      scopeIds: hasInitBusinessId ? [initBusinessId] : [],
      allScopes: !hasInitBusinessId,
      scopeType: ScopeFilterScopeType.BUSINESS,
    },
    setContext: (...args) => setProfileValue(...args),
    diagramId,
    timeSelectorType: undefined,
    createWithPreset,
    isDirty: false,
    saveReportModalVisible: false,
    dateRanges: initDateRanges,
    prePeriodDates: calcPreDates(initDateRanges, { previousType: initPreviousType ?? PreviousType.PREVIOUS_PERIOD }),
    previousType: initPreviousType,
    queryReportMetas: (input, config) => dispatch(customReportQueryMetas(input, config?.signal)),
    queryDimensions: (input, config) => dispatch(customReportGetDimensions(input, config?.signal)),
    queryMetricsCategories: (input, config) => dispatch(customReportGetMetricsCategories(input, config?.signal)),
    fetchReportData: (input, config) => dispatch(customReportFetchData(input, config?.signal)),
    exportData: (input) => dispatch(customReportExportData(input)),
    queryFile: (input) => dispatch(customReportQueryFile(input)),
    saveCustomReport: (input, config) => dispatch(customReportSaveCustomReport(input, config?.signal)),
    modifyCustomDiagram: (input, config) => dispatch(modifyCustomDiagram(input, config?.signal)),
    saveReportCustomizeConfig: (input) => dispatch(customReportSaveCustomizeReportV1Config(input)),
    reportMeta: null,
    onGo2ReportList,
    reportReadyModalVisible: false,
    firstReportReadyDiagramId: undefined,
    showSaveChangesButton: false,
    onSaveReport: () => {},
    onReportCreated: (diagramId) => {
      const { firstReportReady } = customReportMetaRef.current ?? {};
      // normal case
      if (firstReportReady) {
        toastApi.success('Report saved');
        onGo2ReportList();
        return;
      }
      // first report ready case
      // show Awesome! Your first customized report is ready modal
      setProfileValue({
        reportReadyModalVisible: true,
        firstReportReadyDiagramId: diagramId,
      });
    },
    onFirstReportReadyCheckItNow: (_diagramId) => {
      if (!customReportMetaRef.current?.firstReportReady) {
        onChangeCustomReportMeta({ firstReportReady: true });
      }
      onGo2ReportList();
    },
    onBoardingSteps: {
      visible: false,
      stepIndex: 0,
      next: () => {},
      prev: () => {},
      gotIt: () => {},
    },
    loadingApplyMetrics: false,
    selectMetricsModalVisible: defaultSelectMetricsModalVisible,
    draftSelectedMetricKeys: new Set(),
    selectedMetricKeys: new Set(),
    metricsCategories: undefined,
    loadingMetricsCategories: false,
    defaultGroupByDimensions: initGroupByDimensions,
    groupByDimensions: [getDefaultGroupByValue()],
    dimensions: undefined,
    treeTable: {
      sorting: undefined,
      expandIds: new Set([]),
      columnFields: [],
      tableRowData: {
        rowUuid: TableTreeRootRowId,
        data: {},
        subData: undefined,
        dimensionConfig: undefined,
      },
      tablePagination: undefined,
      loadingRowId: TableTreeRootRowId,
      nextFetchTableRowId: TableTreeRootRowId,
      nextPagination: undefined,
      nextDimensionConfig: undefined,
      onRowClick: (rowData) => onRowClick(rowData.drillConfig),
    },
    dynamicColumnMode: false,
    onFiltersDrawerVisibleChange: (visible) => toggleIntercom(visible),
    // filter drawer props
    filterLayerProps: { openMarginLeft: 24, className: 'moe-sticky' },
    stickyHeaderProps: {
      pagePaddingOffset: 24,
      filterDrawerWidth: 360,
      filterDrawerMarginLeft: 24,
      filterDrawerMarginTop: 24,
      filterDrawerMarginTopInSpecial: 4,
      /** Height of the fixed element at the top of the page */
      pageHeaderHeight: 56,
      filterDrawerMarginBottom: 24,
    },
    showSwitchFlatTableModal: false,
    onSatisfyFlatTableMode: async () => {
      if (customReportMetaRef.current?.isFirstTimeSeeFlatTableMode) {
        // Adding a delay before opening the modal for better user experience
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setProfileValue({
          showSwitchFlatTableModal: true,
        });
        // mark meta data
        onChangeCustomReportMeta({ isFirstTimeSeeFlatTableMode: false });
      }
    },
    pageScrollQuerySelector: 'div[data-slot="layout-body"]',
  }));

  const onRowClick = useMemoizedFn((drillConfig?: DrillConfig) => {
    if (!drillConfig) return;
    const { dateRanges, scope } = profileCtxValue;
    redirectDrillConfig({
      drillConfig,
      dateRanges,
      businessId: scope?.scopeIds[0] ?? undefined,
    });
  });

  // provide sticky container to tree table
  const scroller = useScroller();
  useEffect(
    () =>
      setProfileValue((pre) => ({ ...pre, treeTable: { ...pre.treeTable, stickyContainer: scroller ?? undefined } })),
    [scroller],
  );

  return profileCtxValue;
}
