import { MajorCrown, MinorInfoOutlined } from '@moego/icons-react';
import { Tooltip } from '@moego/ui';
import React from 'react';
import { WithPricingEnableUpgrade } from '../../../../../components/Pricing/WithPricingComponents';
import { type PricingPermissionKey } from '../../../../../store/company/company.boxes';
import { useReportPermission } from '../../../hooks/useReportPermission';
import { type Field } from '@moego/api-web/moego/models/reporting/v2/field_model';

export interface TableHeaderPermissionProps {
  field: Field;
}

export function TableHeaderPermission(props: React.PropsWithChildren<TableHeaderPermissionProps>) {
  const { field } = props;
  const { permissionCode, description } = field;
  const enable = useReportPermission(permissionCode as PricingPermissionKey);

  return (
    <div onClick={(e) => !enable && e.stopPropagation()}>
      <WithPricingEnableUpgrade permission={permissionCode as PricingPermissionKey}>
        <div className="moe-flex moe-items-center">
          <div className="moe-flex moe-items-center moe-gap-x-xxs">
            {field.label}
            {description ? (
              <Tooltip content={description} side="top">
                <MinorInfoOutlined className="moe-cursor-pointer moe-text-icon-surface" />
              </Tooltip>
            ) : null}
          </div>
          {!enable ? <MajorCrown /> : null}
        </div>
      </WithPricingEnableUpgrade>
    </div>
  );
}
