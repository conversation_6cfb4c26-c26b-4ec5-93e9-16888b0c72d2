import { useLatestCallback } from '@moego/finance-utils';
import { MinorDownloadOutlined } from '@moego/icons-react';
import { DATE_FORMAT_EXCHANGE } from '@moego/reporting';
import { useSerialCallback } from '@moego/tools';
import { Button, Checkbox, CheckboxGroup, Form, Heading, Input, Modal, Text, useForm, yupResolver } from '@moego/ui';
import { useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import React, { memo, useEffect, useMemo } from 'react';
import * as Yup from 'yup';
import { toastApi } from '../../../../components/Toast/Toast';
import { useIsMultiLocation } from '../../../../components/WithFeature/useIsMultiLocation';
import { http } from '../../../../middleware/api';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectStaffRole } from '../../../../store/business/role.selectors';
import { useBool } from '../../../../utils/hooks/useBool';
import { CommissionType } from '../../../Report/PayrollDetail/api';
import { useBusinessQuotaInfo } from '../../../settings/Settings/hooks/useBusinessQuotaInfo';
import { type StaffFilterValue } from './PayrollStaffFilter';

export interface PayrollStaffCommissionDownloadProps {
  staffId: string;
  staffName: string;
  bizValue: StaffFilterValue;
  dates: [Dayjs, Dayjs];
  exportableTypeList: number[];
}

const downloadSchema = Yup.object().shape({
  fileName: Yup.string().required('File name is required'),
  contentType: Yup.array()
    .of(Yup.number().required())
    .min(1, 'Please select file content')
    .required('Please select file content'),
});

export const PayrollStaffCommissionDownload = memo<PayrollStaffCommissionDownloadProps>(
  function PayrollStaffDownload(props) {
    const { dates, bizValue, staffName, exportableTypeList, staffId } = props;
    const { isAll, locationId } = bizValue;
    const { businessList } = useBusinessQuotaInfo();
    const [business, role] = useSelector(selectCurrentBusiness(), selectStaffRole());
    const showWorkingLocations = role?.onlyHas('viewReport');
    const isMultiLocation = useIsMultiLocation(showWorkingLocations ? 'working' : 'all');
    const visible = useBool();
    const renderRow = (label: React.ReactNode, children: React.ReactNode) => {
      return (
        <div className="moe-flex moe-items-baseline moe-gap-x-m">
          <Heading size="5" className="moe-w-[100px]">
            {label}
          </Heading>
          <Text variant="regular-short" className="moe-flex-1 moe-min-w-0">
            {children}
          </Text>
        </div>
      );
    };
    const resolver = useMemo(() => yupResolver(downloadSchema), []);
    const initFileName = `${staffName} MoeGo Payroll(${dates[0].format(DATE_FORMAT_EXCHANGE)} - ${dates[1].format(
      DATE_FORMAT_EXCHANGE,
    )})`;
    const form = useForm({
      resolver,
      defaultValues: {
        fileName: initFileName,
        contentType: exportableTypeList,
      },
      mode: 'onBlur',
    });
    const handleExportReport = useLatestCallback(async (fileName: string, contentType: number[]) => {
      const reportDownloadUrl = await http.open('POST/business/report/payroll/export', {
        staffId: +staffId,
        startDate: dates[0].format(DATE_FORMAT_EXCHANGE),
        endDate: dates[1].format(DATE_FORMAT_EXCHANGE),
        fileName,
        contentType,
        businessIds: isAll ? [] : [locationId],
        isAllLocation: isAll,
      });
      // download file from url
      if (reportDownloadUrl) {
        try {
          window.open(reportDownloadUrl, '_blank');
        } catch (e) {
          return false;
        }
        return true;
      }
      return false;
    });
    const downLoadReport = useSerialCallback(async (fieldValues: Yup.InferType<typeof downloadSchema>) => {
      const res = await handleExportReport(fieldValues.fileName, fieldValues.contentType);
      if (res) {
        toastApi.success('File download complete.');
        visible.close();
      } else {
        toastApi.error('File download failed. Please try again.');
      }
    });
    const businessName = isAll ? 'All businesses' : businessList.find((item) => item.id === locationId)?.name;

    useEffect(() => {
      if (visible.value) {
        form.setValue('fileName', initFileName);
        form.setValue('contentType', exportableTypeList);
      }
    }, [visible.value]);

    return (
      <>
        <Button onPress={visible.open} className="moe-ml-auto" variant="secondary" icon={<MinorDownloadOutlined />}>
          Download
        </Button>
        <Modal
          autoCloseOnConfirm={false}
          size="s"
          title="Download report data"
          isOpen={visible.value}
          onOpenChange={visible.as}
          onConfirm={() => form.handleSubmit(downLoadReport)()}
          confirmButtonProps={{ isLoading: downLoadReport.isBusy() }}
        >
          <Form form={form} footer={null}>
            <div className="moe-flex moe-flex-col moe-gap-y-m">
              <div className="moe-flex moe-flex-col moe-gap-y-m">
                <Form.Item label="File name" name="fileName" rules={{ required: true }}>
                  <Input
                    isRequired
                    classNames={{
                      base: '[&>div:first-child]:moe-flex [&>div:first-child]:moe-gap-x-m [&>div:first-child]:moe-items-center',
                      label: 'moe-w-[100px]',
                      inputBox: 'moe-flex-1 moe-min-w-0',
                    }}
                    helpTextClassNames={{ error: 'moe-ml-[124px]' }}
                  />
                </Form.Item>
                {renderRow('Time range', `${business.formatDate(dates[0])} - ${business.formatDate(dates[1])}`)}
                {isMultiLocation ? renderRow('Location', businessName) : null}
                <Form.Item label="File content" name="contentType" rules={{ required: true }}>
                  <CheckboxGroup
                    isRequired
                    classNames={{
                      base: 'moe-flex-row moe-gap-x-m moe-items-baseline moe-flex-wrap',
                      label: 'moe-w-[100px]',
                    }}
                    helpTextClassNames={{ error: 'moe-basis-[100%] moe-ml-[124px]' }}
                  >
                    {CommissionType.values.map((type) => {
                      const { contentType } = CommissionType.mapLabels[type];
                      return (
                        <Checkbox key={type} value={type} isDisabled={!exportableTypeList.includes(type)}>
                          {contentType}
                        </Checkbox>
                      );
                    })}
                  </CheckboxGroup>
                </Form.Item>
                {renderRow('File type', 'xlsx')}
              </div>
            </div>
          </Form>
        </Modal>
      </>
    );
  },
);
