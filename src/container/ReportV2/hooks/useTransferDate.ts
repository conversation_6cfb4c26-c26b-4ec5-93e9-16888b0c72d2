import dayjs, { type Dayjs, isDayjs } from 'dayjs';
import { useMemo } from 'react';

export function useTransferDate(start?: string, end?: string) {
  return useMemo<[Dayjs, Dayjs]>(() => {
    const today = dayjs();
    const yesterday = today.add(-1, 'date');
    const startDate = start ? dayjs(start) : null;
    const endDate = end ? dayjs(end) : null;
    const finalStart = isDayjs(startDate) && startDate.isValid() ? startDate : yesterday;
    const finalEnd = isDayjs(endDate) && endDate.isValid() ? endDate : today;

    return [finalStart.startOf('date'), finalEnd.endOf('date')];
  }, [start, end]);
}
