import { type BusinessProfileContext, BusinessProfileProvider } from '@moego/reporting';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';

export function CustomReportsProvider({ children }: React.PropsWithChildren<{}>) {
  const [business] = useSelector(selectCurrentBusiness());
  const profileValue = useMemo<BusinessProfileContext>(
    () => ({
      currencyCode: business.printCurrency(),
      dateFormat: business.dateFormat,
      formatDateTimeStringWithTZ: business.formatDateTimeStringWithTZ.bind(business),
      formatTimeStringWithTZ: business.formatTimeStringWithTZ.bind(business),
      formatDate: business.formatDate.bind(business),
      formatMoney: business.formatMoney.bind(business),
    }),
    [business],
  );
  return <BusinessProfileProvider value={profileValue}>{children}</BusinessProfileProvider>;
}
