import { type StaffBasicView } from '@moego/api-web/moego/models/organization/v1/staff_models';
import { type RowSelectionState } from '@moego/ui';
import { difference } from 'lodash';
import { rowStateMapToArray } from '../../components/BasedTable/BasedTable.utils';
import { toNumber } from '../../store/utils/identifier';

export function getStaffName(staff: StaffBasicView): string {
  return [staff.firstName, staff.lastName].filter((name) => !!name).join(' ');
}

export const getSelectedTaskOrGroupInfo = (
  taskGroupIdsMap: Record<string, string[]>,
  selectedTaskIdMaps: RowSelectionState,
  selectedAllGroupIdMaps: RowSelectionState,
  groupDetailInfoMap: Record<string, Record<string, number | string>>,
) => {
  const selectedGroupIds = rowStateMapToArray(selectedAllGroupIdMaps);
  const selectedTaskIds = rowStateMapToArray(selectedTaskIdMaps);
  const excludedIds = selectedGroupIds?.map((id) => taskGroupIdsMap[id]).flat();
  const selectedIds = difference(selectedTaskIds, excludedIds);
  const selectedGroupTaskCount = selectedGroupIds?.reduce((pre, cur) => {
    try {
      const { count = 0 } = groupDetailInfoMap[cur] || {};
      return pre + toNumber(count);
    } catch (error) {
      return pre;
    }
  }, 0);

  return {
    taskIds: selectedIds,
    groupIds: selectedGroupIds,
    selectedCount: selectedGroupTaskCount + selectedIds.length,
  };
};
