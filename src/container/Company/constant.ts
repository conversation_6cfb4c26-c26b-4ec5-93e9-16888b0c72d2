import { type TableDataForCompanyListAboutEnterprise } from '../../store/company/company.actions';

export interface CompanyListProps {
  companyList: TableDataForCompanyListAboutEnterprise[];
  disabledForCompanyList: Record<string, DisabledForCompanyList>;
}

export interface RowData {
  companyId: number;
  companyName: string;
  locationNum: number;
  vansNum: number;
  sms: Sms;
  campaign: Campaign;
}

export type Sms = {
  amount: number | null;
  isAutoReload: boolean;
  prevPurchaseAmount: number;
};

export type Campaign = {
  amount: number | null;
  isAutoReload: boolean;
};

export enum AddTypeEnum {
  LOCATION = 'location',
  VAN = 'van',
  SMS = 'sms',
  CAMPAIGN = 'campaign',
}

export enum AccountTypeForEnterprise {
  EnterPriseOwner = 1,
  MultiCompanyOwner = 2,
  SingleCompanyOwner = 3,
  EnterPriseStaff = 4,
  NotEnterpriseAccount = 5,
}
export interface DisabledForCompanyList {
  [AddTypeEnum.CAMPAIGN]: boolean;
  [AddTypeEnum.SMS]: boolean;
  [AddTypeEnum.VAN]: boolean;
  [AddTypeEnum.LOCATION]: boolean;
}

export const defaultDisabledForCompanyList = {
  [AddTypeEnum.CAMPAIGN]: true,
  [AddTypeEnum.SMS]: true,
  [AddTypeEnum.VAN]: true,
  [AddTypeEnum.LOCATION]: true,
};

export type DefaultValueEnum = {
  [AddTypeEnum.LOCATION]: number;
  [AddTypeEnum.VAN]: number;
  [AddTypeEnum.SMS]: Sms;
  [AddTypeEnum.CAMPAIGN]: Campaign;
};

type RowDataForAddNum = {
  companyId: number;
};

export interface AddNumProp {
  type: AddTypeEnum;
  row: RowDataForAddNum;
  defaultValue: DefaultValueEnum[AddTypeEnum];
  disabledForCompanyList: {
    [AddTypeEnum.LOCATION]: boolean;
    [AddTypeEnum.VAN]: boolean;
    [AddTypeEnum.SMS]: boolean;
    [AddTypeEnum.CAMPAIGN]: boolean;
  };
  handleAddNum: {
    [AddTypeEnum.LOCATION]: (companyId: number) => void;
    [AddTypeEnum.VAN]: (companyId: number) => void;
    [AddTypeEnum.SMS]: (companyId: number) => void;
    [AddTypeEnum.CAMPAIGN]: (companyId: number) => void;
  };
  handleSwitchAutoReload?: {
    [AddTypeEnum.SMS]: (isAutoReload: boolean, companyId: number) => void;
    [AddTypeEnum.CAMPAIGN]: (isAutoReload: boolean, companyId: number) => void;
  };
}

export interface SubscriptionProp {
  companyList: TableDataForCompanyListAboutEnterprise[];
}
