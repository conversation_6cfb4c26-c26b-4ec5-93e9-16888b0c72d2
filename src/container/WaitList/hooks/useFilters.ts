import { useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { selectWaitListReqParams } from '../../../store/waitList/waitList.selectors';
import { DATE_TIME_FORMAT_FULL_T } from '../../../utils/DateTimeUtil';

export const useFilters = () => {
  const [business] = useSelector(selectCurrentBusiness());
  const [waitListReqParams] = useSelector(selectWaitListReqParams(business.id));

  const format = (d: Dayjs) => d.format(DATE_TIME_FORMAT_FULL_T);

  return useMemo(() => {
    const { filters, pageNum, pageSize, keyword, sort } = waitListReqParams;
    const { isAvailable, serviceAreaIdList, dateList, timeRangeList, ...rest } = filters || {};
    const availableTimeRange = dateList?.length
      ? {
          availableStartDateTime: format(dayjs(dateList[0]).setMinutes(timeRangeList?.[0]?.startTime || 0)),
          availableEndDateTime: format(dayjs(dateList[0]).setMinutes(timeRangeList?.[0]?.endTime || 60 * 24)),
        }
      : {
          availableStartDateTime: format(dayjs()),
          availableEndDateTime: format(dayjs().add(7, 'day')),
        };
    const isAvailableParams = isAvailable ? { isAvailable } : {};
    const serviceParams = business.isMobileGrooming() && { serviceAreaIdList };
    return {
      pageNum,
      pageSize,
      keyword,
      sort,
      filters: {
        ...rest,
        ...isAvailableParams,
        ...serviceParams,
        ...availableTimeRange,
        dateList,
        timeRangeList,
      },
    };
  }, [business.id, waitListReqParams]);
};
