import { type WaitlistViewPetInfo } from '@moego/api-web/moego/api/online_booking/v1/waitlist_api';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useLatestCallback } from '@moego/finance-utils';
import { DATE_FORMAT_EXCHANGE } from '@moego/reporting';
import {
  Alert,
  Controller,
  DatePicker,
  Form,
  Markup,
  Modal,
  type ModalProps,
  Text,
  useForm,
  useWatch,
} from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import React, { memo, useMemo, useState } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { petMapBox } from '../../../../store/pet/pet.boxes';
import { SelectRoom } from '../../../Appt/components/SelectServiceDetail/components/SelectRoom/SelectRoom';
import { FormLabel } from '../../../CreateWaitList/components/Form/Form.label';
import {
  type WaitListBookNowBoardingFormValues,
  type WaitListBookNowDaycareFormValues,
  defaultWaitListBookNowFormValues,
} from './WaitListBookNow.utils';
import { WaitListEndTime } from './WaitListEndTime';
import { WaitListStartTime } from './WaitListStartTime';
import { useCheckSingleDateRangeMultiServiceScheduleRuleHit } from '../../../Appt/components/ApptTimeReSchedule/PetSchedule/hooks/useBoardingAdditionalServiceScheduleAlert';
import { Condition } from '../../../../components/Condition';

export type PetLoadingList = {
  petId: number;
  lodgingRoomId: string;
  errorMessage: string;
}[];

export interface WaitListBookNowModalProps extends Omit<ModalProps, 'onConfirm'> {
  serviceItemType: ServiceItemType.BOARDING | ServiceItemType.DAYCARE;
  pets: WaitlistViewPetInfo[]; // 完整的pets信息，包含services
  value?: Partial<WaitListBookNowBoardingFormValues | WaitListBookNowDaycareFormValues>;
  onConfirm?: (values: Partial<WaitListBookNowBoardingFormValues> & { lodgingRoom: PetLoadingList }) => void;
}

export const WaitListBookNowModal = memo<WaitListBookNowModalProps>((props) => {
  const { serviceItemType, pets, value, onConfirm, ...rest } = props;
  const [business, petMap] = useSelector(selectCurrentBusiness(), petMapBox);
  const form = useForm({
    defaultValues: value ?? defaultWaitListBookNowFormValues,
  });

  const petIds = useMemo(() => pets.map((pet) => Number(pet.petId)), [pets]);

  const boardingServiceIds = useMemo(() => {
    return serviceItemType === ServiceItemType.BOARDING
      ? pets
          .flatMap((p) => p?.services?.map((s) => s?.serviceId))
          ?.filter(Boolean)
          ?.map(Number)
      : [];
  }, [serviceItemType, pets]);

  // 获取指定 petId 的第一个 serviceId (目前只有一个 main service，所以可以这样用)
  // ref: https://moegoworkspace.slack.com/archives/C08H4N55QGJ/p1753155913867479
  const petServiceMap = useMemo(() => {
    const result = new Map<number, number>();
    pets.forEach((pet) => {
      const firstService = pet.services?.[0];
      if (firstService?.serviceId) {
        result.set(Number(pet.petId), Number(firstService.serviceId));
      }
    });
    return result;
  }, [pets]);

  const [startDate, endDate] = useWatch({
    control: form.control,
    name: ['startDate', 'endDate'],
  });
  const [lodgingData, setLodgingData] = useState<PetLoadingList>(
    petIds.map((petId) => ({
      petId,
      lodgingRoomId: '',
      errorMessage: '',
    })),
  );
  const someServiceHitRule = useCheckSingleDateRangeMultiServiceScheduleRuleHit({
    serviceIds: boardingServiceIds,
    startDate,
    endDate,
  });

  const handleConfirm = useLatestCallback(() => {
    form.handleSubmit((values) => {
      let error = false;
      if (isNil(values.startTime) || isNil(values.endTime)) {
        !values.startTime && form.setError('startTime', { message: 'Start time is required' });
        !values.endTime && form.setError('endTime', { message: 'End time is required' });
        error = true;
      }
      if (lodgingData.some((item) => item.lodgingRoomId === '')) {
        setLodgingData((prev) => {
          return prev.map((item) => {
            if (item.lodgingRoomId === '') {
              error = true;
              return { ...item, errorMessage: 'Lodging allocation is required' };
            }
            return item;
          });
        });
      }
      if (error) {
        return;
      }
      onConfirm?.({
        ...values,
        lodgingRoom: lodgingData,
      });
    })();
  });

  return (
    <Modal autoCloseOnConfirm={false} size="s" title="Confirm booking details" onConfirm={handleConfirm} {...rest}>
      <Text variant="regular" className="moe-text-primary moe-mb-m">
        To schedule this waitlist request, please complete the information below.
      </Text>
      <Form form={form} footer={null}>
        <div className="moe-flex moe-gap-s">
          <FormLabel
            label={serviceItemType === ServiceItemType.BOARDING ? 'Start date' : 'Date'}
            className="moe-flex-1"
            isRequired
          >
            <Controller
              control={form.control}
              name="startDate"
              rules={{ required: true }}
              render={({ field, fieldState }) => (
                <DatePicker
                  {...field}
                  isClearable={false}
                  isInvalid={!!fieldState.error?.message}
                  errorMessage={fieldState.error?.message}
                  value={field.value ? dayjs(field.value) : undefined}
                  isRequired
                  onChange={(v) => {
                    field.onChange(v?.format(DATE_FORMAT_EXCHANGE));
                  }}
                  format={business.dateFormat}
                  className="moe-flex-1"
                  placeholder="Select date"
                  disabledDate={(current) => {
                    return current.isBefore(dayjs(), 'day');
                  }}
                />
              )}
            />
          </FormLabel>

          {serviceItemType === ServiceItemType.BOARDING && <WaitListStartTime form={form} business={business} />}
        </div>

        <div className="moe-flex moe-gap-s moe-w-full">
          {serviceItemType === ServiceItemType.BOARDING ? (
            <FormLabel label="End date" className="moe-flex-1" isRequired>
              <Controller
                control={form.control}
                name="endDate"
                rules={{ required: true }}
                render={({ field, fieldState }) => (
                  <DatePicker
                    {...field}
                    isClearable={false}
                    isInvalid={!!fieldState.error?.message}
                    errorMessage={fieldState.error?.message}
                    value={field.value ? dayjs(field.value) : undefined}
                    isRequired
                    onChange={(v) => {
                      field.onChange(v?.format(DATE_FORMAT_EXCHANGE));
                    }}
                    disabledDate={(current) => {
                      return current.isSameOrBefore(dayjs(startDate), 'day');
                    }}
                    format={business.dateFormat}
                    className="moe-flex-1"
                    placeholder="Select date"
                  />
                )}
              />
            </FormLabel>
          ) : (
            <WaitListStartTime form={form} business={business} />
          )}
          <WaitListEndTime form={form} business={business} />
        </div>

        {petIds.map((petId) => (
          <FormLabel
            label={
              petIds.length > 1 ? `Lodging allocation for ${petMap.mustGetItem(petId)?.petName}` : 'Lodging allocation'
            }
            isRequired
            key={petId}
          >
            <SelectRoom
              key={petId}
              isInvalid={!!lodgingData.find((item) => item.petId === petId)?.errorMessage}
              errorMessage={lodgingData.find((item) => item.petId === petId)?.errorMessage}
              range={{ startDate: dayjs(form.getValues('startDate')), endDate: dayjs(form.getValues('endDate')) }}
              isRequired
              petId={petId}
              serviceId={petServiceMap.get(petId)}
              onChange={(v) => {
                if (v) {
                  setLodgingData((prev) => {
                    return prev.map((item) => {
                      if (item.petId === petId) {
                        return { ...item, lodgingRoomId: v, errorMessage: '' };
                      }
                      return item;
                    });
                  });
                }
              }}
              placeholder="Select a room"
            />
          </FormLabel>
        ))}
      </Form>
      <Condition if={someServiceHitRule}>
        <Alert
          className="moe-mt-m"
          isBordered
          isCloseable={false}
          isRounded
          description={
            <Text variant="small">
              The selected service(s) include{' '}
              <Markup variant="small" as="span">
                default service(s)/add-on(s)
              </Markup>{' '}
              based on the length of stay. Please remember to{' '}
              <Markup variant="small" as="span">
                add them manually
              </Markup>{' '}
              to ensure accurate billing and service.
            </Text>
          }
        />
      </Condition>
    </Modal>
  );
});

WaitListBookNowModal.displayName = 'WaitListBookNowModal';
