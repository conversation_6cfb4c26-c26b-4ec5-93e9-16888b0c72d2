import React, { memo } from 'react';
import { ToggleInputCheckbox } from '../../../../../components/Checkbox/ToggleInputCheckbox';
import { Condition } from '../../../../../components/Condition';
import { NumberInput, RE_INPUT_POSITIVE_INTEGER } from '../../../../../components/form/NumberInput';
import { validateInputPanel } from '../../../../../components/form/PanelCheckbox';
import { StyledFormItem } from '../../../../../components/form/VerticalInputItem';
import { DiscountUsageMap } from '../../Discounts.config';
import { StyledCard } from '../../components/styledComponents';
import { DiscountCheckboxContainer } from './styledComponents';

export const DiscountUsage = memo(() => {
  return (
    <StyledCard title="Discount usage">
      <DiscountCheckboxContainer className="moe-gap-y-[16px]">
        {DiscountUsageMap.values.map((value) => {
          const { label, formItemName } = DiscountUsageMap.mapLabels[value];
          return (
            <StyledFormItem key={value} noStyle name={formItemName} valuePropName="checked">
              <ToggleInputCheckbox
                checkboxLabel={label}
                renderInput={() => (
                  <Condition if={value !== DiscountUsageMap.LimitToOneUser}>
                    <StyledFormItem
                      noStyle
                      name="limitUsage"
                      rules={[validateInputPanel(formItemName, 'Please input usage amount')]}
                    >
                      <NumberInput
                        placeholder="Enter number"
                        inputFormat={RE_INPUT_POSITIVE_INTEGER}
                        className="!moe-w-[316px] !moe-mt-[8px] !moe-ml-[24px]"
                      />
                    </StyledFormItem>
                  </Condition>
                )}
              />
            </StyledFormItem>
          );
        })}
      </DiscountCheckboxContainer>
    </StyledCard>
  );
});
