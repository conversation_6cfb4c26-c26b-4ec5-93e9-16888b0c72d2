import { upperFirst } from 'lodash';
import React, { memo, useMemo } from 'react';
import { ToggleInputCheckbox } from '../../../../../components/Checkbox/ToggleInputCheckbox';
import { FormItemHidden } from '../../../../../components/form/FormItemHidden';
import { InputPanel } from '../../../../../components/form/PanelCheckbox';
import { StyledFormItem } from '../../../../../components/form/VerticalInputItem';
import { useBool } from '../../../../../utils/hooks/useBool';
import { LimitToSelectedItemMap } from '../../Discounts.config';
import { useCreateDiscountContext } from '../CreateEditDiscountContext';
import { getSelectedDesc, getSelectedServiceAddonsDesc } from '../createEditDiscount.utils';
import { ProductDrawer, type SaveProductParams } from './ProductDrawer';
import { type SaveServiceAddonParams, ServicesAddonsDrawer } from './ServicesAddonsDrawer';
import { DiscountCheckboxContainer } from './styledComponents';

export const LimitToSelectedItem = memo(() => {
  const { getFormFieldsValue, setFormFieldValueAndValidate } = useCreateDiscountContext();

  const servicesAddonsDrawerVisible = useBool();
  const productDrawerVisible = useBool();

  const {
    serviceIds: serviceIdList = [],
    addOnIds: addonIdList = [],
    productIds: productIdList = [],
    allowedAllServices,
    allowedAllProducts,
  } = getFormFieldsValue(['serviceIds', 'addOnIds', 'productIds', 'allowedAllServices', 'allowedAllProducts']);

  const handleClickPanel = (value: string) => {
    switch (value) {
      case LimitToSelectedItemMap.ServicesAddons:
        servicesAddonsDrawerVisible.open();
        break;
      case LimitToSelectedItemMap.Products:
        productDrawerVisible.open();
        break;
      default:
        return;
    }
  };

  const handleSaveServicesAddons = ({ serviceIdList, addonIdList, isSelectAll }: SaveServiceAddonParams) => {
    setFormFieldValueAndValidate({
      serviceIds: serviceIdList as unknown as string[],
      addOnIds: addonIdList as unknown as string[],
      allowedAllServices: isSelectAll,
    });
  };

  const handleSaveProduct = ({ productIdList, isSelectAll }: SaveProductParams) => {
    setFormFieldValueAndValidate({ productIds: productIdList as unknown as string[], allowedAllProducts: isSelectAll });
  };

  const panelTitleMap = useMemo(() => {
    const selectedServicesAddonsDesc = getSelectedServiceAddonsDesc([
      { isAll: allowedAllServices, count: serviceIdList.length, noun: 'service' },
      {
        isAll: allowedAllServices,
        count: addonIdList.length,
        noun: 'add-on',
      },
    ]);
    const selectedProductsDesc = upperFirst(
      getSelectedDesc({ isAll: allowedAllProducts, count: productIdList.length, noun: 'product' }),
    );
    return {
      [LimitToSelectedItemMap.ServicesAddons]: selectedServicesAddonsDesc,
      [LimitToSelectedItemMap.Products]: selectedProductsDesc,
    };
  }, [serviceIdList, addonIdList, productIdList, allowedAllServices, allowedAllProducts]);

  return (
    <>
      <DiscountCheckboxContainer className="!moe-pl-[24px] !moe-mt-[16px] moe-gap-y-[8px]">
        {LimitToSelectedItemMap.values.map((value) => {
          const { title, checkboxFormItemName, inputFormItemName, placeholder } =
            LimitToSelectedItemMap.mapLabels[value];
          return (
            <StyledFormItem noStyle key={value} name={checkboxFormItemName} valuePropName="checked">
              <ToggleInputCheckbox
                checkboxLabel={title}
                renderInput={() => (
                  <StyledFormItem
                    name={inputFormItemName}
                    rules={[
                      {
                        validator: async () => {
                          if (panelTitleMap[value]) {
                            return Promise.resolve();
                          }
                          return Promise.reject(`Please select at least one ${title.slice(0, -1).toLowerCase()}!`);
                        },
                      },
                    ]}
                  >
                    <InputPanel
                      placeholder={placeholder}
                      panelTitle={panelTitleMap[value]}
                      onClickPanel={() => handleClickPanel(value)}
                    />
                  </StyledFormItem>
                )}
              />
            </StyledFormItem>
          );
        })}
      </DiscountCheckboxContainer>
      <FormItemHidden name="allowedAllServices" />
      <FormItemHidden name="addOnIds" />
      <ServicesAddonsDrawer
        isSelectAll={allowedAllServices}
        serviceIdList={serviceIdList as unknown as number[]}
        addonIdList={addonIdList as unknown as number[]}
        visible={servicesAddonsDrawerVisible.value}
        onClose={servicesAddonsDrawerVisible.close}
        onSave={handleSaveServicesAddons}
      />
      <FormItemHidden name="allowedAllProducts" />
      <ProductDrawer
        isSelectAll={allowedAllProducts}
        productIdList={productIdList}
        visible={productDrawerVisible.value}
        onClose={productDrawerVisible.close}
        onSave={handleSaveProduct}
      />
    </>
  );
});
