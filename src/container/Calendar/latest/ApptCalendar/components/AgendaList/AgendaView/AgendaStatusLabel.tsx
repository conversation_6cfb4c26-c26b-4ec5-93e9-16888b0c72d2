import React, { memo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { type CalendarEventCardId } from '../../../../../../../store/calendarLatest/card.types';
import { type AppointmentStatus } from '../../../../../../TicketDetail/AppointmentStatus';
import { ticketStatusOptions } from '../../../../../../TicketDetail/interfaces';
import { useEventCardState } from '../../../hooks/useEventCardState';
import { AppointmentStatusLabel } from '../../AppointmentStatus';

export interface AgendaStatusLabelProps {
  status: AppointmentStatus;
  className?: string;
  cardId: CalendarEventCardId;
}

export const AgendaStatusLabel = memo(({ status, className = '', cardId }: AgendaStatusLabelProps) => {
  const statusItem = ticketStatusOptions.find((item) => item.id === status);
  const { isOBPending, appointmentStatus } = useEventCardState(cardId);

  return (
    <Condition if={!!statusItem || isOBPending}>
      <AppointmentStatusLabel isOBPending={isOBPending} appointmentStatus={appointmentStatus} className={className} />
    </Condition>
  );
});
