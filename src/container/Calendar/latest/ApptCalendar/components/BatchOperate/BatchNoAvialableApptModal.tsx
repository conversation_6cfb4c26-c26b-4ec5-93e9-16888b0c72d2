import { Modal, Text } from '@moego/ui';
import React, { type FC } from 'react';
import { useModal } from '../../../../../../components/Modal/useModal';
import { BatchModalMode } from '../../../../../../store/calendarLatest/calendar.boxes';
import { createEnum } from '../../../../../../store/utils/createEnum';

const UnfinishedApptEnum = createEnum({
  Cancel: [
    BatchModalMode.Cancel,
    {
      title: 'No unfinished appointment',
      description:
        'Only unfinished appointments can be canceled. There are no appointments available for cancellation.',
    },
  ],
  Reschedule: [
    BatchModalMode.Reschedule,
    {
      title: 'No unfinished appointment',
      description:
        'Only unfinished appointments can be rescheduled. There are no appointments available for rescheduling.',
    },
  ],
  BookAgain: [
    BatchModalMode.BookAgain,
    {
      title: 'No available appointments',
      description: 'There are no available appointments on the selected day.',
    },
  ],
});

interface Props extends React.ComponentProps<typeof Modal> {
  mode: BatchModalMode;
}

const BatchNoAvialableApptModal: FC<Props> = ({ mode, onClose, ...rest }) => {
  const { title, description } = UnfinishedApptEnum.mapLabels[mode];
  return (
    <Modal
      {...rest}
      isOpen
      isMaskCloseable={false}
      showCancelButton={false}
      className="moe-w-[480px]"
      confirmText="Got it"
      title={title}
      onConfirm={onClose}
      onClose={onClose}
    >
      <Text variant="small" className="moe-text-primary">
        {description}
      </Text>
    </Modal>
  );
};

export const useBatchNoAvialableApptModal = () => {
  return useModal(BatchNoAvialableApptModal);
};
