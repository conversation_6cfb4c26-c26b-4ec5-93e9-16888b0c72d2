import { useSelector } from 'amos';
import React, { type FC, type ReactNode, memo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';

interface DrivingContentProps {
  minutes: number | undefined;
  miles: number;
  prefix: ReactNode;
  suffix: ReactNode;
}
export const DrivingContent: FC<DrivingContentProps> = memo(({ minutes, miles, prefix, suffix }) => {
  const [business] = useSelector(selectCurrentBusiness);
  const formattedDistance = business.formatDistance(miles || 0);
  const distanceUnit = business.getDistanceUnit(formattedDistance).replace(/mile[s]?/g, 'mi');
  return (
    <>
      {prefix}
      <span>
        <span className="moe-font-bold">{minutes}</span> min,{' '}
      </span>
      <span>
        <span className="moe-font-bold">{formattedDistance}</span> {distanceUnit}{' '}
      </span>
      {suffix}
    </>
  );
});
