import { useStore } from 'amos';
import dayjs from 'dayjs';
import { useContext } from 'react';
import { smartSchedulingDataStore } from '../../../../store/smartScheduling/smartSchedulingStore.boxes';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { FullCalendarCtx } from '../AwesomeCalendar.utils';

export function useScrollSmartSchedulingCard() {
  const store = useStore();
  const calendar = useContext(FullCalendarCtx);
  const scrollToSSCard = useLatestCallback((delay = 0) => {
    const { isSmartScheduling, addingCard } = store.select(smartSchedulingDataStore);
    if (!isSmartScheduling || !addingCard) {
      return;
    }
    const staffId = addingCard.extendedProps.staffId;
    const time = dayjs(addingCard.start).subtract(90, 'minute');
    let timer: any = null;
    timer = setTimeout(() => {
      const staffTH = document.querySelector(`[data-resource-id="${staffId}"]`);
      staffTH?.scrollIntoView();
      calendar.current?.getApi().scrollToTime(time.format('HH:mm'));
    }, delay);
    return () => window.clearTimeout(timer);
  });

  return scrollToSSCard;
}
