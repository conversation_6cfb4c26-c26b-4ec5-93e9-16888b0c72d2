import classNames from 'classnames';
import React, { forwardRef, memo, useCallback } from 'react';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { Tooltip } from '../../../../../components/Popup/Tooltip';

type IconTextProps<T = undefined> = React.HTMLAttributes<HTMLDivElement> & {
  className?: string;
  title?: string;
  icon?: string;
  iconNode?: T;
  iconSize?: number;
  text: string;
  textClassName?: string;
  disabled?: boolean;
} & (T extends undefined ? { icon: string } : {});

export const IconText = memo(
  forwardRef<HTMLDivElement, IconTextProps>((props, ref) => {
    const { icon, text, title, className, iconSize = 20, textClassName, disabled, iconNode, ...divProps } = props;
    const getPopupContainer = useCallback(() => document.body, []);
    const child = (
      <div
        ref={ref}
        className={classNames(
          'moe-flex moe-flex-col moe-items-center moe-py-[8px] moe-bg-[#F7F8FA] moe-rounded-[8px] moe-gap-y-[4px] moe-cursor-pointer',
          {
            '!moe-cursor-not-allowed !moe-bg-[#F7F8FA]': disabled,
          },
          className,
        )}
        {...divProps}
        onClick={disabled ? undefined : props.onClick}
      >
        {iconNode || <SvgIcon src={icon!} size={iconSize} color={disabled ? '#ccc' : '#333'} className="item-icon" />}
        <div
          className={classNames(
            'moe-text-[12px] moe-font-[600] moe-leading-[18px] moe-text-[#333] moe-select-none',
            {
              '!moe-text-[#CCC]': disabled,
            },
            textClassName,
          )}
        >
          {text}
        </div>
      </div>
    );
    return title ? (
      <Tooltip overlay={title} getPopupContainer={getPopupContainer}>
        {child}
      </Tooltip>
    ) : (
      child
    );
  }),
) as <T = undefined>(props: IconTextProps<T>) => React.ReactElement;
