import { useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import SvgIconFrameSvg from '../../../../../assets/svg/icon-frame.svg';
import { CommonTestIds } from '../../../../../config/testIds/common';
import { PATH_GROOMING_CALENDAR } from '../../../../../router/paths';
import { selectBusinessCalendarConfig } from '../../../../../store/calendarLatest/calendar.selectors';
import { selectPricingPermission } from '../../../../../store/company/company.selectors';
import { META_DATA_KEY_LIST } from '../../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../../store/metadata/metadata.hooks';
import { type GroomingCommonBizOnBoarding } from '../../../../../store/metadata/metadata.types';
import { useRouteQueryV2 } from '../../../../../utils/RoutePath';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useRepeatSeriesLeaveConfirm } from '../../../../Appt/components/RepeatSeries/hooks/useRepeatSeriesLeaveConfirm';
import { SlotCalendarPopover } from '../SlotCalendar/SlotCalendarPopover/SlotCalendarPopover';
import { IconText } from './IconText';
import { SetupCalendarConfig } from './SetupCalendarConfig/SetupCalendarConfig';
import { useEnableMultiPetBySlotFeature } from '../SlotCalendar/hooks/useSlotCalendarFeature';

export interface CalendarConfigProps {
  className?: string;
}

export const CalendarConfig = memo(function CalendarConfig(props: CalendarConfigProps) {
  const { className } = props;
  const modalConfigVisible = useBool(false);
  const { openCalendarCustomization } = useRouteQueryV2(PATH_GROOMING_CALENDAR);
  const [{ magicWaitListMode }] = useSelector(selectBusinessCalendarConfig);
  const [data, setData] = useMetaData<GroomingCommonBizOnBoarding>(META_DATA_KEY_LIST.GroomingCommonBizOnBoarding);
  const enableCalendarSlot = useEnableMultiPetBySlotFeature();
  const [pricingPermission] = useSelector(selectPricingPermission());

  const { triggerLeave: handleClick } = useRepeatSeriesLeaveConfirm(() => {
    modalConfigVisible.open();
  });

  useEffect(() => {
    if (openCalendarCustomization) {
      modalConfigVisible.open();
    }
  }, [openCalendarCustomization]);

  return (
    <div className={className} data-testid={CommonTestIds.CalendarMiniConfig}>
      <SlotCalendarPopover
        isOpen={
          data?.hideCalendarSlotConfigTips === false && enableCalendarSlot && pricingPermission.enable.has('bookBySlot')
        }
        onOpenChange={() => {
          setData({
            ...data,
            hideCalendarSlotConfigTips: true,
          });
        }}
        title="Slot view is available on calendar now!"
        contentProps={{
          showCancelButton: false,
          confirmText: 'Check it out',
          onConfirm: () => {
            modalConfigVisible.open();
          },
        }}
        onLinkClick={() => {
          window.open('https://wiki.moego.pet/book-by-slot/', '_blank', 'noopener,noreferrer');
        }}
      >
        <div>
          <IconText
            icon={SvgIconFrameSvg}
            iconSize={18}
            text="Config"
            title="Calendar customization"
            className="moe-cursor-pointer"
            disabled={Boolean(magicWaitListMode)}
            onClick={handleClick}
          />
        </div>
      </SlotCalendarPopover>
      <SetupCalendarConfig visible={modalConfigVisible.value} onClose={modalConfigVisible.close} />
    </div>
  );
});
