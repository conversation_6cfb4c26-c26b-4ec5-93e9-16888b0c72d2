import { useSelector } from 'amos';
import React, { memo, type ReactNode } from 'react';
import SvgIconCloseSvg from '../../../../../../assets/svg/icon-close.svg';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { type GroomingInvoiceModel } from '../../../../../../store/grooming/grooming.boxes';
import { FlexBetween, MediumText14, OrderItemView, RegularText12, RemoveWrap } from '../InvoiceInfo.style';
import { AbsoluteSvgIcon } from './AddAndEdit.style';

interface OrderLineItemProps {
  lineItem: Pick<GroomingInvoiceModel['items'][number], 'id' | 'name' | 'quantity' | 'unitPrice'>;
  hideQuantity?: boolean;
  customPrice?: string;
  extra?: ReactNode;
  deletable?: boolean;
  onDelete?: () => void;
  deleteIconClassName?: string;
  className?: string;
}

export const OrderLineItem = memo<OrderLineItemProps>(
  ({ lineItem, hideQuantity, customPrice, extra, deletable, onDelete, deleteIconClassName = '', className = '' }) => {
    const [business] = useSelector(selectCurrentBusiness);

    return (
      <OrderItemView key={lineItem.id} className={className}>
        <FlexBetween>
          <div className="!moe-flex-1">
            <MediumText14 as="div" colorType="primary">
              {lineItem.name}
            </MediumText14>
            {!hideQuantity && (
              <RegularText12 as="div" colorType="tertiary">
                x{lineItem.quantity}
              </RegularText12>
            )}
          </div>
          <RemoveWrap>
            <MediumText14 colorType="primary">
              {customPrice || `${business.formatAmount(lineItem.unitPrice)}`}
            </MediumText14>
            {deletable ? (
              <AbsoluteSvgIcon className={deleteIconClassName} src={SvgIconCloseSvg} size={16} onClick={onDelete} />
            ) : null}
          </RemoveWrap>
        </FlexBetween>
        {extra}
      </OrderItemView>
    );
  },
);
