import classNames from 'classnames';
import React, { memo, type ReactNode } from 'react';
import IconPreAuthBadgePng from '../../../../../assets/icon/pre-auth-badge.png';
import { ImgIcon } from '../../../../../components/Icon/Icon';

interface HintTitleProps {
  className?: string;
  textClassName?: string;
  size?: 'small' | 'large';
  children: ReactNode;
}

export const HintTitle = memo<HintTitleProps>(({ className, size, textClassName, children }) => {
  return (
    <div className={classNames('!moe-flex !moe-items-start', className)}>
      <ImgIcon src={IconPreAuthBadgePng} width={24} className="badge-icon !moe-mr-[0px]" />
      <div
        className={classNames(
          '!moe-font-bold !moe-pl-[4px] !moe-text-[#333]',
          size === 'large' ? '!moe-text-[18px] !moe-leading-[22px]' : '!moe-text-sm',
          textClassName,
        )}
      >
        {children}
      </div>
    </div>
  );
});
