import { type PreAuthOnboardingSupportHints } from '../../../../../store/stripe/preAuth.boxes';

export type HintForPreAuthBase<
  Mode extends 'MoeGoPay' | 'NonMoeGoPay' | 'Both',
  Name extends PreAuthOnboardingSupportHints,
  NonMoeGoPayName extends PreAuthOnboardingSupportHints | undefined = undefined,
> = { className?: string; children?: React.ReactNode } & (Mode extends 'MoeGoPay'
  ? {
      mode: 'MoeGoPay';
      name: Name;
    }
  : Mode extends 'NonMoeGoPay'
    ? {
        mode: 'NonMoeGoPayName';
        nonMoeGoPayName: Name;
      }
    : {
        mode: 'Both';
        name: Name;
        nonMoeGoPayName: NonMoeGoPayName;
      });
