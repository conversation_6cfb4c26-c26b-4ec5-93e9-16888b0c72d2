import { Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import dayOfYear from 'dayjs/plugin/dayOfYear';
import { T_DAY } from 'monofile-utilities/lib/consts';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { lazyLoadDateHolidays } from '../../../../../utils/holidays';
import { useAsyncState } from '../../../../../utils/hooks/hooks';
import { lazyLoadCountryDataList } from '../../../../../utils/lazyLoad';
import { getHolidayList } from '../../../../settings/Settings/hooks/useHolidays';
import { ButtonHolidayView } from './ButtonHoliday.style';

dayjs.extend(dayOfYear);

export interface ButtonHolidayProps {
  currentDate: Dayjs;
}

export const ButtonHoliday = memo<ButtonHolidayProps>(({ currentDate }) => {
  const [year, setYear] = useState(dayjs().year());
  const [business] = useSelector(selectCurrentBusiness);
  const Holidays = useAsyncState(() => lazyLoadDateHolidays());
  const countryData = useAsyncState(() => lazyLoadCountryDataList());
  const holidays = useMemo(() => {
    if (Holidays && countryData) {
      const country =
        countryData.lookup.countries({
          name: business?.country || 'United States',
        })?.[0]?.alpha2 ?? 'US';
      return getHolidayList(country, year, Holidays.default);
    }
    return [];
  }, [year, Holidays, countryData, business?.country]);
  const name = useMemo(() => {
    const name: string[] = [];
    holidays.forEach((h) => {
      if (!h.substitute) {
        // h.start & h.end timezone related, h.date timezone unrelated
        // should use h.date here
        const startDate = dayjs(h.date);
        const endDate = startDate.clone().add(Math.ceil((h.end.valueOf() - h.start.valueOf()) / T_DAY) - 1, 'day');
        if (currentDate.dayOfYear() >= startDate.dayOfYear() && currentDate.dayOfYear() <= endDate.dayOfYear()) {
          name.push(h.name);
        }
      }
    });
    return name;
  }, [currentDate, Holidays, countryData, business?.country]);
  useEffect(() => {
    if (currentDate.year() !== year) {
      setYear(currentDate.year());
    }
  }, [currentDate]);
  return name.length ? (
    <div className="action-row-hr-top">
      <ButtonHolidayView className="moe-gap-y-[2px]">
        {name.map((i, index) => (
          <Tooltip key={index} content={i} backgroundTheme="light" side="top">
            <div className="moe-max-w-full moe-truncate">{i}</div>
          </Tooltip>
        ))}
      </ButtonHolidayView>
    </div>
  ) : null;
});
