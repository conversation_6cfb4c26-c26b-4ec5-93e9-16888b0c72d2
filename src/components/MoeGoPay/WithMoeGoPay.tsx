import { type ReactElement, memo } from 'react';
import { useIsMoeGoPay } from '../../utils/hooks/useIsMoeGoPay';

interface WithMoeGoPayProps {
  children?: ReactElement | null;
  nonMoeGoPayChildren?: ReactElement | null;
}

/**
 * @deprecated 有歧义，不要使用
 */
export const WithMoeGoPay = memo<WithMoeGoPayProps>(({ children, nonMoeGoPayChildren }) => {
  const isMoeGoPay = useIsMoeGoPay();
  if (isMoeGoPay) {
    return children || null;
  }

  return nonMoeGoPayChildren || null;
});
