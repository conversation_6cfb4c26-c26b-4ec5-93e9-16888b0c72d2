/*
 * @since 2020-09-09 10:47:37
 * <AUTHOR> <<EMAIL>>
 */

import styled from 'styled-components';

export const UploadPhotoViewView = styled.div`
  width: 100px;
  height: 100px;
  display: block;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;

  &.photo {
    width: 121px;
    height: 120px;
    border-radius: 2px;
    border: dashed 1px #9dadbd;
    > .mask {
      &.empty {
        background: rgba(0, 0, 0, 0);
      }
      color: #9b9b9b;
      > .anticon {
        font-size: 28px;
      }
    }
  }

  &.drag {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    border: dashed 1px #cdcdcd;

    > .mask {
      background: rgb(247 248 250 / 90%);
      &.empty {
        background: #f7f8fa;
      }
      color: #888c96;
      > .anticon {
        font-size: 28px;
      }
    }
  }

  > img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  > .mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.49);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all ease-in-out 200ms;
    opacity: 0;

    &.uploading,
    &:hover,
    &.empty {
      opacity: 1;
    }

    > .anticon {
      font-size: 24px;
    }
  }
`;
