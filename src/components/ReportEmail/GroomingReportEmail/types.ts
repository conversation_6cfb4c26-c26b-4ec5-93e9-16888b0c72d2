// 由于 openApi 无 enum 类型定义，这里手动写一份

import { QuestionType } from '@moego/api-web/moego/models/appointment/v1/daily_report_enums';
import { PetGender } from '@moego/api-web/moego/models/customer/v1/customer_pet_enums';
import { createEnum } from '@moego/finance-utils';

// 当前 rest 使用 string，未来 grpc 会统一为 number
/** question type */
export enum QuestionTypeString {
  UNSPECIFIED = 'unspecified',
  SINGLE_CHOICE = 'single_choice',
  MULTI_CHOICE = 'multi_choice',
  TEXT_INPUT = 'text_input',
  SHORT_TEXT_INPUT = 'short_text_input',
  TAG_CHOICE = 'tag_choice',
}

export function questionTypeStrToNum(type: string) {
  switch (type) {
    case QuestionTypeString.SINGLE_CHOICE:
      return QuestionType.SINGLE_CHOICE;
    case QuestionTypeString.MULTI_CHOICE:
      return QuestionType.MULTI_CHOICE;
    case QuestionTypeString.TEXT_INPUT:
      return QuestionType.TEXT_INPUT;
    case QuestionTypeString.SHORT_TEXT_INPUT:
      return QuestionType.SHORT_TEXT_INPUT;
    case QuestionTypeString.TAG_CHOICE:
      return QuestionType.TAG_CHOICE;
    default:
      return QuestionType.UNSPECIFIED;
  }
}

export function questionTypeNumToStr(type: QuestionType): string {
  switch (type) {
    case QuestionType.SINGLE_CHOICE:
      return QuestionTypeString.SINGLE_CHOICE;
    case QuestionType.MULTI_CHOICE:
      return QuestionTypeString.MULTI_CHOICE;
    case QuestionType.TEXT_INPUT:
      return QuestionTypeString.TEXT_INPUT;
    case QuestionType.SHORT_TEXT_INPUT:
      return QuestionTypeString.SHORT_TEXT_INPUT;
    case QuestionType.TAG_CHOICE:
      return QuestionTypeString.TAG_CHOICE;
    default:
      return QuestionTypeString.UNSPECIFIED;
  }
}

export const PetGenderType = createEnum({
  Unspecified: [PetGender.UNSPECIFIED, 'Unspecified'],
  Male: [PetGender.MALE, 'Male'],
  Female: [PetGender.FEMALE, 'Female'],
  Unknown: [PetGender.UNKNOWN, 'Unknown'],
});
