import { Hr } from '@react-email/hr';
import { Section } from '@react-email/section';
import { Text } from '@react-email/text';
import React, { memo, useMemo } from 'react';
import { CardTitleStyle, CommonTextStyle, MoodStyle } from '../GroomingReportEmail.style';
import { ReportCard } from './ReportCard';
import { QuestionCategory } from '@moego/api-web-v2/backend/proto/fulfillment/v1/fulfillment_report';
import { ReportSettingDefaultQuestionType } from '../../../../container/settings/Settings/ReportCardSetting/TemplateSetting/TemplateSetting.types';
import { type FulfillmentReportQuestion } from '@moego/bff-openapi/clients/client.fulfillment';

export interface ReportFeedbackProps {
  feedbacks?: Array<FulfillmentReportQuestion>;
  isTemplate?: boolean;
}

export const ReportFeedback = memo(({ feedbacks, isTemplate = false }: ReportFeedbackProps) => {
  const feedbackQuestions = feedbacks?.filter((feedback) => feedback.category === QuestionCategory.FEEDBACK);
  const overallFeedback = feedbackQuestions?.find(
    (feedback) => feedback.key === ReportSettingDefaultQuestionType.HowDidItGo,
  );
  const additionalNote = feedbackQuestions?.find(
    (feedback) => feedback.key === ReportSettingDefaultQuestionType.AdditionalNote,
  );
  const mood = feedbackQuestions?.find((feedback) => feedback.key === ReportSettingDefaultQuestionType.Mood);
  const additionalNoteTextList = useMemo(() => {
    if (additionalNote?.inputText) {
      return additionalNote.inputText.split('\n');
    }
    return [];
  }, [additionalNote]);
  let moodText = '';
  if (isTemplate) {
    // TODO:
    // settings 模板场景要读取 options，而非 choices
    // 理论上该组件仅负责渲染，由外部做逻辑处理，props 不应接收整个 feedback，而使用处理好后的 mood 等数据
    moodText = mood?.options?.map((item) => `#${item}`).join(' ') || '';
  } else {
    // 兼容处理，新数据选择 customized 的选项，会出现在 choices 中，而旧数据不会
    const choices = [...new Set([...(mood?.choices || []), ...(mood?.customOptions || [])])];
    moodText = choices.map((item) => `#${item}`).join(' ') || '';
  }
  return (
    <ReportCard title={overallFeedback?.title || 'Overall feedback'}>
      <Section>
        <Text
          style={{
            ...CommonTextStyle,
            marginTop: 8,
          }}
        >
          {overallFeedback?.choices?.[0] || overallFeedback?.options?.[0]}
        </Text>
        <Hr
          style={{
            marginTop: 16,
            marginBottom: 16,
          }}
        />
        <Text
          style={{
            ...CardTitleStyle,
            marginBottom: 8,
          }}
        >
          {additionalNote?.title || 'Additional note'}
        </Text>
        {additionalNoteTextList.map((text, index) => (
          <Text
            key={index}
            style={{
              ...CommonTextStyle,
              marginTop: 2,
            }}
          >
            {text}
          </Text>
        ))}
        <Text style={MoodStyle}>{mood?.title || 'Mood'}</Text>
        <Text
          style={{
            ...CommonTextStyle,
            marginTop: 4,
          }}
        >
          {moodText}
        </Text>
      </Section>
    </ReportCard>
  );
});
