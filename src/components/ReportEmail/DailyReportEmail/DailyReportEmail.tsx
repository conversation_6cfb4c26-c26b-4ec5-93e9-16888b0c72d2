import { Container } from '@react-email/container';
import React from 'react';
import { ReportShowcase } from './components/ReportShowcase';
import { ReportFeedback, type FeedbackItem } from '../components/ReportFeedback';
import { ReportFooter } from './components/ReportFooter';
import { TableGlobalStyle } from './DailyReportEmail.style';
import { ReportHeader } from '../components/ReportHeader';

interface Showcase {
  images?: string[];
  videoLink?: string;
}

export interface DailyReportEmailProps {
  petName?: string;
  reportTitle?: string;
  businessAvatarPath?: string;
  showcase?: Showcase;
  overallFeedbacks?: FeedbackItem[];
  customizedFeedbacks?: FeedbackItem[];
  thankYouMessage?: string;
  businessName?: string;

  themeColor?: string;
  lightThemeColor?: string;
}

export const DailyReportEmail = ({
  petName,
  reportTitle,
  businessAvatarPath,
  showcase,
  overallFeedbacks,
  customizedFeedbacks,
  thankYouMessage,
  businessName,
  themeColor,
  lightThemeColor,
}: DailyReportEmailProps) => {
  // 后端下发的数据有可能为空字符串
  themeColor = themeColor || '#F96B18';
  lightThemeColor = lightThemeColor || '#FEF0E8';
  return (
    <Container
      style={{
        ...TableGlobalStyle,
        maxWidth: '600px', // override the default maxWidth
        minWidth: '600px', // Gmail
        width: '600px', // Outlook / QQ Mail / Apple Mail
        padding: '40px 28px',
        backgroundColor: lightThemeColor,
      }}
    >
      <ReportHeader
        petName={petName ?? 'Pet'}
        reportTitle={reportTitle ?? 'Daily Report'}
        avatarPath={businessAvatarPath}
        imgSize={80}
        style={{ marginBottom: 24 }}
      />

      {showcase && (showcase.images || showcase.videoLink) && (
        <ReportShowcase images={showcase.images} videoLink={showcase.videoLink} themeColor={themeColor} />
      )}

      {overallFeedbacks && overallFeedbacks.length > 0 && <ReportFeedback feedbacks={overallFeedbacks} />}

      {customizedFeedbacks && customizedFeedbacks.length > 0 && <ReportFeedback feedbacks={customizedFeedbacks} />}

      <ReportFooter businessName={businessName} thankYouMessage={thankYouMessage} />
    </Container>
  );
};
