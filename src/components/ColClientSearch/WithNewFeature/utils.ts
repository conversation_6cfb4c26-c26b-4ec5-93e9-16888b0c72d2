import { META_DATA_KEY_LIST } from '../../../store/metadata/metadata.config';

export type NewFeatureConfig = {
  metadataKey: (typeof META_DATA_KEY_LIST)['values'][number];
  // The custom React.Context to be used by the `NewFeatureCustomScope`. Create it with `createNewFeatureContext`.
  customContext?: boolean;
} & (
  | ({
      parseValue: (value: any) => { isNew: boolean; isDismissed: boolean };
    } & (
      | {
          getDismissedValue: (currentValue: any, ...args: any[]) => any;
          dismiss?: undefined;
        }
      | {
          getDismissedValue?: undefined;
          dismiss: (...args: any[]) => Promise<void> | void;
        }
    ))
  | {
      parseValue?: undefined;
      getDismissedValue?: undefined;
      dismiss?: undefined;
    }
);

export const defaultParseValue = (value: any) => {
  return {
    isNew: true,
    isDismissed: value,
  };
};

// Variable with concrete type, so that we can export FeatureKey type later.
const newFeatureConfigMapInner = {
  payOnlineMessageTemplate: {
    metadataKey: META_DATA_KEY_LIST.NFPayOnlineMessageTemplateDismissed,
    customContext: true,
    parseValue: ({ dismissed, newBefore }: { dismissed: boolean; newBefore: number }) => {
      const isNew = Date.now() <= newBefore;
      return {
        isNew,
        isDismissed: dismissed,
      };
    },
    getDismissedValue: ({ newBefore }: { dismissed: boolean; newBefore: number }) => {
      return {
        dismissed: true,
        newBefore,
      };
    },
  },
  onlineBookingPaymentForCertainClients: {
    metadataKey: META_DATA_KEY_LIST.NFOnlineBookingPaymentForCertainClients,
  },
  salesNetIncomeReport: {
    metadataKey: META_DATA_KEY_LIST.NFSalesNetIncomeReportDismissed,
  },
  quickbookV2Dismissed: {
    metadataKey: META_DATA_KEY_LIST.NFQuickBookV2Dismissed,
  },
  financeDismissed: {
    metadataKey: META_DATA_KEY_LIST.NFFinanceDismissed,
    parseValue: (value: { dismissed: boolean; newBefore: number }) => {
      const { dismissed, newBefore } = value || {};
      return {
        isNew: true,
        isDismissed: dismissed,
        newBefore,
      };
    },
    getDismissedValue: ({ newBefore }: { dismissed: boolean; newBefore: number }) => {
      return {
        dismissed: true,
        newBefore,
      };
    },
  },
  financeSwitchLocationCheckDismissed: {
    metadataKey: META_DATA_KEY_LIST.FinanceSwitchLocationCheckDismissed,
  },
} satisfies Record<string, NewFeatureConfig>;

export type FeatureKey = keyof typeof newFeatureConfigMapInner;

// Relaxing value type to NewFeatureConfig, but restrict key type to FeatureKey
export const newFeatureConfigMap: Record<FeatureKey, NewFeatureConfig> = newFeatureConfigMapInner;
