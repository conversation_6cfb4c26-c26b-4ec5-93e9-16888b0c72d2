import { TimePicker as MoeGoTimePicker, type TimePickerProps as MoeGoTimePickerProps } from '@moego/ui';
import dayjs, { type Dayjs } from 'dayjs';
import { isUndefined } from 'lodash';
import React, { memo, useMemo } from 'react';
import { useControllableValue } from '../../utils/hooks/useControlledValue';

export type TimePickerProps = Omit<
  MoeGoTimePickerProps,
  'locale' | 'generateConfig' | 'value' | 'onChange' | 'disabledTimes' | 'defaultValue'
> & {
  defaultValue?: number;
  value?: number;
  onChange?: (value: number) => void;
  disabledTimes?: (value: number) => boolean;
};

/**
 * 入参和出参都是 number
 */
export const TimePicker = memo(function TimePicker(props: TimePickerProps) {
  const { disabledTimes, defaultValue } = props;
  const [value, setValue] = useControllableValue<number | undefined>(props);

  const innerValue = useMemo(() => {
    return isUndefined(value) ? undefined : dayjs().setMinutes(value);
  }, [value]);

  const innerDefaultValue = useMemo(() => {
    return isUndefined(defaultValue) ? undefined : dayjs().setMinutes(defaultValue);
  }, [defaultValue]);

  const handleDisabledTimes = (v: Dayjs) => {
    return disabledTimes?.(v.hour() * 60 + v.minute()) ?? false;
  };

  return (
    <MoeGoTimePicker
      {...props}
      defaultValue={innerDefaultValue}
      value={innerValue}
      disabledTimes={handleDisabledTimes}
      onChange={(v) => {
        if (isUndefined(v)) {
          return setValue(undefined);
        } else if (v) {
          return setValue(v?.hour() * 60 + v?.minute());
        }
      }}
    />
  );
});
