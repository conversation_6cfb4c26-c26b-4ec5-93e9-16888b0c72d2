import styled from 'styled-components';

export const InputBox = styled.div<{ disabled?: boolean }>`
  position: relative;
  display: flex;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #dee1e6;
  margin-bottom: 12px;
  background-color: ${(props) => (props.disabled ? '#F7F8FA' : '#fff')};

  .ant-input-number {
    flex: 1;
    width: auto;
    border: 1px solid transparent;

    &:hover {
      border-color: ${(props) => (props.disabled ? 'transparent' : '#DE5A21')};
    }

    margin-bottom: 0;

    .ant-input-number-handler-wrap {
      display: none;
    }

    .ant-input-number-input {
      height: 28px;
    }
  }

  .addon-after {
    position: absolute;
    right: 0;
    top: 1px;
    bottom: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 8px;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: ${(props) => (props.disabled ? '#999' : '#333')};
    border-left: 1px solid #e6e6e6;
  }

  .ant-input-number-disabled {
    background: #f7f8fa;
  }
`;
