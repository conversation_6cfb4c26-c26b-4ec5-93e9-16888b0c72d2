/*
 * @since 2020-11-18 14:20:47
 * <AUTHOR> <<EMAIL>>
 */

import React, { type ComponentProps, type ComponentType, type ElementRef, useEffect, useRef } from 'react';
import { type MutableRef, forwardRef, useMergeRef } from '../../utils/react';

export type AutoFocusProps<T extends ComponentType<any>> = {
  component: T;
  autoFocus: boolean;
} & ComponentProps<T>;

export const AutoFocus = forwardRef(function AutoFocus<T extends ComponentType<any>>(
  { component: Component, autoFocus, children, ...props }: AutoFocusProps<T>,
  ref: MutableRef<ElementRef<T>> = null,
) {
  const innerRef = useRef<any>();
  useEffect(() => {
    autoFocus && innerRef.current?.focus?.();
  }, [autoFocus]);
  return (
    <Component {...props} ref={useMergeRef(ref, innerRef)}>
      {children}
    </Component>
  );
});
