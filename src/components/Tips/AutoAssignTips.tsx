import classNames from 'classnames';
import { isNumber } from 'lodash';
import React, { memo } from 'react';
import SvgAutoAssignSvg from '../../assets/svg/auto-assign.svg';
import { type AutoAssign } from '../../store/createTicket/ticket.utils';
import { isNormal } from '../../store/utils/identifier';
import { SvgIcon } from '../Icon/Icon';

export interface AutoAssignTipsProps {
  autoAssign: AutoAssign;
  className?: string;
}
export const AutoAssignTips = memo<AutoAssignTipsProps>(({ autoAssign, className }) => {
  const { staffId, appointmentTime } = autoAssign || {};
  const staffStr = isNormal(staffId) ? 'staff' : '';
  const timeStr = isNumber(appointmentTime) ? 'service time' : '';
  const desc = [staffStr, timeStr].filter(Boolean).join(' and ') || 'some fields';
  const pronoun = [staffStr, timeStr].filter(Boolean).length === 1 ? 'it' : 'them';

  return (
    <div
      className={classNames(
        'moe-sticky moe-top-0 moe-py-[10px] moe-px-[16px] moe-bg-white moe-z-[99] moe-flex moe-flex-1 moe-items-start',
        className,
      )}
      style={{
        background: 'linear-gradient(92deg, #96ecff -18.24%, #febdff 149.2%)',
      }}
    >
      <SvgIcon src={SvgAutoAssignSvg} size={24} color="#9c68ff" />
      <div className="moe-ml-[8px]">
        The system auto-assigned <span className="moe-font-bold">{desc}</span> to this booking request, but you can
        re-assign {pronoun} as needed.
      </div>
    </div>
  );
});
