import { ByteUnit } from '../../../../utils/common';

export interface SendImageModalBaseProps {
  visible: boolean;
  onSend: () => void;
  onClose: () => void;
}

// multiple images 发一次需要消耗 3 条 sms 额度
export const MULTIPLE_IMAGES_COST_SMS_COUNT = 3;

// multiple images 一次最多发 5 张
export const MULTIPLE_IMAGES_MAX_COUNT = 5;

// multiple images 一张最大限制 5M
export const MULTIPLE_IMAGES_MAX_SIZE_PER_IMAGE = 5 * ByteUnit.MB;

// single image 一张最大限制 5M
export const SINGLE_IMAGE_MAX_SIZE_PER_IMAGE = 5 * ByteUnit.MB;

export interface PicInfo {
  url: string;
  fileSize: number;
}
