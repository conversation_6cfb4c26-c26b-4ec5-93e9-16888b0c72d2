import { useSelector } from 'amos';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { petMapBox } from '../../../store/pet/pet.boxes';
import { selectOnlineBookingLatestPreference } from '../../../store/onlineBooking/onlineBookingPreference.selectors';
import { queryFulfillmentReport } from '../../../query/fulfillment/report';
import { selectApptStartAndEndTime, selectMainServiceInAppt } from '../../../container/Appt/store/appt.selectors';
import { useEffect, useMemo } from 'react';
import { CareCategory, type GetFulfillmentReportResponse } from '@moego/bff-openapi/clients/client.fulfillment';
import dayjs from 'dayjs';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';

export const usePetAndBusiness = (petId: number | string) => {
  const [business, pet, onlineBookingLatestPreference] = useSelector(
    selectCurrentBusiness,
    petMapBox.mustGetItem(+petId!),
    selectOnlineBookingLatestPreference,
  );
  return {
    petInfo: {
      petId: String(pet.petId),
      petName: pet.petName,
      avatarPath: pet.avatarPath,
      petType: pet.petTypeId,
      petBreed: pet.breed,
      gender: pet.gender,
      weight: pet.weight,
      weightWithUnit: `${pet.weight} ${business.weightUnit}`,
    },
    businessInfo: {
      ...business.toJSON(),
      businessId: String(business.id),
      bookOnlineName: onlineBookingLatestPreference.bookOnlineName,
      bookOnlineEnable: onlineBookingLatestPreference.bookingEnable,
    },
  };
};

export const useServiceDate = (appointmentId: string) => {
  const [mainService, apptDates] = useSelector(
    selectMainServiceInAppt(appointmentId),
    selectApptStartAndEndTime(appointmentId),
  );
  const isBoarding = mainService.serviceItemType === ServiceItemType.BOARDING;
  const { startDateTime, endDateTime } = apptDates;

  const startTime = startDateTime?.valueOf();
  const endTime = endDateTime?.valueOf();

  /**
   * @description 假设service时间是6.1-6.3，如果是当天是5.30，那么默认选中6.1
   *              如果当天是6.4 那么默认选中6.3
   *              如果当天是6.2 就是6.2
   */
  const serviceDate = useMemo(() => {
    if (!startTime || !endTime) {
      return;
    }

    let reportDate = startTime;

    if (isBoarding) {
      const now = Date.now();
      reportDate = now < startTime ? startTime : now > endTime ? endTime : now;
    }

    // Daycare 返回 date 当天
    return dayjs(reportDate);
  }, [isBoarding, startTime, endTime]);

  return serviceDate;
};

interface DailyReportByApptAndPetParams {
  appointmentId: string;
  petId: string;
  onSuccess?: (data: GetFulfillmentReportResponse) => void;
}

export const useDailyReportByApptAndPetId = ({ appointmentId, petId, onSuccess }: DailyReportByApptAndPetParams) => {
  const [mainService] = useSelector(selectMainServiceInAppt(appointmentId));
  const isBoarding = mainService.serviceItemType === ServiceItemType.BOARDING;

  const serviceDate = useServiceDate(appointmentId);
  const { data, isSuccess } = queryFulfillmentReport.report.useQuery(
    {
      appointmentId,
      petId,
      careType: isBoarding ? CareCategory.BOARDING : CareCategory.DAYCARE,
      serviceDate: serviceDate?.format(DATE_FORMAT_EXCHANGE),
    },
    { enabled: !!serviceDate, cacheTime: 0 },
  );

  useEffect(() => {
    if (isSuccess && data?.fulfillmentReport) {
      onSuccess?.(data);
    }
  }, [isSuccess]);
};
