import { ServiceOverrideType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Text, Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import classNames from 'classnames';
import React from 'react';
import IconMoneySvg from '../../../../assets/svg/icon-money.svg';
import { ApptTestIds } from '../../../../config/testIds/apptDrawer';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { Condition } from '../../../Condition';
import { SvgIcon } from '../../../Icon/Icon';
import { type TagServicePriceProps, getTagServiceTooltips } from './TagService.utils';

export function TagServicePrice(props: TagServicePriceProps) {
  const {
    hiddenIcon = false,
    className,
    price,
    iconSize = 14,
    overrideType = ServiceOverrideType.UNSPECIFIED,
    iconClassName,
    txtClassName,
    defaultColor = 'moe-text-secondary',
    petIds,
  } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const priceFmt = typeof price === 'number' ? business.formatAmount(price) : price;

  const isMultiplePets = (petIds?.length ?? 0) > 1;

  const { isOverridden, overriddenClassName, tooltipContent } = getTagServiceTooltips({
    target: 'price',
    overrideType,
    defaultColor,
  });

  return (
    <div
      className={classNames(
        className,
        defaultColor,
        'moe-flex moe-items-center moe-flex-nowrap moe-gap-x-[4px] moe-text-sm moe-font-medium moe-cursor-default',
      )}
    >
      <Condition if={!hiddenIcon}>
        <SvgIcon
          src={IconMoneySvg}
          size={iconSize}
          className={classNames(!isMultiplePets && overriddenClassName, iconClassName)}
        />
      </Condition>
      <Tooltip isDisabled={!isOverridden || isMultiplePets} content={tooltipContent}>
        <Text
          variant="small"
          className={cn(txtClassName, !isMultiplePets && overriddenClassName)}
          data-testid={ApptTestIds.ApptServicePrice}
        >
          {isOverridden && isMultiplePets ? 'Varies by pet' : priceFmt}
        </Text>
      </Tooltip>
    </div>
  );
}
