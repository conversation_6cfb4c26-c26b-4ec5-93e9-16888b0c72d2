import styled from 'styled-components';

export const ClientTagView = styled.span`
  font-size: 13px;
  font-weight: 600;
  color: #101928;
  padding: 2px 8px;
  border-radius: 2px;
  background-color: rgba(94, 114, 228, 0.1);
  margin-left: 8px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  max-width: 140px;
  text-overflow: ellipsis;

  &:first-child {
    margin-left: 0;
  }
`;

export const ClientTagGroupView = styled.div`
  margin-left: 10px;

  .icon-more {
    margin-left: 10px;
    font-size: 16px;
    position: relative;
    top: -5px;
    cursor: pointer;
  }
`;
