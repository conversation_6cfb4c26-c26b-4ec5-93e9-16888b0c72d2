import { type SuggestedAddress } from '@moego/api-web/moego/models/map/v1/map_models';
import { MinorLocationOutlined } from '@moego/icons-react';
import { AutoComplete, type AutoCompleteProps } from '@moego/ui';
import React, { useState } from 'react';
import { uniqBy } from '../../store/utils/utils';
import { googleMaps } from '../../utils/GoogleMaps';
import { useDebounceCallback } from '../../utils/hooks/useDebounceCallback';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { memoForwardRef } from '../../utils/react';

export interface AddressOption {
  value: string;
  address?: SuggestedAddress;
  description?: string;
  title?: string;
}

export interface AddressInputV3Props extends Omit<AutoCompleteProps<AddressOption>, 'children'> {
  onSelectAddress?: (address: SuggestedAddress) => void;
  onInputChange?: (value: string) => void;
}

export const AddressInputV3 = memoForwardRef<HTMLInputElement, AddressInputV3Props>(function AddressInputV3(
  { onInputChange, onSelectAddress, onSelect, ...rest }: AddressInputV3Props,
  ref,
) {
  const [items, setItems] = useState<AddressOption[]>([]);

  const fetchItems = useSerialCallback((value: string) => {
    if (!value?.trim()) return;
    return googleMaps
      .getAddressSuggestions(value)
      .then((res) => {
        const items = res
          .map((address): AddressOption => {
            const mainText = address.additional?.mainText || '';
            return {
              value: mainText,
              title: mainText,
              description: address.formattedAddress,
              address,
            };
          })
          .filter(uniqBy('value'));
        setItems(items);
      })
      .catch(() => {
        setItems([]);
      });
  });

  const debouncedFetchItems = useDebounceCallback(fetchItems, 300);

  const handleSearch = (value: string) => {
    debouncedFetchItems(value);
    onInputChange?.(value);
  };

  const handleSelect = ((key: string, textValue: string) => {
    onSelect?.(key, textValue);
    const item = items.find((item) => item.value === key);
    onSelectAddress?.(item!.address!);
  }) as AddressInputV3Props['onSelect'];

  return (
    <AutoComplete
      ref={ref}
      suffix={<MinorLocationOutlined />}
      filter={null}
      {...rest}
      items={items}
      isLoading={fetchItems.isBusy()}
      onInputChange={handleSearch}
      onSelect={handleSelect}
    >
      {(item) => <AutoComplete.Item key={item.value} description={item.description} title={item.title} />}
    </AutoComplete>
  );
});
