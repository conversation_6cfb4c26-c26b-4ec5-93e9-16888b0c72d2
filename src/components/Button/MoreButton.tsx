import classNames from 'classnames';
import React from 'react';
import IconThreeDots from '../../assets/svg/icon-point-three.svg';
import { SvgIcon } from '../Icon/Icon';

export interface MoreButtonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  disabled?: boolean;
  size?: number;
}

export const MoreButton: React.FC<MoreButtonProps> = (props) => {
  const { disabled, size = 24, className, style, ...rest } = props;

  return (
    <div
      className={classNames(
        `moe-flex moe-justify-center moe-items-center moe-border moe-border-solid moe-border-[#CDCDCD] moe-rounded-full`,
        {
          'moe-cursor-pointer': !disabled,
          'moe-cursor-not-allowed': disabled,
        },
        className,
      )}
      style={{
        width: `${size}px`,
        height: `${size}px`,
        ...style,
      }}
      {...rest}
    >
      <SvgIcon src={IconThreeDots} size={24} className="moe-text-[#666]" />
    </div>
  );
};
