import React, { useEffect, useRef, useCallback } from 'react';
import { EditorView, basicSetup } from 'codemirror';
import { EditorState } from '@codemirror/state';
import { html } from '@codemirror/lang-html';
import { oneDark } from '@codemirror/theme-one-dark';
import { lintGutter, lintKeymap, linter } from '@codemirror/lint';
import { type Diagnostic } from '@codemirror/lint';
import { keymap, placeholder as placeholderExtension } from '@codemirror/view';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  onLintChange?: (hasErrors: boolean, diagnostics: Diagnostic[]) => void;
  placeholder?: string;
  className?: string;
  maxLength?: number;
}

// 轻量级语法检查函数
const createLinter = (onLintChange?: (hasErrors: boolean, diagnostics: Diagnostic[]) => void) => {
  return (view: EditorView) => {
    const diagnostics: Diagnostic[] = [];
    const content = view.state.doc.toString();

    // 检查基本的 HTML 标签匹配
    const scriptOpenMatches = [...content.matchAll(/<script[^>]*>/gi)];
    const scriptCloseMatches = [...content.matchAll(/<\/script\s*>/gi)];

    if (scriptOpenMatches.length !== scriptCloseMatches.length) {
      const errorPosition =
        scriptOpenMatches.length > scriptCloseMatches.length
          ? scriptOpenMatches[scriptOpenMatches.length - 1].index
          : scriptCloseMatches[0].index;

      diagnostics.push({
        from: errorPosition,
        to: errorPosition + 8,
        severity: 'error',
        message: `Mismatched script tags: ${scriptOpenMatches.length > scriptCloseMatches.length ? 'missing closing' : 'missing opening'} tag`,
      });
    }

    // 检查 JavaScript 语法 - 使用更严格的正则表达式来匹配 script 标签
    // 匹配开始标签：<script 后跟任意属性，然后是 >
    // 匹配结束标签：</script 后跟可选的空格和 >
    const jsRegex = /<script[^>]*>([\s\S]*?)<\/script\s*>/gi;
    let jsMatch;
    while ((jsMatch = jsRegex.exec(content)) !== null) {
      const jsCode = jsMatch[1];
      if (jsCode.trim()) {
        try {
          // eslint-disable-next-line sonarjs/code-eval
          new Function(jsCode);
        } catch (error) {
          const start = jsMatch.index + jsMatch[0].indexOf(jsCode);
          diagnostics.push({
            from: start,
            to: start + jsCode.length,
            severity: 'error',
            message: `JavaScript syntax error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          });
        }
      }
    }

    // 调用回调函数传递 lint 状态
    const hasErrors = diagnostics.some((d) => d.severity === 'error');
    onLintChange?.(hasErrors, diagnostics);

    return diagnostics;
  };
};

export const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  onLintChange,
  placeholder = '',
  className = '',
  maxLength = 10000,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const editorViewRef = useRef<EditorView | null>(null);

  const handleContentChange = useCallback(
    (newValue: string) => {
      onChange(newValue);
    },
    [onChange],
  );

  useEffect(() => {
    if (!editorRef.current) return;

    const state = EditorState.create({
      doc: value,
      extensions: [
        basicSetup,
        html(), // 使用 HTML 语言支持，它会自动处理 HTML、CSS 和 JavaScript
        oneDark,
        lintGutter(),
        keymap.of(lintKeymap),
        placeholderExtension(placeholder),
        EditorView.updateListener.of((update: any) => {
          if (update.docChanged) {
            const newValue = update.state.doc.toString();
            handleContentChange(newValue);
          }
        }),
        EditorView.theme({
          '&': {
            fontSize: '14px',
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            minHeight: '300px', // 添加默认最小高度
            height: '100%', // 确保编辑器撑满容器
          },
          '.cm-scroller': {
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            minHeight: '300px', // 确保滚动区域也有最小高度
          },
          '.cm-content': {
            padding: '8px 0',
          },
          '.cm-line': {
            padding: '0px',
          },
          '.cm-placeholder': {
            color: '#999',
            fontStyle: 'italic',
            backgroundColor: '#282c34',
          },
          '.cm-lintRange': {
            background: 'rgba(255, 0, 0, 0.1)',
            borderBottom: '2px solid #ff0000',
          },
          '.cm-lintRange-error': {
            background: 'rgba(255, 0, 0, 0.1)',
            borderBottom: '2px solid #ff0000',
          },
          '.cm-lintRange-warning': {
            background: 'rgba(255, 165, 0, 0.1)',
            borderBottom: '2px solid #ffa500',
          },
          '.cm-gutters': {
            backgroundColor: '#282c34',
          },
          '.cm-lintGutter': {
            width: '20px',
          },
          '.cm-activeLineGutter': {
            backgroundColor: '#282c34 !important',
          },
          '.cm-lintGutterElement': {
            padding: '0 2px',
            fontSize: '12px',
          },
          '.cm-lintGutterElement-error': {
            color: '#ff0000',
          },
          '.cm-lintGutterElement-warning': {
            color: '#ffa500',
          },
        }),
        linter(createLinter(onLintChange)),
      ],
    });

    const view = new EditorView({
      state,
      parent: editorRef.current,
    });

    editorViewRef.current = view;

    return () => {
      view.destroy();
    };
  }, [onLintChange, handleContentChange]);

  // 更新编辑器内容
  useEffect(() => {
    if (editorViewRef.current && value !== editorViewRef.current.state.doc.toString()) {
      const transaction = editorViewRef.current.state.update({
        changes: {
          from: 0,
          to: editorViewRef.current.state.doc.length,
          insert: value,
        },
      });
      editorViewRef.current.dispatch(transaction);
    }
  }, [value]);

  return (
    <div className={`moe-relative moe-h-full moe-min-h-[300px] ${className}`}>
      <div ref={editorRef} className="moe-h-full moe-min-h-[300px] moe-overflow-y-auto" />
      <div
        className={`moe-absolute moe-bottom-2 moe-right-2 moe-text-xs moe-bg-white moe-px-2 moe-py-1 moe-rounded-1 ${
          value.length > maxLength ? 'moe-text-danger' : 'moe-text-gray-500'
        }`}
      >
        {value.length}/{maxLength}
        {value.length > maxLength && <span className="moe-ml-1 moe-text-red-500">⚠️</span>}
      </div>
    </div>
  );
};
