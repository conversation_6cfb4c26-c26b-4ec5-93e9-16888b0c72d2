import { type CustomerRecord } from '../../store/customer/customer.boxes';
import { type PetRecord } from '../../store/pet/pet.boxes';

export interface RowRecord {
  customer: CustomerRecord;
  petList: PetRecord[];
}

export type FieldName =
  | 'avatar'
  | 'clientName'
  | 'clientTags'
  | 'pets'
  | 'contact'
  | 'totalPaid'
  | 'totalBookings'
  | 'upcomingBooking'
  | 'lastService'
  | 'expectedService'
  | 'overdue';
