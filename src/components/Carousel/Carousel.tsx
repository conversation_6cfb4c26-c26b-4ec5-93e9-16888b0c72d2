import { cn } from '@moego/ui';
import React, { useEffect, useRef } from 'react';
import { useSetState } from 'react-use';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { CarouselArrow } from './CarouselArrow';

interface State {
  scrollLeft: number;
  scrollWidth: number;
  offsetWidth: number;
  perWidth: number;
}

export interface CarouselProps {
  className?: string;
  columnGap?: number;
}

export const Carousel = function Carousel(props: React.PropsWithChildren<CarouselProps>) {
  const { className, children, columnGap = 24 } = props;
  const ref = useRef<HTMLDivElement | null>(null);
  const [{ scrollLeft, scrollWidth, offsetWidth, perWidth }, setState] = useSetState<State>({
    scrollLeft: 0,
    scrollWidth: 0,
    offsetWidth: 0,
    perWidth: 0,
  });
  const isOverflown = scrollWidth > offsetWidth;

  useEffect(() => {
    const divEle = ref.current;
    if (!divEle) return;
    const childEle = divEle.firstElementChild as HTMLDivElement;
    if (!childEle) return;

    setState({
      scrollLeft: divEle.scrollLeft,
      scrollWidth: divEle.scrollWidth,
      offsetWidth: divEle.offsetWidth,
      perWidth: childEle.offsetWidth,
    });
  }, []);

  const showLeftArrow = isOverflown && scrollLeft > 0;
  const showRightArrow = isOverflown && scrollLeft + offsetWidth < scrollWidth;

  const swapCard = useLatestCallback((go2Next: boolean) => {
    const divEle = ref.current;
    if (!divEle) return;
    const { scrollLeft } = divEle;
    if (go2Next) {
      const nextScrollLeft = scrollLeft + perWidth;
      setState({
        scrollLeft: nextScrollLeft,
      });
    } else {
      const nextScrollLeft = scrollLeft - perWidth;
      setState({
        scrollLeft: Math.max(0, nextScrollLeft),
      });
    }
  });

  useEffect(() => {
    const divEle = ref.current;
    if (!divEle) return;
    divEle.scrollLeft = scrollLeft;
  }, [scrollLeft]);

  return (
    <div className={cn('moe-relative moe-z-0', className)}>
      {showLeftArrow && (
        <CarouselArrow
          className="moe-absolute moe-top-0 moe-bottom-0 moe-left-0 moe-z-[1]"
          reverse
          onClick={() => swapCard(false)}
        />
      )}
      <div
        className="moe-flex moe-items-center moe-overflow-x-hidden moe-scroll-smooth"
        style={{ columnGap }}
        ref={ref}
      >
        {children}
      </div>
      {showRightArrow && (
        <CarouselArrow
          className="moe-absolute moe-top-0 moe-bottom-0 moe-right-0 moe-z-[1]"
          onClick={() => swapCard(true)}
        />
      )}
    </div>
  );
};
