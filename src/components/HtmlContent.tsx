import { safeHtml } from 'html5parser';
import { type SafeHtmlOptions } from 'html5parser/src/safeHtml';
import React, { memo, useEffect, useMemo, useRef } from 'react';

interface ProcessContentOptions extends Partial<SafeHtmlOptions> {
  replaceBr?: boolean;
}

const reFindNewline = /<p>/g;
const reReplaceNewline = /(\r\n)|(\n\r)|(\r)|(\n)/g; // To match the \r\n, \n, \n\r and \r
const processContent = (raw: string, options?: ProcessContentOptions) => {
  const { replaceBr = true, ...restOptions } = options || {};
  const context = safeHtml(raw, restOptions);
  if (replaceBr && context.match(reFindNewline) === null) {
    return context.replace(reReplaceNewline, '<br/>');
  }
  return context;
};

interface Props {
  content: string;
  className?: string;
  options?: ProcessContentOptions;
}

export const HtmlContent = memo<Props>(({ content, className, options }) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const agreementContent = useMemo(() => processContent(content, options), [content, options]);

  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.innerHTML = agreementContent;
    }
  }, [agreementContent]);

  return (
    <>
      {/* follow the document flow to enable user print out the whole content. */}
      {/* the header and footer selector is from: */}
      {/* https://chromium.googlesource.com/chromium/src/+/lkgr/components/printing/resources/print_header_footer_template_page.html */}
      <style>{`
        @media print {
          body, #root {
            height: auto;
          }

          .animated-switch {
            height: auto;
          }

          .animated-switch > div {
            position: relative;
          }

          #header, #footer {
            display: none !important;
          }

          td {
            page-break-inside: avoid;
          }
        }
      `}</style>
      <div ref={contentRef} className={className} />
    </>
  );
});
