import React from 'react';
import { cn, Heading } from '@moego/ui';
import { petMapBox } from '../../store/pet/pet.boxes';
import { Condition } from '../Condition';
import { VaccineIcon } from '../VaccineIcon/VaccineIcon';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';

interface PetNameInfoProps {
  petId: number;
  endDate?: string;
  showVaccine?: boolean;
  className?: string;
}
export const PetNameInfo: React.FC<PetNameInfoProps> = (props) => {
  const { petId, showVaccine, endDate, className } = props;
  const [pet] = useSelector(petMapBox.mustGetItem(petId));
  return (
    <div className={cn('moe-flex moe-items-center moe-gap-x-[8px]', className)}>
      <Heading size="5" className="moe-text-primary">
        {pet.petName}
      </Heading>
      <Condition if={showVaccine}>
        <VaccineIcon petId={petId} appointmentDate={endDate ?? dayjs().format(DATE_FORMAT_EXCHANGE)} />
      </Condition>
    </div>
  );
};
