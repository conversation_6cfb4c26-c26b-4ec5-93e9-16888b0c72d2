import { MinorRemoveFilled } from '@moego/icons-react';
import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import ImageSmilingFaceWithSunglassesGif from '../../assets/image/nps/smiling-face-with-sunglasses.gif';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { selectNPSResult, updateNPSResult } from './NPS.state';

export interface NPSButtonProps {
  className?: string;
  onClick?: () => void;
}

export const NPSButton = memo<NPSButtonProps>(({ className, onClick }) => {
  const [result] = useSelector(selectNPSResult());
  const dispatch = useDispatch();
  const handleDismiss = useLatestCallback(async (e: React.MouseEvent<HTMLSpanElement>) => {
    e.stopPropagation();
    await dispatch(updateNPSResult({ dismissed: true }));
  });
  return (
    <a
      onClick={onClick}
      className={cn(
        'moe-h-[40px] moe-flex moe-flex-row moe-items-center moe-justify-center moe-pl-[15px] moe-pr-[17px]',
        'moe-rounded-[24px] moe-bg-warning-subtle hover:moe-bg-warning-mild moe-transition-colors',
        'moe-text-warning hover:moe-text-warning',
        className,
      )}
    >
      {result?.score == null ? (
        <img
          className="moe-w-[28px] moe-h-[28px] moe-mr-[8px]"
          src={ImageSmilingFaceWithSunglassesGif}
          alt="Smiling sunglasses"
        />
      ) : (
        <MinorRemoveFilled onClickCapture={handleDismiss} className="moe-mr-[8px] moe-w-[20px] moe-h-[20px]" />
      )}
      <span>MoeGoer badge</span>
    </a>
  );
});
