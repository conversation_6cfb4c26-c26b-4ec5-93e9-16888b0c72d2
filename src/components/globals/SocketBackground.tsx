/*
 * @since 2020-12-03 15:29:54
 * <AUTHOR> <<EMAIL>>
 */

import { useSelector, useStore } from 'amos';
import { T_SECOND, noop } from 'monofile-utilities/lib/consts';
import { memo, useEffect, useRef } from 'react';
import { usePageVisibility } from 'react-page-visibility';
import { useHistory } from 'react-router';
import { useLatest } from 'react-use';
import IconLogoOnlyPng from '../../assets/icon/logo_only.png';
import MediaMoegoMp3 from '../../assets/media/moego.mp3';
import { PATH_MESSAGE_CENTER, PUSH_MESSAGE_NEW, PUSH_NOTIFICATION } from '../../router/paths';
import { switchBusiness } from '../../store/business/business.actions';
import { ALL_BUSINESS, currentBusinessIdBox } from '../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { customerMapBox } from '../../store/customer/customer.boxes';
import { getMessageSummary } from '../../store/message/message.actions';
import { MessageDetailFromType, MessageThreadQueryType, messageSummaryMapBox } from '../../store/message/message.boxes';
import { getNotificationUnreadCount } from '../../store/notification/notification.actions';
import { RefreshRedDotsNotificationTypeMap } from '../../store/notification/red-dot.constants';
import { isNormal } from '../../store/utils/identifier';
import { useWs } from '../../utils/MoegoWs-react';
import { useThrottleCallback } from '../../utils/hooks/useThrottleCallback';
import { useIsWorkingLocation } from '../WithFeature/useIsWorkingLocation';
import { type NotificationType } from '../../store/notification/notification.boxes';

export async function showNotification(title: string, options: NotificationOptions, callback?: () => void) {
  if (typeof Notification !== 'function' || Notification.permission === 'denied') {
    return;
  }
  if (Notification.permission === 'default') {
    const permission = await Notification.requestPermission();
    if (permission !== 'granted') {
      return;
    }
  }
  options.icon ||= IconLogoOnlyPng;
  const n = new Notification(title, options);
  n.addEventListener('click', () => {
    window.focus();
    callback?.();
  });
}

export const AUDIO_NEW_MESSAGE = new Audio(MediaMoegoMp3);

export const SocketBackground = memo(() => {
  const { ws } = useWs();
  const history = useHistory();
  const store = useStore();
  const visible = useLatest(usePageVisibility());
  const [businessId] = useSelector(currentBusinessIdBox);
  const isWorkingLocation = useIsWorkingLocation();
  // 记录页面在后台时，是否有新的消息
  const hasNewMessageWhenInvisible = useRef(false);
  // 记录页面在后台时，新的消息类型
  const newMessageTypes = useRef<NotificationType[]>([]);
  useEffect(() => {
    isNormal(businessId) && store.dispatch(getMessageSummary());
  }, [businessId]);

  const messageHandler = useThrottleCallback(async (payload: any) => {
    if (payload.type !== MessageDetailFromType.Customer) {
      return;
    }
    console.log('new message push', { ...payload });
    // non-working location, should not show browser notification FDN-873
    if (isNormal(payload.businessId) && !isWorkingLocation(payload.businessId)) {
      return;
    }
    store.dispatch(messageSummaryMapBox.updateItem(payload.businessId, (v) => v.set('totalUnread', v.totalUnread + 1)));
    if (store.select(selectCurrentBusiness()).notificationSoundEnable) {
      AUDIO_NEW_MESSAGE.play().catch(noop);
    }
    if (!visible.current || !PATH_MESSAGE_CENTER.regex.test(history.location.pathname)) {
      const customer = payload.groomingCustomerInfoDTO || store.select(customerMapBox.mustGetItem(payload.customerId));
      showNotification(
        payload.contactName
          ? payload.contactName
          : customer
            ? `${customer.firstName} ${customer.lastName}`
            : 'New message',
        {
          body: payload.messageText,
        },
        async () => {
          if (payload.businessId) {
            await store.dispatch(switchBusiness(Number(payload.businessId)));
          }
          if (PATH_MESSAGE_CENTER.regex.test(history.location.pathname)) {
            history.replace(
              PATH_MESSAGE_CENTER.queried({
                clientId: payload.customerId,
                chatsType: MessageThreadQueryType.Open,
              }),
            );
          } else {
            history.push(
              PATH_MESSAGE_CENTER.queried({
                setClientId: payload.customerId,
                chatsType: MessageThreadQueryType.Open,
              }),
            );
          }
        },
      );
    }
  }, 5 * T_SECOND);

  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible' && hasNewMessageWhenInvisible.current) {
      // 页面从后台切换到前台，刷新通知
      hasNewMessageWhenInvisible.current = false;
      store.dispatch(getNotificationUnreadCount(ALL_BUSINESS));
      for (const type of newMessageTypes.current) {
        store.dispatch(RefreshRedDotsNotificationTypeMap.get(type)?.());
      }
      newMessageTypes.current = [];
    }
  };

  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  useEffect(() => {
    return ws?.handlePush(PUSH_MESSAGE_NEW, messageHandler);
  }, [ws]);
  useEffect(() => {
    return ws?.handlePush(PUSH_NOTIFICATION, (payload) => {
      if (document.visibilityState !== 'visible') {
        // 此时页面在后台，标记一下，等页面切换到前台时，再刷新通知
        hasNewMessageWhenInvisible.current = true;
        if (RefreshRedDotsNotificationTypeMap.has(payload.type) && !newMessageTypes.current.includes(payload.type)) {
          newMessageTypes.current.push(payload.type);
        }
        return;
      }
      store.dispatch(getNotificationUnreadCount(ALL_BUSINESS));
      if (RefreshRedDotsNotificationTypeMap.has(payload.type)) {
        store.dispatch(RefreshRedDotsNotificationTypeMap.get(payload.type)?.());
      }
    });
  }, [ws]);
  return null;
});
