import { Tooltip } from 'antd';
import { type TooltipPropsWithOverlay } from 'antd/lib/tooltip';
import React, { type ReactNode, memo } from 'react';
import { useTextOverflow } from '../../container/Calendar/latest/ApptCalendar/hooks/useTextOverflow';
import { useBool } from '../../utils/hooks/useBool';

interface AutoTooltipProps extends Omit<TooltipPropsWithOverlay, 'overlay'> {
  text: ReactNode;
  innerClassName?: string;
  innerStyle?: React.CSSProperties;
}

/**
 * @deprecated use <Text ellipsis>text</Text> instead
 */
export const AutoTooltip = memo((props: AutoTooltipProps) => {
  const { text, innerClassName, innerStyle, ...rest } = props;
  const visible = useBool(false);

  const ref = useTextOverflow<HTMLDivElement>(visible.open);

  return (
    <Tooltip {...rest} overlay={visible.value ? text : null}>
      <div ref={ref} className={innerClassName} style={innerStyle}>
        {text}
      </div>
    </Tooltip>
  );
});
