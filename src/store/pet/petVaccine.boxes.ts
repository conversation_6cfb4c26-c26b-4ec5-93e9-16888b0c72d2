/*
 * @since 2021-02-08 10:40:30
 * <AUTHOR> <<EMAIL>>
 */

import { Record } from 'immutable';
import { OwnList, createOwnListBox } from '../utils/OwnList';
import { createRecordMapBox } from '../utils/RecordMap';
import { ID_ANONYMOUS } from '../utils/identifier';
import { createBox } from '../utils/utils';
import { type PetVaccineModal } from './petVaccine.utils';

export class PetVaccineRecord extends Record<PetVaccineModal>({
  id: ID_ANONYMOUS,
  name: '',
  sort: 0,
  onlyForSpecificPetType: false,
  availablePetTypes: [],
  isRequiredForScheduling: false,
  requiredByServiceItemTypes: [],
}) {}

export const petVaccineMapBox = createRecordMapBox('pets/vaccines', PetVaccineRecord, 'id');
export const businessPetVaccineListBox = createOwnListBox('pets/vaccines/business', OwnList.nn());

export const petVaccineTemplateListBox = createBox<string[]>('pets/vaccines/template', []);
