import { type BusinessPetMetadataName } from '@moego/api-web/moego/models/business_customer/v1/business_pet_metadata_enums';
import { type BusinessPetMetadataView } from '@moego/api-web/moego/models/business_customer/v1/business_pet_metadata_models';
import { Record } from 'immutable';
import { OwnList, createOwnListBox } from '../utils/OwnList';
import { createRecordMapBox } from '../utils/RecordMap';

export type PetMetaDataRecordType = Omit<BusinessPetMetadataView, 'id'> & {
  id: number;
  ownKey: string;
};

export class PetMetaDataRecord extends Record<PetMetaDataRecordType>({
  id: 0,
  metadataName: 0,
  metadataValue: '',
  ownKey: '',
  sort: 0,
  extraJson: {},
}) {
  static ownKey(accountId: number, type: BusinessPetMetadataName) {
    return `${accountId}-${type}`;
  }
}

export const petMetaDataMapBox = createRecordMapBox('pets/metadata', PetMetaDataRecord, 'id');
export const petMetaDataListBox = createOwnListBox('pets/metadata/categories', OwnList.sn());
