/*
 * @since 2021-02-08 10:40:30
 * <AUTHOR> <<EMAIL>>
 */

import { action } from 'amos';
import { BusinessPetCoatTypeClient } from '../../middleware/clients';
import { type OpenApiModels } from '../../openApi/schema';
import { sec } from '../../utils/DateTimeUtil';
import { id2NumberAndRequired } from '../../utils/api/api';
import { trimText } from '../../utils/utils';
import { currentAccountIdBox } from '../account/account.boxes';
import { businessPetHairLengthListBox, coatTypeTemplateListBox, petHairLengthMapBox } from './petHairLength.boxes';

export const addPetHairLength = action(
  async (dispatch, select, input: OpenApiModels['POST/customer/pet/hairlength']['Req']) => {
    input.name = input.name?.trim();
    if (!input.name) {
      throw new Error('name is required');
    }
    const accountId = select(currentAccountIdBox);
    const res = await BusinessPetCoatTypeClient.createPetCoatType({
      coatType: {
        name: trimText(input.name),
      },
    });
    dispatch([
      petHairLengthMapBox.mergeItem(Number(res.coatType.id), {
        sort: 0,
        ...input,
        createTime: sec(),
        updateTime: sec(),
      }),
      businessPetHairLengthListBox.unshiftList(accountId, Number(res.coatType.id)),
    ]);
  },
);

export const getPetHairLengthList = action(async (dispatch, select) => {
  const accountId = select(currentAccountIdBox);
  const res = await BusinessPetCoatTypeClient.listPetCoatType({});
  dispatch([
    petHairLengthMapBox.mergeItems(res.coatTypes.map((item) => ({ ...item, id: Number(item.id) }))),
    businessPetHairLengthListBox.setList(
      accountId,
      res.coatTypes.map((item) => Number(item.id)),
    ),
  ]);
});

export const sortPetHairLength = action(async (dispatch, select, idList: number[]) => {
  const accountId = select(currentAccountIdBox);
  await BusinessPetCoatTypeClient.sortPetCoatType({
    ids: idList.map((item) => `${item}`),
  });
  dispatch(businessPetHairLengthListBox.setList(accountId, idList));
});

export const updatePetHairLength = action(
  async (dispatch, select, input: OpenApiModels['PUT/customer/pet/hairlength']['Req']) => {
    input.name = input.name?.trim();
    if (!input.name) {
      throw new Error('name is required');
    }
    await BusinessPetCoatTypeClient.updatePetCoatType({
      id: `${input.id}`,
      coatType: {
        name: input.name,
      },
    });
    dispatch(petHairLengthMapBox.mergeItem(input.id!, id2NumberAndRequired(input)));
  },
);

export const removePetHairLength = action(async (dispatch, select, id: number) => {
  const accountId = select(currentAccountIdBox);
  await BusinessPetCoatTypeClient.deletePetCoatType({
    id: `${id}`,
  });

  dispatch(businessPetHairLengthListBox.deleteItem(accountId, id));
});

// 这个是个新接口，只有在新 AS 改造后的前端才使用，所以不用判断旧逻辑
export const getDefaultCoatTypeTemplateList = action(async (dispatch, select, forceUpdate: boolean = false) => {
  const coatTypeTemplateList = select(coatTypeTemplateListBox);
  if (coatTypeTemplateList.length > 0 && !forceUpdate) {
    return coatTypeTemplateList;
  }
  const res = await BusinessPetCoatTypeClient.listPetCoatTypeTemplate({});
  const list = [...new Set(res.coatTypes.map((item) => item.name))];
  dispatch(coatTypeTemplateListBox.setState(list));

  return list;
});
