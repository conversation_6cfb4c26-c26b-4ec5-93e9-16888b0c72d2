/*
 * @since 2021-02-08 10:40:30
 * <AUTHOR> <<EMAIL>>
 */

import { Record } from 'immutable';
import { EPetType } from '../../config/interface';
import { type OpenApiModels } from '../../openApi/schema';
import { type IntakeFormPetModel } from '../intakeForm/intakeForm.boxes';
import { OwnList, createOwnListBox } from '../utils/OwnList';
import { createRecordMapBox } from '../utils/RecordMap';
import { LegacyBool, createEnum } from '../utils/createEnum';
import { ID_ANONYMOUS } from '../utils/identifier';

export type PetBriefType = 'dog' | 'cat' | 'other';

export type VaccineList = Omit<
  OpenApiModels['GET/customer/pet/detail']['Res']['data']['petDetail']['vaccineList'][number],
  'source' | 'verifyStatus'
>[];

export const PetGender = createEnum({
  Male: [1, 'Male'],
  Female: [2, 'Female'],
});

export const PetLifeStatus = createEnum({
  Alive: [1, 'Live'],
  PassAway: [2, 'Pass-away'],
});

export class PetRecord extends Record({
  petId: ID_ANONYMOUS,
  customerId: ID_ANONYMOUS,
  petName: '',
  petTypeId: 0,
  typeName: '',
  avatarPath: '',
  lifeStatus: PetLifeStatus.Alive,
  breed: '',
  breedMix: 0,
  birthday: '',
  gender: PetGender.Male,
  hairLength: '',
  weight: '',
  fixed: '',
  behavior: '',
  petCodeIdList: [] as number[],
  vetName: '',
  vetPhone: '',
  vetAddress: '',
  vaccineList: [] as VaccineList,
  emergencyContactName: '',
  emergencyContactPhone: '',
  healthIssues: '',
  expiryNotification: LegacyBool.False,
  haveVaccineBinding: false,
  haveExpiredVaccineBinding: false,
  deleted: false,
  /** @deprecated */
  evaluationStatus: 0,
  /** 后端新字段代替 lifeStatus */
  passedAway: false,
  deactivateReason: '',
  incidentReportIds: [] as (number | string)[],
  petAppearanceColor: '',
  petAppearanceNotes: '',
  playgroupId: '' as number | string,
}) {
  isLive() {
    return this.lifeStatus === PetLifeStatus.Alive;
  }
}

export const isSamePet = (pet1: PetRecord, pet2: Pick<IntakeFormPetModel, 'breed' | 'petTypeId' | 'petName'>) =>
  pet1.breed.trim().toLowerCase() === pet2.breed.trim().toLowerCase() &&
  pet1.petTypeId === pet2.petTypeId &&
  pet1.petName.trim().toLowerCase() === pet2.petName.trim().toLowerCase();

export interface PetSummaryModel {
  petTypeId: number;
  count: number;
  typeName: string;
}

export const petMapBox = createRecordMapBox('pets', PetRecord, 'petId');
export const customerPetListBox = createOwnListBox('pets/customer', OwnList.nn());
// 这个 box 目前在不同页面只有唯一状态，所以没有支持 source 和 viewId
export const petSummaryListBox = createOwnListBox(
  'pets/summaries/business',
  new OwnList<number, Readonly<PetSummaryModel>, 'businessSummary'>(0),
);

export const isDogOrCatType = (typeId: number) => {
  return typeId === EPetType.Cat || typeId === EPetType.Dog;
};
