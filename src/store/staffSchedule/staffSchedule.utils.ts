import type {
  TimeAvailabilityDay,
  SlotAvailabilityDay,
  LimitationGroup,
} from '@moego/api-web/moego/models/organization/v1/staff_availability_models';
import { type FullWeekDay, FullWeekDayListMondayFirst } from '../onlineBooking/models/OnlineBookingPreference';
import { type WorkingSlotValue, type WorkingHourValue } from '../staff/staff.boxes';
import { type FullWeekData, type StaffScheduleBase, type RotateWeek, RotateWeekList } from './staffSchedule.types';
import {
  LIMITATION_KEY_LIST,
  type ValidLimitationFormValue,
  type LimitationFormValue,
} from '../../container/settings/Settings/StaffSetting/ShiftManagement/components/Limitation/LimitationFormContext';

// 把 raw 转化成数组形式，共 4 x 7 = 28 周，用于转换 staff 的 availability 数据
const transformToDayList = <T, P>(
  raw: StaffScheduleBase<FullWeekData<T>>,
  transform: (
    raw: T,
    meta: {
      weekNum: number;
      dayNum: number;
    },
  ) => P,
) => {
  const { firstWeek, secondWeek, thirdWeek, forthWeek } = raw;

  return [firstWeek, secondWeek, thirdWeek, forthWeek].reduce((dayList, weekData, weekNum) => {
    const currentWeekDayList = FullWeekDayListMondayFirst.reduce((currWeekList, upperCaseDayKey, dayNum) => {
      const dayKey = upperCaseDayKey.toLowerCase() as Lowercase<FullWeekDay>;
      const dayData = weekData[dayKey];

      const transformedData = transform(dayData, {
        weekNum,
        dayNum,
      });

      currWeekList.push(transformedData);

      return currWeekList;
    }, [] as P[]);

    return dayList.concat(currentWeekDayList);
  }, [] as P[]);
};

/**
 * 把本地 working hour 数据转化成 availability 数据用于发送请求
 * @param raw
 * @returns
 */
export const transformWorkingHourMap2DayList = (raw: StaffScheduleBase<FullWeekData<WorkingHourValue>>) => {
  return transformToDayList(raw, (dayRaw, { weekNum, dayNum }) => {
    const { timeRange, limitationGroups, isAvailable } = dayRaw;

    return {
      dayOfWeek: dayNum + 1,
      scheduleType: weekNum + 1,
      isAvailable: isAvailable && timeRange.length > 0,
      timeDailySetting: {
        limitationGroups,
      },
      timeHourSettingList: timeRange,
    };
  });
};

/**
 * 把本地 working slot 数据转化成 availability 数据用于发送请求
 * @param raw
 * @returns
 */
export const transformWorkingSlotMap2DayList = (raw: StaffScheduleBase<FullWeekData<WorkingSlotValue>>) => {
  return transformToDayList(raw, (dayRaw, { weekNum, dayNum }) => {
    const { slotDailySetting, slotHourSettingList, isAvailable } = dayRaw;

    return {
      dayOfWeek: dayNum + 1,
      scheduleType: weekNum + 1,
      isAvailable,
      slotDailySetting,
      slotHourSettingList,
    };
  });
};

interface DayListItem {
  dayOfWeek: number; // 当前是第几天，monday: 1, tuesday: 2, ... sunday: 7
  scheduleType: number; // 当前是第几周，1: firstWeek, 2: secondWeek, 3: thirdWeek, 4: forthWeek
}
/**
 * 把 list 转化成 week map，一定是 4 周 x 7 天 = 28 项
 *
 * @param list 使用之前确保 list 为 28 项，否则数据可能出现错误
 * @param transform
 */
const transformToWeekMap = <T extends DayListItem, P>(
  list: T[],
  transform: (item: T) => P,
): {
  [key in RotateWeek]: FullWeekData<P>;
} => {
  return list.reduce(
    (weekMap, item) => {
      const { dayOfWeek, scheduleType } = item;
      const weekNum = scheduleType - 1;
      const dayNum = dayOfWeek - 1;

      const weekKey = RotateWeekList[weekNum];
      const dayKey = FullWeekDayListMondayFirst[dayNum].toLowerCase() as Lowercase<FullWeekDay>;

      const transformedDayData = transform(item);

      if (!weekMap[weekKey]) {
        weekMap[weekKey] = {
          [dayKey]: transformedDayData,
        };
      } else {
        weekMap[weekKey][dayKey] = transformedDayData;
      }

      return weekMap;
    },
    {} as {
      [key in RotateWeek]: Partial<FullWeekData<P>>;
    },
  ) as {
    [key in RotateWeek]: FullWeekData<P>;
  };
};

export const transformWeekDayList2WorkingHour = (list: TimeAvailabilityDay[]) => {
  return transformToWeekMap(list, (item) => {
    const { timeDailySetting, timeHourSettingList, isAvailable } = item;
    return {
      isAvailable: isAvailable && timeHourSettingList.length > 0,
      timeRange: timeHourSettingList,
      limitationGroups: timeDailySetting.limitationGroups,
    };
  });
};

export const transformWeekDayList2WorkingSlot = (list: SlotAvailabilityDay[]) => {
  return transformToWeekMap(list, (item) => item);
};

/**
 *
 * 把 limitationGroups 转化成 limitationFormValue 的格式
 *
 * 目前的 UI 不支持混合配置，后端接口为了扩展性，在接口结构上是可支持的，前端在使用时需要做转换才可以正确渲染
 *
 * @param limitationGroups [
 *    { serviceLimits: [], onlyAcceptSelected: false }, // 每一项之间都是 or 逻辑，目前每一项都只允许配置一个 limits
 *    { serviceLimits: [], onlyAcceptSelected: false },
 *    { petSizeLimits: [], onlyAcceptSelected: false },
 *    { petBreedLimits: [], onlyAcceptSelected: false }
 * ]
 *
 */
export const transformLimitationGroup2FormValue = (limitationGroups: LimitationGroup[]): LimitationFormValue => {
  const serviceLimitsGroup = limitationGroups.filter((item) => item.serviceLimits.length > 0);
  const petSizeLimitsGroup = limitationGroups.filter((item) => item.petSizeLimits.length > 0);
  const petBreedLimitsGroup = limitationGroups.filter((item) => item.petBreedLimits.length > 0);

  const formValue: LimitationFormValue = {
    serviceLimits: [],
    petSizeLimits: [],
    petBreedLimits: [],
  };

  serviceLimitsGroup.forEach((item) => {
    formValue.serviceLimits.push({
      value: item.serviceLimits,
      isOnlyChecked: item.onlyAcceptSelected,
    });
  });

  petSizeLimitsGroup.forEach((item) => {
    formValue.petSizeLimits.push({
      value: item.petSizeLimits,
      isOnlyChecked: item.onlyAcceptSelected,
    });
  });

  petBreedLimitsGroup.forEach((item) => {
    formValue.petBreedLimits.push({
      value: item.petBreedLimits,
      isOnlyChecked: item.onlyAcceptSelected,
    });
  });

  return formValue;
};

export const transformFormValue2LimitationGroups = (formValue: ValidLimitationFormValue): LimitationGroup[] => {
  return LIMITATION_KEY_LIST.reduce((limitationGroups, limitKey) => {
    formValue[limitKey].forEach(({ value, isOnlyChecked }) => {
      const isOnlyDisabled = value.every((item) => item.capacity === 0);

      const item: LimitationGroup = {
        serviceLimits: [],
        petSizeLimits: [],
        petBreedLimits: [],
        onlyAcceptSelected: isOnlyChecked && !isOnlyDisabled,
      };

      (item[limitKey] as LimitationGroup[typeof limitKey]) = value;

      limitationGroups.push(item);
    });

    return limitationGroups;
  }, [] as LimitationGroup[]);
};
