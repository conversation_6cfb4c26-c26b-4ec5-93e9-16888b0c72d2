import { Record } from 'immutable';
import { type Overwrite } from 'utility-types';
import { type OpenApiModels } from '../../openApi/schema';
import { createRecordMapBox } from '../utils/RecordMap';
import { createEnum } from '../utils/createEnum';
import { BoxMap, createMapBox } from '../utils/immutable';

type TSummaryRes = OpenApiModels['GET/payment/stripe/payout/details/summary']['Res'];

export const STRIPE_PAYOUT_SUMMARY_WHITE_LIST_KEY = 'payout_summary_enable';

export class PayoutSummaryRecord extends Record<
  Pick<TSummaryRes, 'createTime' | 'arrivalTime'> &
    TSummaryRes['payoutSummary'] & {
      payoutId: string;
    }
>({
  createTime: 0,
  arrivalTime: 0,
  convenienceFee: 0,
  discounts: 0,
  fees: 0,
  grossSales: 0,
  netSales: 0,
  nets: 0,
  returns: 0,
  tax: 0,
  tips: 0,
  totalCollected: 0,
  transfer: 0,
  payoutId: '',
  adjustment: 0,
  amountsSum: 0,
  bookingFees: 0,
  repayments: [],
  capitalPayouts: [],
}) {}

export const stripePayoutSummaryMapBox = createRecordMapBox('stripe/payout/summary', PayoutSummaryRecord, 'payoutId');

/**
 * T+1 Payout status.
 *
 * 目前只对美国开放，因此非美国地区会返回 NotSupportedCountry。
 * 对于开放地区，符合两个条件之一才可以开启（即显示Enable按钮）：
 * - In allow-list，可以直接开启
 * - 累计 Transaction 额度达标，需要过风控检查，如果检查不通过就需要人工 review
 *
 * 详见 https://mengshikeji.feishu.cn/wiki/R3DQwfYuAicOYJkFp6zc3n5AnNf
 */
export const NextDayPayoutStatus = createEnum({
  // 非可用地区
  NotSupportedCountry: ['NOT_SUPPORTED_COUNTRY', 'Not supported country'],
  // 不可开启（不在名单里也不达标，这里应该是固定展示达标需求的 UI）
  NotAvailable: ['NOT_AVAILABLE', 'Unavailable'],
  // 可开启
  Available: ['AVAILABLE', 'Available'],
  // 已开启
  Enabled: ['ENABLED', 'Enabled'],
  // 待人工审批
  Reviewing: ['REVIEWING', 'Reviewing'],
  // 人工审批不通过
  ReviewFailed: ['REVIEW_FAILED', 'ReviewFailed'],
});

export const stripeScheduledPayoutSettingMapBox = createMapBox(
  '/stripe/payout/schedule/setting',
  BoxMap<
    number,
    Overwrite<
      OpenApiModels['GET/payment/stripe/payout/schedule']['Res'],
      {
        payoutScheduleStatus: string;
      }
    >
  >(),
);
