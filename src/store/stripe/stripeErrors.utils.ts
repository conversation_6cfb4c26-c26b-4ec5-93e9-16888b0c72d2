import { printCardName } from '../utils/card';

const MATCH_DICT = {
  insufficient: 'insufficient funds',
  invalid: 'invalid',
  stripeCommon: 'code: ',
};

const CARD_PREFIX_IN_ERROR = /^\[\w+\(\d+\)\]/;
function replaceCardPrefix(msg: string) {
  return msg.replace(CARD_PREFIX_IN_ERROR, '');
}

/**
 * 统一打印pre auth失败的信息
 * 注意：pre auth failed msg 会用[]拼接卡号在前面，如[Visa(3155)]Card Not Support.xxxxx
 * @param message pre auth failed message, e.g.,
 *  Card Not Support:requires_action
 *  [Visa(3155)]Card Not Support:requires_action. Please contact client or try a different card.
 * @param card e.g., 1111 or visa(1111)
 * stripe error example:
 * Your card was declined.; code: card_declined; request-id: req_sUrAzz93xqzy3j
 * Your card has insufficient funds.; code: card_declined; request-id: req_EudfYytsDkClwe
 * Your card was declined.; code: card_declined; request-id: req_cN1vX52noZEWh2
 * Your card's security code is incorrect.; code: incorrect_cvc; request-id: req_0YdTSkuMAimtLm
 * Your card was declined.; code: card_declined; request-id: req_T7615PmmWqCHH6
 * Your card was declined.; code: card_declined; request-id: req_0LaOL4EdsvIJtl
 */
export function printPreAuthError(
  message: string = '',
  card: string = '',
): {
  message: string;
  previewMessage: string;
  cardLast4: string;
  card: string;
  type: 'insufficient' | 'invalid' | 'unknown';
} {
  let msg = message;
  let _card = card;

  // 从msg中提取卡号
  if (!_card) {
    _card = splitCardFromFailedMsg(msg).card;
  }

  const cardLast4 = _card ? printCardName(_card, true) : '';
  const isInsufficient = msg.includes(MATCH_DICT.insufficient);
  if (cardLast4 && isInsufficient) {
    return {
      previewMessage: `Insufficient balance on the card ending in ${cardLast4}.`,
      message: `The card ending in ${cardLast4} has insufficient balance to proceed.`,
      cardLast4,
      card: _card,
      type: 'insufficient',
    };
  }

  if (cardLast4) {
    return {
      previewMessage: `Invalid card (${cardLast4}).`,
      message: `The card ending in ${cardLast4} is invalid.`,
      cardLast4,
      card: _card,
      type: 'invalid',
    };
  }

  msg = replaceCardPrefix(msg);
  const msgWithDot = msg.charAt(msg.length - 1) === '.' ? msg : `${msg}.`;
  return {
    previewMessage: msgWithDot,
    message: msgWithDot,
    cardLast4,
    card: _card,
    type: 'unknown',
  };
}

export function splitCardFromFailedMsg(msg: string) {
  let card = '';
  if (CARD_PREFIX_IN_ERROR.test(msg)) {
    card = msg.match(CARD_PREFIX_IN_ERROR)?.[0] || '';
    card = card.replace(/^\[/, '').replace(/\]$/, '');
  }

  const cardLast4 = printCardName(card, true);
  return {
    card,
    cardLast4,
  };
}
