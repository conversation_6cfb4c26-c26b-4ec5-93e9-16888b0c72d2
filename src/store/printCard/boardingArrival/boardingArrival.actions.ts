import type { ListBoardingArrivalCardParams } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { action, type Dispatch, type Select } from 'amos';
import { isUndefined } from 'lodash';
import { type BoardingPrintCardContent } from '../../../components/MultiTypePrintModal/components/common/BoardingCard/BoardingCard.types';
import { PrintCardClient } from '../../../middleware/clients';
import { currentBusinessIdBox } from '../../business/business.boxes';
import { getMetadataByKey, updateMetadata } from '../../metadata/metadata.actions';
import { META_DATA_KEY_LIST } from '../../metadata/metadata.config';
import {
  BoardingCardSortEnum,
  withMapCareTypeView2PetDetail,
  type BoardingPrintCardSettingsChildrenKey,
  type BoardingPrintCardSettingsParentKey,
  type BoardingPrintCardSettingsType,
} from '../printCard.utils';
import { boardingArrivalSettingsBox } from './boardingArrival.boxes';

const upgradeBoardingArrivalPrintSettings = (settings: BoardingPrintCardSettingsType) => {
  if (isUndefined(settings.sort)) {
    settings.sort = {
      sortBy: BoardingCardSortEnum.ClientLastName,
    };
  }
  if (isUndefined(settings.petCode)) {
    settings.petCode = {
      showUniqueComment: false,
    };
  }
  return settings;
};

export const getBoardingArrivalPrintCardSettings = action(async (dispatch) => {
  const settings = await dispatch(
    getMetadataByKey<BoardingPrintCardSettingsType>(META_DATA_KEY_LIST.BoardingArrivalPrintCardSetting),
  );
  const upgradedSettings = upgradeBoardingArrivalPrintSettings(settings);

  dispatch(boardingArrivalSettingsBox.setState(upgradedSettings));
  return settings;
});

export const setBoardingArrivalPrintCardSettings = action(
  async <PK extends BoardingPrintCardSettingsParentKey>(
    dispatch: Dispatch,
    select: Select,
    input: {
      parentKey: PK;
      childrenKey: BoardingPrintCardSettingsChildrenKey<PK>;
      value: boolean | string;
    },
  ) => {
    const { parentKey, childrenKey, value } = input;
    const settings = select(boardingArrivalSettingsBox);
    const nextSettings = {
      ...settings,
      [parentKey]: {
        ...settings[parentKey],
        [childrenKey]: value,
      },
    };
    dispatch(boardingArrivalSettingsBox.setState(nextSettings));
    dispatch(updateMetadata(META_DATA_KEY_LIST.BoardingArrivalPrintCardSetting, nextSettings));
  },
);

export const getBoardingArrivalPrintCardContent = action(
  async (_dispatch, select, input: Omit<ListBoardingArrivalCardParams, 'businessId'>) => {
    const currentBusinessId = select(currentBusinessIdBox);

    const { boardings, appointments, pets, lodgingTypes, lodgingUnits } = await PrintCardClient.listBoardingArrivalCard(
      {
        ...input,
        businessId: String(currentBusinessId),
      },
    );

    const mapCareTypeView2PetDetail = withMapCareTypeView2PetDetail(appointments, pets);

    const boardingArrivalCardContent: BoardingPrintCardContent[] = [
      {
        petDetails: boardings.map((item) => ({
          ...mapCareTypeView2PetDetail(item),
          petBelongings: item.petBelongings,
        })),
        lodgingTypes,
        lodgingUnits,
      },
    ].filter((item) => item.petDetails.length > 0);

    return boardingArrivalCardContent;
  },
);
