import { createEnum } from '../../utils/createEnum';
import { type BoardingPrintCardSettingsType } from '../printCard.utils';

interface AdditionalInfoEnumLabels {
  valueKey: keyof BoardingPrintCardSettingsType['additionalInfo'];
  label: string;
}

export const AdditionalInfoEnum = createEnum<string, string, AdditionalInfoEnumLabels>({
  PetNotes: [
    '2',
    {
      valueKey: 'showPetNotes',
      label: 'Pet notes',
    },
  ],
  AlertNotes: [
    '3',
    {
      valueKey: 'showAlertNotes',
      label: 'Alert notes',
    },
  ],
  TicketComments: [
    '4',
    {
      valueKey: 'showTicketComments',
      label: 'Ticket comments',
    },
  ],
});
