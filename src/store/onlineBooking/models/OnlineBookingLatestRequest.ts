import {
  type CustomerDetail,
  type BoardingAddOnDetail,
  type BoardingServiceDetail,
  type DaycareAddOnDetail,
  type DaycareServiceDetail,
  type FeedingDetail,
  type GetBookingRequestResponse,
  type GroomingAddOnDetail,
  type GroomingAutoAssignDetail,
  type GroomingServiceDetail,
  type GroupClassServiceDetail,
  type IncompleteDetails,
  type MedicationDetail,
} from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { ServiceItemType, ServiceOverrideType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  type BookingRequestModel,
  BookingRequestModelSource,
} from '@moego/api-web/moego/models/online_booking/v1/booking_request_models';
import { type Select } from 'amos';
import dayjs from 'dayjs';
import { Record } from 'immutable';
import { isUndefined } from 'lodash';
import { type Overwrite } from 'utility-types';
import { type OpenApiDefinitions } from '../../../openApi/schema';
import { dateMessageToString } from '../../../utils/utils';
import { ServiceType } from '../../service/category.boxes';
import { staffMapBox } from '../../staff/staff.boxes';
import { type PartialRequired } from '../../utils/RecordMap';
import { isNormal, toNumber } from '../../utils/identifier';
import { getMainCareType } from '../../../components/PetAndServicePicker/utils/getMainCareType';
import { store } from '../../../provider';

interface OBRequestDetailVO
  extends Exclude<OpenApiDefinitions['grooming']['com.moego.server.grooming.web.vo.ob.OBRequestDetailVO'], ''> {}

interface OrigMergedOBServiceDetail
  extends BoardingServiceDetail,
    DaycareServiceDetail,
    GroomingServiceDetail,
    GroupClassServiceDetail,
    Omit<OBRequestDetailVO['services'][number], 'serviceId' | 'durationOverrideType' | 'priceOverrideType'> {
  serviceItemType?: ServiceItemType;
  lodgingUnitId?: string;
  serviceDetailId?: string;
  evaluationId?: string;
}

/**
 * 在 OB B Request 的场景，Service 会集合多种不同类型，大杂烩在一起，各种字段来源不明，导致难以维护
 *
 * 后续可能需要考虑下将其分开处理，比如分两类：
 * 1. common：如 petServiceDetailId, serviceId, serviceName / serviceItemType (careType)
 * 2. 按 care type 分类：boarding / daycare / grooming / evaluation / dog walking 等相关
 *
 * 第一步先从明确用到的字段开始
 */
export type MergedOBServiceDetail = PartialRequired<OrigMergedOBServiceDetail, 'serviceId' | 'serviceName'>;

export interface MergedOBAddOnDetail extends Omit<BoardingAddOnDetail | GroomingAddOnDetail | DaycareAddOnDetail, ''> {
  /** add-on 绑定 service 的 careType */
  serviceItemType: ServiceItemType;
}

export interface OnlineBookingLatestRequestModelPet extends Exclude<OBRequestDetailVO['pets'][number], ''> {
  evaluationId?: string;
}
export interface OnlineBookingLatestRequestModel
  extends Omit<OBRequestDetailVO, 'customer' | 'services' | 'serviceItemTypes' | 'pets'>,
    Pick<BookingRequestModel, 'source'> {
  customer: Omit<OBRequestDetailVO['customer'], 'primaryAddress' | 'newAddresses' | 'birthday'> & {
    primaryAddress: Overwrite<
      Partial<OBRequestDetailVO['customer']['primaryAddress']>,
      {
        isPrimary?: number | boolean;
      }
    >;
    newAddresses: Overwrite<
      OBRequestDetailVO['customer']['newAddresses'][number],
      {
        isPrimary: number | boolean;
      }
    >[];
    hasPetParentAppAccount?: boolean;
  } & Partial<Pick<CustomerDetail, 'emergencyContact' | 'pickupContact'>>;
  services: MergedOBServiceDetail[];
  pets: OnlineBookingLatestRequestModelPet[];
  appointmentEndDate?: string;
  appointmentEndTime?: number;
  specificDates?: string[];
  feedings?: Partial<FeedingDetail>[];
  medications?: Partial<MedicationDetail>[];
  addons?: MergedOBAddOnDetail[];
  staffName?: string;
  serviceItemTypes?: ServiceItemType[];
  incompleteDetails: IncompleteDetails;
  appointmentId?: number;
  requestId: number;
}

/**
 * OnlineBookingLatestRequestRecord 是 Modal 内部用的 Record
 */
export class OnlineBookingLatestRequestRecord extends Record<OnlineBookingLatestRequestModel>({
  specificDates: [],
  incompleteDetails: {
    boardingServices: [],
    boardingAddons: [],
    daycareServices: [],
    daycareAddons: [],
    groomingServices: [],
    groomingAddons: [],
    evaluationServices: [],
  },
  appointmentId: 0,
  requestId: 0,
  serviceItemTypes: [],
  status: 0,
  source: BookingRequestModelSource.UNSPECIFIED,
  hasRequestUpdate: false,
  additionalNote: '',
  address: {
    address1: '',
    address2: '',
    addressId: 0,
    city: '',
    country: '',
    lat: '',
    lng: '',
    state: '',
    zipcode: '',
  },
  prepay: {
    paidAmount: 0,
    prepaidAmount: 0,
    prepayRate: 0,
    prepayStatus: 0,
    refundAmount: 0,
  },
  bookOnlineStatus: 0,
  isWaitingList: 0,
  appointmentDate: '',
  appointmentStartTime: 0,
  appointmentEndDate: '',
  appointmentEndTime: 0,
  apptId: 0,
  createTime: '',
  customer: {
    avatarPath: '',
    clientColor: '',
    businessId: 0,
    customerId: 0,
    email: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    preferredDay: [],
    preferredFrequencyDay: 0,
    preferredFrequencyType: 0,
    preferredGroomerId: 0,
    preferredTime: [],
    questionAnswerList: [],
    referralSourceId: 0,
    hasPetParentAppAccount: false,
    primaryAddress: {
      customerId: 0,
      customerAddressId: 0,
      address1: '',
      address2: '',
      city: '',
      country: '',
      lat: '',
      lng: '',
      state: '',
      zipcode: '',
      isPrimary: 0,
      isProfileRequestAddress: false,
    },
    newAddresses: [],
    emergencyContact: undefined,
    pickupContact: undefined,
  },
  isPaid: 0,
  pets: [],
  services: [],
  staff: {
    avatarPath: '',
    firstName: '',
    lastName: '',
    staffId: 0,
  },
  noStartTime: false,
  sourcePlatform: '',
  autoAssign: {
    appointmentId: 0,
    appointmentTime: 0,
    id: 0,
    staffId: 0,
  },
  isAutoAccept: false,
  discountCode: {
    discountCodeName: '',
  },
  staffName: '',
  feedings: [],
  medications: [],
  addons: [],
  emergencyContact: {
    firstName: '',
    lastName: '',
    phone: '',
    title: '',
    type: 0,
    email: '',
    id: 0,
  },
}) {
  firstStaffName() {
    const { firstName = '', lastName = '' } = this.staff || {};
    return this.staffName || `${firstName} ${lastName}`.trim();
  }
  staffNames() {
    const staffIds = [...new Set(this.services.map((item) => item.staffId!).filter(Boolean))];
    return staffIds
      .map((staffId) => {
        const { firstName = '', lastName = '' } = store.select(staffMapBox.mustGetItem(+staffId));
        return `${firstName} ${lastName}`.trim();
      })
      .join(', ');
  }
  get mainServiceItemType() {
    return getMainCareType(this.serviceItemTypes ?? []);
  }
  // currently, only eva + bd is hybrid request, grooming + bd is not hybrid request
  get isHybridEvaluationRequest() {
    return this.serviceItemTypes?.includes(ServiceItemType.EVALUATION) && (this.serviceItemTypes?.length ?? 0) > 1;
  }
  get hasEvaluation() {
    return !!this.serviceItemTypes?.includes(ServiceItemType.EVALUATION);
  }
  get isGroupClass() {
    return !!this.serviceItemTypes?.includes(ServiceItemType.GROUP_CLASS);
  }
}

export const transformLatestRequest = (
  val: GetBookingRequestResponse,
  select: Select,
): OnlineBookingLatestRequestModel => {
  const {
    customerDetail: customer,
    bookingRequest,
    serviceDetails,
    pay,
    address,
    hasRequestUpdate,
    order,
    incompleteDetails,
  } = val;
  const { source } = bookingRequest;
  const feedings: Partial<FeedingDetail>[] = [];
  const medications: Partial<MedicationDetail>[] = [];
  const addons: MergedOBAddOnDetail[] = [];
  let autoAssign = {} as GroomingAutoAssignDetail;

  const petServiceDetailList = serviceDetails.map((item) => {
    const { services, petDetail } = item;

    return {
      petDetail,
      services: services.map((service) => {
        const { boarding, grooming, evaluation, daycare, groupClass, serviceItemType } = service;

        autoAssign = { ...autoAssign, ...grooming?.autoAssign };

        const serviceFeedings = [...(daycare?.feedings || []), ...(boarding?.feedings || [])];
        serviceFeedings.forEach((item) => {
          if (Object.values(item).length) {
            feedings.push(item);
          }
        });

        const serviceMedications = [...(daycare?.medications || []), ...(boarding?.medications || [])];
        serviceMedications.forEach((item) => {
          if (Object.values(item).length) {
            medications.push(item);
          }
        });

        const mergeService = {
          ...boarding?.service,
          ...grooming?.service,
          ...evaluation?.service,
          ...daycare?.service,
          ...groupClass?.service,
        };
        const mixedServiceDetail: MergedOBServiceDetail = {
          serviceItemType,
          id: mergeService.id!,
          serviceId: mergeService.serviceId!,
          serviceName: mergeService.serviceName!,
          ...mergeService,
        };

        /**
         * boarding service A + grooming service B + bundled grooming addon C 的情况下
         * C 没有 dateType 字段，所以这里给它补上，用 B 的 dateType
         */
        const groomingAddons = (grooming?.addons || []).map((item) => {
          const dateType = grooming?.service?.dateType;
          return {
            ...item,
            dateType,
          };
        });

        const addon = [...(daycare?.addons || []), ...(boarding?.addons || []), ...groomingAddons].map((item) => ({
          ...item,
          serviceItemType,
        }));
        addon?.length && addons.push(...addon);

        return mixedServiceDetail;
      }),
    };
  });

  const staff: OnlineBookingLatestRequestModel['staff'] = petServiceDetailList.reduce(
    (prev, curr) => {
      const { services } = curr;
      const service = services.find((service) => isNormal(service.staffId));
      if (!service) return prev;

      const staffId = toNumber(service.staffId);
      const { avatarPath, firstName, lastName } = select(staffMapBox.mustGetItem(staffId));
      return { avatarPath, firstName, lastName, staffId };
    },
    { avatarPath: '', firstName: '', lastName: '', staffId: 0 },
  );

  return {
    incompleteDetails,
    specificDates: bookingRequest.specificDates,
    serviceItemTypes: bookingRequest.serviceItemTypes,
    bookOnlineStatus: bookingRequest.status,
    autoAssign,
    address,
    hasRequestUpdate,
    feedings,
    medications,
    addons,
    additionalNote: bookingRequest.additionalNote,
    isPaid: bookingRequest.isPrepaid ? 1 : 0,
    appointmentId: toNumber(bookingRequest.appointmentId),
    // apptId 是 GET/grooming/ob/v2/ob-request 返回的 appointment id，这里保持行为一致，后续改造参见 MER-3921
    apptId: toNumber(bookingRequest?.appointmentId),
    requestId: toNumber(bookingRequest.id),
    createTime: dayjs(bookingRequest.createdAt || '').unix(),
    appointmentDate: bookingRequest.startDate,
    appointmentStartTime: bookingRequest.startTime,
    appointmentEndTime: bookingRequest.endTime,
    appointmentEndDate: bookingRequest.endDate,
    source,
    sourcePlatform: bookingRequest.sourcePlatform.toString(),
    noStartTime: bookingRequest.noStartTime,
    staffName: bookingRequest.staffName,
    customer: {
      ...customer,
      newAddresses: customer?.newAddress?.map((item) => ({
        ...item,
        customerAddressId: toNumber(item.addressId),
        customerId: toNumber(item.customerId),
        isProfileRequestAddress: item.isProfileRequestAddress!,
      })),
      primaryAddress: {
        ...(customer.primaryAddress || {}),
        customerAddressId: toNumber(customer?.primaryAddress?.addressId),
        customerId: toNumber(customer?.primaryAddress?.customerId),
      },
      questionAnswerList: customer.questionAnswers,
      businessId: toNumber(customer.businessId),
      referralSourceId: toNumber(customer.referralSourceId),
      customerId: toNumber(customer.customerId),
    },
    pets: petServiceDetailList.map((item) => {
      const {
        services: [service] = [
          {
            id: '',
            serviceId: '',
            evaluationId: '',
          },
        ],
        petDetail,
      } = item || {};
      const { emergencyContactPhoneNumber, questionAnswers, id, birthday, coatType, petType, petVaccines } =
        petDetail || {};

      return {
        ...petDetail,
        emergencyContactPhone: emergencyContactPhoneNumber,
        questionAnswerList: questionAnswers,
        petId: toNumber(id),
        birthday: !isUndefined(birthday) ? dateMessageToString(birthday) : '',
        addOnIds: [],
        hairLength: coatType,
        petTypeId: petType,
        // 对应着一个 request 的主 service 的 serviceId
        serviceId: toNumber(service?.serviceId),
        evaluationId: service?.evaluationId,
        vaccineList: petVaccines.map((item) => ({
          ...item,
          vaccineId: toNumber(item.vaccineId),
          vaccineBindingId: toNumber(item.vaccineBindingId),
          expirationDate: !isUndefined(item.expirationDate) ? dateMessageToString(item.expirationDate) : '',
        })),
      };
    }),
    services: petServiceDetailList
      .map((items) =>
        items.services.map((item) => {
          return {
            petId: item.petId,
            duration: item?.serviceTime || 0,
            serviceDetailId: item?.id,
            serviceId: item?.serviceId,
            serviceName: item?.serviceName,
            serviceType: ServiceType.Service,
            serviceItemType: item.serviceItemType,
            price: item.servicePrice || 0,
            lodgingUnitName: item.lodgingUnitName,
            lodgingUnitId: item.lodgingId,
            evaluationId: item.evaluationId,
            priceOverrideType: item.priceOverrideType ?? ServiceOverrideType.UNSPECIFIED,
            durationOverrideType: item.durationOverrideType ?? ServiceOverrideType.UNSPECIFIED,
            startDate: item.startDate,
            endDate: item.endDate,
            specificDates: item.specificDates,
            occurrence: item.occurrence,
            numSessions: item.numSessions,
            startTime: item.startTime,
            endTime: item.endTime,
            priceUnit: item.priceUnit,
            dateType: item?.dateType,
            staffId: item.staffId,
          };
        }),
      )
      .flat(1),
    prepay: {
      prepaidAmount: pay.prePayAmount!,
      prepayRate: pay.prePayRate!,
      prepayStatus: pay.prePayStatus!,
      paidAmount: pay.paidAmount!,
      refundAmount: pay.refundAmount!,
    },
    discountCode: order,
    isAutoAccept: false,
    isWaitingList: 0,
    staff,
    status: 0,
    emergencyContact: {
      firstName: '',
      lastName: '',
      phone: '',
      title: '',
      type: 0,
      email: '',
      id: 0,
    },
  };
};
