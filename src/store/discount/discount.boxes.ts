import { type GetDiscountCodeListRequest } from '@moego/api-web/moego/api/marketing/v1/discount_code_api';
import { type GetAvailableDiscountListRequest } from '@moego/api-web/moego/api/order/v1/order_discount_code_api';
import {
  DiscountCodeStatus,
  DiscountCodeType,
  ExpiryType,
} from '@moego/api-web/moego/models/marketing/v1/discount_code_enums';
import { type DiscountCodeModel } from '@moego/api-web/moego/models/marketing/v1/discount_code_models';
import { Record } from 'immutable';
import { type OpenApiModels } from '../../openApi/schema';
import { PagedList } from '../utils/PagedList';
import { createRecordMapBox } from '../utils/RecordMap';
import { createEnum } from '../utils/createEnum';

export type DiscountModel = DiscountCodeModel & {
  clientNames?: string[];
  productNames?: string[];
  serviceNames?: string[];
};

export const DiscountStatusMap = createEnum({
  Active: [
    DiscountCodeStatus.ACTIVE,
    {
      label: 'Active',
      color: '#3985f5',
    },
  ],
  Inactive: [
    DiscountCodeStatus.INACTIVE,
    {
      label: 'Inactive',
      color: '#333',
    },
  ],
  Archived: [
    DiscountCodeStatus.ARCHIVED,
    {
      label: 'Archived',
      color: '#999',
    },
  ],
  Deleted: [
    DiscountCodeStatus.DELETED,
    {
      label: 'Deleted',
      color: '#999',
    },
  ],
  Expired: [
    DiscountCodeStatus.EXPIRED,
    {
      label: 'Expired',
      color: '#999',
    },
  ],
});

export class DiscountRecord extends Record<DiscountModel>({
  allowedAllClients: false,
  allowedAllProducts: false,
  allowedAllServices: false,
  allowedNewClients: false,
  allowedAllThing: false,
  amount: 0,
  autoApplyAssociation: false,
  businessId: '',
  clientIds: [],
  clientsGroup: '',
  createBy: 0,
  createTime: '',
  description: '',
  discountCode: '',
  discountSales: 0,
  enableOnlineBooking: false,
  endDate: '',
  id: '',
  limitUsage: 0,
  limitNumberPerClient: 0,
  limitBudget: 0,
  productIds: [],
  serviceIds: [],
  addOnIds: [],
  startDate: '',
  status: 0,
  totalUsage: 0,
  type: DiscountCodeType.UNSPECIFIED,
  updateBy: 0,
  updateTime: '',
  serviceNames: [],
  productNames: [],
  clientNames: [],
  locationIds: [],
  expiryTime: '',
  expiryType: ExpiryType.UNSPECIFIED,
}) {
  get hasBeenUsed() {
    return this.totalUsage > 0;
  }
}

export class DiscountListFilterRecord extends Record<GetDiscountCodeListRequest>({
  pagination: {
    pageNum: 0,
    pageSize: 10,
  },
  status: [DiscountStatusMap.Active],
  discountCode: '',
}) {}

export const businessDiscountListBox = createRecordMapBox(
  'discounts/business',
  new PagedList({ filter: new DiscountListFilterRecord(), key: 0 }, ''),
  'key',
);

export const discountMapBox = createRecordMapBox('discounts', DiscountRecord, 'id');

export class AvailableDiscountListFilterRecord extends Record<GetAvailableDiscountListRequest>({
  pagination: {
    pageNum: 0,
    pageSize: 10,
  },
  invoiceId: '',
  codeName: '',
}) {}

export const businessAvailableDiscountListBox = createRecordMapBox(
  'available/discounts/business',
  new PagedList({ filter: new AvailableDiscountListFilterRecord(), key: 0 }, ''),
  'key',
);

export type DiscountSearchProductRecordSchema = {
  businessId: string;
  total: number;
  loading: boolean;
  list: OpenApiModels['GET/retail/product/query']['Res']['resultList'];
};

export class DiscountSearchProductRecord extends Record<DiscountSearchProductRecordSchema>({
  businessId: '',
  loading: false,
  total: 0,
  list: [],
}) {}

export const discountSearchProductMapBox = createRecordMapBox(
  'discounts/search/product',
  DiscountSearchProductRecord,
  'businessId',
);
