import { type SetBusinessesParams } from '@moego/api-web/moego/api/accounting/v1/accounting_api';
import { ChannelType } from '@moego/api-web/moego/models/accounting/v1/accounting_enums';
import { action } from 'amos';
import { AccountingClient } from '../../../../../middleware/clients';
import { createAccountingObservableAction } from '../../../../observableServices/observableServices';
import { FinanceAccountingBlockBox, type FinanceRecordModal } from '../../accounting.boxes';

export const setGlobalFinanceAccountingBlock = action((dispatch, select, input: FinanceRecordModal) => {
  const state = select(FinanceAccountingBlockBox);
  dispatch(FinanceAccountingBlockBox.setState({ ...state, ...input }));
});

export const getLayerBusinesses = createAccountingObservableAction('getLayerBusinesses', async () => {
  const res = await AccountingClient.getBusinesses({ channelType: ChannelType.LAYER });
  return res;
});

export const getOnboardingStatus = createAccountingObservableAction('getOnboardingStatus', async () => {
  const res = await AccountingClient.getOnboardingStatus({ channelType: ChannelType.LAYER });
  return res;
});

export const setChannelBusiness = createAccountingObservableAction(
  'setChannelBusiness',
  async (dispatch, select, input: Omit<SetBusinessesParams, 'channelType'>) => {
    const res = await AccountingClient.setBusinesses({ ...input, channelType: ChannelType.LAYER });
    return res;
  },
);

export const removeChannelBusinesses = createAccountingObservableAction(
  'removeChannelBusinesses',
  async (dispatch, select, input: string[]) => {
    const res = await AccountingClient.removeBusinesses({
      channelType: ChannelType.LAYER,
      businessIds: input,
    });
    return res;
  },
);

export const addChannelBusinesses = createAccountingObservableAction(
  'addChannelBusinesses',
  async (dispatch, select, input: string[]) => {
    const res = await AccountingClient.addBusinesses({
      channelType: ChannelType.LAYER,
      businessIds: input,
    });
    return res;
  },
);

export const getAuthToken = createAccountingObservableAction('getAuthToken', async () => {
  const res = await AccountingClient.getAuthToken({ channelType: ChannelType.LAYER });
  return res;
});
