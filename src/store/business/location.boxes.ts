import { BusinessType } from '@moego/api-web/moego/models/organization/v1/location_enums';
import {
  type LocationBriefView,
  type LocationModel,
} from '@moego/api-web/moego/models/organization/v1/location_models';
import { Record } from 'immutable';
import { OwnList, createOwnListBox } from '../utils/OwnList';
import { createRecordMapBox } from '../utils/RecordMap';
import { createEnum } from '../utils/createEnum';
import { ID_ANONYMOUS } from '../utils/identifier';

export const BusinessTypeLabelMap = createEnum({
  Salon: [BusinessType.SALON, 'Salon'],
  Mobile: [BusinessType.MOBILE, 'Mobile'],
  Hybrid: [BusinessType.HYBRID, 'Hybrid'],
});

export class LocationRecord extends Record<Partial<LocationModel> & LocationBriefView>({
  id: `${ID_ANONYMOUS}`,
  name: '',
  companyId: '',
  contactEmail: '',
  address: {
    address1: '',
    address2: '',
    city: '',
    state: '',
    zipcode: '',
    country: '',
    coordinate: undefined,
  },
  website: '',
  avatarPath: '',
  numberOfVansUsed: 0,
  facebookLink: '',
  instagramLink: '',
  yelpLink: '',
  googleLink: '',
  tiktokLink: '',
  twilioPhoneNumber: '',
  contactPhoneNumber: '',
  isWorkingLocation: false,
  // 后端下面两个字段都会写入，businessType 有三个值，businessMode 只有两个值（没有 hybrid）。
  businessType: BusinessType.SALON,
  businessMode: BusinessType.SALON,
}) {
  isMobileGrooming() {
    return this.businessType !== BusinessType.SALON;
  }
}

export const locationMapBox = createRecordMapBox('locations', LocationRecord, 'id');
export const locationIdListBox = createOwnListBox('locations/id_list', OwnList.ns());
export const allCompanyWorkingLocationIdListBox = createOwnListBox(
  'company_list/working_locations/id_list',
  OwnList.ns(),
);

export const formatLocationAddress = (address: LocationModel['address']): string => {
  if (!address) return '';
  const { address1, address2, city: addressCity = '', state, zipcode, country } = address;
  // ERP-10965 address1 与 city 匹配时，不展示 city
  const city = address1 === addressCity ? '' : addressCity;

  return [address1, address2, city, state, zipcode, country].filter(Boolean).join(', ');
};
