/*
 * @since 2021-04-07 16:01:25
 * <AUTHOR> <<EMAIL>>
 */

import { Record } from 'immutable';
import { type OpenApiModels } from '../../openApi/schema';
import { createRecordMapBox } from '../utils/RecordMap';

export type ArrivalWindowModel = OpenApiModels['GET/message/reminder/arrival']['Res']['data'] & {
  businessId: number;
};

export class ArrivalWindowRecord extends Record<ArrivalWindowModel>({
  id: 0,
  status: 0,
  arrivalAfter: 0,
  arrivalBefore: 0,
  businessId: 0,
}) {}

export const arrivalWindowMapBox = createRecordMapBox(
  'arrival_window_settings',
  new ArrivalWindowRecord(),
  'businessId',
);
