import { Record } from 'immutable';
import { createRecordMapBox } from '../utils/RecordMap';
import { ID_ANONYMOUS } from '../utils/identifier';

export class PetParentPortalInviteLinkRecord extends Record({
  customerId: ID_ANONYMOUS,
  petParentPortalInviteLink: '',
}) {}

export const petParentPortalInviteLinkMapBox = createRecordMapBox(
  'petParentPortal/invite/link',
  PetParentPortalInviteLinkRecord,
  'customerId',
);
