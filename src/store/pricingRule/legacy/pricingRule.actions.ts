import {
  type ListAssociatedServicesParams,
  type ListPricingRulesParams,
  type UpsertPricingRuleParams,
} from '@moego/api-web/moego/api/offering/v1/pricing_rule_api';
import { type CalculatePricingRuleParams } from '@moego/api-web/moego/api/offering/v2/pricing_rule_api';
import { type PricingRule } from '@moego/api-web/moego/models/offering/v2/pricing_rule_models';
import { action } from 'amos';
import { CreateApptId } from '../../../container/Appt/store/appt.types';
import { PricingRuleServiceV2Client, pricingRuleClient } from '../../../middleware/clients';
import { currentCompanyIdBox } from '../../company/company.boxes';
import {
  AppointmentPricingRuleRecord,
  PetDetailCalculateDefRecord,
  PricingRuleAssociatedServices,
  appointmentPricingRuleListBox,
  appointmentPricingRuleRecordMap,
  companyPetDetailCalculatePriceMapBox,
  companyPreviewPetDetailCalculatePriceListBox,
  companyPricingRuleAssociatedServicesRecordMap,
  companyPricingRuleListBox,
  companyPricingRuleMapBox,
} from './pricingRule.boxes';
import { type SaveAppointmentPricingRuleDetailV2, type SaveAppointmentPricingRuleListDetailV2 } from './types';

export const savePricingRule = action(async (dispatch, select, params: UpsertPricingRuleParams) => {
  const res = await pricingRuleClient.upsertPricingRule(params);
  dispatch(companyPricingRuleMapBox.mergeItem(res.pricingRule.id, res.pricingRule));
  return res;
});

export const getPricingRuleList = action(
  async (dispatch, select, params: Pick<ListPricingRulesParams, 'filter'>, signal?: AbortSignal) => {
    const companyId = select(currentCompanyIdBox);
    const state = select(companyPricingRuleListBox.mustGetItem(companyId));
    const filter = state.getFilter(params);

    dispatch(
      companyPricingRuleListBox.updateItem(companyId, (v) => {
        return v.applyStart(filter);
      }),
    );

    try {
      const res = await pricingRuleClient.listPricingRules(filter, {
        signal,
      });
      dispatch(companyPricingRuleMapBox.mergeItems(res.pricingRules));

      dispatch(
        companyPricingRuleListBox.updateItem(companyId, (v) => {
          return v.applySuccess(
            res.pricingRules.map((item) => item.id),
            res.pagination.total,
            filter.pageNum,
            filter.clear,
          );
        }),
      );
    } catch {
      dispatch(
        companyPricingRuleListBox.updateItem(companyId, (v) => {
          return v.applyFail(filter.pageNum);
        }),
      );
    }
  },
);

export const getPricingRuleDetail = action(async (dispatch, select, ruleId: string) => {
  const res = await pricingRuleClient.getPricingRule({
    id: ruleId,
  });

  dispatch(companyPricingRuleMapBox.mergeItem(ruleId, res.pricingRule));

  return res;
});

export const calculatePricingRule = action(
  async (
    dispatch,
    select,
    params: CalculatePricingRuleParams,
    signal: AbortSignal,
    companyId: number = select(currentCompanyIdBox),
  ) => {
    const res = await PricingRuleServiceV2Client.calculatePricingRule(params);
    const pricingRuleMap = new Map<string, PricingRule>();
    res.pricingRules.forEach((rule) => {
      pricingRuleMap.set(rule.id, rule);
    });

    dispatch([
      companyPetDetailCalculatePriceMapBox.mergeItems(
        res.petDetails.map((detail) => {
          return {
            key: PetDetailCalculateDefRecord.ownKey(detail.petId, detail.serviceId),
            data: {
              ...detail,
              servicePrice: detail.adjustedPrice,
            },
          };
        }),
      ),
      companyPreviewPetDetailCalculatePriceListBox.setList(
        companyId,
        res.petDetails.map((detail) => PetDetailCalculateDefRecord.ownKey(detail.petId, detail.serviceId)),
      ),
      // 只有新建 appointment 时才需要调用接口来计算 pricing rule 并展示在界面上
      // 编辑 appointment 时会在 GetAppointment 接口返回
      saveAppointmentPricingRuleDetail(
        CreateApptId,
        res.petDetails
          .map((detail) => {
            const appliedRuleIds = detail.appliedRuleIds || [];
            return appliedRuleIds.map((ruleId) => ({
              ...detail,
              id: ruleId,
            }));
          })
          .flat(1)
          .filter((detail) => pricingRuleMap.has(detail.id || ''))
          .map((detail) => {
            return {
              petId: detail.petId,
              pricingRule: pricingRuleMap.get(detail.id || '')!,
              servicePrice: detail.adjustedPrice,
              serviceId: detail.serviceId,
              ownKey: AppointmentPricingRuleRecord.ownKey(detail.petId, detail.serviceId),
            };
          }),
      ),
    ]);

    return res;
  },
);

export const deletePricingRule = action(async (dispatch, select, id: string, applyToUpcomingAppointments: boolean) => {
  await pricingRuleClient.deletePricingRule({
    id,
    applyToUpcomingAppointments,
  });

  const companyId = select(currentCompanyIdBox);

  dispatch([
    companyPricingRuleListBox.updateItem(companyId, (v) => {
      return v.deleteItem(id);
    }),
    companyPricingRuleMapBox.deleteItem(id),
  ]);
});

export const getAssociatedServices = action(async (dispatch, select, input: ListAssociatedServicesParams) => {
  const res = await pricingRuleClient.listAssociatedServices(input);
  const companyId = select(currentCompanyIdBox);

  const resKey = PricingRuleAssociatedServices.ownKey(
    companyId,
    input.serviceType,
    input.serviceItemType,
    input.filter?.excludePricingRuleId,
  );
  dispatch(
    companyPricingRuleAssociatedServicesRecordMap.mergeItem(resKey, {
      key: resKey,
      data: res,
    }),
  );

  return res;
});

export const saveAppointmentPricingRuleDetail = action(
  async (dispatch, select, appointmentId: string, details: SaveAppointmentPricingRuleDetailV2[]) => {
    dispatch(
      appointmentPricingRuleListBox.setList(
        appointmentId,
        details.map((detail) => detail.ownKey),
      ),
    );
    const priceRuleMap = new Map<string, SaveAppointmentPricingRuleListDetailV2>();
    for (const detail of details) {
      const key = `${detail.petId}-${detail.serviceId}`;
      if (!priceRuleMap.has(key)) {
        priceRuleMap.set(key, {
          petId: detail.petId,
          serviceId: detail.serviceId,
          servicePrice: detail.servicePrice,
          pricingRuleList: [] as unknown as PricingRule[],
          ownKey: detail.ownKey,
        });
      }
      const priceRuleList = priceRuleMap.get(key)?.pricingRuleList || [];
      priceRuleList.push(detail.pricingRule);
    }
    const priceRuleArray = Array.from(priceRuleMap.values());
    dispatch(appointmentPricingRuleRecordMap.mergeItems(priceRuleArray));
  },
);
