import { selector } from 'amos';
import { currentCompanyIdBox } from '../company/company.boxes';
import {
  PricingRuleAssociatedServices,
  companyPricingDiscountSettingMapBox,
  companyPricingRuleListBox,
  companyPricingRuleMapBox,
  pricingRuleAssociatedServicesRecordMap,
  pricingRuleOverviewBox,
} from './pricingRule.boxes';

export const selectPricingDiscountSetting = selector((select) => {
  const companyId = select(currentCompanyIdBox);
  return select(companyPricingDiscountSettingMapBox.mustGetItem(companyId));
});

export const selectPricingRuleIdList = selector((select) => {
  const companyId = select(currentCompanyIdBox);
  return select(companyPricingRuleListBox.mustGetItem(companyId));
});

export const selectPricingRuleList = selector((select) => {
  return select(selectPricingRuleIdList)
    .getList()
    .map((id) => select(companyPricingRuleMapBox.mustGetItem(id)));
});

export const selectPricingRuleAssociatedServices = selector((select, excludePricingRuleId: string | undefined) => {
  const companyId = select(currentCompanyIdBox);
  const key = PricingRuleAssociatedServices.ownKey(companyId, excludePricingRuleId);
  return select(pricingRuleAssociatedServicesRecordMap.mustGetItem(key));
});

export const selectPricingRuleDetail = selector((select, ruleId: string) => {
  return select(companyPricingRuleMapBox.mustGetItem(ruleId));
});

export const selectPricingRuleOverview = selector((select) => {
  const companyId = select(currentCompanyIdBox);
  return select(pricingRuleOverviewBox.mustGetItem(companyId));
});
