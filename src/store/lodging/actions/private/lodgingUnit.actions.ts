import {
  type LodgingInUseCheckParams,
  type LodgingTransferParams,
} from '@moego/api-web/moego/api/appointment/v1/lodging_api';
import {
  type CreateLodgingUnitParams,
  type DeleteLodgingUnitParams,
  type UpdateLodgingUnitParams,
} from '@moego/api-web/moego/api/offering/v1/lodging_unit_api';
import { action } from 'amos';
import { LodgingClient, LodgingUnitClient } from '../../../../middleware/clients';
import { ErrorCodes } from '../../../../middleware/codes';
import { type GoogleRpcApiError } from '../../../../types/grpc';
import { currentAccountIdBox } from '../../../account/account.boxes';
import { createLodgingObservableAction } from '../../../observableServices/observableServices';
import {
  type LodgingUnitListFilterModel,
  businessLodgingUnitListBox,
  lodgingUnitListFilterMapBox,
  lodgingUnitMapBox,
} from '../../lodgingUnit.boxes';

export const updateLodgingUnitListFilter = action(
  (dispatch, select, input: Partial<Omit<LodgingUnitListFilterModel, 'accountId'>>) => {
    const accountId = select(currentAccountIdBox).toString();
    dispatch(lodgingUnitListFilterMapBox.mergeItem(accountId, input));
  },
);

export const addLodgingUnits = createLodgingObservableAction(
  'addLodgingUnits',
  async (dispatch, select, input: CreateLodgingUnitParams) => {
    const res = await LodgingUnitClient.createLodgingUnit(input);
    const lodgingUnitList = res.lodgingUnitList;
    dispatch([
      businessLodgingUnitListBox.pushLists([[input.businessId, lodgingUnitList.map((item) => item.id)]]),
      lodgingUnitMapBox.mergeItems(lodgingUnitList),
    ]);
  },
);

export const updateLodgingUnit = createLodgingObservableAction(
  'updateLodgingUnit',
  async (dispatch, select, input: UpdateLodgingUnitParams) => {
    const res = await LodgingUnitClient.updateLodgingUnit(input);
    const { lodgingUnit } = res;

    dispatch(lodgingUnitMapBox.mergeItem(lodgingUnit.id, lodgingUnit));
  },
);

export const deleteLodgingUnit = createLodgingObservableAction(
  'deleteLodgingUnit',
  async (dispatch, select, input: DeleteLodgingUnitParams) => {
    try {
      await LodgingUnitClient.deleteLodgingUnit(input);
      dispatch([
        businessLodgingUnitListBox.deleteItem(input.businessId, input.id),
        lodgingUnitMapBox.deleteItem(input.id),
      ]);
      return { isLodgingUnitInUse: false };
    } catch (error) {
      const errorCode = (error as GoogleRpcApiError)?.data?.details?.[0].code;
      const isLodgingUnitInUse = errorCode === ErrorCodes.LODGING_UNIT_IN_USE;
      if (isLodgingUnitInUse) {
        return { isLodgingUnitInUse: true };
      }
      throw error;
    }
  },
);

export const checkLodgingUnitUsage = createLodgingObservableAction(
  'checkLodgingUnitUsage',
  async (dispatch, select, input: LodgingInUseCheckParams) => {
    const res = await LodgingClient.lodgingInUseCheck(input);
    return res;
  },
);

export const transferLodgingUnit = createLodgingObservableAction(
  'transferLodgingUnit',
  async (dispatch, select, input: LodgingTransferParams) => {
    const res = await LodgingClient.lodgingTransfer(input);
    return res;
  },
);
