import { Record } from 'immutable';
import {
  ComMoegoCommonParamsFilterParamsProperty,
  ComMoegoCommonParamsFilterParamsProperty as FilterParamsProperty,
} from '../../openApi/customer-schema';
import { OwnList, createOwnListBox } from '../utils/OwnList';
import { createRecordMapBox } from '../utils/RecordMap';
import { type EnumOptions, createEnum } from '../utils/createEnum';
import { ID_LOADING, isNormal } from '../utils/identifier';
import { createBox } from '../utils/utils';
import { TEMPORARY_VIEW_ID } from './clientView.constants';
import { OperatorMap } from '../../container/Client/ClientList/components/FilterItems/FilterProperty.operator';
import { type CustomerFilterItem } from './customer.boxes';

export const leadInternalFilter: CustomerFilterItem = {
  operator: OperatorMap.In,
  property: ComMoegoCommonParamsFilterParamsProperty.customer_type,
  value: '',
  values: ['LEAD'],
};

export const customerInternalFilter: CustomerFilterItem = {
  operator: OperatorMap.In,
  property: ComMoegoCommonParamsFilterParamsProperty.customer_type,
  value: '',
  values: ['CUSTOMER'],
};

export type ClientFilterListSource = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
export const ClientFilterListSourceMap: EnumOptions<
  | 'ClientList'
  | 'MassText'
  | 'MassEmail'
  | 'AbandonedList'
  | 'Discount'
  | 'Payment'
  | 'Retention'
  | 'LeadList'
  | 'LeadKanban'
  | 'DepositRule',
  ClientFilterListSource,
  {
    // 标记是否使用大写的operator，原来做了一层映射，新的实现直接去掉了
    // 每个 source 更新后需要明确标记使用了何种 operator
    useStandardOperator?: boolean;
  }
> = createEnum({
  ClientList: [
    1,
    {
      useStandardOperator: true,
    },
  ],
  MassText: [
    2,
    {
      useStandardOperator: true,
    },
  ],
  MassEmail: [
    3,
    {
      useStandardOperator: false,
    },
  ],
  AbandonedList: [
    4,
    {
      useStandardOperator: false,
    },
  ],
  Discount: [
    5,
    {
      useStandardOperator: false,
    },
  ],
  Payment: [
    6,
    {
      useStandardOperator: false,
    },
  ],
  Retention: [
    7,
    {
      useStandardOperator: false,
    },
  ],
  LeadList: [
    8,
    {
      useStandardOperator: true,
    },
  ],
  // LeadKanban 其实只用到了 chosen customer 相关状态，filter 用的 LeadList
  LeadKanban: [
    9,
    {
      useStandardOperator: true,
    },
  ],
  DepositRule: [
    10,
    {
      useStandardOperator: false,
    },
  ],
});

export type ClientFiltersValue = string | number;

export interface ClientFiltersModel<ValueType extends ClientFiltersValue = ClientFiltersValue> {
  ownKey: string;
  source: ClientFilterListSource;
  viewId?: number;
  property: FilterParamsProperty;
  operator?: string; // TODO(yueyue: p2): 应该是 FilterParamsOperator，但是目前改动量太大
  values?: ValueType[];
  value?: ValueType;
  valuesForFetch?: ValueType | ValueType[];
  isCustom?: boolean;
}

export type AddFilterModel = Omit<ClientFiltersModel, 'ownKey'>;
export type FilterEventPropsModel<T extends ClientFiltersValue = ClientFiltersValue> = Omit<
  ClientFiltersModel<T>,
  'property' | 'source' | 'ownKey'
>;

export type SyncClientFilterOptions = {
  fromSource: ClientFilterListSource;
  fromViewId?: number;
  toSource: ClientFilterListSource;
  toViewId?: number;
  /**
   * @description 是否对新老 filter 操作符进行兼容
   */
  compatible?: boolean;
};

export class ClientFiltersRecord extends Record<ClientFiltersModel>({
  ownKey: '',
  source: ClientFilterListSourceMap.ClientList,
  viewId: 0,
  property: FilterParamsProperty.client_status,
  operator: '',
  valuesForFetch: [],
  value: '',
  values: [],
  isCustom: false,
}) {
  static ownListKey(accountId: number, source: ClientFilterListSource, viewId: number = TEMPORARY_VIEW_ID) {
    return `${accountId}-${source}${appendViewIdToKey(viewId)}`;
  }

  static ownKey(source: ClientFilterListSource, property: FilterParamsProperty, viewId: number = TEMPORARY_VIEW_ID) {
    return `${source}${appendViewIdToKey(viewId)}-${property}`;
  }

  /**
   * @description change filter property key
   */
  static syncPropertyKey(propertyKey: string, options: SyncClientFilterOptions) {
    const { fromSource, toSource, fromViewId, toViewId } = options;
    const fromPrefix = `${fromSource}${appendViewIdToKey(fromViewId)}-`;
    const toPrefix = `${toSource}${appendViewIdToKey(toViewId)}-`;
    return propertyKey.replace(fromPrefix, toPrefix);
  }
}

export const ClientFiltersInitState = new ClientFiltersRecord();

export function appendViewIdToKey(viewId: number = TEMPORARY_VIEW_ID) {
  return isNormal(viewId) ? `-${viewId}` : '';
}

export class EditingClientFiltersRecord extends Record<{
  accountId: number;
  property?: FilterParamsProperty;
}>({
  accountId: ID_LOADING,
  property: undefined,
}) {}

// 这个 ui box 其实只有 online booking 中用到，其他地方也没法用，可以考虑移除
export const clientFiltersVisibleBox = createBox('customer/filters/visible', false);
export const clientFiltersMapBox = createRecordMapBox('customer/filters', ClientFiltersRecord, 'ownKey');
export const clientFiltersListBox = createOwnListBox('customer/filters/list', OwnList.ss());

// use for save state of editing filter temporary ( such as custom / total / date / in not in filters )
// it will be clear when add other filter and dropdown disappear
export const editingClientFilterBox = createRecordMapBox(
  'customer/filters/editing',
  EditingClientFiltersRecord,
  'accountId',
);
