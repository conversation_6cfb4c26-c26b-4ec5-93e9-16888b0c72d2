import { Record as ImmutableRecord } from 'immutable';
import {
  ComMoegoCommonParamsSortParamsOrder as SortParamsOrder,
  ComMoegoCommonParamsSortParamsProperty as SortParamsProperty,
} from '../../openApi/customer-schema';
import { OwnList, createOwnListBox } from '../utils/OwnList';
import { createRecordMapBox } from '../utils/RecordMap';
import { ID_ANONYMOUS, ID_LOADING } from '../utils/identifier';
import { createBox } from '../utils/utils';
import { type ClientFilterListSource, ClientFilterListSourceMap } from './clientFilters.boxes';
import { type ViewDetail } from './clientView.types';

export type ClientViewRecordModel = Omit<ViewDetail, 'filter'> & {
  filter: ViewDetail['filter'] | null;
  isFilterChanged: boolean;
  isReady: boolean;
};

export class ClientViewRecord extends ImmutableRecord<ClientViewRecordModel>({
  id: ID_ANONYMOUS,
  title: '',
  createdAt: '',
  updatedAt: '',
  companyId: ID_ANONYMOUS,
  fields: [],
  orderBy: {
    order: SortParamsOrder.asc,
    property: SortParamsProperty.first_name,
  },
  filter: null,
  isDefault: false,
  isReady: false,
  staffId: ID_ANONYMOUS,
  isFilterChanged: false,
}) {
  static readonly ownListKey = (accountId: number, source: number) => `${accountId}-${source}`;
  get filterSize() {
    return this.filter?.filters?.length || 0;
  }
}

export const clientViewListBox = createOwnListBox('client/view/list', OwnList.sn());
export const clientViewMapBox = createRecordMapBox('client/view/map', ClientViewRecord, 'id');
export const initialClientViewMapBox = createRecordMapBox('client/view/map/initial', ClientViewRecord, 'id');

export class EditingClientViewRecord extends ImmutableRecord<{
  accountId: number;
  viewId: number;
  source: ClientFilterListSource;
  ownKey: string;
}>({
  accountId: ID_LOADING,
  source: ClientFilterListSourceMap.ClientList,
  viewId: 0,
  ownKey: '',
}) {
  static ownKey(accountId: number, source: ClientFilterListSource) {
    return `${accountId}-${source}`;
  }
}

export const currentClientViewMapBox = createRecordMapBox('client/view/current', EditingClientViewRecord, 'ownKey');
export const customizeColumnsVisibleBox = createBox('client/customize/column/visible', false);
export const clientViewOnboardingStepBox = createBox('client/view/onboarding/step/current', 1);
