import { action } from 'amos';
import { BffLeadsClient } from '../../middleware/bff';
import { type LeadLifeCycleRecord, leadLifeCycleListBox, leadLifeCycleMapBox } from './leadLifeCycle.boxes';
import { currentAccountIdBox } from '../account/account.boxes';
import { type RecordProps } from '../utils/RecordMap';

export const getLeadLifeCycleList = action(async (dispatch, select) => {
  const accountId = select(currentAccountIdBox);
  const { lifeCycleList } = await BffLeadsClient.getLifeCycleList({});
  dispatch([
    leadLifeCycleMapBox.mergeItems(lifeCycleList),
    leadLifeCycleListBox.setList(
      accountId,
      lifeCycleList.map((item) => item.id),
    ),
  ]);
});

export const createLeadLifeCycle = action(
  async (dispatch, select, input: Omit<RecordProps<LeadLifeCycleRecord>, 'id' | 'sort'>) => {
    const accountId = select(currentAccountIdBox);
    const { id } = await BffLeadsClient.createLifeCycle(input);
    dispatch([
      leadLifeCycleMapBox.mergeItem(id, {
        ...input,
        id,
      }),
      leadLifeCycleListBox.pushList(accountId, id),
    ]);
  },
);

export const updateLeadLifeCycle = action(
  async (dispatch, _select, input: Omit<RecordProps<LeadLifeCycleRecord>, 'sort'>) => {
    const { isDefault, ...restInput } = input;
    await BffLeadsClient.updateLifeCycle(restInput);
    dispatch(leadLifeCycleMapBox.mergeItem(input.id, input));
  },
);

export const deleteAndTransferLeadLifeCycle = action(
  async (dispatch, select, { fromId, toId }: { fromId: string; toId?: string }) => {
    const accountId = select(currentAccountIdBox);
    await BffLeadsClient.deleteLifeCycle({ fromId, toId: toId ?? '0' });
    dispatch([leadLifeCycleMapBox.deleteItem(fromId), leadLifeCycleListBox.deleteItem(accountId, fromId)]);
  },
);

export const sortLeadLifeCycleList = action(async (dispatch, select, lifeCycleList: string[]) => {
  const accountId = select(currentAccountIdBox);
  await BffLeadsClient.sortLifeCycleList({
    lifeCycleList: lifeCycleList.map((id) => ({ id })),
  });
  dispatch([leadLifeCycleListBox.setList(accountId, lifeCycleList)]);
});
