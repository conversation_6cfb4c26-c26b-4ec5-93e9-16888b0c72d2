import { Record } from 'immutable';
import { type OpenApiModels } from '../../openApi/schema';
import { PagedList } from '../utils/PagedList';
import { createRecordMapBox } from '../utils/RecordMap';
import { ID_LOADING } from '../utils/identifier';

export class RepeatExpiryRecord extends Record<
  OpenApiModels['GET/message/reminder/expiry/query']['Res']['customerList'][0] & {
    expiryApptNum: number;
  }
>({
  expiryApptNum: -1,
  appointmentDate: '',
  appointmentEndTime: 0,
  appointmentStartTime: 0,
  customerId: 0,
  firstName: '',
  lastApptId: 0,
  lastName: '',
  petBreedList: [
    {
      breed: '',
      name: '',
      petId: 0,
      // customerId: 0,
      // petName: '',
    },
  ],
  upcomingCount: 0,
}) {}

export class RepeatExpiryRecordFilterRecord extends Record({
  pageNum: 1,
  pageSize: 10,
}) {}

export const repeatExpiryMapBox = createRecordMapBox('repeat/expiry/map', RepeatExpiryRecord, 'customerId');
export const repeatExpiryListBox = createRecordMapBox(
  'repeat/expiry/list',
  new PagedList({ filter: new RepeatExpiryRecordFilterRecord(), key: 0 }, 0),
  'key',
);

export class RepeatExpiryApptNumRecord extends Record({
  expiryApptNum: ID_LOADING,
  businessId: ID_LOADING,
}) {}

export const repeatExpiryApptNumMapBox = createRecordMapBox(
  'repeat/expiry/appt/num',
  RepeatExpiryApptNumRecord,
  'businessId',
);
