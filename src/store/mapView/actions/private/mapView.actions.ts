import { type ListStaffTrackingParams } from '@moego/api-web/moego/api/organization/v1/staff_tracking_api';
import { action } from 'amos';
import { getNumericLatlng } from '../../../../container/MapViewV2/components/Map/utils';
import { http } from '../../../../middleware/api';
import { StaffTrackingClient } from '../../../../middleware/clients';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { selectMobileBusinessId } from '../../../../store/business/location.selectors';
import { omitEmpty } from '../../../../utils/misc';
import { createMapViewObservableAction } from '../../../observableServices/observableServices';
import { get, group } from '../../../utils/utils';
import {
  type DrivingInfoCalculationParams,
  DrivingInfoCalculationRecord,
  type MapViewConfigModel,
  StaffTrackingRecord,
  drivingInfoCalculationMapBox,
  mapViewConfigMapBox,
  mapViewStaffTrackingListBox,
  mapViewStaffTrackingMapBox,
} from '../../mapView.boxes';

export type MapViewConfigModelParams = Partial<Omit<MapViewConfigModel, 'businessId'>>;

export const setMapViewConfig = action(
  async (dispatch, select, params: Partial<MapViewConfigModelParams>, bizId = select(selectMobileBusinessId)) => {
    dispatch(mapViewConfigMapBox.mergeItem(`${bizId}`, params));
  },
);

export const getStaffTrackingList = createMapViewObservableAction(
  'getStaffTrackingList',
  async (dispatch, select, staffIds: ListStaffTrackingParams['staffIds']) => {
    // 后端接口要求 array length > 0，否则会报错，暂时由前端过滤这种 case
    const { staffTrackings } =
      staffIds?.length > 0
        ? await StaffTrackingClient.listStaffTracking({ staffIds: staffIds })
        : { staffTrackings: [] };

    const trackingMap = new Map(
      group(
        staffTrackings,
        (r) => r.staffId,
        (r) => r,
      ),
    );
    trackingMap.forEach((items, staffId) => {
      dispatch([
        mapViewStaffTrackingListBox.setList(staffId, items.map(get('id'))),
        mapViewStaffTrackingMapBox.mergeItems(items.map(StaffTrackingRecord.build)),
      ]);
    });

    return staffTrackings;
  },
);

export const getDrivingInfoCalculation = createMapViewObservableAction(
  'getDrivingInfoCalculation',
  async (dispatch, select, staffId: number, input: DrivingInfoCalculationParams, signal?: AbortSignal) => {
    const params = omitEmpty(input);
    if (
      !params.addressLat ||
      !params.addressLng ||
      (!params.clientIdsFrom?.length &&
        !params.clientIdsTo?.length &&
        !params.vanStaffIdsFrom?.length &&
        !params.vanStaffIdsTo?.length)
    ) {
      // 地址不存在或者没有 client，不需要计算
      return;
    }
    if (!params.businessId) {
      const businessId = select(currentBusinessIdBox);
      params.businessId = businessId;
    }
    const r = await http.open('POST/grooming/google/map/driving-info/cal', params, {
      signal,
    });
    const ownId = DrivingInfoCalculationRecord.ownId(
      staffId,
      getNumericLatlng({ lat: params.addressLat, lng: params.addressLng }),
    );
    dispatch(drivingInfoCalculationMapBox.mergeItemDeep(ownId, r));
  },
);
