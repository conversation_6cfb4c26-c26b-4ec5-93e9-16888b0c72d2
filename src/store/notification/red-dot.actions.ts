import { action } from 'amos';
import { http } from '../../middleware/api';
import { LoanClient } from '../../middleware/clients';
import { currentBusinessIdBox } from '../business/business.boxes';
import { selectCurrentBusiness } from '../business/business.selectors';
import { selectCurrentStaff } from '../staff/staff.selectors';
import { RedDotScenes, businessRedDotBox } from './red-dot.boxes';
import { selectDisputeHasUnread, selectFinanceHasUpdated } from './red-dot.selectors';

export const getDisputeRedDotStatus = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const disputeHasUnread = select(selectDisputeHasUnread);
    if (disputeHasUnread) return;
    const res = await http.open('GET/payment/stripe/dispute/readStatus');
    dispatch(
      businessRedDotBox.updateItem(businessId, (v) => {
        return v.set(
          'redDots',
          v.redDots.update(RedDotScenes.DISPUTE, (redDot) => ({
            ...redDot,
            display: res.hasUnread,
          })),
        );
      }),
    );
  },
);

export const markDisputeRedDotRead = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const disputeHasUnread = select(selectDisputeHasUnread);
    if (!disputeHasUnread) return;
    await http.open('PUT/payment/stripe/dispute/readStatus');
    dispatch(
      businessRedDotBox.updateItem(businessId, (v) => {
        return v.set(
          'redDots',
          v.redDots.update(RedDotScenes.DISPUTE, (redDot) => ({
            ...redDot,
            display: false,
          })),
        );
      }),
    );
  },
);

export const getFinanceRedDotStatus = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const staff = select(selectCurrentStaff);
    const business = select(selectCurrentBusiness);
    const financeHasUpdated = select(selectFinanceHasUpdated);
    const isUS = business.isUS();
    if (!isUS || !staff.isCompanyOwner() || financeHasUpdated) return;
    const res = await LoanClient.getNotableUpdates({});
    dispatch(
      businessRedDotBox.updateItem(businessId, (v) => {
        return v.set(
          'redDots',
          v.redDots.update(RedDotScenes.FINANCE, (redDot) => ({
            ...redDot,
            display: res.haveUpdates,
          })),
        );
      }),
    );
  },
);

export const markFinanceRedDotRead = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const financeHasUpdated = select(selectFinanceHasUpdated);
    if (!financeHasUpdated) return;
    await LoanClient.dismissNotableUpdates({});
    dispatch(
      businessRedDotBox.updateItem(businessId, (v) => {
        return v.set(
          'redDots',
          v.redDots.update(RedDotScenes.FINANCE, (redDot) => ({
            ...redDot,
            display: false,
          })),
        );
      }),
    );
  },
);
