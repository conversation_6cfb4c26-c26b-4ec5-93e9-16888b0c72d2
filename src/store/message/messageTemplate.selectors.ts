import { selector } from 'amos';
import { currentBusinessIdBox } from '../business/business.boxes';
import { selectBDFeatureEnable } from '../company/company.selectors';
import { PET_PARENT_PORTAL_LINK_VARIABLE, TemplateMessageListMapBox } from './messageTemplate.boxes';

export const selectTemplateMessageList = selector((select, businessId: number = select(currentBusinessIdBox)) => {
  return select(TemplateMessageListMapBox).mustGetItem(businessId).messageTemplateSimpleViews;
});

export const selectEnterpriseTemplateMessageList = selector(
  (select, businessId: number = select(currentBusinessIdBox)) => {
    return select(TemplateMessageListMapBox).mustGetItem(businessId).messageEnterpriseTemplateViews;
  },
);
export const selectTemplateMessageCustomList = selector((select, businessId: number = select(currentBusinessIdBox)) => {
  return select(TemplateMessageListMapBox).mustGetItem(businessId).messageTemplateSimpleViews;
});

export const selectTemplateMessageSystemList = selector((select, businessId: number = select(currentBusinessIdBox)) => {
  return select(TemplateMessageListMapBox).mustGetItem(businessId).messageSystemTemplateSimpleViews;
});

export const selectTemplatePlaceholderList = selector((select, businessId: number = select(currentBusinessIdBox)) => {
  const isBDEnabled = select(selectBDFeatureEnable());
  const list = select(TemplateMessageListMapBox).mustGetItem(businessId).messageTemplatePlaceholderSimpleViews;

  return list
    .filter((item) => {
      // BD 用户过滤掉 petParentPortalLink
      if (item.placeholderText === PET_PARENT_PORTAL_LINK_VARIABLE) {
        return !isBDEnabled;
      }
      return true;
    })
    .sort((a, b) => a.placeholderGroup?.localeCompare?.(b.placeholderGroup));
});
