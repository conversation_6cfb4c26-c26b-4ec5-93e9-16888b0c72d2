package com.moego.svc.online.booking.server;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.google.protobuf.Int64Value;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.PetToStaffDef;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.CreateBoardingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import com.moego.lib.common.util.Tx;
import com.moego.server.grooming.api.IBookOnlineDepositService;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.payment.api.IPaymentPreAuthService;
import com.moego.server.payment.api.IPaymentRefundService;
import com.moego.server.payment.api.IPaymentStripeService;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.eventbus.OnlineBookingProducer;
import com.moego.svc.online.booking.helper.OrderHelper;
import com.moego.svc.online.booking.helper.OrganizationHelper;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.service.BoardingAddOnDetailService;
import com.moego.svc.online.booking.service.BoardingServiceDetailService;
import com.moego.svc.online.booking.service.BookingRequestAppointmentMappingService;
import com.moego.svc.online.booking.service.BookingRequestModifyService;
import com.moego.svc.online.booking.service.BookingRequestService;
import com.moego.svc.online.booking.service.DaycareAddOnDetailService;
import com.moego.svc.online.booking.service.DaycareServiceDetailService;
import com.moego.svc.online.booking.service.EvaluationTestDetailService;
import com.moego.svc.online.booking.service.FeedingMedicationService;
import com.moego.svc.online.booking.service.FeedingService;
import com.moego.svc.online.booking.service.GroomingAddOnDetailService;
import com.moego.svc.online.booking.service.GroomingServiceDetailService;
import com.moego.svc.online.booking.service.LodgingService;
import com.moego.svc.online.booking.service.MedicationService;
import com.moego.svc.online.booking.service.PetDetailService;
import com.moego.svc.online.booking.service.WaitlistService;
import io.grpc.stub.StreamObserver;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BookingRequestServerTest {

    @Mock
    private BookingRequestService bookingRequestService;

    @Mock
    private BookingRequestModifyService bookingRequestModifyService;

    @Mock
    private FeedingMedicationService feedingMedicationService;

    @Mock
    private LodgingService lodgingService;

    @Mock
    private PetDetailService petDetailService;

    @Mock
    private PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceStub;

    @Mock
    private StreamObserver<AcceptBookingRequestResponse> responseObserver;

    @Mock
    private StreamObserver<Int64Value> int64ResponseObserver;

    @Mock
    private DaycareServiceDetailService daycareServiceDetailService;

    @Mock
    private DaycareAddOnDetailService daycareAddOnDetailService;

    @Mock
    private BoardingServiceDetailService boardingServiceDetailService;

    @Mock
    private BoardingAddOnDetailService boardingAddOnDetailService;

    @Mock
    private EvaluationTestDetailService evaluationTestDetailService;

    @Mock
    private GroomingServiceDetailService groomingServiceDetailService;

    @Mock
    private GroomingAddOnDetailService groomingAddOnDetailService;

    @Mock
    private FeedingService feedingService;

    @Mock
    private OnlineBookingProducer onlineBookingProducer;

    @Mock
    private MedicationService medicationService;

    @Mock
    private WaitlistService waitlistService;

    @Mock
    private IBookOnlineDepositService depositApi;

    @Mock
    private IPaymentRefundService refundApi;

    @Mock
    private BookingRequestAppointmentMappingService bookingRequestAppointmentMappingService;

    @Mock
    private IPaymentStripeService paymentStripeApi;

    @Mock
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    @Mock
    private OrderHelper orderHelper;

    @Mock
    private IPaymentPreAuthService preAuthApi;

    @Mock
    private OrganizationHelper organizationHelper;

    @Mock
    private ServiceHelper serviceHelper;

    @InjectMocks
    private BookingRequestServer bookingRequestServer;

    @Test
    void shouldCallGetLastLodgingUnitIdForDaycareService() {

        // Given
        long companyId = 1L;
        long businessId = 2L;
        long customerId = 3L;
        long bookingRequestId = 4L;
        long petId = 5L;
        long lodgingUnitId = 7L;

        // Mock booking request with daycare service
        var bookingRequestModel = BookingRequestModel.newBuilder()
                .setId(bookingRequestId)
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setCustomerId(customerId)
                .setServiceTypeInclude(ServiceItemEnum.DAYCARE.getBitValue())
                .build();

        when(bookingRequestService.mustGetBookingRequestModel(any())).thenReturn(bookingRequestModel);
        // mock event bus
        doNothing().when(onlineBookingProducer).pushOnlineBookingAcceptedEvent(any());

        // Mock last lodging unit response
        lenient()
                .when(petDetailService.getLastLodgingUnitId(
                        eq(companyId), eq(businessId), eq(customerId), anyList(), anyList()))
                .thenReturn(Collections.singletonMap(petId, lodgingUnitId));

        // Mock lodging unit exists
        lenient()
                .when(lodgingService.getLodgingUnitByUnitIds(
                        companyId, businessId, Collections.singleton(lodgingUnitId)))
                .thenReturn(Collections.singletonList(
                        LodgingUnitModel.newBuilder().setId(lodgingUnitId).build()));

        // Mock appointment creation
        when(bookingRequestModifyService.createAppointment(
                        any(BookingRequestModel.class),
                        eq(companyId),
                        eq(businessId),
                        eq(0L),
                        eq(AppointmentStatus.UNCONFIRMED),
                        anyList(),
                        anyList(),
                        anyList(),
                        anyList()))
                .thenReturn(Collections.singletonList(1L));

        // When
        AcceptBookingRequestRequest request = AcceptBookingRequestRequest.newBuilder()
                .setId(bookingRequestId)
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .build();

        bookingRequestServer.acceptBookingRequest(request, responseObserver);

        verify(responseObserver).onNext(any(AcceptBookingRequestResponse.class));
        verify(responseObserver).onCompleted();
    }

    /**
     * {@link BookingRequestServer#afterBookingRequestAccepted(List, BookingRequestModel)}
     */
    @Test
    void afterBookingRequestAccepted_whenPreAuth_thenShouldHandlePaymentAndUpdateDeposit() {
        // Arrange
        var appointmentIds = List.of(1L, 2L);
        var bookingRequestId = 123L;
        var businessId = 456L;

        var deposit = new BookOnlineDepositDTO();
        deposit.setId(1);
        deposit.setBusinessId(Math.toIntExact(businessId));
        deposit.setBookingRequestId(bookingRequestId);
        deposit.setDepositType(DepositPaymentTypeEnum.PreAuth);
        deposit.setPaymentId(111);
        deposit.setPreauthInfo(new BookOnlineDepositDTO.PreAuth() {
            {
                setTipsAmount(1.0);
                setServiceTotal(2.0);
                setTaxAmount(3.0);
                setServiceChargeAmount(4.0);
                setPaymentMethodId("paymentMethodId");
                setCardNumber("cardNumber");
                setChargeToken("chargeToken");
            }
        });

        when(depositApi.getOBDepositByBookingRequestId(any(), eq(bookingRequestId)))
                .thenReturn(deposit);

        var appointmentBuilder = AppointmentModel.newBuilder();
        appointmentBuilder.setId(appointmentIds.get(0));
        appointmentBuilder.setBusinessId(businessId);
        var appointment = appointmentBuilder.build();
        when(appointmentStub.getAppointment(any()))
                .thenReturn(GetAppointmentResponse.newBuilder()
                        .setAppointment(appointment)
                        .build());

        when(orderHelper.getBySource(OrderSourceType.APPOINTMENT, appointment.getId(), true))
                .thenReturn(OrderDetailModel.getDefaultInstance());

        var bookingRequestModel = BookingRequestModel.newBuilder()
                .setId(bookingRequestId)
                .setBusinessId(businessId)
                .build();

        // Act
        bookingRequestServer.afterBookingRequestAccepted(appointmentIds, bookingRequestModel);

        // Assert
        verify(bookingRequestAppointmentMappingService, times(appointmentIds.size()))
                .insert(any());
        verify(depositApi).update(any());
        verify(paymentStripeApi, never()).capturePaymentIntent(any());
        verify(preAuthApi).create(any());
        verify(bookingRequestAppointmentMappingService, never()).deleteByBookingRequestId(bookingRequestId);
    }

    /**
     * {@link BookingRequestServer#afterBookingRequestAccepted(List, BookingRequestModel)}
     */
    @Test
    void afterBookingRequestAccepted_whenPrePay_thenShouldHandlePaymentAndUpdateDeposit() {
        // Arrange
        var appointmentIds = List.of(1L, 2L);
        var bookingRequestId = 123L;
        var businessId = 456L;

        var deposit = new BookOnlineDepositDTO();
        deposit.setId(1);
        deposit.setBusinessId(Math.toIntExact(businessId));
        deposit.setBookingRequestId(bookingRequestId);
        deposit.setDepositType(DepositPaymentTypeEnum.PrePay);
        deposit.setPaymentId(111);

        when(depositApi.getOBDepositByBookingRequestId(any(), eq(bookingRequestId)))
                .thenReturn(deposit);

        var bookingRequestModel = BookingRequestModel.newBuilder()
                .setId(bookingRequestId)
                .setBusinessId(businessId)
                .build();

        // Act
        bookingRequestServer.afterBookingRequestAccepted(appointmentIds, bookingRequestModel);

        // Assert
        verify(bookingRequestAppointmentMappingService, times(appointmentIds.size()))
                .insert(any());
        verify(depositApi).update(any());
        verify(paymentStripeApi).capturePaymentIntent(111);
        verify(bookingRequestAppointmentMappingService, never()).deleteByBookingRequestId(bookingRequestId);
    }

    /**
     * {@link BookingRequestServer#afterBookingRequestAccepted(List, BookingRequestModel)}
     */
    @Test
    void afterBookingRequestAccepted_shouldRollbackOnError() {
        // Arrange
        var appointmentIds = List.of(1L);
        var bookingRequestId = 123L;

        var bookingRequestModel =
                BookingRequestModel.newBuilder().setId(bookingRequestId).build();

        when(depositApi.getOBDepositByBookingRequestId(any(), eq(bookingRequestId)))
                .thenThrow(new RuntimeException("Test Exception"));

        // Act
        assertThatCode(() -> bookingRequestServer.afterBookingRequestAccepted(appointmentIds, bookingRequestModel))
                .isInstanceOf(RuntimeException.class);

        // Assert
        verify(bookingRequestAppointmentMappingService, times(appointmentIds.size()))
                .insert(any());
        verify(bookingRequestAppointmentMappingService).deleteByBookingRequestId(bookingRequestId);
        verify(depositApi, never()).update(any());
        verifyNoInteractions(appointmentStub);
    }

    @Test
    void createBookingRequest() {

        var bookingRequestServer = spy(this.bookingRequestServer);

        CreateBookingRequestRequest request = CreateBookingRequestRequest.newBuilder()
                .setCompanyId(1)
                .setBusinessId(1)
                .setCustomerId(1)
                .setStartDate("2024-01-01")
                .setEndDate("2024-01-01")
                .addServices(CreateBookingRequestRequest.Service.newBuilder()
                        .setBoarding(CreateBookingRequestRequest.BoardingService.newBuilder()
                                .setService(CreateBoardingServiceDetailRequest.newBuilder()
                                        .setPetId(1)
                                        .setLodgingId(1)
                                        .setServiceId(1)
                                        .setServicePrice(1)
                                        .setTaxId(1)
                                        .setStartDate("2024-01-01")
                                        .setEndDate("2024-01-01")
                                        .setStartTime(9 * 60)
                                        .setEndTime(11 * 60)
                                        .build())
                                .build())
                        .build())
                .setStartTime(9 * 60)
                .setEndTime(11 * 60)
                .setStatus(1)
                .build();

        try (var tx = mockStatic(Tx.class)) {
            tx.when(() -> Tx.doInTransaction(any(Supplier.class))).thenReturn(1L);

            doNothing().when(bookingRequestServer).refreshBookingRequest(anyLong());

            bookingRequestServer.createBookingRequest(request, int64ResponseObserver);
        }

        verify(int64ResponseObserver).onNext(Int64Value.of(1L));
        verify(int64ResponseObserver).onCompleted();
    }

    @Test
    void doStaffAssign_withEmptyEvaluationServiceDetails_shouldReturnImmediately() {
        var bookingRequestModel = BookingRequestModel.getDefaultInstance();
        var result = GetAutoAssignResponse.newBuilder();

        bookingRequestServer.doStaffAssign(bookingRequestModel, result);

        var expect = GetAutoAssignResponse.getDefaultInstance();

        assertThat(result.build()).isEqualTo(expect);
    }

    @Test
    void doStaffAssign_withValidEvaluationServiceDetails_shouldAssignStaffs() {
        var bookingRequestModel = BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setEvaluation(BookingRequestModel.EvaluationService.newBuilder()
                                .setService(EvaluationTestDetailModel.newBuilder()
                                        .setPetId(1L)
                                        .setEvaluationId(1L)
                                        .build())
                                .build())
                        .build())
                .setBusinessId(100L)
                .build();
        var result = GetAutoAssignResponse.newBuilder();

        when(organizationHelper.listStaffForBusiness(anyLong(), anyLong(), eq(true)))
                .thenReturn(List.of(StaffBasicView.newBuilder().setId(1L).build()));
        when(serviceHelper.getEvaluationByIds(anyList(), eq(100L)))
                .thenReturn(Map.of(
                        1L,
                        EvaluationBriefView.newBuilder()
                                .setId(1L)
                                .setAllowStaffAutoAssign(true)
                                .setIsAllStaff(true)
                                .build()));

        bookingRequestServer.doStaffAssign(bookingRequestModel, result);

        var expect = GetAutoAssignResponse.newBuilder()
                .addEvaluationAssignRequires(GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(1L)
                        .setServiceId(1L)
                        .build())
                .addEvaluationPetToStaffs(PetToStaffDef.newBuilder()
                        .setPetId(1L)
                        .setServiceId(1L)
                        .setStaffId(1L)
                        .build())
                .build();

        assertThat(result.build()).isEqualTo(expect);
    }

    @Test
    void testReCalculateBookingRequestTime() {
        // Arrange
        var groomingService = BookingRequestModel.Service.newBuilder()
                .setGrooming(BookingRequestModel.GroomingService.newBuilder()
                        .setService(GroomingServiceDetailModel.newBuilder()
                                .setStartDate("2025-01-01")
                                .setEndDate("2025-01-02")
                                .setStartTime(9)
                                .setEndTime(18)
                                .build())
                        .build())
                .build();
        var boardingService = BookingRequestModel.Service.newBuilder()
                .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                        .setService(BoardingServiceDetailModel.newBuilder()
                                .setStartDate("2025-01-01")
                                .setEndDate("2025-01-05")
                                .setStartTime(10)
                                .setEndTime(17)
                                .build())
                        .build())
                .build();
        var daycareService = BookingRequestModel.Service.newBuilder()
                .setDaycare(BookingRequestModel.DaycareService.newBuilder()
                        .setService(DaycareServiceDetailModel.newBuilder()
                                .addAllSpecificDates(List.of("2025-01-03", "2025-01-04"))
                                .setStartTime(8)
                                .setEndTime(16)
                                .build())
                        .build())
                .build();

        // Act
        var result = BookingRequestServer.reCalculateBookingRequestTime(
                List.of(groomingService, boardingService, daycareService));

        var expect = new BookingRequest();
        expect.setStartDate("2025-01-01");
        expect.setStartTime(9);
        expect.setEndDate("2025-01-05");
        expect.setEndTime(17);

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void testReCalculateBookingRequestTime_WithServiceOutsideDateRange() {
        // Arrange - grooming service outside boarding/daycare date range
        var groomingService = BookingRequestModel.Service.newBuilder()
                .setGrooming(BookingRequestModel.GroomingService.newBuilder()
                        .setService(GroomingServiceDetailModel.newBuilder()
                                .setStartDate("2025-01-10") // Outside range
                                .setEndDate("2025-01-11") // Outside range
                                .setStartTime(9)
                                .setEndTime(18)
                                .build())
                        .build())
                .build();
        var boardingService = BookingRequestModel.Service.newBuilder()
                .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                        .setService(BoardingServiceDetailModel.newBuilder()
                                .setStartDate("2025-01-01")
                                .setEndDate("2025-01-05")
                                .setStartTime(10)
                                .setEndTime(17)
                                .build())
                        .build())
                .build();

        // Act
        var result = BookingRequestServer.reCalculateBookingRequestTime(List.of(groomingService, boardingService));

        // Assert - only boarding service should be considered
        var expect = new BookingRequest();
        expect.setStartDate("2025-01-01");
        expect.setStartTime(10);
        expect.setEndDate("2025-01-05");
        expect.setEndTime(17);

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void testReCalculateBookingRequestTime_OnlyGroomingService() {
        // Arrange - only grooming service, no boarding/daycare
        var groomingService = BookingRequestModel.Service.newBuilder()
                .setGrooming(BookingRequestModel.GroomingService.newBuilder()
                        .setService(GroomingServiceDetailModel.newBuilder()
                                .setStartDate("2025-01-01")
                                .setEndDate("2025-01-02")
                                .setStartTime(9)
                                .setEndTime(18)
                                .build())
                        .build())
                .build();

        // Act
        var result = BookingRequestServer.reCalculateBookingRequestTime(List.of(groomingService));

        // Assert - should fall back to original logic
        var expect = new BookingRequest();
        expect.setStartDate("2025-01-01");
        expect.setStartTime(9);
        expect.setEndDate("2025-01-02");
        expect.setEndTime(18);

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void testReCalculateBookingRequestTime_PartiallyOverlappingServices() {
        // Arrange - grooming service partially overlaps with boarding range
        var groomingService = BookingRequestModel.Service.newBuilder()
                .setGrooming(BookingRequestModel.GroomingService.newBuilder()
                        .setService(GroomingServiceDetailModel.newBuilder()
                                .setStartDate("2025-01-01") // In range
                                .setEndDate("2025-01-10") // Out of range
                                .setStartTime(8)
                                .setEndTime(19)
                                .build())
                        .build())
                .build();
        var boardingService = BookingRequestModel.Service.newBuilder()
                .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                        .setService(BoardingServiceDetailModel.newBuilder()
                                .setStartDate("2025-01-01")
                                .setEndDate("2025-01-05")
                                .setStartTime(10)
                                .setEndTime(17)
                                .build())
                        .build())
                .build();

        // Act
        var result = BookingRequestServer.reCalculateBookingRequestTime(List.of(groomingService, boardingService));

        // Assert - only grooming start date should be considered, not end date
        var expect = new BookingRequest();
        expect.setStartDate("2025-01-01");
        expect.setStartTime(8); // Earlier time from grooming
        expect.setEndDate("2025-01-05");
        expect.setEndTime(17);

        assertThat(result).isEqualTo(expect);
    }
}
