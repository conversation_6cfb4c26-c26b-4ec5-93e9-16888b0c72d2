package com.moego.svc.account.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.moego.lib.common.exception.BizException;
import com.moego.svc.account.model.enums.SessionArchiveTaskStatus;
import com.moego.svc.account.model.params.SessionArchiveTaskCreateParams;
import com.moego.svc.account.model.params.SessionCreateParams;
import java.time.Duration;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@Transactional
@ActiveProfiles("unit-test")
class SessionArchiveTaskServiceTest {

    @Autowired
    private SessionArchiveTaskService sessionArchiveTaskService;

    @Autowired
    private SessionService sessionService;

    @Test
    void testArchiveTask() {
        // 创建任务
        var params = new SessionArchiveTaskCreateParams();
        params.setStartId(1L);
        params.setEndId(100L);
        params.setStep(10);
        params.setMaxDate(LocalDate.now());

        var taskId = sessionArchiveTaskService.createArchiveTask(params);
        assertThat(taskId).isGreaterThan(0);

        // 获取任务
        var task = sessionArchiveTaskService.getArchiveTask(taskId);
        assertThat(task).isNotNull();
        assertThat(task.getId()).isEqualTo(taskId);
        assertThat(task.getStartId()).isEqualTo(params.getStartId());
        assertThat(task.getEndId()).isEqualTo(params.getEndId());
        assertThat(task.getStep()).isEqualTo(params.getStep());
        assertThat(task.getMaxDate()).isEqualTo(params.getMaxDate());
        assertThat(task.getCurrentId()).isEqualTo(params.getStartId());
        assertThat(task.getStatus()).isEqualTo(SessionArchiveTaskStatus.CREATED);

        // 开始 moving
        task = sessionArchiveTaskService.updateTaskStatus(taskId, SessionArchiveTaskStatus.MOVING);
        assertThat(task.getStatus()).isEqualTo(SessionArchiveTaskStatus.MOVING);

        // 停止 moving
        task = sessionArchiveTaskService.updateTaskStatus(taskId, SessionArchiveTaskStatus.STOP_MOVING);
        assertThat(task.getStatus()).isEqualTo(SessionArchiveTaskStatus.STOP_MOVING);
    }

    @Test
    void testStartNonExistentTask() {
        assertThatThrownBy(() -> sessionArchiveTaskService.updateTaskStatus(-1L, SessionArchiveTaskStatus.MOVING))
                .isInstanceOf(BizException.class);
    }

    @Test
    void testStartRunningTask() {
        // 创建任务
        var params = new SessionArchiveTaskCreateParams();
        params.setStartId(1L);
        params.setEndId(100L);
        params.setStep(10);
        params.setMaxDate(LocalDate.now());

        var taskId = sessionArchiveTaskService.createArchiveTask(params);

        // 开始 moving
        var task = sessionArchiveTaskService.updateTaskStatus(taskId, SessionArchiveTaskStatus.MOVING);
        assertThat(task.getStatus()).isEqualTo(SessionArchiveTaskStatus.MOVING);

        // 尝试再次开始 moving
        assertThatThrownBy(() -> sessionArchiveTaskService.updateTaskStatus(taskId, SessionArchiveTaskStatus.MOVING))
                .isInstanceOf(BizException.class);
    }

    @Test
    void testStopNonExistentTask() {
        assertThatThrownBy(() -> sessionArchiveTaskService.updateTaskStatus(-1L, SessionArchiveTaskStatus.STOP_MOVING))
                .isInstanceOf(BizException.class);
    }

    @Test
    void testStopNonRunningTask() {
        // 创建任务
        var params = new SessionArchiveTaskCreateParams();
        params.setStartId(1L);
        params.setEndId(100L);
        params.setStep(10);
        params.setMaxDate(LocalDate.now());

        var taskId = sessionArchiveTaskService.createArchiveTask(params);

        // 尝试停止未运行的任务
        assertThatThrownBy(
                        () -> sessionArchiveTaskService.updateTaskStatus(taskId, SessionArchiveTaskStatus.STOP_MOVING))
                .isInstanceOf(BizException.class);
    }

    @Test
    void testGetGreaterEqualId() {
        // 创建三个 session，获取它们的 ID
        var session1Id = sessionService
                .createSession(SessionCreateParams.builder()
                        .accountId(1L)
                        .maxAge(Duration.ofSeconds(3600))
                        .source("junit")
                        .build())
                .getSessionId();

        var session2Id = sessionService
                .createSession(SessionCreateParams.builder()
                        .accountId(1L)
                        .maxAge(Duration.ofSeconds(3600))
                        .source("junit")
                        .build())
                .getSessionId();

        var result = sessionArchiveTaskService.getGreaterEqualSessionId(session1Id);
        assertThat(result).isEqualTo(session1Id);

        result = sessionArchiveTaskService.getGreaterEqualSessionId(session2Id);
        assertThat(result).isEqualTo(session2Id);

        result = sessionArchiveTaskService.getGreaterEqualSessionId(session2Id + *********);
        assertThat(result).isNull();
    }
}
