package com.moego.svc.account.service;

import static com.moego.svc.account.repository.jooq.Tables.ACCOUNT;
import static com.moego.svc.account.repository.jooq.Tables.ACCOUNT_HISTORY;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;

import com.moego.lib.common.exception.BizException;
import com.moego.svc.account.model.dto.AccountDTO;
import com.moego.svc.account.model.enums.AccountStatusEnum;
import com.moego.svc.account.model.params.AccountCreateParams;
import com.moego.svc.account.model.params.AccountUpdateParams;
import com.moego.svc.account.repository.jooq.Tables;
import com.moego.svc.account.repository.jooq.tables.records.AccountHistoryRecord;
import com.moego.svc.account.repository.jooq.tables.records.AccountRecord;
import com.moego.svc.account.utils.Namespace;
import com.moego.svc.account.utils.NamespaceUtils;
import java.util.List;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * AccountService 的测试用例
 * 使用 Transactional 注解会在单元测试完毕后回滚所有数据库变更，避免污染数据库
 */
@SpringBootTest
@Transactional
@ActiveProfiles("unit-test")
public class AccountServiceTest {

    private static final String EMPTY = "";

    @Autowired
    private AccountService accountService;

    @Autowired
    private DSLContext dsl;

    private String randomEmail() {
        String email = "junit-test-" + System.currentTimeMillis() + "@moego.pet";
        assertThat(accountService.getAccountByEmail(email, NamespaceUtils.MOEGO))
                .isNull();
        return email;
    }

    private String randomPhoneNumber() {
        String phoneNumber = "+999" + Long.toString(System.currentTimeMillis()).substring(3);
        assertThat(accountService.getAccountByPhoneNumber(phoneNumber, NamespaceUtils.MOEGO))
                .isNull();
        return phoneNumber;
    }

    private void checkPassword(AccountDTO account, String validPassword, String... wrongPasswords) {
        assertThat(account).isNotNull();

        // 正确的密码验证通过
        if (validPassword != null) {
            assertThat(accountService.validatePassword(account.getId(), validPassword))
                    .isTrue();
        }

        // 错误的密码验证不通过
        if (wrongPasswords != null) {
            for (String wrongPassword : wrongPasswords) {
                assertThat(accountService.validatePassword(account.getId(), wrongPassword))
                        .isFalse();
            }
        }
    }

    /**
     * 创建一个 profile 完整的账号, 并进行查询、验证密码、更新、删除等操作
     */
    @Test
    public void testAccount() {
        // 随机生成邮箱，其他参数固定
        String email = randomEmail();
        String phoneNumber = randomPhoneNumber();
        String password = "123456Abc!";
        String firstName = "Hello";
        String lastName = "World";
        String avatarPath =
                "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1601019907ee1ff1ea8c3445a0877db8e417b15111.png";

        AccountCreateParams request = AccountCreateParams.builder()
                .email(email)
                .phoneNumber(phoneNumber)
                .password(password)
                .firstName(firstName)
                .lastName(lastName)
                .avatarPath(avatarPath)
                .build();

        // 创建
        AccountDTO account = accountService.createAccount(request);
        // System.out.println(account);
        assertThat(account).isNotNull();
        assertThat(account.getEmail()).isEqualTo(email);
        assertThat(account.getPhoneNumber()).isEqualTo(phoneNumber);
        assertThat(account.getFirstName()).isEqualTo(firstName);
        assertThat(account.getLastName()).isEqualTo(lastName);
        assertThat(account.getAvatarPath()).isEqualTo(avatarPath);
        assertThat(account.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
        assertThat(account.getCreatedAt()).isEqualTo(account.getUpdatedAt());
        checkPassword(account, password, password + password);

        // 查询
        assertThat(accountService.getAccountById(account.getId(), NamespaceUtils.MOEGO))
                .isEqualTo(account);
        assertThat(accountService.getAccountByEmail(email, NamespaceUtils.MOEGO))
                .isEqualTo(account);
        assertThat(accountService.getAccountByPhoneNumber(phoneNumber, NamespaceUtils.MOEGO))
                .isEqualTo(account);

        String newEmail = randomEmail();
        String newPhoneNumber = randomPhoneNumber();
        String newFirstName = "New";
        String newLastName = "Test";
        String newAvatarPath =
                "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1601019907ee1ff1ea8c3445a0877db8e417b15111.jpg";
        String newPassword = "Vfjh3982rdij&";

        // 修改姓名
        AccountUpdateParams updateParams = AccountUpdateParams.builder()
                .id(account.getId())
                .firstName(newFirstName)
                .lastName(newLastName)
                .build();
        AccountDTO updateNameAccount = accountService.updateAccount(updateParams);
        assertThat(updateNameAccount).isNotNull();
        assertThat(updateNameAccount.getEmail()).isEqualTo(email);
        assertThat(updateNameAccount.getPhoneNumber()).isEqualTo(phoneNumber);
        assertThat(updateNameAccount.getFirstName()).isEqualTo(newFirstName);
        assertThat(updateNameAccount.getLastName()).isEqualTo(newLastName);
        assertThat(updateNameAccount.getAvatarPath()).isEqualTo(avatarPath);
        assertThat(updateNameAccount.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
        assertThat(updateNameAccount.getCreatedAt()).isEqualTo(account.getCreatedAt());
        assertThat(updateNameAccount.getUpdatedAt()).isAfter(account.getUpdatedAt());

        // 修改头像
        updateParams = AccountUpdateParams.builder()
                .id(account.getId())
                .avatarPath(newAvatarPath)
                .build();
        AccountDTO updateAvatarAccount = accountService.updateAccount(updateParams);
        assertThat(updateAvatarAccount).isNotNull();
        assertThat(updateAvatarAccount.getEmail()).isEqualTo(email);
        assertThat(updateAvatarAccount.getPhoneNumber()).isEqualTo(phoneNumber);
        assertThat(updateAvatarAccount.getFirstName()).isEqualTo(newFirstName);
        assertThat(updateAvatarAccount.getLastName()).isEqualTo(newLastName);
        assertThat(updateAvatarAccount.getAvatarPath()).isEqualTo(newAvatarPath);
        assertThat(updateAvatarAccount.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
        assertThat(updateAvatarAccount.getCreatedAt()).isEqualTo(account.getCreatedAt());
        assertThat(updateAvatarAccount.getUpdatedAt()).isAfter(updateNameAccount.getUpdatedAt());

        // 修改邮箱
        updateParams = AccountUpdateParams.builder()
                .id(account.getId())
                .email(newEmail)
                .build();
        AccountDTO updateEmailAccount = accountService.updateAccount(updateParams);
        assertThat(updateEmailAccount).isNotNull();
        assertThat(updateEmailAccount.getEmail()).isEqualTo(newEmail);
        assertThat(updateEmailAccount.getPhoneNumber()).isEqualTo(phoneNumber);
        assertThat(updateEmailAccount.getFirstName()).isEqualTo(newFirstName);
        assertThat(updateEmailAccount.getLastName()).isEqualTo(newLastName);
        assertThat(updateEmailAccount.getAvatarPath()).isEqualTo(newAvatarPath);
        assertThat(updateEmailAccount.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
        assertThat(updateEmailAccount.getCreatedAt()).isEqualTo(account.getCreatedAt());
        assertThat(updateEmailAccount.getUpdatedAt()).isAfter(updateAvatarAccount.getUpdatedAt());

        var histories = dsl.select()
                .from(ACCOUNT_HISTORY)
                .where(ACCOUNT_HISTORY.ACCOUNT_ID.eq(account.getId()))
                .orderBy(ACCOUNT_HISTORY.ID.desc())
                .fetchInto(AccountHistoryRecord.class);
        assertThat(histories).hasSize(1);
        assertThat(histories.get(0).getField()).isEqualTo(Tables.ACCOUNT.EMAIL.getName());
        assertThat(histories.get(0).getOldValue()).isEqualTo(email);
        assertThat(histories.get(0).getDiscardedAt()).isEqualTo(updateEmailAccount.getUpdatedAt());
        assertThat(histories.get(0).getDiscardedBy()).isEqualTo("");

        // 修改手机号
        updateParams = AccountUpdateParams.builder()
                .id(account.getId())
                .phoneNumber(newPhoneNumber)
                .build();
        AccountDTO updatePhoneAccount = accountService.updateAccount(updateParams);
        assertThat(updatePhoneAccount).isNotNull();
        assertThat(updatePhoneAccount.getEmail()).isEqualTo(newEmail);
        assertThat(updatePhoneAccount.getPhoneNumber()).isEqualTo(newPhoneNumber);
        assertThat(updatePhoneAccount.getFirstName()).isEqualTo(newFirstName);
        assertThat(updatePhoneAccount.getLastName()).isEqualTo(newLastName);
        assertThat(updatePhoneAccount.getAvatarPath()).isEqualTo(newAvatarPath);
        assertThat(updatePhoneAccount.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
        assertThat(updatePhoneAccount.getCreatedAt()).isEqualTo(account.getCreatedAt());
        assertThat(updatePhoneAccount.getUpdatedAt()).isAfter(updateEmailAccount.getUpdatedAt());

        histories = dsl.select()
                .from(ACCOUNT_HISTORY)
                .where(ACCOUNT_HISTORY.ACCOUNT_ID.eq(account.getId()))
                .orderBy(ACCOUNT_HISTORY.ID.desc())
                .fetchInto(AccountHistoryRecord.class);
        assertThat(histories).hasSize(2);
        assertThat(histories.get(0).getField()).isEqualTo(Tables.ACCOUNT.PHONE_NUMBER.getName());
        assertThat(histories.get(0).getOldValue()).isEqualTo(phoneNumber);
        assertThat(histories.get(0).getDiscardedAt()).isEqualTo(updatePhoneAccount.getUpdatedAt());
        assertThat(histories.get(0).getDiscardedBy()).isEqualTo("");

        // 修改密码
        updateParams = AccountUpdateParams.builder()
                .id(account.getId())
                .password(newPassword)
                .build();
        AccountDTO updatePasswordAccount = accountService.updateAccount(updateParams);
        assertThat(updatePasswordAccount).isNotNull();
        assertThat(updatePasswordAccount.getEmail()).isEqualTo(newEmail);
        assertThat(updatePasswordAccount.getPhoneNumber()).isEqualTo(newPhoneNumber);
        assertThat(updatePasswordAccount.getFirstName()).isEqualTo(newFirstName);
        assertThat(updatePasswordAccount.getLastName()).isEqualTo(newLastName);
        assertThat(updatePasswordAccount.getAvatarPath()).isEqualTo(newAvatarPath);
        assertThat(updatePasswordAccount.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
        assertThat(updatePasswordAccount.getCreatedAt()).isEqualTo(account.getCreatedAt());
        assertThat(updatePasswordAccount.getUpdatedAt()).isAfter(updatePhoneAccount.getUpdatedAt());
        checkPassword(updatePasswordAccount, newPassword, password);

        histories = dsl.select()
                .from(ACCOUNT_HISTORY)
                .where(ACCOUNT_HISTORY.ACCOUNT_ID.eq(account.getId()))
                .orderBy(ACCOUNT_HISTORY.ID.desc())
                .fetchInto(AccountHistoryRecord.class);
        assertThat(histories).hasSize(3);
        assertThat(histories.get(0).getField()).isEqualTo(Tables.ACCOUNT.PASSWORD.getName());
        assertThat(histories.get(0).getOldValue()).isEqualTo(account.getPassword());
        assertThat(histories.get(0).getDiscardedAt()).isEqualTo(updatePasswordAccount.getUpdatedAt());
        assertThat(histories.get(0).getDiscardedBy()).isEqualTo("");

        // 删除账号
        var operator = "admin";
        accountService.deleteAccount(account.getId(), operator);
        assertThat(accountService.getAccountByEmail(email, NamespaceUtils.MOEGO))
                .isNull();
        assertThat(accountService.getAccountByEmail(newEmail, NamespaceUtils.MOEGO))
                .isNull();
        assertThat(accountService.getAccountByPhoneNumber(phoneNumber, NamespaceUtils.MOEGO))
                .isNull();
        assertThat(accountService.getAccountByPhoneNumber(newPhoneNumber, NamespaceUtils.MOEGO))
                .isNull();

        AccountDTO deletedAccount = accountService.getAccountById(account.getId(), NamespaceUtils.MOEGO);
        assertThat(deletedAccount).isNotNull();
        assertThat(deletedAccount.getStatus()).isEqualTo(AccountStatusEnum.DELETED);
        assertThat(deletedAccount.getCreatedAt()).isEqualTo(account.getCreatedAt());
        assertThat(deletedAccount.getUpdatedAt()).isAfter(updatePasswordAccount.getUpdatedAt());

        histories = dsl.select()
                .from(ACCOUNT_HISTORY)
                .where(ACCOUNT_HISTORY.ACCOUNT_ID.eq(account.getId()))
                .orderBy(ACCOUNT_HISTORY.ID.desc())
                .fetchInto(AccountHistoryRecord.class);
        assertThat(histories).hasSize(4);
        assertThat(histories.get(0).getField()).isEqualTo(Tables.ACCOUNT.STATUS.getName());
        assertThat(histories.get(0).getOldValue()).isEqualTo(String.valueOf(AccountStatusEnum.ACTIVE.getValue()));
        assertThat(histories.get(0).getDiscardedAt()).isEqualTo(deletedAccount.getUpdatedAt());
        assertThat(histories.get(0).getDiscardedBy()).isEqualTo(operator);
    }

    /**
     * 邮箱 + 密码 创建账号
     */
    @Test
    public void testCreateAccount_onlyEmailPassword() {
        String email = randomEmail();
        String password = "123456Abc!";

        AccountCreateParams request =
                AccountCreateParams.builder().email(email).password(password).build();

        AccountDTO account = accountService.createAccount(request);
        // System.out.println(account);
        assertThat(account).isNotNull();
        assertThat(account.getEmail()).isEqualTo(email);
        assertThat(account.getPhoneNumber()).isEqualTo(EMPTY);
        assertThat(account.getFirstName()).isEqualTo(EMPTY);
        assertThat(account.getLastName()).isEqualTo(EMPTY);
        assertThat(account.getAvatarPath()).isEqualTo(EMPTY);
        assertThat(account.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
        assertThat(account.getCreatedAt()).isEqualTo(account.getUpdatedAt());

        checkPassword(account, password, password + password);
    }

    /**
     * 手机号 (无密码) 创建账号
     */
    @Test
    public void testCreateAccount_onlyPhoneNumber() {
        String phoneNumber = randomPhoneNumber();

        AccountCreateParams request =
                AccountCreateParams.builder().phoneNumber(phoneNumber).build();

        AccountDTO account = accountService.createAccount(request);
        // System.out.println(account);
        assertThat(account).isNotNull();
        assertThat(account.getEmail()).isEqualTo(EMPTY);
        assertThat(account.getPhoneNumber()).isEqualTo(phoneNumber);
        assertThat(account.getFirstName()).isEqualTo(EMPTY);
        assertThat(account.getLastName()).isEqualTo(EMPTY);
        assertThat(account.getAvatarPath()).isEqualTo(EMPTY);
        assertThat(account.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
        assertThat(account.getCreatedAt()).isEqualTo(account.getUpdatedAt());

        // 创建账号时用户没设置密码，因此理论上输入啥都是密码错误
        checkPassword(account, null, "", "alkscfnj123");
    }

    /**
     * 无邮箱，无手机号，无密码 创建账号 (第三方注册登录)，并且可以重复创建
     */
    @Test
    public void testCreateAccount_allEmpty() {
        for (int i = 0; i < 3; i++) {
            AccountCreateParams request = AccountCreateParams.builder().build();

            AccountDTO account = accountService.createAccount(request);
            // System.out.println(account);
            assertThat(account).isNotNull();
            assertThat(account.getEmail()).isEqualTo(EMPTY);
            assertThat(account.getPhoneNumber()).isEqualTo(EMPTY);
            assertThat(account.getFirstName()).isEqualTo(EMPTY);
            assertThat(account.getLastName()).isEqualTo(EMPTY);
            assertThat(account.getAvatarPath()).isEqualTo(EMPTY);
            assertThat(account.getStatus()).isEqualTo(AccountStatusEnum.ACTIVE);
            assertThat(account.getCreatedAt()).isEqualTo(account.getUpdatedAt());

            // 创建账号时用户没设置密码，因此理论上输入啥都是密码错误
            checkPassword(account, null, "", "alkscfnj123");
        }
    }

    /**
     * 不存在的账号/空密码，验证密码不通过
     */
    @Test
    public void testValidatePassword_nullAccount() {
        long notExistAccountId = 0L;
        String randomPassword = "qoidnajksz";

        assertThat(accountService.validatePassword(notExistAccountId, randomPassword))
                .isFalse();
        assertThat(accountService.validatePassword(notExistAccountId, null)).isFalse();
    }

    /**
     * 测试 email 相同 namespace 下冲突, 不同 namespace 不冲突
     */
    @Test
    public void testEmailConflict() {
        String email = randomEmail();

        var namespace1 = NamespaceUtils.MOEGO;
        var request1 =
                AccountCreateParams.builder().email(email).namespace(namespace1).build();

        var namespace2 = new Namespace("JUNIT", 1);
        var request2 =
                AccountCreateParams.builder().email(email).namespace(namespace2).build();

        // 创建 account1
        var account1 = accountService.createAccount(request1);

        // 创建 account2
        var account2 = accountService.createAccount(request2);

        // 在两个 namespace 下创建相同 email 的账号，不会冲突
        assertThat(account1).isNotNull();
        assertThat(account2).isNotNull();
        assertThat(account1.getId()).isNotEqualTo(account2.getId());
        assertThat(account1.getEmail()).isEqualTo(email);
        assertThat(account1.getNamespace()).isEqualTo(namespace1);
        assertThat(account2.getEmail()).isEqualTo(email);
        assertThat(account2.getNamespace()).isEqualTo(namespace2);

        // 重复创建会抛异常
        assertThatThrownBy(() -> accountService.createAccount(request1)).isInstanceOf(BizException.class);
    }

    /**
     * 测试忽略大小写相同 email 冲突
     */
    @Test
    public void testCiEmailConflict() {
        String email = randomEmail();
        var request = AccountCreateParams.builder().email(email).build();

        // 创建 account
        accountService.createAccount(request);

        // 转大写
        email = email.toUpperCase();
        var upperRequest = AccountCreateParams.builder().email(email).build();
        // 重复创建（忽略大小写）会抛异常
        assertThatThrownBy(() -> accountService.createAccount(upperRequest)).isInstanceOf(BizException.class);
    }

    /**
     * 测试 phone number 相同 namespace 下冲突, 不同 namespace 不冲突
     */
    @Test
    public void testPhoneNumberConflict() {
        String phoneNumber = randomPhoneNumber();

        var namespace1 = NamespaceUtils.MOEGO;
        var request1 = AccountCreateParams.builder()
                .phoneNumber(phoneNumber)
                .namespace(namespace1)
                .build();

        var namespace2 = new Namespace("JUNIT", 1);
        var request2 = AccountCreateParams.builder()
                .phoneNumber(phoneNumber)
                .namespace(namespace2)
                .build();

        // 创建 account1
        var account1 = accountService.createAccount(request1);

        // 创建 account2
        var account2 = accountService.createAccount(request2);

        // 在两个 namespace 下创建相同 phone number 的账号，不会冲突
        assertThat(account1).isNotNull();
        assertThat(account2).isNotNull();
        assertThat(account1.getId()).isNotEqualTo(account2.getId());
        assertThat(account1.getPhoneNumber()).isEqualTo(phoneNumber);
        assertThat(account1.getNamespace()).isEqualTo(namespace1);
        assertThat(account2.getPhoneNumber()).isEqualTo(phoneNumber);
        assertThat(account2.getNamespace()).isEqualTo(namespace2);

        // 重复创建会抛异常
        assertThatThrownBy(() -> accountService.createAccount(request1)).isInstanceOf(BizException.class);
    }

    /**
     * 测试删除账号后重复使用 email
     */
    @Test
    public void testEmailReuse() {
        var email = randomEmail();
        var namespace = NamespaceUtils.MOEGO;
        var request =
                AccountCreateParams.builder().email(email).namespace(namespace).build();

        // 创建 account
        var oldAccount = accountService.createAccount(request);
        // 删除 account
        accountService.deleteAccount(oldAccount.getId(), null);
        // 删除后查询为 null
        assertThat(accountService.getAccountByEmail(email, namespace)).isNull();

        // 重新创建成功
        assertThatCode(() -> accountService.createAccount(request)).doesNotThrowAnyException();
        // 查询 email，新旧账号 id 不相同
        var newAccount = accountService.getAccountByEmail(email, namespace);
        assertThat(newAccount.getId()).isNotEqualTo(oldAccount.getId());
    }

    /**
     * 测试删除账号后重复使用 phone number
     */
    @Test
    public void testPhoneNumberReuse() {
        var phoneNumber = randomPhoneNumber();
        var namespace = NamespaceUtils.MOEGO;
        var request = AccountCreateParams.builder()
                .phoneNumber(phoneNumber)
                .namespace(namespace)
                .build();

        // 创建 account
        var oldAccount = accountService.createAccount(request);
        // 删除 account
        accountService.deleteAccount(oldAccount.getId(), null);
        // 删除后查询为 null
        assertThat(accountService.getAccountByPhoneNumber(phoneNumber, namespace))
                .isNull();

        // 重新创建成功
        assertThatCode(() -> accountService.createAccount(request)).doesNotThrowAnyException();
        // 查询 email，新旧账号 id 不相同
        var newAccount = accountService.getAccountByPhoneNumber(phoneNumber, namespace);
        assertThat(newAccount.getId()).isNotEqualTo(oldAccount.getId());
    }

    /**
     * 测试 email 大小写的查询（大小写不敏感）
     */
    @Test
    public void testEmailInsensitive() {
        var email = randomEmail();
        var namespace = NamespaceUtils.MOEGO;
        // 创建 account
        var request =
                AccountCreateParams.builder().email(email).namespace(namespace).build();
        var account = accountService.createAccount(request);

        // 小写能查到
        var accountSmallCase = accountService.getAccountByEmail(email.toLowerCase(), namespace);
        assertThat(account).isEqualTo(accountSmallCase);

        // 大写也能查到
        var accountCapitalCase = accountService.getAccountByEmail(email.toUpperCase(), namespace);
        assertThat(account).isEqualTo(accountCapitalCase);
    }

    @Test
    public void testRecoverAccount_emailConflict() {
        String email = randomEmail();
        // 创建 account1
        AccountCreateParams request = AccountCreateParams.builder().email(email).build();
        AccountDTO account1 = accountService.createAccount(request);

        // 删除 account1
        accountService.deleteAccount(account1.getId(), null);

        // 相同邮箱创建 account2
        AccountDTO account2 = accountService.createAccount(request);

        // 删除 account2
        accountService.deleteAccount(account2.getId(), null);

        // 恢复 account1，成功
        accountService.recoverAccount(account1.getId(), null);

        // 恢复 account2，失败（邮箱冲突）
        assertThatThrownBy(() -> accountService.recoverAccount(account2.getId(), null))
                .isInstanceOf(BizException.class);
    }

    @Test
    public void testRecoverAccount_phoneConflict() {
        String phoneNumber = randomPhoneNumber();
        // 创建 account1
        AccountCreateParams request =
                AccountCreateParams.builder().phoneNumber(phoneNumber).build();
        AccountDTO account1 = accountService.createAccount(request);

        // 删除 account1
        accountService.deleteAccount(account1.getId(), null);

        // 相同手机号创建 account2
        AccountDTO account2 = accountService.createAccount(request);

        // 删除 account2
        accountService.deleteAccount(account2.getId(), null);

        // 恢复 account1，成功
        accountService.recoverAccount(account1.getId(), null);

        // 恢复 account2，失败（手机号冲突）
        assertThatThrownBy(() -> accountService.recoverAccount(account2.getId(), null))
                .isInstanceOf(BizException.class);
    }

    @Test
    public void testRecoverAccount_alreadyRecovered() {
        String email = randomEmail();
        // 创建 account
        AccountCreateParams request = AccountCreateParams.builder().email(email).build();
        AccountDTO account = accountService.createAccount(request);

        // 删除 account
        accountService.deleteAccount(account.getId(), null);

        // 恢复 account，成功
        accountService.recoverAccount(account.getId(), null);

        // 重复恢复 account，失败
        assertThatThrownBy(() -> accountService.recoverAccount(account.getId(), null))
                .isInstanceOf(BizException.class);
    }

    /**
     * 数据库中 email 字段使用的是 citext 类型
     * 该用例将测试能否查出忽略大小写的所有结果
     */
    @Test
    public void testEmailQuery() {
        String lowerEmail = randomEmail();
        String upperEmail = lowerEmail.toUpperCase();

        var lowerRequest = AccountCreateParams.builder().email(lowerEmail).build();
        var upperRequest = AccountCreateParams.builder().email(upperEmail).build();
        // 创建 lower email account, 然后删除
        var lowerAccount = accountService.createAccount(lowerRequest);
        accountService.deleteAccount(lowerAccount.getId(), null);
        // 创建 upper email account
        accountService.createAccount(upperRequest);

        // 以下用例均能查出 2 条数据

        // 用 lower email eq 查询
        var records =
                dsl.select().from(ACCOUNT).where(ACCOUNT.EMAIL.eq(lowerEmail)).fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 用 upper email eq 查询
        records = dsl.select().from(ACCOUNT).where(ACCOUNT.EMAIL.eq(upperEmail)).fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 用 lower email like (全等) 模糊匹配
        records =
                dsl.select().from(ACCOUNT).where(ACCOUNT.EMAIL.like(lowerEmail)).fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 用 upper email like (全等) 模糊匹配
        records =
                dsl.select().from(ACCOUNT).where(ACCOUNT.EMAIL.like(upperEmail)).fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 用 lower email like (前缀) 模糊匹配
        String lowerPrefix = lowerEmail.substring(0, lowerEmail.indexOf("@"));
        records = dsl.select()
                .from(ACCOUNT)
                .where(ACCOUNT.EMAIL.startsWith(lowerPrefix))
                .fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 用 upper email like (前缀) 模糊匹配
        String upperPrefix = upperEmail.substring(0, upperEmail.indexOf("@"));
        records = dsl.select()
                .from(ACCOUNT)
                .where(ACCOUNT.EMAIL.startsWith(upperPrefix))
                .fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 用 lower email like (两端) 模糊匹配
        records = dsl.select()
                .from(ACCOUNT)
                .where(ACCOUNT.EMAIL.contains(lowerEmail))
                .fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 用 upper email like (两端) 模糊匹配
        records = dsl.select()
                .from(ACCOUNT)
                .where(ACCOUNT.EMAIL.contains(upperEmail))
                .fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 使用 lower email ilike 模糊匹配
        records = dsl.select()
                .from(ACCOUNT)
                .where(ACCOUNT.EMAIL.likeIgnoreCase(lowerEmail))
                .fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);

        // 使用 upper email ilike 模糊匹配
        records = dsl.select()
                .from(ACCOUNT)
                .where(ACCOUNT.EMAIL.likeIgnoreCase(upperEmail))
                .fetchInto(AccountRecord.class);
        checkEmailQuery(records, lowerEmail, upperEmail);
    }

    private void checkEmailQuery(List<AccountRecord> records, String lowerEmail, String upperEmail) {
        assertThat(records).hasSize(2);
        // 检查两条数据的 email
        var emails = records.stream().map(AccountRecord::getEmail).toList();
        assertThat(emails).contains(lowerEmail, upperEmail);
    }
}
