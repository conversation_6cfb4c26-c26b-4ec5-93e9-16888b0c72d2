package com.moego.api.v3.appointment.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetEvaluationInfo;
import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

class LodgingUtilTest {

    @Test
    void calPetCntPerLodgingPerDay_ReturnsCorrectCounts() {
        List<LodgingAssignInfo> assignInfoList = List.of(LodgingAssignInfo.newBuilder()
                .setLodgingId(1L)
                .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                        .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                .setPetId(1)
                                .setStartDate("2022-01-01")
                                .setEndDate("2022-01-02"))
                        .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                .setPetId(2)
                                .addAllSpecificDates(List.of("2022-01-01", "2022-01-02", "2022-01-03"))))
                .build());

        Map<Long, Map<String, Integer>> result =
                LodgingUtil.calPetCntPerLodgingPerDay("2022-01-01", "2022-01-03", assignInfoList);

        assertThat(result.get(1L).get("2022-01-01")).isEqualTo(2);
        assertThat(result.get(1L).get("2022-01-02")).isEqualTo(2);
        assertThat(result.get(1L).get("2022-01-03")).isEqualTo(1);

        assignInfoList = List.of(
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(1)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setPetId(1)
                                        .setStartDate("2022-01-01")
                                        .setEndDate("2022-01-02"))
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setPetId(2)
                                        .addAllSpecificDates(List.of("2022-01-01", "2022-01-02", "2022-01-03"))))
                        .build(),
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(2)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setPetId(1)
                                        .setStartDate("2022-01-01")
                                        .setEndDate("2022-01-02"))
                                .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                        .setPetId(2)
                                        .addAllSpecificDates(List.of("2022-01-01", "2022-01-02", "2022-01-03"))))
                        .build(),
                LodgingAssignInfo.newBuilder()
                        .setLodgingId(1)
                        .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                                .addPetEvaluations(LodgingAssignPetEvaluationInfo.newBuilder()
                                        .setPetId(1)
                                        .setStartDate("2022-01-01")
                                        .setEndDate("2022-01-01")))
                        .build());
        result = LodgingUtil.calPetCntPerLodgingPerDay("2022-01-01", "2022-01-05", assignInfoList);
        assertThat(result).hasSize(2);

        assertThat(result.get(1L)).hasSize(3);
        assertThat(result.get(1L).get("2022-01-01")).isEqualTo(2);
        assertThat(result.get(1L).get("2022-01-02")).isEqualTo(2);
        assertThat(result.get(1L).get("2022-01-03")).isEqualTo(1);

        assertThat(result.get(2L)).hasSize(3);
        assertThat(result.get(2L).get("2022-01-01")).isEqualTo(2);
        assertThat(result.get(2L).get("2022-01-02")).isEqualTo(2);
        assertThat(result.get(2L).get("2022-01-03")).isEqualTo(1);
    }

    @Test
    void calPetCntPerLodgingPerDay_WhenNoAssignments_ReturnsEmptyMap() {
        List<LodgingAssignInfo> assignInfoList = new ArrayList<>();

        Map<Long, Map<String, Integer>> result =
                LodgingUtil.calPetCntPerLodgingPerDay("2022-01-01", "2022-01-03", assignInfoList);

        assertThat(result).isEmpty();
    }

    @Test
    void calPetCntPerLodgingPerDay_WhenDatesOutOfRange_ReturnsEmptyMap() {
        List<LodgingAssignInfo> assignInfoList = List.of(LodgingAssignInfo.newBuilder()
                .setLodgingId(1L)
                .addAppointments(LodgingAssignAppointmentInfo.newBuilder()
                        .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                .setPetId(1)
                                .setStartDate("2022-01-01")
                                .setEndDate("2022-01-02"))
                        .addPetDetails(LodgingAssignPetDetailInfo.newBuilder()
                                .setPetId(2)
                                .addAllSpecificDates(List.of("2022-01-01", "2022-01-02", "2022-01-03"))))
                .build());

        Map<Long, Map<String, Integer>> result =
                LodgingUtil.calPetCntPerLodgingPerDay("2022-01-04", "2022-01-05", assignInfoList);

        assertThat(result).isEmpty();
    }

    @Test
    void isLodgingAvailable_WhenLodgingHasEnoughCapacity_ReturnsTrue() {
        Map<String, Integer> datePetCntNeed = Map.of("2022-01-01", 1, "2022-01-02", 2);
        Map<String, Integer> petCntPerDay = Map.of("2022-01-01", 1, "2022-01-02", 2);
        assertThat(LodgingUtil.isLodgingAvailable(4, datePetCntNeed, petCntPerDay))
                .isTrue();
    }

    @Test
    void isLodgingAvailable_WhenLodgingDoesNotHaveEnoughCapacity_ReturnsFalse() {
        Map<String, Integer> datePetCntNeed = Map.of("2022-01-01", 1, "2022-01-02", 2);
        Map<String, Integer> petCntPerDay = Map.of("2022-01-01", 1, "2022-01-02", 2);
        assertThat(LodgingUtil.isLodgingAvailable(3, datePetCntNeed, petCntPerDay))
                .isFalse();
    }

    @Test
    void isLodgingAvailable_WhenNoPetsNeedSpace_ReturnsTrue() {
        Map<String, Integer> datePetCntNeed = Map.of("2022-01-01", 0, "2022-01-02", 0);
        Map<String, Integer> petCntPerDay = Map.of("2022-01-01", 1, "2022-01-02", 2);
        assertThat(LodgingUtil.isLodgingAvailable(1, datePetCntNeed, petCntPerDay))
                .isTrue();
    }

    @Test
    void isLodgingAvailable_WhenNoPetsCurrentlyAllocatedButSomePetsNeedSpace_ReturnsTrue() {
        Map<String, Integer> datePetCntNeed = Map.of("2022-01-01", 1, "2022-01-02", 2);
        Map<String, Integer> petCntPerDay = Map.of("2022-01-01", 0, "2022-01-02", 0);
        assertThat(LodgingUtil.isLodgingAvailable(2, datePetCntNeed, petCntPerDay))
                .isTrue();
    }

    @Test
    void isLodgingAvailable_WhenNoPetsCurrentlyAllocatedButSomeDatesHaveNotEnoughSpace_ReturnsFalse() {
        Map<String, Integer> datePetCntNeed = Map.of("2022-01-01", 1, "2022-01-02", 2);
        Map<String, Integer> petCntPerDay = Map.of("2022-01-01", 0, "2022-01-02", 0);
        assertThat(LodgingUtil.isLodgingAvailable(1, datePetCntNeed, petCntPerDay))
                .isFalse();
    }

    @Test
    void filterLodgingTypeByService_WhenLodgingTypeListIsEmpty_ReturnsEmptyList() {
        ServiceBriefView service =
                ServiceBriefView.newBuilder().setLodgingFilter(true).build();
        List<LodgingTypeModel> result = LodgingUtil.filterLodgingTypeByService(new ArrayList<>(), service);
        assertThat(result).isEmpty();
    }

    @Test
    void filterLodgingTypeByService_WhenLodgingFilterIsFalse_ReturnsOriginalList() {
        ServiceBriefView service =
                ServiceBriefView.newBuilder().setLodgingFilter(false).build();
        List<LodgingTypeModel> lodgingTypeList =
                List.of(LodgingTypeModel.newBuilder().setId(1).build());
        List<LodgingTypeModel> result = LodgingUtil.filterLodgingTypeByService(lodgingTypeList, service);
        assertThat(result).isEqualTo(lodgingTypeList);
    }

    @Test
    void filterLodgingTypeByService_WhenLodgingFilterIsTrue_ReturnsFilteredLodgingTypes() {
        ServiceBriefView service = ServiceBriefView.newBuilder()
                .setLodgingFilter(true)
                .addCustomizedLodgings(1)
                .build();
        List<LodgingTypeModel> lodgingTypeList = List.of(
                LodgingTypeModel.newBuilder().setId(1).build(),
                LodgingTypeModel.newBuilder().setId(2).build());
        List<LodgingTypeModel> result = LodgingUtil.filterLodgingTypeByService(lodgingTypeList, service);
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getId()).isEqualTo(1);
    }

    @Nested
    @DisplayName("calLodgingStatus Test")
    class CalLodgingStatusTest {
        private final String startDate = "2024-03-01";
        private final String endDate = "2024-03-03";

        @Test
        @DisplayName("When lodging unit list is empty, should return empty map")
        void whenLodgingUnitListEmpty_shouldReturnEmptyMap() {
            // Arrange
            List<LodgingUnitModel> lodgingUnitList = List.of();
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();

            // Act
            Map<Long, LodgingOccupiedStatus> result =
                    LodgingUtil.calLodgingStatus(startDate, endDate, lodgingUnitList, petCntPerDayPerLodging);

            // Assert
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("When lodging unit has no pets on any day, should return VACANT")
        void whenLodgingUnitHasNoPets_shouldReturnVacant() {
            // Arrange
            LodgingUnitModel unit = LodgingUnitModel.newBuilder().setId(1L).build();
            List<LodgingUnitModel> lodgingUnitList = List.of(unit);
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();
            petCntPerDayPerLodging.put("2024-03-01", new HashMap<>());
            petCntPerDayPerLodging.put("2024-03-02", new HashMap<>());
            petCntPerDayPerLodging.put("2024-03-03", new HashMap<>());

            // Act
            Map<Long, LodgingOccupiedStatus> result =
                    LodgingUtil.calLodgingStatus(startDate, endDate, lodgingUnitList, petCntPerDayPerLodging);

            // Assert
            assertThat(result).containsEntry(1L, LodgingOccupiedStatus.VACANT);
        }

        @Test
        @DisplayName("When lodging unit has pets every day, should return FULLY_OCCUPIED")
        void whenLodgingUnitHasPetsEveryDay_shouldReturnFullyOccupied() {
            // Arrange
            LodgingUnitModel unit = LodgingUnitModel.newBuilder().setId(1L).build();
            List<LodgingUnitModel> lodgingUnitList = List.of(unit);

            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();
            Map<Long, Integer> day1 = new HashMap<>();
            day1.put(1L, 1);
            Map<Long, Integer> day2 = new HashMap<>();
            day2.put(1L, 2);
            Map<Long, Integer> day3 = new HashMap<>();
            day3.put(1L, 1);

            petCntPerDayPerLodging.put("2024-03-01", day1);
            petCntPerDayPerLodging.put("2024-03-02", day2);
            petCntPerDayPerLodging.put("2024-03-03", day3);

            // Act
            Map<Long, LodgingOccupiedStatus> result =
                    LodgingUtil.calLodgingStatus(startDate, endDate, lodgingUnitList, petCntPerDayPerLodging);

            // Assert
            assertThat(result).containsEntry(1L, LodgingOccupiedStatus.FULLY_OCCUPIED);
        }

        @Test
        @DisplayName("When lodging unit has pets on some days but not all, should return PARTIALLY_OCCUPIED")
        void whenLodgingUnitHasPetsSomeDays_shouldReturnPartiallyOccupied() {
            // Arrange
            LodgingUnitModel unit = LodgingUnitModel.newBuilder().setId(1L).build();
            List<LodgingUnitModel> lodgingUnitList = List.of(unit);

            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();
            Map<Long, Integer> day1 = new HashMap<>();
            day1.put(1L, 1);
            Map<Long, Integer> day2 = new HashMap<>();
            day2.put(1L, 0);
            Map<Long, Integer> day3 = new HashMap<>();
            day3.put(1L, 1);

            petCntPerDayPerLodging.put("2024-03-01", day1);
            petCntPerDayPerLodging.put("2024-03-02", day2);
            petCntPerDayPerLodging.put("2024-03-03", day3);

            // Act
            Map<Long, LodgingOccupiedStatus> result =
                    LodgingUtil.calLodgingStatus(startDate, endDate, lodgingUnitList, petCntPerDayPerLodging);

            // Assert
            assertThat(result).containsEntry(1L, LodgingOccupiedStatus.PARTIALLY_OCCUPIED);
        }

        @Test
        @DisplayName("When multiple lodging units have different occupancy patterns")
        void whenMultipleLodgingUnits_shouldReturnCorrectStatuses() {
            // Arrange
            LodgingUnitModel unit1 = LodgingUnitModel.newBuilder().setId(1L).build();
            LodgingUnitModel unit2 = LodgingUnitModel.newBuilder().setId(2L).build();
            LodgingUnitModel unit3 = LodgingUnitModel.newBuilder().setId(3L).build();
            List<LodgingUnitModel> lodgingUnitList = List.of(unit1, unit2, unit3);

            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();

            // Unit 1: Fully occupied (has pets every day)
            // Unit 2: Partially occupied (has pets some days)
            // Unit 3: Vacant (no pets any day)
            Map<Long, Integer> day1 = new HashMap<>();
            day1.put(1L, 1);
            day1.put(2L, 1);

            Map<Long, Integer> day2 = new HashMap<>();
            day2.put(1L, 2);

            Map<Long, Integer> day3 = new HashMap<>();
            day3.put(1L, 1);
            day3.put(2L, 1);

            petCntPerDayPerLodging.put("2024-03-01", day1);
            petCntPerDayPerLodging.put("2024-03-02", day2);
            petCntPerDayPerLodging.put("2024-03-03", day3);

            // Act
            Map<Long, LodgingOccupiedStatus> result =
                    LodgingUtil.calLodgingStatus(startDate, endDate, lodgingUnitList, petCntPerDayPerLodging);

            // Assert
            assertThat(result)
                    .containsEntry(1L, LodgingOccupiedStatus.FULLY_OCCUPIED)
                    .containsEntry(2L, LodgingOccupiedStatus.PARTIALLY_OCCUPIED)
                    .containsEntry(3L, LodgingOccupiedStatus.VACANT);
        }
    }

    @Nested
    @DisplayName("calculateCapacityPerDay Test")
    class CalculateCapacityPerDayTest {
        private final String startDate = "2024-03-01";
        private final String endDate = "2024-03-03";
        private final List<LodgingUnitModel> lodgingUnits = List.of(
                LodgingUnitModel.newBuilder().setId(1L).build(),
                LodgingUnitModel.newBuilder().setId(2L).build());

        @Test
        @DisplayName("When lodging type is AREA, should return total pet count per day")
        void whenLodgingTypeIsArea_shouldReturnTotalPetCount() {
            // Arrange
            LodgingTypeModel lodgingType = LodgingTypeModel.newBuilder()
                    .setLodgingUnitType(LodgingUnitType.AREA)
                    .build();

            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();
            // Day 1: Unit 1 has 2 pets, Unit 2 has 1 pet
            Map<Long, Integer> day1 = new HashMap<>();
            day1.put(1L, 2);
            day1.put(2L, 1);

            // Day 2: Unit 1 has 1 pet, Unit 2 has 0 pets
            Map<Long, Integer> day2 = new HashMap<>();
            day2.put(1L, 1);
            day2.put(2L, 0);

            // Day 3: Unit 1 has 0 pets, Unit 2 has 2 pets
            Map<Long, Integer> day3 = new HashMap<>();
            day3.put(1L, 0);
            day3.put(2L, 2);

            petCntPerDayPerLodging.put("2024-03-01", day1);
            petCntPerDayPerLodging.put("2024-03-02", day2);
            petCntPerDayPerLodging.put("2024-03-03", day3);

            // Act
            Map<String, Integer> result = LodgingUtil.calculateCapacityPerDay(
                    startDate, endDate, lodgingType, lodgingUnits, petCntPerDayPerLodging);

            // Assert
            assertThat(result)
                    .containsEntry("2024-03-01", 3) // 2 + 1
                    .containsEntry("2024-03-02", 1) // 1 + 0
                    .containsEntry("2024-03-03", 2); // 0 + 2
        }

        @Test
        @DisplayName("When lodging type is ROOM, should return occupied room count per day")
        void whenLodgingTypeIsRoom_shouldReturnOccupiedRoomCount() {
            // Arrange
            LodgingTypeModel lodgingType = LodgingTypeModel.newBuilder()
                    .setLodgingUnitType(LodgingUnitType.ROOM)
                    .build();

            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();
            // Day 1: Both units occupied
            Map<Long, Integer> day1 = new HashMap<>();
            day1.put(1L, 2);
            day1.put(2L, 1);

            // Day 2: Only unit 1 occupied
            Map<Long, Integer> day2 = new HashMap<>();
            day2.put(1L, 1);
            day2.put(2L, 0);

            // Day 3: Only unit 2 occupied
            Map<Long, Integer> day3 = new HashMap<>();
            day3.put(2L, 2);

            petCntPerDayPerLodging.put("2024-03-01", day1);
            petCntPerDayPerLodging.put("2024-03-02", day2);
            petCntPerDayPerLodging.put("2024-03-03", day3);

            // Act
            Map<String, Integer> result = LodgingUtil.calculateCapacityPerDay(
                    startDate, endDate, lodgingType, lodgingUnits, petCntPerDayPerLodging);

            // Assert
            assertThat(result)
                    .containsEntry("2024-03-01", 2) // Both units occupied
                    .containsEntry("2024-03-02", 1) // Only unit 1 occupied
                    .containsEntry("2024-03-03", 1); // Only unit 2 occupied
        }

        @Test
        @DisplayName("When no pets in any unit, should return zero for all days")
        void whenNoPets_shouldReturnZeroForAllDays() {
            // Arrange
            LodgingTypeModel lodgingType = LodgingTypeModel.newBuilder()
                    .setLodgingUnitType(LodgingUnitType.AREA)
                    .build();

            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();
            Map<Long, Integer> emptyDay = new HashMap<>();
            petCntPerDayPerLodging.put("2024-03-01", emptyDay);
            petCntPerDayPerLodging.put("2024-03-02", emptyDay);
            petCntPerDayPerLodging.put("2024-03-03", emptyDay);

            // Act
            Map<String, Integer> result = LodgingUtil.calculateCapacityPerDay(
                    startDate, endDate, lodgingType, lodgingUnits, petCntPerDayPerLodging);

            // Assert
            assertThat(result)
                    .containsEntry("2024-03-01", 0)
                    .containsEntry("2024-03-02", 0)
                    .containsEntry("2024-03-03", 0);
        }

        @Test
        @DisplayName("When some units have no data, should handle missing data correctly")
        void whenSomeUnitsMissingData_shouldHandleCorrectly() {
            // Arrange
            LodgingTypeModel lodgingType = LodgingTypeModel.newBuilder()
                    .setLodgingUnitType(LodgingUnitType.AREA)
                    .build();

            Map<String, Map<Long, Integer>> petCntPerDayPerLodging = new HashMap<>();
            // Day 1: Only unit 1 has data
            Map<Long, Integer> day1 = new HashMap<>();
            day1.put(1L, 2);

            // Day 2: Only unit 2 has data
            Map<Long, Integer> day2 = new HashMap<>();
            day2.put(2L, 1);

            // Day 3: No data for any unit
            Map<Long, Integer> day3 = new HashMap<>();

            petCntPerDayPerLodging.put("2024-03-01", day1);
            petCntPerDayPerLodging.put("2024-03-02", day2);
            petCntPerDayPerLodging.put("2024-03-03", day3);

            // Act
            Map<String, Integer> result = LodgingUtil.calculateCapacityPerDay(
                    startDate, endDate, lodgingType, lodgingUnits, petCntPerDayPerLodging);

            // Assert
            assertThat(result)
                    .containsEntry("2024-03-01", 2) // Only unit 1 has pets
                    .containsEntry("2024-03-02", 1) // Only unit 2 has pets
                    .containsEntry("2024-03-03", 0); // No pets
        }
    }
}
