package com.moego.api.v3.online_booking.converter;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.api.online_booking.v1.BoardingAddOnDetail;
import com.moego.idl.api.online_booking.v1.BoardingService;
import com.moego.idl.api.online_booking.v1.BoardingServiceDetail;
import com.moego.idl.api.online_booking.v1.DaycareAddOnDetail;
import com.moego.idl.api.online_booking.v1.DaycareService;
import com.moego.idl.api.online_booking.v1.DaycareServiceDetail;
import com.moego.idl.api.online_booking.v1.FeedingDetail;
import com.moego.idl.api.online_booking.v1.MedicationDetail;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class BookingRequestConverterTest {

    @Test
    void toBoardingService_withValidInputs_returnsDaycareService() {
        var serviceDetailModel = BoardingServiceDetailModel.newBuilder()
                .setId(1L)
                .setBookingRequestId(1L)
                .setPetId(1L)
                .setLodgingId(1L)
                .setServiceId(1L)
                .setServicePrice(1.1)
                .setTaxId(1L)
                .setStartDate("2021-01-01")
                .setStartTime(600)
                .setEndDate("2021-01-03")
                .setEndTime(660)
                .build();
        var addOn = BoardingAddOnDetailModel.newBuilder()
                .setId(2L)
                .setBookingRequestId(1L)
                .setServiceDetailId(2L)
                .setPetId(1L)
                .setAddOnId(2L)
                .setServicePrice(1.2)
                .setTaxId(2L)
                .setDuration(2)
                .setQuantityPerDay(2)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                .addAllSpecificDates(List.of("2021-01-01", "2021-01-02"))
                .build();
        var feeding = FeedingModel.newBuilder()
                .setId(3L)
                .setBookingRequestId(1L)
                .setServiceDetailId(1L)
                .setServiceDetailType(ServiceItemType.DAYCARE)
                .addTime(FeedingModel.FeedingSchedule.newBuilder()
                        .setLabel("label")
                        .setTime(600)
                        .build())
                .setUnit("unit")
                .setFoodType("foodType")
                .setFoodSource("foodSource")
                .setInstruction("instruction")
                .setNote("note")
                .setAmountStr("amountStr")
                .build();
        var medication = MedicationModel.newBuilder()
                .setId(4L)
                .setBookingRequestId(1L)
                .setServiceDetailId(1L)
                .setServiceDetailType(ServiceItemType.DAYCARE)
                .addTime(MedicationModel.MedicationSchedule.newBuilder()
                        .setLabel("medication label")
                        .setTime(720)
                        .build())
                .setUnit("medication unit")
                .setMedicationName("medication name")
                .setNotes("medication notes")
                .setAmountStr("medication amountStr")
                .build();
        var serviceMap = Map.of(
                1L,
                ServiceBriefView.newBuilder().setId(1L).setName("name").build(),
                2L,
                ServiceBriefView.newBuilder()
                        .setId(2L)
                        .setName("addOn name")
                        .setRequireDedicatedStaff(true)
                        .build());
        var lodgingMap = new AbstractMap.SimpleEntry<>(
                Map.of(
                        1L,
                        LodgingUnitModel.newBuilder()
                                .setId(1L)
                                .setName("lodging unit")
                                .setLodgingTypeId(1L)
                                .build()),
                Map.of(
                        1L,
                        LodgingTypeModel.newBuilder()
                                .setId(1L)
                                .setName("lodging type")
                                .build()));

        var result = BookingRequestConverter.INSTANCE.toBoardingService(
                serviceDetailModel, List.of(addOn), List.of(feeding), List.of(medication), serviceMap, lodgingMap);
        var expectFeeding = FeedingDetail.newBuilder()
                .setId(3L)
                .setBookingRequestId(1L)
                .setServiceDetailId(1L)
                .addTime(FeedingModel.FeedingSchedule.newBuilder()
                        .setLabel("label")
                        .setTime(600)
                        .build())
                .setUnit("unit")
                .addAllFoodType(List.of("foodType"))
                .setFoodSource("foodSource")
                .setInstruction("instruction")
                .setNote("note")
                .setAmountStr("amountStr")
                .build();
        var expectMedication = MedicationDetail.newBuilder()
                .setId(4L)
                .setBookingRequestId(1L)
                .setServiceDetailId(1L)
                .addTime(MedicationModel.MedicationSchedule.newBuilder()
                        .setLabel("medication label")
                        .setTime(720)
                        .build())
                .setUnit("medication unit")
                .setMedicationName("medication name")
                .setNotes("medication notes")
                .setAmountStr("medication amountStr")
                .build();
        var expect = BoardingService.newBuilder()
                .setService(BoardingServiceDetail.newBuilder()
                        .setId(1L)
                        .setBookingRequestId(1L)
                        .setPetId(1L)
                        .setLodgingId(1L)
                        .setServiceId(1L)
                        .setServicePrice(1.1)
                        .setTaxId(1L)
                        .setStartDate("2021-01-01")
                        .setStartTime(600)
                        .setEndDate("2021-01-03")
                        .setEndTime(660)
                        .setServiceName("name")
                        .setLodgingUnitName("lodging unit")
                        .build())
                .addAddons(BoardingAddOnDetail.newBuilder()
                        .setId(2L)
                        .setBookingRequestId(1L)
                        .setServiceDetailId(2L)
                        .setPetId(1L)
                        .setAddOnId(2L)
                        .setServicePrice(1.2)
                        .setTaxId(2L)
                        .setDuration(2)
                        .setQuantityPerDay(2)
                        .setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                        .addAllSpecificDates(List.of("2021-01-01", "2021-01-02"))
                        .setServiceName("addOn name")
                        .setRequireDedicatedStaff(true)
                        .build())
                .setFeeding(expectFeeding)
                .setMedication(expectMedication)
                .addAllFeedings(List.of(expectFeeding))
                .addAllMedications(List.of(expectMedication))
                .build();

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void toBoardingAddOnDetail_withEmptyAddOn_returnsEmpty() {
        var result = BookingRequestConverter.INSTANCE.toBoardingAddOnDetail(List.of(), Map.of());
        assertThat(result).isEmpty();
    }

    @Test
    void toBoardingAddOnDetail_withAddOns_returnBoardingAddOnDetails() {
        var addOn = BoardingAddOnDetailModel.newBuilder()
                .setId(2L)
                .setBookingRequestId(1L)
                .setServiceDetailId(2L)
                .setPetId(1L)
                .setAddOnId(2L)
                .setServicePrice(1.2)
                .setTaxId(2L)
                .setDuration(2)
                .setQuantityPerDay(2)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                .addAllSpecificDates(List.of("2021-01-01", "2021-01-02"))
                .build();
        var serviceMap = Map.of(
                2L,
                ServiceBriefView.newBuilder()
                        .setId(2L)
                        .setName("addOn name")
                        .setRequireDedicatedStaff(true)
                        .build());

        var result = BookingRequestConverter.INSTANCE.toBoardingAddOnDetail(List.of(addOn), serviceMap);
        var expect = List.of(BoardingAddOnDetail.newBuilder()
                .setId(2L)
                .setBookingRequestId(1L)
                .setServiceDetailId(2L)
                .setPetId(1L)
                .setAddOnId(2L)
                .setServicePrice(1.2)
                .setTaxId(2L)
                .setDuration(2)
                .setQuantityPerDay(2)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                .addAllSpecificDates(List.of("2021-01-01", "2021-01-02"))
                .setServiceName("addOn name")
                .setRequireDedicatedStaff(true)
                .build());
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void toDaycareService_withValidInputs_returnsDaycareService() {
        var serviceDetailModel = DaycareServiceDetailModel.newBuilder()
                .setId(1L)
                .setBookingRequestId(1L)
                .setPetId(1L)
                .setServiceId(1L)
                .addAllSpecificDates(List.of("2021-01-01", "2021-01-02", "2021-01-03"))
                .setServicePrice(1.1)
                .setTaxId(1L)
                .setMaxDuration(1)
                .setStartTime(600)
                .setEndTime(660)
                .build();
        var addOn = DaycareAddOnDetailModel.newBuilder()
                .setId(2L)
                .setBookingRequestId(1L)
                .setServiceDetailId(2L)
                .setPetId(1L)
                .setAddOnId(2L)
                .addAllSpecificDates(List.of("2021-01-01", "2021-01-02"))
                .setServicePrice(1.2)
                .setTaxId(2L)
                .setDuration(2)
                .setQuantityPerDay(2)
                .build();
        var feeding = FeedingModel.newBuilder()
                .setId(3L)
                .setBookingRequestId(1L)
                .setServiceDetailId(1L)
                .setServiceDetailType(ServiceItemType.DAYCARE)
                .addTime(FeedingModel.FeedingSchedule.newBuilder()
                        .setLabel("label")
                        .setTime(600)
                        .build())
                .setUnit("unit")
                .setFoodType("foodType")
                .setFoodSource("foodSource")
                .setInstruction("instruction")
                .setNote("note")
                .setAmountStr("amountStr")
                .build();
        var medication = MedicationModel.newBuilder()
                .setId(4L)
                .setBookingRequestId(1L)
                .setServiceDetailId(1L)
                .setServiceDetailType(ServiceItemType.DAYCARE)
                .addTime(MedicationModel.MedicationSchedule.newBuilder()
                        .setLabel("medication label")
                        .setTime(720)
                        .build())
                .setUnit("medication unit")
                .setMedicationName("medication name")
                .setNotes("medication notes")
                .setAmountStr("medication amountStr")
                .build();
        var serviceMap = Map.of(
                1L,
                ServiceBriefView.newBuilder().setId(1L).setName("name").build(),
                2L,
                ServiceBriefView.newBuilder()
                        .setId(2L)
                        .setName("addOn name")
                        .setRequireDedicatedStaff(true)
                        .build());

        var result = BookingRequestConverter.INSTANCE.toDaycareService(
                serviceDetailModel, List.of(addOn), List.of(feeding), List.of(medication), serviceMap);
        var expectFeeding = FeedingDetail.newBuilder()
                .setId(3L)
                .setBookingRequestId(1L)
                .setServiceDetailId(1L)
                .addTime(FeedingModel.FeedingSchedule.newBuilder()
                        .setLabel("label")
                        .setTime(600)
                        .build())
                .setUnit("unit")
                .addAllFoodType(List.of("foodType"))
                .setFoodSource("foodSource")
                .setInstruction("instruction")
                .setNote("note")
                .setAmountStr("amountStr")
                .build();
        var expectMedication = MedicationDetail.newBuilder()
                .setId(4L)
                .setBookingRequestId(1L)
                .setServiceDetailId(1L)
                .addTime(MedicationModel.MedicationSchedule.newBuilder()
                        .setLabel("medication label")
                        .setTime(720)
                        .build())
                .setUnit("medication unit")
                .setMedicationName("medication name")
                .setNotes("medication notes")
                .setAmountStr("medication amountStr")
                .build();
        var expect = DaycareService.newBuilder()
                .setService(DaycareServiceDetail.newBuilder()
                        .setId(1L)
                        .setBookingRequestId(1L)
                        .setPetId(1L)
                        .setServiceId(1L)
                        .addAllSpecificDates(List.of("2021-01-01", "2021-01-02", "2021-01-03"))
                        .setServicePrice(1.1)
                        .setTaxId(1L)
                        .setMaxDuration(1)
                        .setStartTime(600)
                        .setEndTime(660)
                        .setServiceName("name")
                        .build())
                .addAddons(DaycareAddOnDetail.newBuilder()
                        .setId(2L)
                        .setBookingRequestId(1L)
                        .setServiceDetailId(2L)
                        .setPetId(1L)
                        .setAddOnId(2L)
                        .setServicePrice(1.2)
                        .setTaxId(2L)
                        .setDuration(2)
                        .setQuantityPerDay(2)
                        .addAllSpecificDates(List.of("2021-01-01", "2021-01-02"))
                        .setServiceName("addOn name")
                        .setRequireDedicatedStaff(true)
                        .build())
                .setFeeding(expectFeeding)
                .setMedication(expectMedication)
                .addAllFeedings(List.of(expectFeeding))
                .addAllMedications(List.of(expectMedication))
                .build();

        assertThat(result).isEqualTo(expect);
    }
}
