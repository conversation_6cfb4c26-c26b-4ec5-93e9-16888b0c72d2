package com.moego.client.api.v1.online_booking.converter;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.client.online_booking.v1.ReschedulePetFeedingMedicationParams;
import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.service.online_booking.v1.CreateFeedingRequest;
import com.moego.idl.service.online_booking.v1.CreateFeedingRequestList;
import com.moego.idl.service.online_booking.v1.CreateMedicationRequest;
import com.moego.idl.service.online_booking.v1.CreateMedicationRequestList;
import com.moego.idl.service.online_booking.v1.UpdateBoardingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateDaycareServiceDetailRequest;
import org.junit.jupiter.api.Test;

class BookingRequestConverterTest {

    @Test
    void testToUpdateBookingRequestRequest_Boarding() {
        long bookingRequestId = 1L;
        long petDetailId = 2L;

        var request = ReschedulePetFeedingMedicationParams.newBuilder()
                .setBookingRequestId(bookingRequestId)
                .addSchedules(ReschedulePetFeedingMedicationParams.PetScheduleDef.newBuilder()
                        .setPetDetailId(petDetailId)
                        .setCareType(ServiceItemType.BOARDING)
                        .addFeedings(AppointmentPetFeedingScheduleDef.getDefaultInstance())
                        .addMedications(AppointmentPetMedicationScheduleDef.getDefaultInstance())
                        .build())
                .build();

        var bookingRequest = BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setId(petDetailId)
                                        .build())
                                .build())
                        .build())
                .build();
        var result = BookingRequestConverter.toUpdateBookingRequestRequest(request, bookingRequest);

        var expect = UpdateBookingRequestRequest.newBuilder()
                .setId(bookingRequestId)
                .addServiceDetails(UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                        .setUpdate(UpdateBookingRequestRequest.Service.newBuilder()
                                .setBoarding(UpdateBookingRequestRequest.BoardingService.newBuilder()
                                        .setService(UpdateBoardingServiceDetailRequest.newBuilder()
                                                .setId(petDetailId)
                                                .build())
                                        .setFeedingsUpsert(CreateFeedingRequestList.newBuilder()
                                                .addValues(CreateFeedingRequest.newBuilder()
                                                        .setUnit("")
                                                        .setFoodType("")
                                                        .setFoodSource("")
                                                        .setAmountStr("")
                                                        .build())
                                                .build())
                                        .setMedicationsUpsert(CreateMedicationRequestList.newBuilder()
                                                .addValues(CreateMedicationRequest.newBuilder()
                                                        .setUnit("")
                                                        .setMedicationName("")
                                                        .setAmountStr("")
                                                        .build())
                                                .build())
                                        .build())
                                .build())
                        .build())
                .build();

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void testToUpdateBookingRequestRequest_Daycare() {
        long bookingRequestId = 1L;
        long petDetailId = 2L;

        var request = ReschedulePetFeedingMedicationParams.newBuilder()
                .setBookingRequestId(bookingRequestId)
                .addSchedules(ReschedulePetFeedingMedicationParams.PetScheduleDef.newBuilder()
                        .setPetDetailId(petDetailId)
                        .setCareType(ServiceItemType.DAYCARE)
                        .addFeedings(AppointmentPetFeedingScheduleDef.getDefaultInstance())
                        .addMedications(AppointmentPetMedicationScheduleDef.getDefaultInstance())
                        .build())
                .build();

        var bookingRequest = BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDaycare(BookingRequestModel.DaycareService.newBuilder()
                                .setService(DaycareServiceDetailModel.newBuilder()
                                        .setId(petDetailId)
                                        .build())
                                .build())
                        .build())
                .build();
        var result = BookingRequestConverter.toUpdateBookingRequestRequest(request, bookingRequest);

        var expect = UpdateBookingRequestRequest.newBuilder()
                .setId(bookingRequestId)
                .addServiceDetails(UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                        .setUpdate(UpdateBookingRequestRequest.Service.newBuilder()
                                .setDaycare(UpdateBookingRequestRequest.DaycareService.newBuilder()
                                        .setService(UpdateDaycareServiceDetailRequest.newBuilder()
                                                .setId(petDetailId)
                                                .build())
                                        .setFeedingsUpsert(CreateFeedingRequestList.newBuilder()
                                                .addValues(CreateFeedingRequest.newBuilder()
                                                        .setUnit("")
                                                        .setFoodType("")
                                                        .setFoodSource("")
                                                        .setAmountStr("")
                                                        .build())
                                                .build())
                                        .setMedicationsUpsert(CreateMedicationRequestList.newBuilder()
                                                .addValues(CreateMedicationRequest.newBuilder()
                                                        .setUnit("")
                                                        .setMedicationName("")
                                                        .setAmountStr("")
                                                        .build())
                                                .build())
                                        .build())
                                .build())
                        .build())
                .build();

        assertThat(result).isEqualTo(expect);
    }
}
