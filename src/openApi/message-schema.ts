export namespace message {
  export interface paths {
    'POST/message/a2p/brand/otp/retry': operations['retryBrandOTP'];
    'GET/message/a2p/config': operations['queryCompanyA2pConfig'];
    'PUT/message/a2p/submit': operations['saveCompanyA2pConfig'];
    'GET/message/agreement/template/achieve': operations['getAgreementTemplate'];
    'GET/message/agreement/template/get': operations['getBusinessAgreementTemplate'];
    'POST/message/agreement/template/save': operations['saveBusinessAgreementTemplate'];
    'GET/message/auto/reply/get': operations['getBusinessAutoReply'];
    /** @deprecated */
    'POST/message/auto/reply/save': operations['saveAutoReply'];
    'PUT/message/auto/reply/update': operations['updateAutoReply'];
    'GET/message/auto/template/get': operations['getBusinessAutoTemplate'];
    'POST/message/auto/template/preview': operations['previewMessageBody'];
    'POST/message/auto/template/save': operations['saveBusinessAutoTemplate'];
    /** @deprecated */
    'PUT/message/auto/template/update': operations['updateBusinessAutoTemplate'];
    'GET/message/callback/forward/needAutoReply': operations['needAutoReply'];
    'PUT/message/callback/forward/needAutoReply': operations['needAutoReply_3'];
    'POST/message/callback/forward/needAutoReply': operations['needAutoReply_2'];
    'DELETE/message/callback/forward/needAutoReply': operations['needAutoReply_5'];
    'OPTIONS/message/callback/forward/needAutoReply': operations['needAutoReply_6'];
    'HEAD/message/callback/forward/needAutoReply': operations['needAutoReply_1'];
    'PATCH/message/callback/forward/needAutoReply': operations['needAutoReply_4'];
    'GET/message/callback/twilio/receive/new': operations['twilioReceiveNew'];
    'PUT/message/callback/twilio/receive/new': operations['twilioReceiveNew_3'];
    'POST/message/callback/twilio/receive/new': operations['twilioReceiveNew_2'];
    'DELETE/message/callback/twilio/receive/new': operations['twilioReceiveNew_5'];
    'OPTIONS/message/callback/twilio/receive/new': operations['twilioReceiveNew_6'];
    'HEAD/message/callback/twilio/receive/new': operations['twilioReceiveNew_1'];
    'PATCH/message/callback/twilio/receive/new': operations['twilioReceiveNew_4'];
    'GET/message/callback/twilio/sms/status': operations['twilioCallback'];
    'PUT/message/callback/twilio/sms/status': operations['twilioCallback_3'];
    'POST/message/callback/twilio/sms/status': operations['twilioCallback_2'];
    'DELETE/message/callback/twilio/sms/status': operations['twilioCallback_5'];
    'OPTIONS/message/callback/twilio/sms/status': operations['twilioCallback_6'];
    'HEAD/message/callback/twilio/sms/status': operations['twilioCallback_1'];
    'PATCH/message/callback/twilio/sms/status': operations['twilioCallback_4'];
    'GET/message/callback/twilio/sms/verification/status': operations['twilioVerificationCallback'];
    'PUT/message/callback/twilio/sms/verification/status': operations['twilioVerificationCallback_3'];
    'POST/message/callback/twilio/sms/verification/status': operations['twilioVerificationCallback_2'];
    'DELETE/message/callback/twilio/sms/verification/status': operations['twilioVerificationCallback_5'];
    'OPTIONS/message/callback/twilio/sms/verification/status': operations['twilioVerificationCallback_6'];
    'HEAD/message/callback/twilio/sms/verification/status': operations['twilioVerificationCallback_1'];
    'PATCH/message/callback/twilio/sms/verification/status': operations['twilioVerificationCallback_4'];
    'GET/message/checkPermission': operations['listThread_2'];
    'POST/message/contact/send': operations['addContactMessage'];
    'GET/message/count/unread': operations['getUnreadMessageNumber'];
    'POST/message/email/events': operations['handleEmailEvent'];
    'POST/message/email/receive': operations['receiveEmail'];
    'POST/message/email/reply': operations['handleEmailReply'];
    /** @deprecated */
    'POST/message/notification/agreement': operations['agreement'];
    'POST/message/notification/app/push/token': operations['saveAppPushToken'];
    /** @deprecated */
    'POST/message/notification/bookingCreated': operations['bookingCreated'];
    'PUT/message/notification/dismiss': operations['setNotificationDismiss'];
    /** @deprecated */
    'POST/message/notification/intake': operations['intake'];
    /** @deprecated */
    'POST/message/notification/invoice': operations['invoice'];
    'GET/message/notification/list': operations['getNotificationPage'];
    'POST/message/notification/mobile/list': operations['getNotificationPageForMobile'];
    'GET/message/notification/mobile/unreadCount': operations['getUnreadCountForMobile'];
    'POST/message/notification/notification/type': operations['migrateNotificationType'];
    /** @deprecated */
    'POST/message/notification/pay': operations['pay'];
    'PUT/message/notification/read': operations['setNotificationRead'];
    'PUT/message/notification/read/all': operations['setAllRead'];
    /** @deprecated */
    'POST/message/notification/review': operations['review'];
    'GET/message/notification/unreadCount': operations['getUnreadCount'];
    /** @deprecated */
    'POST/message/ob/v2/client/code': operations['sendVerifyCode_2'];
    /** @deprecated */
    'POST/message/ob/v2/client/email-code': operations['sendVerifyCodeToEmail'];
    'POST/message/onlineBooking/manual/notify': operations['bookOnlineNotify'];
    'GET/message/optout/config/get': operations['getMessageOptoutConfig'];
    'POST/message/optout/config/update': operations['updateMessageOptoutConfig'];
    'GET/message/optout/customer/list': operations['listMessageOptoutCustomers'];
    'POST/message/optout/customer/update': operations['updateMessageOptoutCustomers'];
    'GET/message/reminder/arrival': operations['getArrivalWindow'];
    'PUT/message/reminder/arrival': operations['updateArrivalWindow'];
    'GET/message/reminder/detail/appointment': operations['detailAppointment'];
    'GET/message/reminder/detail/appointment/real': operations['detailAppointmentReal'];
    'GET/message/reminder/detail/petBirthday': operations['detailPetBirthday'];
    'GET/message/reminder/detail/rebook': operations['detailRebook'];
    'GET/message/reminder/detail/repeat': operations['detailRepeat'];
    'POST/message/reminder/dismiss': operations['dismissReminder'];
    'POST/message/reminder/expiry/dismiss': operations['expiryReminderDismissCustomer'];
    'GET/message/reminder/expiry/query': operations['queryExpiryReminderList'];
    'GET/message/reminder/get': operations['getBusinessReminder'];
    'GET/message/reminder/push/calendar/task': operations['pushCalendarReminderTask'];
    'GET/message/reminder/setting/repeat/expiry': operations['getRepeatExpirySetting'];
    'PUT/message/reminder/setting/repeat/expiry': operations['updateRepeatExpirySetting'];
    'GET/message/reminder/summary': operations['reminderSummary'];
    'PUT/message/reminder/update': operations['updateBusinessReminder'];
    /** @deprecated */
    'GET/message/reminder/update/forTester': operations['updateBusinessReminderForTest'];
    'GET/message/review/booster/count': operations['countReviewBooster'];
    'GET/message/review/booster/get': operations['getReviewBooster'];
    'POST/message/review/booster/grooming-report/record': operations['saveRecordReviewBoosterForGroomingReport'];
    'GET/message/review/booster/record': operations['recordReviewBooster'];
    'POST/message/review/booster/record/list': operations['getReviewBoosterRecordList'];
    'GET/message/review/booster/record/listClientReviewForLandingPageConfig': operations['listClientReviewForLandingPageConfig'];
    'POST/message/review/booster/save': operations['saveBooster'];
    /** @deprecated */
    'GET/message/send/account/email/dataImport': operations['sendAccountEmailDataImport'];
    'POST/message/send/auto/reminder/send/appointment': operations['autoTemplateSendAppointment_1'];
    'POST/message/send/auto/template/send/appointment': operations['autoTemplateSendAppointment'];
    'GET/message/send/autoMessage': operations['getParsedAutoMessage'];
    'POST/message/send/autoMessage/querySent': operations['getAutoMessageSentRecords'];
    'GET/message/send/batch': operations['getBatchTaskInfo'];
    'POST/message/send/batch/auto/template/send/appointment': operations['batchAutoTemplateBatchSendAppointment'];
    'GET/message/send/batch/detail': operations['detailBatchSendMessages'];
    'GET/message/send/batch/list': operations['listBatchSendMessages'];
    /** @deprecated */
    'POST/message/send/bookOnline/customerLogin': operations['sendVerifyCode_1'];
    'POST/message/send/bookOnline/sendVerifyCode': operations['sendVerifyCode'];
    /**
     * 废弃接口，请调用/countUsed接口
     * @deprecated
     */
    'GET/message/send/count': operations['getMessageCount'];
    'GET/message/send/count/report': operations['getMessageReport'];
    'GET/message/send/countUsed': operations['messageCountCurrentMonth'];
    'DELETE/message/send/delete': operations['deleteSendMessage'];
    'GET/message/send/email/count/report': operations['getEmailMessageReport'];
    'POST/message/send/grooming-report': operations['sendGroomingReport'];
    'GET/message/send/grooming-report/content': operations['getGroomingReportSendContent'];
    'POST/message/send/grooming-report/preview': operations['sendGroomingReportPreview'];
    /** @deprecated */
    'POST/message/send/grooming/invoice': operations['sendEmailInvoice'];
    'POST/message/send/grooming/upcoming': operations['sendUpcomingEmail'];
    'POST/message/send/invoice/receipt': operations['sendInvoiceEmail'];
    'POST/message/send/mms/group': operations['fileInfoGroupCheck'];
    'POST/message/send/payment/online': operations['sendPayOnlineEmail'];
    'GET/message/send/reminder/content': operations['getParsedReminderMessage'];
    /** @deprecated */
    'POST/message/send/retail/invoice': operations['sendEmailRetailInvoice'];
    'POST/message/send/services/message': operations['sendServiceMessageToCustomer'];
    'PUT/message/send/set/all/read': operations['setAllMessageRead'];
    'GET/message/send/thread/list': operations['listThreadSendMessages'];
    /** @deprecated */
    'POST/message/send/toCustomer/batch': operations['sendBatchMessageToCustomer'];
    'POST/message/send/toCustomer/batch/asyn': operations['sendBatchMessageToCustomerAsyn'];
    /** @deprecated */
    'POST/message/send/toCustomer/code': operations['sendCode'];
    'POST/message/send/toCustomer/mms/one': operations['sendOneMMsToCustomer'];
    'POST/message/send/toCustomer/multipleReceipts': operations['sendReceiptsToCustomer'];
    'POST/message/send/toCustomer/one': operations['sendOneMessageToCustomer'];
    'POST/message/send/toCustomer/ready/notification': operations['sendReadyNotification'];
    'PUT/message/send/unassign/dismiss': operations['dismissUnassignMessageDetail'];
    'GET/message/send/unassign/list': operations['listUnassignMessageCallDetail'];
    'PUT/message/send/unassign/read': operations['setUnassignMessageDetailRead'];
    /** @deprecated */
    'POST/message/sendmandrill/dataMessage': operations['sendDataMessage'];
    /** @deprecated */
    'POST/message/sendmandrill/message': operations['sendMessage'];
    /** @deprecated */
    'POST/message/sendmandrill/message/batchSend': operations['batchSendMessage'];
    /** @deprecated */
    'POST/message/sendmandrill/notification': operations['sendNotification'];
    'GET/message/template': operations['listMessageTemplate'];
    'GET/message/test': operations['test'];
    'POST/message/thread/add': operations['addThread'];
    'GET/message/thread/detail': operations['listThread_1'];
    'GET/message/thread/list': operations['listThread'];
    'POST/message/thread/update': operations['updateThread'];
    'POST/message/transfer/reminder': operations['transferReminder'];
    'GET/message/twilio/setting': operations['getBusinessTwilioSetting'];
    'PUT/message/twilio/setting': operations['updateBusinessTwilioSetting'];
    'POST/message/voice/status/webhook': operations['twilioVoiceWebhookStatus'];
    'POST/message/voice/twiml': operations['getReminderStartTwiML'];
    'POST/message/voice/webhook': operations['twilioVoiceWebhook'];
    'GET/s/{shortUuid}': operations['redirectShortLink'];
    'PUT/s/{shortUuid}': operations['redirectShortLink_3'];
    'POST/s/{shortUuid}': operations['redirectShortLink_2'];
    'DELETE/s/{shortUuid}': operations['redirectShortLink_5'];
    'OPTIONS/s/{shortUuid}': operations['redirectShortLink_6'];
    'HEAD/s/{shortUuid}': operations['redirectShortLink_1'];
    'PATCH/s/{shortUuid}': operations['redirectShortLink_4'];
  }
  export interface operations {
    retryBrandOTP: {
      Req: Partial<components['schemas']['com.moego.server.message.web.params.CompanyIdParams']>;
      Res: {};
    };
    queryCompanyA2pConfig: {
      Req: Partial<{
        companyId: number;
      }>;
      Res: components['schemas']['com.moego.server.message.service.dto.A2pRecordDto'];
    };
    saveCompanyA2pConfig: {
      Req: Partial<components['schemas']['com.moego.server.message.web.vo.A2pRecordUpdateVo']>;
      Res: {};
    };
    getAgreementTemplate: {
      Req: Partial<{
        unsignId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.AgreementSmsTemplateDto'];
    };
    getBusinessAgreementTemplate: {
      Req: Partial<{
        agreementId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAgreementSendTemplate'];
    };
    saveBusinessAgreementTemplate: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MoeBusinessAgreementTemplateParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAgreementSendTemplate'];
    };
    getBusinessAutoReply: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.MoeBusinessAutoReplyDto'];
    };
    saveAutoReply: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MoeBusinessAutoReplyParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAutoReply'];
    };
    updateAutoReply: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MoeBusinessAutoReplyParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAutoReply'];
    };
    getBusinessAutoTemplate: {
      Req: Partial<{
        type: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate'];
    };
    previewMessageBody: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendMessagesParams']>;
      Res: components['schemas']['com.moego.server.message.web.model.AutoMessagePreviewResp'];
    };
    saveBusinessAutoTemplate: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MoeBusinessAutoMessageTemplateParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate'];
    };
    updateBusinessAutoTemplate: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MoeBusinessAutoMessageTemplateParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate'];
    };
    needAutoReply: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: boolean;
    };
    needAutoReply_3: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: boolean;
    };
    needAutoReply_2: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: boolean;
    };
    needAutoReply_5: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: boolean;
    };
    needAutoReply_6: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: boolean;
    };
    needAutoReply_1: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: boolean;
    };
    needAutoReply_4: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: boolean;
    };
    twilioReceiveNew: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: {};
    };
    twilioReceiveNew_3: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: {};
    };
    twilioReceiveNew_2: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: {};
    };
    twilioReceiveNew_5: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: {};
    };
    twilioReceiveNew_6: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: {};
    };
    twilioReceiveNew_1: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: {};
    };
    twilioReceiveNew_4: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveMessageCallbackParams']>;
      Res: {};
    };
    twilioCallback: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioMessageCallbackParams']>;
      Res: string;
    };
    twilioCallback_3: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioMessageCallbackParams']>;
      Res: string;
    };
    twilioCallback_2: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioMessageCallbackParams']>;
      Res: string;
    };
    twilioCallback_5: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioMessageCallbackParams']>;
      Res: string;
    };
    twilioCallback_6: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioMessageCallbackParams']>;
      Res: string;
    };
    twilioCallback_1: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioMessageCallbackParams']>;
      Res: string;
    };
    twilioCallback_4: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioMessageCallbackParams']>;
      Res: string;
    };
    twilioVerificationCallback: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioVerificationMessageCallbackParams']>;
      Res: string;
    };
    twilioVerificationCallback_3: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioVerificationMessageCallbackParams']>;
      Res: string;
    };
    twilioVerificationCallback_2: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioVerificationMessageCallbackParams']>;
      Res: string;
    };
    twilioVerificationCallback_5: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioVerificationMessageCallbackParams']>;
      Res: string;
    };
    twilioVerificationCallback_6: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioVerificationMessageCallbackParams']>;
      Res: string;
    };
    twilioVerificationCallback_1: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioVerificationMessageCallbackParams']>;
      Res: string;
    };
    twilioVerificationCallback_4: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioVerificationMessageCallbackParams']>;
      Res: string;
    };
    listThread_2: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.CommonResultDto'];
    };
    addContactMessage: {
      Req: Partial<components['schemas']['com.moego.server.message.web.vo.MoeContactMessageVo']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    getUnreadMessageNumber: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.service.dto.UnreadMessageNumberDto'];
    };
    handleEmailEvent: {
      Req: Partial<{
        payload: {
          [key: string]: string | undefined;
        };
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    receiveEmail: {
      Req: Partial<{
        payload: {
          [key: string]: string | undefined;
        };
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    handleEmailReply: {
      Req: Partial<{
        payload: {
          [key: string]: string | undefined;
        };
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    agreement: {
      Req: Partial<
        ({
          kind: string;
        } & {
          token: string;
        } & {
          type: string;
        }) &
          components['schemas']['com.moego.server.customer.dto.CustomerAgreementInfoDTO']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    saveAppPushToken: {
      Req: Partial<components['schemas']['com.moego.server.message.params.notification.AppPushTokenSaveParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    bookingCreated: {
      Req: Partial<
        ({
          kind: string;
        } & {
          token: string;
        } & {
          type: string;
        }) &
          components['schemas']['com.moego.server.grooming.dto.MoeGroomingAppointmentDTO']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    setNotificationDismiss: {
      Req: Partial<components['schemas']['com.moego.server.message.params.NotificationIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    intake: {
      Req: Partial<
        components['schemas']['com.moego.server.customer.dto.CustomerContactDto'] & {
          kind: string;
        } & {
          token: string;
        } & {
          type: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    invoice: {
      Req: Partial<
        ({
          kind: string;
        } & {
          token: string;
        } & {
          type: string;
        }) &
          components['schemas']['com.moego.server.grooming.dto.MoeGroomingAppointmentDTO']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getNotificationPage: {
      Req: Partial<
        {
          limit: number;
        } & {
          startingAfter: number;
        } & {
          tab: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.NotificationPageDto'];
    };
    getNotificationPageForMobile: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MobileQueryNotificationParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.NotificationPageDto'];
    };
    getUnreadCountForMobile: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.MobileNotificationUnreadCountDto'];
    };
    migrateNotificationType: {
      Req: Partial<components['schemas']['com.moego.server.message.web.params.MigrateNotificationTypeParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    pay: {
      Req: Partial<
        {
          kind: string;
        } & {
          notificationId: number;
        } & {
          recordId: number;
        } & {
          token: string;
        } & {
          type: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    setNotificationRead: {
      Req: Partial<components['schemas']['com.moego.server.message.params.NotificationIdParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    setAllRead: {
      Req: Partial<components['schemas']['com.moego.server.message.params.NotificationSetReadParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    review: {
      Req: Partial<
        ({
          kind: string;
        } & {
          token: string;
        } & {
          type: string;
        }) &
          components['schemas']['com.moego.server.grooming.dto.MoeGroomingAppointmentDTO']
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getUnreadCount: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.notificationDto.StaffNotificationTotalDto'];
    };
    sendVerifyCode_2: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.message.web.dto.ob.SendVerifyCodeDTO']
      >;
      Res: components['schemas']['com.moego.server.message.web.dto.ob.SendVerifyCodeResultDto'];
    };
    sendVerifyCodeToEmail: {
      Req: Partial<
        components['schemas']['com.moego.server.grooming.params.ob.OBAnonymousParams'] &
          components['schemas']['com.moego.server.message.web.params.SendVerifyCodeParams']
      >;
      Res: string;
    };
    bookOnlineNotify: {
      Req: Partial<components['schemas']['com.moego.server.message.params.OnlineBookWaitingNotifyParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getMessageOptoutConfig: {
      Req: Partial<
        {
          method: string;
        } & {
          source: string;
        }
      >;
      Res: components['schemas']['com.moego.server.message.web.model.GetMessageOptoutConfigResponse'];
    };
    updateMessageOptoutConfig: {
      Req: Partial<components['schemas']['com.moego.server.message.web.params.UpdateMessageOptoutConfigParam']>;
      Res: components['schemas']['com.moego.server.message.web.vo.MessageOptoutConfigVO'];
    };
    listMessageOptoutCustomers: {
      Req: Partial<
        {
          method: string;
        } & {
          pageNum: number;
        } & {
          pageSize: number;
        } & {
          searchKeyword: string;
        } & {
          source: string;
        }
      >;
      Res: components['schemas']['com.moego.server.message.web.model.ListMessageOptoutCustomerResponse'];
    };
    updateMessageOptoutCustomers: {
      Req: Partial<components['schemas']['com.moego.server.message.web.params.UpdateMessageOptoutCustomerParam']>;
      Res: {};
    };
    getArrivalWindow: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.ArrivalWindowSettingDto'];
    };
    updateArrivalWindow: {
      Req: Partial<components['schemas']['com.moego.server.message.dto.ArrivalWindowSettingDto']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    detailAppointment: {
      Req: Partial<components['schemas']['com.moego.common.params.PageParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.ApptReminderDetailDTO'];
    };
    detailAppointmentReal: {
      Req: Partial<components['schemas']['com.moego.common.params.PageParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.ApptReminderDetailDTO'];
    };
    detailPetBirthday: {
      Req: Partial<components['schemas']['com.moego.common.params.PageParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.BirthdayReminderDetailDTO'];
    };
    detailRebook: {
      Req: Partial<components['schemas']['com.moego.common.params.PageParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.RebookReminderDetailDTO'];
    };
    detailRepeat: {
      Req: Partial<components['schemas']['com.moego.common.params.PageParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.RepeatReminderDetailDTO'];
    };
    dismissReminder: {
      Req: Partial<components['schemas']['com.moego.server.message.params.ReminderDismissParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessReminderDismiss'];
    };
    expiryReminderDismissCustomer: {
      Req: Partial<{
        customerId: number;
      }>;
      Res: boolean;
    };
    queryExpiryReminderList: {
      Req: Partial<
        {
          pageNum: number;
        } & {
          pageSize: number;
        }
      >;
      Res: components['schemas']['com.moego.server.message.service.dto.ExpiryListDto'];
    };
    getBusinessReminder: {
      Req: Partial<
        {
          includeAppointReminder: boolean;
        } & {
          reminderType: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.mapperbean.MoeBusinessReminder'];
    };
    pushCalendarReminderTask: {
      Req: Partial<{}>;
      Res: {};
    };
    getRepeatExpirySetting: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.message.dto.RepeatExpitySettingDto'];
    };
    updateRepeatExpirySetting: {
      Req: Partial<{
        expiryApptNum: number;
      }>;
      Res: boolean;
    };
    reminderSummary: {
      Req: Partial<{
        appointmentReminderType: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.ReminderSummaryDTO'];
    };
    updateBusinessReminder: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MessageReminderParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessReminder'];
    };
    updateBusinessReminderForTest: {
      Req: Partial<
        {
          beforeDay: number;
        } & {
          minutes: number;
        } & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessReminder'];
    };
    countReviewBooster: {
      Req: Partial<
        {
          customerId: number;
        } & {
          searchBusinessId?: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.MoeBusinessReviewBoosterCountDTO'];
    };
    getReviewBooster: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessReviewBooster'];
    };
    saveRecordReviewBoosterForGroomingReport: {
      Req: Partial<components['schemas']['com.moego.server.message.params.ReviewBoosterForGroomingReportParams']>;
      Res: components['schemas']['com.moego.server.message.dto.ReviewBoosterRecordDTO'];
    };
    recordReviewBooster: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MoeBusinessReviewBoosterParamsRecord']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecord'];
    };
    getReviewBoosterRecordList: {
      Req: Partial<components['schemas']['com.moego.server.message.params.GetReviewBoosterRecordListParams']>;
      Res: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.dto.ReviewBoosterRecordDetailDTO'];
    };
    listClientReviewForLandingPageConfig: {
      Req: Partial<
        {
          minScore?: number;
        } & {
          pageNum?: number;
        } & {
          pageSize?: number;
        }
      >;
      Res: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.web.MoeBusinessMessageReviewBoosterController$LandingPageConfigClientReviewVO'];
    };
    saveBooster: {
      Req: Partial<components['schemas']['com.moego.server.message.params.MoeBusinessReviewBoosterParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessReviewBooster'];
    };
    sendAccountEmailDataImport: {
      Req: Partial<
        {
          businessId: number;
        } & {
          dataImportUrl: string;
        } & {
          toEmail: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResult[Lcom.microtripit.mandrillapp.lutung.view.MandrillMessageStatus;'];
    };
    autoTemplateSendAppointment_1: {
      Req: Partial<components['schemas']['com.moego.server.message.web.vo.ManualSendReminderVo']>;
      Res: components['schemas']['com.moego.common.dto.CommonResultDto'];
    };
    autoTemplateSendAppointment: {
      Req: Partial<components['schemas']['com.moego.server.message.params.AutoTemplateSendParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getParsedAutoMessage: {
      Req: Partial<
        {
          appointmentId: number;
        } & {
          customerId: number;
        } & {
          type: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getAutoMessageSentRecords: {
      Req: Partial<components['schemas']['com.moego.server.message.params.AutoMessageParams']>;
      Res: components['schemas']['com.moego.server.message.dto.MessageDetailDTO'];
    };
    getBatchTaskInfo: {
      Req: Partial<{
        batchId: number;
      }>;
      Res: components['schemas']['com.moego.server.message.service.dto.BatchSendMessageResultDto'];
    };
    batchAutoTemplateBatchSendAppointment: {
      Req: Partial<components['schemas']['com.moego.server.message.params.BatchAutoTemplateSendParams']>;
      Res: components['schemas']['com.moego.common.dto.CommonResultDto'];
    };
    detailBatchSendMessages: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendMessagesParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.web.vo.MoeBusinessMessageDetailVO'];
    };
    listBatchSendMessages: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendMessagesParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessMessageBatch'];
    };
    sendVerifyCode_1: {
      Req: Partial<
        {
          businessName: string;
        } & {
          phoneNumber: string;
        } & {
          verifyCode: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.customer.dto.CustomerInfoBookOnlineDto'];
    };
    sendVerifyCode: {
      Req: Partial<
        {
          businessName: string;
        } & {
          phoneNumber: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.web.dto.ob.SendVerifyCodeResultDto'];
    };
    getMessageCount: {
      Req: Partial<
        {
          endDate?: string;
        } & {
          startDate?: string;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.MessageCountDTO'];
    };
    getMessageReport: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.dto.MessageReportItem'];
    };
    messageCountCurrentMonth: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.dto.MessageCountDTO'];
    };
    deleteSendMessage: {
      Req: Partial<{
        messageDetailId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getEmailMessageReport: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.dto.EmailReportItem'];
    };
    sendGroomingReport: {
      Req: Partial<components['schemas']['com.moego.server.message.dto.GroomingReportSendParams']>;
      Res: boolean;
    };
    getGroomingReportSendContent: {
      Req: Partial<
        {
          reportId: number;
        } & {
          sendingMethod?: number;
        }
      >;
      Res: string;
    };
    sendGroomingReportPreview: {
      Req: Partial<components['schemas']['com.moego.server.message.dto.GroomingReportSendPreviewParams']>;
      Res: boolean;
    };
    sendEmailInvoice: {
      Req: Partial<
        {
          email: string;
        } & {
          invoiceId: number;
        } & {
          needSendCustomerId: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    sendUpcomingEmail: {
      Req: Partial<components['schemas']['com.moego.server.message.dto.UpcomingEmailDto']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    sendInvoiceEmail: {
      Req: Partial<components['schemas']['com.moego.server.message.web.params.SendInvoiceReceiptParams']>;
      Res: boolean;
    };
    fileInfoGroupCheck: {
      Req: Partial<components['schemas']['com.moego.server.message.web.params.FileInfoGroupParams']>;
      Res: components['schemas']['com.moego.server.message.web.dto.ob.FileInfoDto'][][];
    };
    sendPayOnlineEmail: {
      Req: Partial<components['schemas']['com.moego.server.message.dto.PayOnlineEmailDto']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    getParsedReminderMessage: {
      Req: Partial<
        {
          targetId: number;
        } & {
          type: number;
        }
      >;
      Res: string;
    };
    sendEmailRetailInvoice: {
      Req: Partial<
        {
          email: string;
        } & {
          invoiceId: number;
        }
      >;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    sendServiceMessageToCustomer: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendMessagesParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    setAllMessageRead: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Boolean'];
    };
    listThreadSendMessages: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendMessagesParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.web.vo.MoeBusinessMessageDetailVO'];
    };
    sendBatchMessageToCustomer: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendMessagesParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.mapperbean.MoeBusinessMessageDetail'];
    };
    sendBatchMessageToCustomerAsyn: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendMessagesParams']>;
      Res: components['schemas']['com.moego.server.message.service.dto.BatchSendMessageResultDto'];
    };
    sendCode: {
      Req: Partial<components['schemas']['com.moego.server.message.web.model.CodeSendRequest']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.web.model.CodeSendResponse'];
    };
    sendOneMMsToCustomer: {
      Req: Partial<components['schemas']['com.moego.server.message.web.params.SendMMSParams']>;
      Res: components['schemas']['com.moego.server.message.web.dto.ob.SendMMSResult'];
    };
    sendReceiptsToCustomer: {
      Req: Partial<components['schemas']['com.moego.server.message.web.params.BatchSendReceiptParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.Integer'];
    };
    sendOneMessageToCustomer: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendMessagesParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.web.vo.MoeBusinessMessageDetailVO'];
    };
    sendReadyNotification: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendReadyNotificationParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessMessageDetail'];
    };
    dismissUnassignMessageDetail: {
      Req: Partial<components['schemas']['com.moego.server.message.web.vo.PhoneNumberVo']>;
      Res: boolean;
    };
    listUnassignMessageCallDetail: {
      Req: Partial<
        {
          pageNum: number;
        } & {
          pageSize: number;
        }
      >;
      Res: components['schemas']['com.moego.server.message.service.dto.UnassignRecordListDto'];
    };
    setUnassignMessageDetailRead: {
      Req: Partial<{}>;
      Res: boolean;
    };
    sendDataMessage: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendDataMessageParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    sendMessage: {
      Req: Partial<components['schemas']['com.google.firebase.messaging.Message']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    batchSendMessage: {
      Req: Partial<components['schemas']['com.google.firebase.messaging.Message'][]>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.google.firebase.messaging.BatchResponse'];
    };
    sendNotification: {
      Req: Partial<components['schemas']['com.moego.server.message.params.SendNotificationParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    listMessageTemplate: {
      Req: Partial<
        {
          customerId?: number;
        } & {
          type: string;
        }
      >;
      Res: components['schemas']['com.moego.server.message.web.vo.MessageTemplateVO'][];
    };
    test: {
      Req: Partial<{
        isLog: boolean;
      }>;
      Res: {};
    };
    addThread: {
      Req: Partial<components['schemas']['com.moego.server.message.params.ThreadAddParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessMessageThread'];
    };
    listThread_1: {
      Req: Partial<{
        threadId: number;
      }>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessMessageThread'];
    };
    listThread: {
      Req: Partial<components['schemas']['com.moego.server.message.params.ThreadQueryParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessMessageThread'];
    };
    updateThread: {
      Req: Partial<components['schemas']['com.moego.server.message.params.ThreadUpdateParams']>;
      Res: components['schemas']['com.moego.common.response.ResponseResultJava.lang.String'];
    };
    transferReminder: {
      Req: Partial<{}>;
      Res: {};
    };
    getBusinessTwilioSetting: {
      Req: Partial<{}>;
      Res: components['schemas']['com.moego.server.message.service.dto.TwilioSettingDto'];
    };
    updateBusinessTwilioSetting: {
      Req: Partial<components['schemas']['com.moego.server.message.service.dto.TwilioSettingUpdateDto']>;
      Res: boolean;
    };
    twilioVoiceWebhookStatus: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveVoiceCallbackParams']>;
      Res: {};
    };
    getReminderStartTwiML: {
      Req: Partial<
        {
          apptId: number;
        } & {
          Digits?: string;
        }
      >;
      Res: {};
    };
    twilioVoiceWebhook: {
      Req: Partial<components['schemas']['com.moego.server.message.params.TwilioReceiveVoiceCallbackParams']>;
      Res: {};
    };
    redirectShortLink: {
      Req: Partial<{}>;
      Res: {};
    };
    redirectShortLink_3: {
      Req: Partial<{}>;
      Res: {};
    };
    redirectShortLink_2: {
      Req: Partial<{}>;
      Res: {};
    };
    redirectShortLink_5: {
      Req: Partial<{}>;
      Res: {};
    };
    redirectShortLink_6: {
      Req: Partial<{}>;
      Res: {};
    };
    redirectShortLink_1: {
      Req: Partial<{}>;
      Res: {};
    };
    redirectShortLink_4: {
      Req: Partial<{}>;
      Res: {};
    };
  }
  export interface components {
    schemas: {
      'com.google.api.client.http.HttpContent': {
        /** Format: int64 */
        length: string | number;
        type: string;
      };
      'com.google.firebase.IncomingHttpResponse': {
        content: string;
        headers: {
          [key: string]: any;
        };
        request: components['schemas']['com.google.firebase.OutgoingHttpRequest'];
        /** Format: int32 */
        statusCode: number;
      };
      'com.google.firebase.messaging.BatchResponse': {
        /** Format: int32 */
        failureCount: number;
        responses: components['schemas']['com.google.firebase.messaging.SendResponse'][];
        /** Format: int32 */
        successCount: number;
      };
      'com.google.firebase.messaging.FirebaseMessagingException': {
        cause: {
          localizedMessage?: string;
          message?: string;
          stackTrace?: {
            classLoaderName?: string;
            className?: string;
            fileName?: string;
            /** Format: int32 */
            lineNumber?: number;
            methodName?: string;
            moduleName?: string;
            moduleVersion?: string;
            nativeMethod?: boolean;
          }[];
        };
        /** @enum {string} */
        errorCode: ComGoogleFirebaseMessagingFirebaseMessagingExceptionErrorCode;
        httpResponse: components['schemas']['com.google.firebase.IncomingHttpResponse'];
        localizedMessage: string;
        message: string;
        /** @enum {string} */
        messagingErrorCode: ComGoogleFirebaseMessagingFirebaseMessagingExceptionMessagingErrorCode;
        stackTrace: {
          classLoaderName?: string;
          className?: string;
          fileName?: string;
          /** Format: int32 */
          lineNumber?: number;
          methodName?: string;
          moduleName?: string;
          moduleVersion?: string;
          nativeMethod?: boolean;
        }[];
        suppressed: {
          localizedMessage?: string;
          message?: string;
          stackTrace?: {
            classLoaderName?: string;
            className?: string;
            fileName?: string;
            /** Format: int32 */
            lineNumber?: number;
            methodName?: string;
            moduleName?: string;
            moduleVersion?: string;
            nativeMethod?: boolean;
          }[];
        }[];
      };
      'com.google.firebase.messaging.Message': any;
      'com.google.firebase.messaging.SendResponse': {
        exception: components['schemas']['com.google.firebase.messaging.FirebaseMessagingException'];
        messageId: string;
        successful: boolean;
      };
      'com.google.firebase.OutgoingHttpRequest': {
        content: components['schemas']['com.google.api.client.http.HttpContent'];
        headers: {
          [key: string]: any;
        };
        method: string;
        url: string;
      };
      'com.microtripit.mandrillapp.lutung.view.MandrillMessageStatus': {
        email: string;
        id: string;
        rejectReason: string;
        status: string;
      };
      'com.moego.common.dto.CommonResultDto': {
        result: boolean;
      };
      'com.moego.common.dto.notificationDto.NotificationBeanDto': {
        body: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int64 */
        createTime: string | number;
        extra: any;
        /** Format: int32 */
        notificationId: number;
        /** Format: int64 */
        readTime: string | number;
        /** Format: int64 */
        sendTime: string | number;
        title: string;
        type: string;
      };
      'com.moego.common.dto.notificationDto.StaffNotificationTotalDto': {
        /** Format: int32 */
        activity: number;
        /** Format: int32 */
        system: number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.dto.ApptReminderDetailDTO': {
        dataList: components['schemas']['com.moego.server.message.dto.ApptReminderDetailDTO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.dto.BirthdayReminderDetailDTO': {
        dataList: components['schemas']['com.moego.server.message.dto.BirthdayReminderDetailDTO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.dto.RebookReminderDetailDTO': {
        dataList: components['schemas']['com.moego.server.message.dto.RebookReminderDetailDTO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.dto.RepeatReminderDetailDTO': {
        dataList: components['schemas']['com.moego.server.message.dto.RepeatReminderDetailDTO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.dto.ReviewBoosterRecordDetailDTO': {
        dataList: components['schemas']['com.moego.server.message.dto.ReviewBoosterRecordDetailDTO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessMessageBatch': {
        dataList: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessMessageBatch'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessMessageThread': {
        dataList: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessMessageThread'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecord': {
        dataList: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecord'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.web.MoeBusinessMessageReviewBoosterController$LandingPageConfigClientReviewVO': {
        dataList: components['schemas']['com.moego.server.message.web.MoeBusinessMessageReviewBoosterController$LandingPageConfigClientReviewVO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.dto.PageDTOCom.moego.server.message.web.vo.MoeBusinessMessageDetailVO': {
        dataList: components['schemas']['com.moego.server.message.web.vo.MoeBusinessMessageDetailVO'][];
        end: boolean;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int64 */
        total: string | number;
      };
      'com.moego.common.params.FilterParams': {
        /** @description Group of filters */
        filters: components['schemas']['com.moego.common.params.FilterParams'][];
        /**
         * @description Filter comparison operator
         * @enum {string}
         */
        operator: ComMoegoCommonParamsFilterParamsOperator;
        /**
         * @description Filter property
         * @enum {string}
         */
        property: ComMoegoCommonParamsFilterParamsProperty;
        /**
         * @description Filter group conditions, contains and or
         * @enum {string}
         */
        type: ComMoegoCommonParamsFilterParamsType;
        /** @description Filter condition single value */
        value: string;
        /** @description Filter condition multi value */
        values: string[];
      };
      'com.moego.common.params.PageParams': {
        orderBy: string;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
      };
      'com.moego.common.params.QueryParams': {
        /** @description Query keyword */
        keyword: string;
      };
      'com.moego.common.response.ResponseResult[Lcom.microtripit.mandrillapp.lutung.view.MandrillMessageStatus;': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.microtripit.mandrillapp.lutung.view.MandrillMessageStatus'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.google.firebase.messaging.BatchResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.google.firebase.messaging.BatchResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.CommonResultDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.CommonResultDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.notificationDto.StaffNotificationTotalDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.notificationDto.StaffNotificationTotalDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.ApptReminderDetailDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.dto.ApptReminderDetailDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.BirthdayReminderDetailDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.dto.BirthdayReminderDetailDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.RebookReminderDetailDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.dto.RebookReminderDetailDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.dto.RepeatReminderDetailDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.dto.RepeatReminderDetailDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessMessageBatch': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessMessageBatch'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessMessageThread': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessMessageThread'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecord': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecord'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.common.dto.PageDTOCom.moego.server.message.web.vo.MoeBusinessMessageDetailVO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.common.dto.PageDTOCom.moego.server.message.web.vo.MoeBusinessMessageDetailVO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.customer.dto.CustomerInfoBookOnlineDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.customer.dto.CustomerInfoBookOnlineDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.dto.AgreementSmsTemplateDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.AgreementSmsTemplateDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.dto.ArrivalWindowSettingDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.ArrivalWindowSettingDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.dto.MessageCountDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.MessageCountDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.dto.MobileNotificationUnreadCountDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.MobileNotificationUnreadCountDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.dto.MoeBusinessAutoReplyDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.MoeBusinessAutoReplyDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.dto.MoeBusinessReviewBoosterCountDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.MoeBusinessReviewBoosterCountDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.dto.NotificationPageDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.NotificationPageDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.dto.ReminderSummaryDTO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.ReminderSummaryDTO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAgreementSendTemplate': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessAgreementSendTemplate'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessAutoReply': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessAutoReply'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessMessageDetail': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessMessageDetail'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessMessageThread': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessMessageThread'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessReminder': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessReminder'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessReminderDismiss': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessReminderDismiss'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.mapperbean.MoeBusinessReviewBooster': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessReviewBooster'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.service.dto.UnreadMessageNumberDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.service.dto.UnreadMessageNumberDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.web.dto.ob.SendVerifyCodeResultDto': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.web.dto.ob.SendVerifyCodeResultDto'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.web.model.CodeSendResponse': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.web.model.CodeSendResponse'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultCom.moego.server.message.web.vo.MoeBusinessMessageDetailVO': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.web.vo.MoeBusinessMessageDetailVO'];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.Boolean': {
        /** Format: int32 */
        code: number;
        data: boolean;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.Integer': {
        /** Format: int32 */
        code: number;
        /** Format: int32 */
        data: number;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.lang.String': {
        /** Format: int32 */
        code: number;
        data: string;
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.dto.EmailReportItem': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.EmailReportItem'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.dto.MessageReportItem': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.dto.MessageReportItem'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.mapperbean.MoeBusinessMessageDetail': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessMessageDetail'][];
        message: string;
        success: boolean;
      };
      'com.moego.common.response.ResponseResultJava.util.ListCom.moego.server.message.mapperbean.MoeBusinessReminder': {
        /** Format: int32 */
        code: number;
        data: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessReminder'][];
        message: string;
        success: boolean;
      };
      'com.moego.server.customer.dto.CustomerAddressDto': {
        address1: string;
        address2: string;
        city: string;
        country: string;
        /** Format: int32 */
        customerAddressId: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        isPrimary: number;
        isProfileRequestAddress: boolean;
        lat: string;
        lng: string;
        state: string;
        zipcode: string;
      };
      'com.moego.server.customer.dto.CustomerAgreementInfoDTO': {
        agreementContent: string;
        agreementHeader: string;
        /** Format: int32 */
        agreementId: number;
        /** Format: int32 */
        businessId: number;
        businessName: string;
        /** Format: int64 */
        createTime: string | number;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        /** Format: int32 */
        id: number;
        inputs: string[];
        servicesType: string;
        signature: string;
        /** Format: int32 */
        type: number;
        /** Format: int64 */
        updateTime: string | number;
        uploadImages: string;
      };
      'com.moego.server.customer.dto.CustomerContactDto': {
        /** Format: int32 */
        contactId: number;
        /** Format: int32 */
        customerContactId: number;
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        /** Format: int32 */
        isPrimary: number;
        lastName: string;
        name: string;
        phoneNumber: string;
        title: string;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.customer.dto.CustomerInfoBookOnlineDto': {
        /** Format: int64 */
        accountId: string | number;
        actionState: string;
        agreementIsNeedSignature: {
          [key: string]: boolean | undefined;
        };
        /** Format: int64 */
        allocateStaffId: string | number;
        /** @description 对应复选框选项： 1-Message 2-email  3-phone call */
        apptReminderByList: number[];
        avatarPath: string;
        /** Format: date-time */
        birthday: string;
        /**
         * Format: int32
         * @deprecated
         */
        businessId: number;
        clientColor: string;
        /** Format: int64 */
        createTime: string | number;
        /** @description customer code(8), all customers have and only have pre-generated one. */
        customerCode: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        expectedServiceDate: string;
        externalId: string;
        firstName: string;
        /** @description 商家primary card processor 对应的customer是否已绑定卡 */
        hasCreditCard: boolean;
        /** @deprecated */
        hasStripeCard: boolean;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        inactive: number;
        /** Format: int32 */
        isBlockMessage: number;
        /** Format: int32 */
        isBlockOnlineBooking: number;
        isHaveAddress: boolean;
        isNeedSignature: boolean;
        /** Format: int32 */
        isRecurring: number;
        /** Format: int32 */
        isUnsubscribed: number;
        lastName: string;
        lastServiceTime: string;
        lifeCycle: string;
        /** @deprecated */
        loginEmail: string;
        outOfArea: boolean;
        phoneNumber: string;
        /** @description preferred business id */
        preferredBusinessId: string;
        preferredDay: number[];
        /** Format: int32 */
        preferredFrequencyDay: number;
        /** Format: int32 */
        preferredFrequencyType: number;
        /** Format: int32 */
        preferredGroomerId: number;
        preferredTime: number[];
        primaryAddress: string;
        /** Format: int32 */
        primaryAddressId: number;
        primaryAddressZipcode: string;
        primaryLat: string;
        primaryLng: string;
        referralSourceDesc: string;
        /** Format: int32 */
        referralSourceId: number;
        /** Format: int32 */
        sendAppAutoMessage: number;
        /** Format: int32 */
        sendAutoEmail: number;
        /** Format: int32 */
        sendAutoMessage: number;
        source: string;
        /** Format: int32 */
        status: number;
        type: string;
        /**
         * Format: int32
         * @deprecated
         */
        unconfirmedReminderBy: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.customer.dto.CustomerInfoDto': {
        /** Format: int64 */
        accountId: string | number;
        actionState: string;
        address: components['schemas']['com.moego.server.customer.dto.CustomerAddressDto'];
        /** Format: int64 */
        allocateStaffId: string | number;
        /** @description 对应复选框选项： 1-Message 2-email  3-phone call */
        apptReminderByList: number[];
        avatarPath: string;
        /** Format: date-time */
        birthday: string;
        /**
         * Format: int32
         * @deprecated
         */
        businessId: number;
        clientColor: string;
        /** Format: int64 */
        createTime: string | number;
        /** @description customer code(8), all customers have and only have pre-generated one. */
        customerCode: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        expectedServiceDate: string;
        externalId: string;
        firstName: string;
        hasPetParentAppAccount: boolean;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        inactive: number;
        /** Format: int32 */
        isBlockMessage: number;
        /** Format: int32 */
        isBlockOnlineBooking: number;
        isNewCustomer: boolean;
        isProspectCustomer: boolean;
        /** Format: int32 */
        isRecurring: number;
        /** Format: int32 */
        isUnsubscribed: number;
        lastName: string;
        lastServiceTime: string;
        lifeCycle: string;
        /** @deprecated */
        loginEmail: string;
        phoneNumber: string;
        /** @description preferred business id */
        preferredBusinessId: string;
        preferredDay: number[];
        /** Format: int32 */
        preferredFrequencyDay: number;
        /** Format: int32 */
        preferredFrequencyType: number;
        /** Format: int32 */
        preferredGroomerId: number;
        preferredTime: number[];
        referralSource: string;
        referralSourceDesc: string;
        /** Format: int32 */
        referralSourceId: number;
        /** Format: int32 */
        sendAppAutoMessage: number;
        /** Format: int32 */
        sendAutoEmail: number;
        /** Format: int32 */
        sendAutoMessage: number;
        /** @description 当share_range_type为3时，记录的所有apptIds，仅shareRangeType为3时生效 */
        shareApptIds: number[];
        /** Format: int32 */
        shareApptStatus: number;
        /** Format: int32 */
        shareRangeType: number;
        /**
         * Format: int32
         * @description 不同type时的value
         */
        shareRangeValue: number;
        source: string;
        /** Format: int32 */
        status: number;
        type: string;
        /**
         * Format: int32
         * @deprecated
         */
        unconfirmedReminderBy: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      'com.moego.server.customer.dto.CustomerPetReminderDTO': {
        avatarPath: string;
        behavior: string;
        birthday: string;
        /** Format: int32 */
        birthDayTime: number;
        breed: string;
        /** Format: int32 */
        breedMix: number;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        /** Format: int32 */
        expiryNotification: number;
        fixed: string;
        /** Format: int32 */
        gender: number;
        hairLength: string;
        /** Format: int32 */
        lifeStatus: number;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        /** Format: int32 */
        status: number;
        typeName: string;
        weight: string;
      };
      'com.moego.server.customer.dto.GroomingCustomerInfoDTO': {
        /** @description 对应复选框选项： 1-Message 2-email  3-phone call */
        apptReminderByList: number[];
        avatarPath: string;
        /** Format: int32 */
        customerId: number;
        email: string;
        firstName: string;
        /** Format: int32 */
        inactive: number;
        lastName: string;
        phoneNumber: string;
        /** Format: int32 */
        sendAppAutoMessage: number;
        /** Format: int32 */
        sendAutoEmail: number;
        /** Format: int32 */
        sendAutoMessage: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        unconfirmedReminderBy: number;
      };
      /** @description pet name list (没有pass away) */
      'com.moego.server.customer.dto.PetNameBreedDto': {
        breed: string;
        name: string;
        /** Format: int32 */
        petId: number;
      };
      'com.moego.server.customer.params.CustomerSearchStatusVo': {
        activeClient: boolean;
        all: boolean;
        blockMessage: boolean;
        blockOnlineBooking: boolean;
        fromIntakeForm: boolean;
        fromOnlineBooking: boolean;
        inactiveClient: boolean;
        lapsed: boolean;
      };
      'com.moego.server.grooming.dto.CustomerRebookReminderDTO': {
        appointmentDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        avatarPath: string;
        /** Format: int32 */
        businessId: number;
        customerFirstName: string;
        /** Format: int32 */
        customerId: number;
        customerLastName: string;
        expectedServiceDate: string;
        /** Format: int32 */
        groomingId: number;
        /** Format: int32 */
        preferredFrequencyDay: number;
      };
      /** @description body_view question marked result url */
      'com.moego.server.grooming.dto.groomingreport.BodyViewUrl': {
        left: string;
        right: string;
      };
      'com.moego.server.grooming.dto.groomingreport.GroomingRecommendation': {
        /**
         * Format: int32
         * @description customer prefer frequency day
         */
        frequencyDay: number;
        /** @description formatted frequency text: Every xx days, Every xx weeks, Every xx months */
        frequencyText: string;
        /** Format: int32 */
        frequencyType: number;
        /** @description next appointment date, standard format: yyyy-MM-dd, need to be formatted by front end */
        nextAppointmentDate: string;
        /** @description next appointment date */
        nextAppointmentDateText: string;
      };
      'com.moego.server.grooming.dto.MoeGroomingAppointmentDTO': {
        appointmentDate: string;
        appointmentEndDate: string;
        /** Format: int32 */
        appointmentEndTime: number;
        /** Format: int32 */
        appointmentStartTime: number;
        /** Format: int32 */
        bookOnlineStatus: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        cancelBy: number;
        /** Format: int32 */
        cancelByType: number;
        /** Format: int64 */
        canceledTime: string | number;
        /** Format: int64 */
        checkInTime: string | number;
        /** Format: int64 */
        checkOutTime: string | number;
        colorCode: string;
        /** Format: int32 */
        confirmBy: number;
        /** Format: int32 */
        confirmByType: number;
        /** Format: int64 */
        confirmedTime: string | number;
        /** Format: int32 */
        createdById: number;
        /** Format: int64 */
        createTime: string | number;
        /** Format: int32 */
        customerAddressId: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isBlock: number;
        /** Format: int32 */
        isPaid: number;
        /** Format: int32 */
        isPustNotification: number;
        /** Format: int32 */
        isWaitingList: number;
        /** Format: int32 */
        moveWaitingBy: number;
        /** Format: int32 */
        noShow: number;
        orderId: string;
        /** Format: int32 */
        outOfArea: number;
        /** Format: int32 */
        repeatId: number;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        status: number;
        /** Format: int64 */
        updateTime: string | number;
      };
      /** @description content to preview */
      'com.moego.server.grooming.params.groomingreport.GroomingReportContentParams': {
        feedbacks: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams'][];
        petConditions: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams'][];
        recommendation: components['schemas']['com.moego.server.grooming.dto.groomingreport.GroomingRecommendation'];
        showcase: string[];
      };
      'com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams': {
        content: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportContentParams'];
        /**
         * Format: int32
         * @description preview actual report
         */
        reportId: number;
        template: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams'];
        /** @description theme code */
        themeCode: string;
      };
      'com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams': {
        /** @description default options list, not allow to change */
        buildInOptions: string[];
        /** Format: int32 */
        category: number;
        /** @description default selected options */
        choices: string[];
        /** @description single_choice/multi_choice, custom options list */
        customOptions: string[];
        /**
         * Format: int32
         * @description question id, not need for creating a new question
         */
        id: number;
        /** @description system default question key */
        key: string;
        /** @description single_choice/multi_choice, options list */
        options: string[];
        /** @description question options editable */
        optionsEditable: boolean;
        /** @description text_input question placeholder */
        placeholder: string;
        /** @description is required to fill in */
        required: boolean;
        /** @description non required question show at question list */
        show: boolean;
        /**
         * Format: int32
         * @description sort value, in descending order
         */
        sort: number;
        /** Format: int32 */
        status: number;
        /** @description text_input question default value */
        text: string;
        /** @description question title */
        title: string;
        /** @description question title editable */
        titleEditable: boolean;
        /** @description question type: single_choice/multi_choice/text_input/body_view/tag_choice/short_text_input */
        type: string;
        /** @description question type editable */
        typeEditable: boolean;
        urls: components['schemas']['com.moego.server.grooming.dto.groomingreport.BodyViewUrl'];
      };
      /** @description real-time template to preview */
      'com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams': {
        /** @description facebook review icon jump link */
        facebookReviewLink: string;
        /** @description google review icon jump link */
        googleReviewLink: string;
        /** @description light theme color: #xxxxxx or #xxx, moe_grooming_report_template.light_theme_color */
        lightThemeColor: string;
        /** Format: int32 */
        nextAppointmentDateFormatType: number;
        questions: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams$TemplateQuestionParams'];
        /** @description is after photo required when edit grooming report */
        requireAfterPhoto: boolean;
        /** @description is before photo required when edit grooming report */
        requireBeforePhoto: boolean;
        /** @description show facebook review icon */
        showFacebookReview: boolean;
        /** @description show google review icon */
        showGoogleReview: boolean;
        /** @description is next appointment section show */
        showNextAppointment: boolean;
        /** @description show overall feedback section */
        showOverallFeedback: boolean;
        /** @description show pet condition section when edit or show on client page */
        showPetCondition: boolean;
        /** @description show review booster on report page */
        showReviewBooster: boolean;
        /** @description show staff name */
        showServiceStaffName: boolean;
        /** @description is showcase section show */
        showShowcase: boolean;
        /** @description show yelp review icon */
        showYelpReview: boolean;
        /** @description thank you message in grooming report, moe_grooming_report_template.thank_you_message */
        thankYouMessage: string;
        /** @description default theme code */
        themeCode: string;
        /** @description theme color: #xxxxxx or #xxx, moe_grooming_report_template.theme_color */
        themeColor: string;
        /** @description customized grooming report title */
        title: string;
        /** @description yelp review icon jump link */
        yelpReviewLink: string;
      };
      /** @description question list: overall feedback, pet conditions */
      'com.moego.server.grooming.params.groomingreport.GroomingReportTemplateParams$TemplateQuestionParams': {
        feedbacks: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams'][];
        petConditions: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportQuestionParams'][];
      };
      'com.moego.server.grooming.params.ob.OBAnonymousParams': {
        /** @description Customized URL domain, demo URL: crazycutepetspa.moego.online */
        domain: string;
        /** @description Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa */
        name: string;
      };
      'com.moego.server.message.dto.AgreementSmsTemplateDto': {
        smsTemplate: string;
      };
      'com.moego.server.message.dto.ApptReminderDetailDTO': {
        /** Format: int32 */
        actualSendTime: number;
        /** Format: int32 */
        customerId: number;
        customerInfo: components['schemas']['com.moego.server.customer.dto.GroomingCustomerInfoDTO'];
        detail: components['schemas']['com.moego.server.grooming.dto.MoeGroomingAppointmentDTO'];
        /** Format: int32 */
        id: number;
        order1: string;
        /** Format: int32 */
        order2: number;
        /** Format: int32 */
        order3: number;
        /** Format: int32 */
        planSendTime: number;
        /** Format: int32 */
        reminderType: number;
        /** Format: int32 */
        sendCount: number;
        sendDetail: components['schemas']['com.moego.server.message.dto.MessageDetailDTO'];
      };
      /** @description ArrivalWindowSettingDto */
      'com.moego.server.message.dto.ArrivalWindowSettingDto': {
        /**
         * Format: int32
         * @description arrival after 分钟数
         */
        arrivalAfter: number;
        /**
         * Format: int32
         * @description arrival before 分钟数
         */
        arrivalBefore: number;
        /** Format: int32 */
        businessId?: number;
        /** Format: int32 */
        id?: number;
        /**
         * Format: int32
         * @description 是否打开 1打开 0关闭
         */
        status: number;
      };
      'com.moego.server.message.dto.BirthdayReminderDetailDTO': {
        /** Format: int32 */
        actualSendTime: number;
        /** Format: int32 */
        customerId: number;
        customerInfo: components['schemas']['com.moego.server.customer.dto.GroomingCustomerInfoDTO'];
        detail: components['schemas']['com.moego.server.customer.dto.CustomerPetReminderDTO'];
        /** Format: int32 */
        id: number;
        order1: string;
        /** Format: int32 */
        order2: number;
        /** Format: int32 */
        order3: number;
        /** Format: int32 */
        planSendTime: number;
        /** Format: int32 */
        reminderType: number;
        /** Format: int32 */
        sendCount: number;
        sendDetail: components['schemas']['com.moego.server.message.dto.MessageDetailDTO'];
      };
      'com.moego.server.message.dto.EmailReportItem': {
        /** Format: int32 */
        availableEmails: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        companyId: number;
        /** Format: int32 */
        cycleBeginTime: number;
        /** Format: int32 */
        cycleEndTime: number;
        /** Format: int32 */
        purchaseEmailAmount: number;
        /** Format: int32 */
        purchaseEmailLeftover: number;
        /** Format: int32 */
        subscriptionEmailAmount: number;
        /** Format: int32 */
        usedEmails: number;
      };
      'com.moego.server.message.dto.GroomingReportSendParams': {
        /** @description 需要发送的 reportId 列表 */
        reportIdList: number[];
        /** @description sending method to send，如不传，则根据设置项选择的方式来发送 */
        sendingMethodList?: number[];
      };
      'com.moego.server.message.dto.GroomingReportSendPreviewParams': {
        /** @description email title */
        emailSubject?: string;
        previewParams?: components['schemas']['com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams'];
        /** @description recipient email */
        recipientEmail: string;
      };
      'com.moego.server.message.dto.MessageCountDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        companyId: number;
        isUnlimited: boolean;
        /** Format: int32 */
        totalMessage: number;
        /** Format: int32 */
        totalMessageUsed: number;
        /** Format: int32 */
        used2wayMessage: number;
        /** Format: int32 */
        usedAutoMessage: number;
        /** Format: int32 */
        usedCall: number;
      };
      'com.moego.server.message.dto.MessageDetailDTO': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        contactName: string;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        customerIsRead: number;
        /** Format: int32 */
        deleteBy: number;
        /** Format: int32 */
        deleteTime: number;
        /** Format: int32 */
        errorCode: number;
        errorMessage: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isRead: number;
        /** Format: int32 */
        isSuccessed: number;
        mediaUrl1: string;
        messageSid: string;
        messageText: string;
        /** Format: int32 */
        messageType: number;
        /** Format: int32 */
        method: number;
        mimeType: string;
        /** Format: int32 */
        numSegments: number;
        phoneNumber: string;
        /** Format: int32 */
        readTime: number;
        reversePhone: string;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        targetId: number;
        /** Format: int32 */
        targetType: number;
        /** Format: int32 */
        type: number;
        /** Format: int32 */
        unconfirmApptid: number;
      };
      /** @description Message 关联的信息，目前只有 grLinkOpenedCount，可能为空，不同类型的 Message 关联的信息不同 */
      'com.moego.server.message.dto.MessageExtraInfo': {
        /**
         * Format: int32
         * @description Grooming Report 链接被打开的次数
         */
        grLinkOpenedCount: number;
      };
      'com.moego.server.message.dto.MessageReportItem': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        companyId: number;
        /** Format: int32 */
        cycleBeginTime: number;
        /** Format: int32 */
        cycleEndTime: number;
        /** Format: int32 */
        freeAutoMessage: number;
        /** Format: int32 */
        purchaseAmount: number;
        /** Format: int32 */
        purchaseLeftover: number;
        /** Format: int32 */
        subscriptionAmount: number;
        /** Format: int32 */
        used2wayMessage: number;
        /** Format: int32 */
        usedAutoMessage: number;
        /** Format: int32 */
        usedCall: number;
      };
      'com.moego.server.message.dto.MobileNotificationUnreadCountDto': {
        /** Format: int32 */
        financeCapitalOfferApprovedCount: number;
        /** Format: int32 */
        intakeFormCount: number;
        notificationTypeCount: {
          [key: string]: number | undefined;
        };
        /** Format: int32 */
        onlineBookingAbandonedCount: number;
        /** Format: int32 */
        onlineBookingCount: number;
        /** Format: int32 */
        onlineBookingWaitlistCount: number;
        /** Format: int32 */
        unpaidApptCount: number;
      };
      'com.moego.server.message.dto.MoeBusinessAutoReplyDto': {
        autoReplyBody: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        crateTime: number;
        /** Format: int32 */
        endTime: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        status: number;
        timeRange: string;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.dto.MoeBusinessReviewBoosterCountDTO': {
        avgScore: number;
        /**
         * Format: int32
         * @description grooming report 来源的 review 数量
         */
        grReviewCount: number;
        /**
         * Format: int32
         * @description pet parent portal 来源的 review 数量
         */
        pppReviewCount: number;
        /** Format: int32 */
        reviewTotal: number;
        /** Format: int32 */
        sendTotal: number;
        /**
         * Format: int32
         * @description SMS 来源的 review 数量
         */
        smsReviewCount: number;
        sumScore: number;
      };
      'com.moego.server.message.dto.NotificationPageDto': {
        hasMore: boolean;
        notificationList: components['schemas']['com.moego.common.dto.notificationDto.NotificationBeanDto'][];
      };
      'com.moego.server.message.dto.PayOnlineEmailDto': {
        depositAmount?: number;
        email: string;
        /** Format: int32 */
        invoiceId: number;
        invoiceIds?: number[];
        /** @description 顾客支付时是否需要添加processing fee */
        requiredProcessingFee?: boolean;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.message.dto.RebookReminderDetailDTO': {
        /** Format: int32 */
        actualSendTime: number;
        /** Format: int32 */
        customerId: number;
        customerInfo: components['schemas']['com.moego.server.customer.dto.GroomingCustomerInfoDTO'];
        detail: components['schemas']['com.moego.server.grooming.dto.CustomerRebookReminderDTO'];
        /** Format: int32 */
        id: number;
        order1: string;
        /** Format: int32 */
        order2: number;
        /** Format: int32 */
        order3: number;
        /** Format: int32 */
        planSendTime: number;
        /** Format: int32 */
        reminderType: number;
        /** Format: int32 */
        sendCount: number;
        sendDetail: components['schemas']['com.moego.server.message.dto.MessageDetailDTO'];
      };
      'com.moego.server.message.dto.ReminderSummaryDTO': {
        /** Format: int64 */
        birthday: string | number;
        /** Format: int64 */
        rebook: string | number;
        /** Format: int64 */
        repeat: string | number;
        /** Format: int64 */
        unconfirmedAppointment: string | number;
      };
      /** @description ArrivalWindowSettingDto */
      'com.moego.server.message.dto.RepeatExpitySettingDto': {
        /**
         * Format: int32
         * @description 过期前预约数量，0时不做过期查询
         */
        expiryApptNum: number;
      };
      'com.moego.server.message.dto.RepeatReminderDetailDTO': {
        /** Format: int32 */
        actualSendTime: number;
        /** Format: int32 */
        customerId: number;
        customerInfo: components['schemas']['com.moego.server.customer.dto.GroomingCustomerInfoDTO'];
        detail: components['schemas']['com.moego.server.grooming.dto.MoeGroomingAppointmentDTO'];
        /** Format: int32 */
        id: number;
        order1: string;
        /** Format: int32 */
        order2: number;
        /** Format: int32 */
        order3: number;
        /** Format: int32 */
        planSendTime: number;
        /** Format: int32 */
        reminderType: number;
        /** Format: int32 */
        sendCount: number;
        sendDetail: components['schemas']['com.moego.server.message.dto.MessageDetailDTO'];
      };
      'com.moego.server.message.dto.ReviewBoosterRecordDetailDTO': {
        appointmentInfo: components['schemas']['com.moego.server.message.dto.ReviewBoosterRecordDetailDTO$AppointmentInfo'];
        customerInfo: components['schemas']['com.moego.server.message.dto.ReviewBoosterRecordDetailDTO$CustomerInfo'];
        petInfoList: components['schemas']['com.moego.server.message.dto.ReviewBoosterRecordDetailDTO$PetInfo'][];
        reviewDetail: components['schemas']['com.moego.server.message.dto.ReviewBoosterRecordDTO'];
        staffInfoList: components['schemas']['com.moego.server.message.dto.ReviewBoosterRecordDetailDTO$StaffInfo'][];
      };
      'com.moego.server.message.dto.ReviewBoosterRecordDetailDTO$AppointmentInfo': {
        appointmentDateTime: string;
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.message.dto.ReviewBoosterRecordDetailDTO$CustomerInfo': {
        avatarPath: string;
        /** Format: int32 */
        customerId: number;
        firstName: string;
        lastName: string;
        phoneNumber: string;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.message.dto.ReviewBoosterRecordDetailDTO$PetInfo': {
        avatarPath: string;
        /** Format: int32 */
        lifeStatus: number;
        petBreed: string;
        /** Format: int32 */
        petId: number;
        petName: string;
        /** Format: int32 */
        petTypeId: number;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.message.dto.ReviewBoosterRecordDetailDTO$StaffInfo': {
        avatarPath: string;
        firstName: string;
        lastName: string;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.message.dto.ReviewBoosterRecordDTO': {
        appointmentDate: string;
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        id: number;
        petIds: number[];
        /** Format: int32 */
        positiveScore: number;
        /** Format: int32 */
        reviewBoosterId: number;
        reviewContent: string;
        /** Format: int32 */
        reviewTime: number;
        /** Format: int32 */
        source: number;
        staffIds: number[];
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.dto.UpcomingEmailDto': {
        /** Format: int32 */
        customerId: number;
        email?: string;
      };
      'com.moego.server.message.mapperbean.MoeBusinessAgreementSendTemplate': {
        /** Format: int32 */
        agreementId: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        byStaffId: number;
        /** Format: int32 */
        crateTime: number;
        emailBody: string;
        emailTitle: string;
        /** Format: int32 */
        id: number;
        smsBody: string;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate': {
        body: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        crateTime: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        type: number;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessAutoReply': {
        autoReplyBody: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        crateTime: number;
        /** Format: int32 */
        endTime: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        status: number;
        timeRange: string;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessMessageBatch': {
        /** Format: int32 */
        batchSize: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isFinish: number;
        messageText: string;
        /** Format: int32 */
        method: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessMessageDetail': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        contactName: string;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        customerIsRead: number;
        /** Format: int32 */
        deleteBy: number;
        /** Format: int32 */
        deleteTime: number;
        /** Format: int32 */
        errorCode: number;
        errorMessage: string;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isRead: number;
        /** Format: int32 */
        isSuccessed: number;
        mediaUrl1: string;
        messageSid: string;
        messageText: string;
        /** Format: int32 */
        messageType: number;
        /** Format: int32 */
        method: number;
        mimeType: string;
        /** Format: int32 */
        numSegments: number;
        phoneNumber: string;
        /** Format: int32 */
        readTime: number;
        reversePhone: string;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        targetId: number;
        /** Format: int32 */
        targetType: number;
        /** Format: int32 */
        type: number;
        /** Format: int32 */
        unconfirmApptid: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessMessageThread': {
        /** Format: int32 */
        blockTime: number;
        /** Format: int32 */
        businessId: number;
        clientColor: string;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createTime: number;
        customerAvatar: string;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        customerUnreadCount: number;
        /** Format: int32 */
        deleteTime: number;
        firstName: string;
        /** Format: int32 */
        id: number;
        isNewCustomer: boolean;
        isProspectCustomer: boolean;
        /** Format: int32 */
        lastErrorCode: number;
        lastErrorMsg: string;
        /** Format: int32 */
        lastMessageId: number;
        lastMessageText: string;
        /** Format: int32 */
        lastMessageTime: number;
        lastName: string;
        /** Format: int32 */
        oldGroupId: number;
        /** Format: int32 */
        openStatus: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        starsTime: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        unreadCount: number;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessReminder': {
        /** Format: int32 */
        afterMinute: number;
        /** Format: int32 */
        atHour: number;
        /** Format: int32 */
        beforeDay: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        crateTime: number;
        /** Format: int32 */
        id: number;
        reminderBody: string;
        /** Format: int32 */
        reminderType: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessReminderDismiss': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        crateTime: number;
        /** Format: int32 */
        dismissId: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        reminderType: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessReviewBooster': {
        /** Format: int32 */
        autoWaitingMins: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        enableAuto: number;
        /** Format: int32 */
        id: number;
        negativeBody: string;
        positiveBody: string;
        positiveFacebook: string;
        positiveGoogle: string;
        /** Format: int32 */
        positiveScore: number;
        positiveYelp: string;
        reviewBody: string;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecord': {
        appointmentDate: string;
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        id: number;
        petIds: string;
        /** Format: int32 */
        positiveScore: number;
        /** Format: int32 */
        reviewBoosterId: number;
        reviewContent: string;
        /** Format: date-time */
        reviewTime: string;
        /** Format: int32 */
        source: number;
        staffIds: string;
        /** Format: int32 */
        updateTime: number;
      };
      'com.moego.server.message.params.AutoMessageParams': {
        /** Format: int32 */
        businessId?: number;
        /** Format: int32 */
        targetId: number;
        /** Format: int32 */
        targetType: number;
      };
      'com.moego.server.message.params.AutoTemplateSendParams': {
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        autoTemplateType: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        customerId: number;
      };
      'com.moego.server.message.params.BatchAutoTemplateSendParams': {
        appointmentCustomers: components['schemas']['com.moego.server.message.params.BatchAutoTemplateSendParams$AppointmentCustomer'][];
        /** Format: int32 */
        autoTemplateType: number;
        /** Format: int32 */
        businessId: number;
      };
      'com.moego.server.message.params.BatchAutoTemplateSendParams$AppointmentCustomer': {
        /** Format: int32 */
        appointmentId: number;
        /** Format: int32 */
        customerId: number;
      };
      'com.moego.server.message.params.GetReviewBoosterRecordListParams': {
        /**
         * Format: int32
         * @description query param: customer id
         */
        customerId: number;
        order: string;
        orderType: string[];
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** @description query param: only for the replied records */
        repliedOnly: boolean;
        /** @description query param: scores list, 1-5 */
        scores: number[];
        sortBy: string;
        sortFields: string[];
        /** @description query param: source list, 1-sms, 2-grooming report link, 3-pet parent portal */
        sources: number[];
        /** @description query param: staff id list */
        staffIds: number[];
      };
      'com.moego.server.message.params.MessageReminderParams': {
        /**
         * Format: int32
         * @description when to send after a specified event
         */
        afterMinute: number;
        /** Format: int32 */
        atHour: number;
        /** Format: int32 */
        beforeDay: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        id: number;
        reminderBody: string;
        /** Format: int32 */
        reminderType: number;
      };
      'com.moego.server.message.params.MobileQueryNotificationParams': {
        /** Format: int32 */
        limit: number;
        /** Format: int32 */
        startingAfter?: number;
        type: string[];
      };
      'com.moego.server.message.params.MoeBusinessAgreementTemplateParams': {
        /** Format: int32 */
        agreementId: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        byStaffId: number;
        emailBody: string;
        emailTitle: string;
        /** Format: int32 */
        id: number;
        smsBody: string;
      };
      'com.moego.server.message.params.MoeBusinessAutoMessageTemplateParams': {
        body: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.message.params.MoeBusinessAutoReplyParams': {
        autoReplyBody: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        endTime: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        startTime: number;
        /** Format: int32 */
        status: number;
        timeRange: string;
      };
      'com.moego.server.message.params.MoeBusinessReviewBoosterParams': {
        /**
         * Format: int32
         * @description 自动发送等待分钟数，0~10080(7d)
         */
        autoWaitingMins: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        enableAuto: number;
        /** Format: int32 */
        id: number;
        negativeBody: string;
        positiveBody: string;
        positiveFacebook: string;
        positiveGoogle: string;
        /** Format: int32 */
        positiveScore: number;
        positiveYelp: string;
        reviewBody: string;
      };
      'com.moego.server.message.params.MoeBusinessReviewBoosterParamsRecord': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        customerId: number;
        orderBy: string;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int32 */
        searchBusinessId: number;
      };
      'com.moego.server.message.params.notification.AppPushTokenSaveParams': {
        /** @description ios 或 android */
        deviceType: string;
        /** @description 需要移除的旧expo token */
        oldExpoToken: string;
        pushToken: string;
        /** Format: int32 */
        tokenType: number;
      };
      'com.moego.server.message.params.NotificationIdParams': {
        /** Format: int32 */
        notificationId: number;
        source: string;
      };
      'com.moego.server.message.params.NotificationSetReadParams': {
        source: string;
        tab: string;
        type: string[];
      };
      'com.moego.server.message.params.OnlineBookWaitingNotifyParams': {
        /** Format: int64 */
        bookingRequestId?: string | number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId?: string | number;
        /** Format: int32 */
        groomingId?: number;
        isBusinessSendEmail?: boolean;
        isSendAppMessage?: boolean;
        isSendEmail?: boolean;
        isSendText?: boolean;
        type: string;
      };
      'com.moego.server.message.params.ReminderDismissParams': {
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        dismissId: number;
        /** Format: int32 */
        reminderType: number;
        /** Format: int32 */
        staffId: number;
      };
      'com.moego.server.message.params.ReviewBoosterForGroomingReportParams': {
        /** @description 当前 report id */
        reportUuid: string;
        /** @description review 填写内容，可以不填 */
        reviewContent?: string;
        /**
         * Format: int32
         * @description review 分数，必填：1-5
         */
        score: number;
      };
      'com.moego.server.message.params.SendDataMessageParams': {
        dataMap: {
          [key: string]: string | undefined;
        };
        token: string;
      };
      'com.moego.server.message.params.SendMessageCustomerParams': {
        /** Format: int32 */
        contactId: number;
        /** @deprecated */
        contactName: string;
        customerEmail: string;
        /** Format: int32 */
        customerId: number;
        customerName: string;
        customerNumber: string;
      };
      'com.moego.server.message.params.SendMessagesParams': {
        /** Format: int32 */
        accountId: number;
        /** Format: int32 */
        businessId: number;
        businessName: string;
        businessOwnerEmail: string;
        /** Format: int64 */
        companyId: string | number;
        customer: components['schemas']['com.moego.server.message.params.SendMessageCustomerParams'];
        customerList: components['schemas']['com.moego.server.message.params.SendMessageCustomerParams'][];
        dateFormat: string;
        emailBody: string;
        extParams: {
          [key: string]: any;
        };
        filters: components['schemas']['com.moego.common.params.FilterParams'];
        isAllCustomer: boolean;
        keyword: string;
        messageBody: string;
        /** Format: int32 */
        messageDetailId: number;
        /** Format: int32 */
        messageType: number;
        /** Format: int32 */
        method: number;
        needRefreshCOF: boolean;
        notificationEmailList: string[];
        orderBy: string;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        queries: components['schemas']['com.moego.common.params.QueryParams'];
        /** Format: int32 */
        serviceId: number;
        smartFilter: boolean;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        staffId: number;
        statusSetting: components['schemas']['com.moego.server.customer.params.CustomerSearchStatusVo'];
        subject: string;
        tagIds: number[];
        targetEmail: string;
        /** Format: int32 */
        targetId: number;
        /** Format: int32 */
        targetType: number;
        /** Format: int32 */
        timeFormatType: number;
        /** Format: int32 */
        type: number;
      };
      'com.moego.server.message.params.SendNotificationParams': {
        body: string;
        title: string;
        token: string;
      };
      'com.moego.server.message.params.SendReadyNotificationParams': {
        customer: components['schemas']['com.moego.server.message.params.SendMessageCustomerParams'];
        /** Format: int32 */
        groomingId: number;
        messageBody: string;
        /** Format: int32 */
        messageType: number;
        needRefreshCOF: boolean;
        /** Format: int32 */
        targetId: number;
      };
      'com.moego.server.message.params.ThreadAddParams': {
        /** Format: int32 */
        businessId: number;
        customerAvatar: string;
        /** Format: int32 */
        customerId: number;
        firstName: string;
        lastName: string;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        unreadCount: number;
      };
      'com.moego.server.message.params.ThreadQueryParams': {
        /** Format: int32 */
        businessId: number;
        keyword: string;
        orderBy: string;
        /** Format: int32 */
        pageNo: number;
        /** Format: int32 */
        pageSize: number;
        /** Format: int32 */
        queryType: number;
        /** Format: int32 */
        staffId: number;
        targetTypeList: string;
      };
      'com.moego.server.message.params.ThreadUpdateParams': {
        /** Format: int32 */
        businessId?: number;
        ids: number[];
        /** Format: int32 */
        staffId?: number;
        /** Format: int32 */
        updateType: number;
      };
      'com.moego.server.message.params.TwilioMessageCallbackParams': {
        accountSid: string;
        apiVersion: string;
        delete: boolean;
        /** Format: int32 */
        errorCode: number;
        errorMessage: string;
        from: string;
        /** Format: int32 */
        id: number;
        ids: string;
        messageDetailIdList: number[];
        messageSid: string;
        messageStatus: string;
        read: boolean;
        smsSid: string;
        smsStatus: string;
        to: string;
      };
      'com.moego.server.message.params.TwilioReceiveMessageCallbackParams': {
        body: string;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        customerId: number;
        customerName: string;
        from: string;
        mediaList: components['schemas']['com.moego.server.message.params.TwilioReceiveMessageMedia'][];
        /** @deprecated */
        mediaUrl0: string;
        /** @deprecated */
        mediaUrl1: string;
        smsMessageSid: string;
        to: string;
      };
      'com.moego.server.message.params.TwilioReceiveMessageMedia': {
        mediaUrl: string;
        mimeType: string;
      };
      'com.moego.server.message.params.TwilioReceiveVoiceCallbackParams': {
        accountSid: string;
        apiVersion: string;
        /** Format: int32 */
        businessId: number;
        callbackSource: string;
        /** Format: int32 */
        callDuration: number;
        called: string;
        calledCity: string;
        calledCountry: string;
        calledState: string;
        calledZip: string;
        caller: string;
        callerCity: string;
        callerCountry: string;
        callerState: string;
        callerZip: string;
        /** Format: int32 */
        callHandleType: number;
        callPhoneNumber: string;
        callSid: string;
        callStatus: string;
        callToken: string;
        /** Format: int32 */
        customerId: number;
        direction: string;
        /** Format: int32 */
        duration: number;
        from: string;
        fromCity: string;
        fromCountry: string;
        fromState: string;
        fromZip: string;
        sequenceNumber: string;
        /** Format: int32 */
        status: number;
        stirPassportToken: string;
        stirVerstat: string;
        timestamp: string;
        to: string;
        toCity: string;
        toCountry: string;
        toState: string;
        toZip: string;
      };
      'com.moego.server.message.params.TwilioVerificationMessageCallbackParams': {
        accountSid: string;
        apiVersion: string;
        delete: boolean;
        /** Format: int32 */
        errorCode: number;
        errorMessage: string;
        from: string;
        messageSid: string;
        messageStatus: string;
        phoneNumber: string;
        read: boolean;
        smsSid: string;
        smsStatus: string;
        to: string;
      };
      'com.moego.server.message.service.dto.A2pRecordDto': {
        a2pFeeStatus: string;
        address1: string;
        address2: string;
        businessName: string;
        businessType: string;
        city: string;
        /** Format: int32 */
        companyId: number;
        ein: string;
        einFiles: string[];
        email: string;
        firstName: string;
        iosCountry: string;
        isPaid: boolean;
        lastName: string;
        lat: string;
        lng: string;
        phoneNumber: string;
        region: string;
        website: string;
        withEin: boolean;
        zipcode: string;
      };
      'com.moego.server.message.service.dto.BatchSendMessageResultDto': {
        batchTaskInfo: components['schemas']['com.moego.server.message.mapperbean.MoeBusinessMessageBatch'];
      };
      'com.moego.server.message.service.dto.ExpiryCustomerDto': {
        /** @description last appt date */
        appointmentDate: string;
        /**
         * Format: int32
         * @description last appt end time(分钟数)
         */
        appointmentEndTime: number;
        /**
         * Format: int32
         * @description last appt start time(分钟数)
         */
        appointmentStartTime: number;
        /** Format: int32 */
        customerId: number;
        firstName: string;
        /**
         * Format: int32
         * @description last appt id
         */
        lastApptId: number;
        lastName: string;
        /** @description pet name list (没有pass away) */
        petBreedList: components['schemas']['com.moego.server.customer.dto.PetNameBreedDto'][];
        /**
         * Format: int32
         * @description upcoming 数量
         */
        upcomingCount: number;
      };
      'com.moego.server.message.service.dto.ExpiryListDto': {
        customerList: components['schemas']['com.moego.server.message.service.dto.ExpiryCustomerDto'][];
        /** Format: int32 */
        total: number;
      };
      'com.moego.server.message.service.dto.TwilioSettingDto': {
        /** Format: int32 */
        callHandleType: number;
        /** @description 呼叫转移的个人号码 */
        callPhoneNumber: string;
        /** Format: int32 */
        callReplyType: number;
        /** @description 来电回复的短信内容 */
        replyMessage: string;
        /** @description 商家的twilio设置，为Null则没有分配twilio号码，一般是没有付费，或者需要联系intercom处理 */
        twilioNumber: string;
      };
      'com.moego.server.message.service.dto.TwilioSettingUpdateDto': {
        /** Format: int32 */
        callHandleType: number;
        /** @description 呼叫转移的个人号码 */
        callPhoneNumber: string;
        /** Format: int32 */
        callReplyType: number;
        /** @description 来电回复的短信内容 */
        replyMessage: string;
      };
      'com.moego.server.message.service.dto.UnassignRecordDto': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        isRead: number;
        /** @description type是短信时，短信内容 */
        messageText: string;
        /**
         * Format: int32
         * @description record type 1-msg 4-call
         */
        method: number;
        /** @description 号码 */
        phoneNumber: string;
        sid: string;
      };
      'com.moego.server.message.service.dto.UnassignRecordListDto': {
        unassignRecordList: {
          [key: string]: components['schemas']['com.moego.server.message.service.dto.UnassignRecordDto'][] | undefined;
        };
        /** Format: int64 */
        unreadCount: string | number;
      };
      'com.moego.server.message.service.dto.UnreadMessageNumberDto': {
        /** Format: int32 */
        businessId: number;
        /** Format: int32 */
        totalUnread: number;
      };
      /** @description 分组后的 mms 发送列表，会消耗 list.size 条，如果不符合size 或 count，则抛出异常 */
      'com.moego.server.message.web.dto.ob.FileInfoDto': {
        /** Format: int64 */
        fileSize: string | number;
        url: string;
      };
      'com.moego.server.message.web.dto.ob.SendMMSResult': {
        mmsMessageDetailList: components['schemas']['com.moego.server.message.web.vo.MoeBusinessMessageDetailVO'][];
      };
      'com.moego.server.message.web.dto.ob.SendVerifyCodeDTO': {
        /**
         * Format: int32
         * @deprecated
         */
        businessId?: number;
        businessName?: string;
        /** @description optional, If you fill in the email for double check, the verification code will be sent to the email after passing */
        email?: string;
        phoneNumber: string;
      };
      'com.moego.server.message.web.dto.ob.SendVerifyCodeResultDto': {
        /** @description 是否查到 customer. 当 false 时, result 必为 false (找不到 existing customer 不发短信验证码) */
        findAccount: boolean;
        /** @description 短信验证码是否发送成功, true: 成功, false: 失败 */
        result: boolean;
      };
      'com.moego.server.message.web.model.AutoMessagePreviewResp': {
        content: string;
      };
      'com.moego.server.message.web.model.CodeSendRequest': {
        businessName: string;
        code: string;
        /** Format: int32 */
        customerId: number;
        customerName: string;
        customerNumber: string;
      };
      'com.moego.server.message.web.model.CodeSendResponse': {
        errorMessage: string;
        /** Format: int32 */
        messageId: number;
        success: boolean;
        /** Format: int32 */
        twilioErrorCode: number;
        twilioMessageSid: string;
      };
      'com.moego.server.message.web.model.GetMessageOptoutConfigResponse': {
        messageOptoutConfigVO: components['schemas']['com.moego.server.message.web.vo.MessageOptoutConfigVO'];
      };
      'com.moego.server.message.web.model.ListMessageOptoutCustomerResponse': {
        customers: components['schemas']['com.moego.server.customer.dto.CustomerInfoDto'][];
        /** Format: int32 */
        total: number;
      };
      'com.moego.server.message.web.MoeBusinessMessageReviewBoosterController$LandingPageConfigClientReviewVO': {
        /** Format: int32 */
        customerId: number;
        customerName: string;
        /** Format: int32 */
        petId: number;
        /** Format: int32 */
        petTypeId: number;
        reviewContent: string;
        /** Format: int32 */
        reviewId: number;
        /** Format: date-time */
        reviewTime: string;
        /** Format: int32 */
        score: number;
        showcasePhotoUrl: string;
      };
      'com.moego.server.message.web.params.BatchSendReceiptParams': {
        /** Format: int64 */
        businessId: string | number;
        /**
         * Format: int32
         * @description 当选择发送message 时，需要用contactId获取发送号码
         */
        contactId?: number;
        /** Format: int64 */
        customerId: string | number;
        invoiceIds: (string | number)[];
        /**
         * Format: int32
         * @description message method
         */
        messageMethod: number;
        /** @description email for recipient */
        recipientEmail?: string;
      };
      'com.moego.server.message.web.params.CompanyIdParams': {
        /** Format: int32 */
        companyId: number;
      };
      'com.moego.server.message.web.params.FileInfoGroupParams': {
        fileInfoList: components['schemas']['com.moego.server.message.web.dto.ob.FileInfoDto'][];
      };
      'com.moego.server.message.web.params.MigrateNotificationTypeParams': {
        /** Format: int32 */
        endID: number;
        /** Format: int32 */
        startID: number;
      };
      'com.moego.server.message.web.params.SendInvoiceReceiptParams': {
        /**
         * Format: int32
         * @description invoice/order id
         */
        orderId: number;
        /**
         * Format: int32
         * @deprecated
         * @description 前端忽略即可
         */
        recipientCustomerId?: number;
        /** @description email for recipient */
        recipientEmail: string;
        /** @description invoice/order type: appointment, noshow, product, package */
        type: string;
      };
      'com.moego.server.message.web.params.SendMMSParams': {
        customer: components['schemas']['com.moego.server.message.params.SendMessageCustomerParams'];
        /** @description 分组后的 mms 发送列表，会消耗 list.size 条，如果不符合size 或 count，则抛出异常 */
        fileInfoGroupList?: components['schemas']['com.moego.server.message.web.dto.ob.FileInfoDto'][][];
        /** @description 未分组的发送列表，可能消耗 1 条，也可能消耗 n 条，发送时会转化为 group */
        fileInfoList?: components['schemas']['com.moego.server.message.web.dto.ob.FileInfoDto'][];
      };
      'com.moego.server.message.web.params.SendVerifyCodeParams': {
        /** @description Online booking name, url business name */
        businessName?: string;
        /** @description Phone number */
        phoneNumber: string;
      };
      'com.moego.server.message.web.params.UpdateMessageOptoutConfigParam': {
        /** @description 对齐 MessageDetailEnum 的 MESSAGE_METHOD */
        method: string;
        optinContent: string;
        optinContentPlaceholder: string;
        optinKeyword: string[];
        optinReplyContent: string;
        optoutContent: string;
        optoutContentPlaceholder: string;
        optoutKeyword: string[];
        optoutReplyContent: string;
        /** @description 对齐 MessageDetailEnum 的 MESSAGE_SOURCE */
        source: string;
      };
      'com.moego.server.message.web.params.UpdateMessageOptoutCustomerParam': {
        customerIDs: (string | number)[];
        method: string;
        source: string;
        /** Format: int32 */
        status: number;
      };
      'com.moego.server.message.web.vo.A2pRecordUpdateVo': {
        address1?: string;
        address2?: string;
        businessName?: string;
        businessType?: string;
        city?: string;
        /** Format: int32 */
        companyId: number;
        ein?: string;
        einFiles?: string[];
        email: string;
        firstName: string;
        iosCountry?: string;
        lastName: string;
        lat?: string;
        lng?: string;
        phoneNumber: string;
        region?: string;
        website?: string;
        withEin: boolean;
        zipcode?: string;
      };
      'com.moego.server.message.web.vo.ManualSendReminderVo': {
        /** Format: int32 */
        groomingId: number;
        /**
         * Format: int32
         * @description 需要发送的类型，目前支持 1 unconfirm first、2 unconfirm second、7 appointment reminder
         */
        reminderType: number;
      };
      'com.moego.server.message.web.vo.MessageOptoutConfigVO': {
        /** Format: int32 */
        companyId: number;
        /** Format: int32 */
        id: number;
        method: string;
        optinContent: string;
        optinContentPlaceholder: string;
        optinKeyword: string[];
        optinReplyContent: string;
        optoutContent: string;
        optoutContentPlaceholder: string;
        optoutKeyword: string[];
        optoutReplyContent: string;
        source: string;
      };
      'com.moego.server.message.web.vo.MessageTemplateVO': {
        content: string;
        type: string;
      };
      'com.moego.server.message.web.vo.MoeBusinessMessageDetailVO': {
        /** Format: int32 */
        businessId: number;
        /** @description 客户头像 */
        clientAvatar: string;
        /** @description 客户头像颜色 */
        clientColor: string;
        /** Format: int64 */
        companyId: string | number;
        contactName: string;
        /** Format: int32 */
        createTime: number;
        /** Format: int32 */
        customerId: number;
        /** Format: int32 */
        customerIsRead: number;
        /** Format: int32 */
        deleteBy: number;
        /** Format: int32 */
        deleteTime: number;
        /** Format: int32 */
        errorCode: number;
        errorMessage: string;
        extraInfo: components['schemas']['com.moego.server.message.dto.MessageExtraInfo'];
        groomingCustomerInfoDTO: components['schemas']['com.moego.server.customer.dto.GroomingCustomerInfoDTO'];
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        isRead: number;
        /** Format: int32 */
        isSuccessed: number;
        mediaUrl1: string;
        messageReceiverDTO: components['schemas']['com.moego.server.customer.dto.CustomerContactDto'];
        messageSid: string;
        messageText: string;
        /** Format: int32 */
        messageType: number;
        /** Format: int32 */
        method: number;
        mimeType: string;
        moeStaffDto: components['schemas']['com.moego.server.message.web.vo.MoeBusinessMessageDetailVO$MoeStaffDto'];
        /** Format: int32 */
        numSegments: number;
        phoneNumber: string;
        /** Format: int32 */
        readTime: number;
        /** Format: int32 */
        source: number;
        /** Format: int32 */
        staffId: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        targetId: number;
        /** Format: int32 */
        targetType: number;
        /** Format: int32 */
        type: number;
        /** Format: int32 */
        unconfirmApptid: number;
      };
      'com.moego.server.message.web.vo.MoeBusinessMessageDetailVO$MoeStaffDto': {
        /** Format: int32 */
        accountId: number;
        /** Format: int32 */
        addonPayRate: number;
        /** Format: int32 */
        allowLogin: number;
        avatarPath: string;
        /** Format: int32 */
        bookOnlineAvailable: number;
        /** Format: int32 */
        businessId: number;
        /** Format: int64 */
        companyId: string | number;
        /** Format: int32 */
        createById: number;
        /** Format: int32 */
        employeeCategory: number;
        /** Format: int64 */
        fireDate: string | number;
        firstName: string;
        /** Format: int32 */
        groupLeaderId: number;
        /** Format: int64 */
        hireDate: string | number;
        hourlyPayRate: number;
        /** Format: int32 */
        id: number;
        /** Format: int32 */
        inactive: number;
        isWorkingStaff: boolean;
        lastName: string;
        note: string;
        /** Format: int32 */
        payBy: number;
        /** Format: int32 */
        roleId: number;
        /** Format: int32 */
        servicePayRate: number;
        /** Format: int32 */
        showCalendarStaffAll: number;
        /** Format: int32 */
        showOnCalendar: number;
        /** Format: int32 */
        sort: number;
        /** Format: int32 */
        status: number;
        /** Format: int32 */
        tipsPayRate: number;
        /** Format: int64 */
        vanId: string | number;
        vanName: string;
      };
      'com.moego.server.message.web.vo.MoeContactMessageVo': {
        content: string;
        email: string;
        name: string;
      };
      'com.moego.server.message.web.vo.PhoneNumberVo': {
        phoneNumber: string;
      };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
  }
}
export enum ComGoogleFirebaseMessagingFirebaseMessagingExceptionErrorCode {
  INVALID_ARGUMENT = 'INVALID_ARGUMENT',
  FAILED_PRECONDITION = 'FAILED_PRECONDITION',
  OUT_OF_RANGE = 'OUT_OF_RANGE',
  UNAUTHENTICATED = 'UNAUTHENTICATED',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  ABORTED = 'ABORTED',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  RESOURCE_EXHAUSTED = 'RESOURCE_EXHAUSTED',
  CANCELLED = 'CANCELLED',
  DATA_LOSS = 'DATA_LOSS',
  UNKNOWN = 'UNKNOWN',
  INTERNAL = 'INTERNAL',
  UNAVAILABLE = 'UNAVAILABLE',
  DEADLINE_EXCEEDED = 'DEADLINE_EXCEEDED',
}
export enum ComGoogleFirebaseMessagingFirebaseMessagingExceptionMessagingErrorCode {
  THIRD_PARTY_AUTH_ERROR = 'THIRD_PARTY_AUTH_ERROR',
  INVALID_ARGUMENT = 'INVALID_ARGUMENT',
  INTERNAL = 'INTERNAL',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  SENDER_ID_MISMATCH = 'SENDER_ID_MISMATCH',
  UNAVAILABLE = 'UNAVAILABLE',
  UNREGISTERED = 'UNREGISTERED',
}
export enum ComMoegoCommonParamsFilterParamsOperator {
  OPERATOR_EQUAL = 'OPERATOR_EQUAL',
  OPERATOR_NOT_EQUAL = 'OPERATOR_NOT_EQUAL',
  OPERATOR_IN = 'OPERATOR_IN',
  OPERATOR_NOT_IN = 'OPERATOR_NOT_IN',
  OPERATOR_LIKE_MULTI = 'OPERATOR_LIKE_MULTI',
  OPERATOR_LIKE = 'OPERATOR_LIKE',
  OPERATOR_NOT_LIKE = 'OPERATOR_NOT_LIKE',
  OPERATOR_PREFIX_LIKE = 'OPERATOR_PREFIX_LIKE',
  OPERATOR_SUFFIX_LIKE = 'OPERATOR_SUFFIX_LIKE',
  OPERATOR_GREATER_THAN = 'OPERATOR_GREATER_THAN',
  OPERATOR_LESS_THAN = 'OPERATOR_LESS_THAN',
  OPERATOR_GREATER_THAN_OR_EQUAL = 'OPERATOR_GREATER_THAN_OR_EQUAL',
  OPERATOR_LESS_THAN_OR_EQUAL = 'OPERATOR_LESS_THAN_OR_EQUAL',
  OPERATOR_AFTER = 'OPERATOR_AFTER',
  OPERATOR_BEFORE = 'OPERATOR_BEFORE',
  OPERATOR_ON = 'OPERATOR_ON',
  OPERATOR_RANGE = 'OPERATOR_RANGE',
}
export enum ComMoegoCommonParamsFilterParamsProperty {
  client_id = 'client_id',
  client_status = 'client_status',
  client_type = 'client_type',
  first_name = 'first_name',
  last_name = 'last_name',
  client_created_from = 'client_created_from',
  referral_source = 'referral_source',
  preferred_frequency_day = 'preferred_frequency_day',
  preferred_groomer = 'preferred_groomer',
  preferred_weekday = 'preferred_weekday',
  unsubscribed_marketing_emails = 'unsubscribed_marketing_emails',
  primary_email = 'primary_email',
  preferred_business = 'preferred_business',
  has_account = 'has_account',
  account_id = 'account_id',
  creation_date = 'creation_date',
  customer_type = 'customer_type',
  allocate_staff_id = 'allocate_staff_id',
  life_cycle_id = 'life_cycle_id',
  action_state_id = 'action_state_id',
  blocked_from_message = 'blocked_from_message',
  blocked_from_ob = 'blocked_from_ob',
  inactive_client = 'inactive_client',
  lapsed_client = 'lapsed_client',
  blocked_from_ob_selected_service = 'blocked_from_ob_selected_service',
  address_cnt = 'address_cnt',
  zipcode = 'zipcode',
  service_areas = 'service_areas',
  phone_number = 'phone_number',
  email = 'email',
  email_cnt = 'email_cnt',
  client_tag = 'client_tag',
  new_client = 'new_client',
  recurring_client = 'recurring_client',
  waitlist_client = 'waitlist_client',
  review_rating = 'review_rating',
  review_cnt = 'review_cnt',
  pet_code = 'pet_code',
  pet_name = 'pet_name',
  pet_type = 'pet_type',
  pet_breed = 'pet_breed',
  pet_weight = 'pet_weight',
  pet_size = 'pet_size',
  hair_length = 'hair_length',
  pet_cnt = 'pet_cnt',
  expired_vaccine_cnt = 'expired_vaccine_cnt',
  vaccine_cnt = 'vaccine_cnt',
  pet_vaccine = 'pet_vaccine',
  first_appt_date = 'first_appt_date',
  upcoming_appt_date = 'upcoming_appt_date',
  last_appt_date = 'last_appt_date',
  next_appt_date = 'next_appt_date',
  expected_service_date = 'expected_service_date',
  total_appt_cnt = 'total_appt_cnt',
  total_appt_ob_requests_cnt = 'total_appt_ob_requests_cnt',
  client_total_appt_cnt = 'client_total_appt_cnt',
  finished_appt_cnt = 'finished_appt_cnt',
  upcoming_appt_cnt = 'upcoming_appt_cnt',
  no_show_appt_cnt = 'no_show_appt_cnt',
  cancelled_appt_cnt = 'cancelled_appt_cnt',
  waitlist_appt_cnt = 'waitlist_appt_cnt',
  unpaid_invoice_cnt = 'unpaid_invoice_cnt',
  total_paid = 'total_paid',
  overdue = 'overdue',
  last_appt_groomer = 'last_appt_groomer',
  has_cof = 'has_cof',
  cof_cnt = 'cof_cnt',
  cof_status = 'cof_status',
  cof_request = 'cof_request',
  cof_request_date = 'cof_request_date',
  lead_type = 'lead_type',
  abandoned_step = 'abandoned_step',
  abandoned_status = 'abandoned_status',
  abandoned_date = 'abandoned_date',
  last_contact_time = 'last_contact_time',
  performance_history = 'performance_history',
  membership = 'membership',
  membership_status = 'membership_status',
}
export enum ComMoegoCommonParamsFilterParamsType {
  TYPE_AND = 'TYPE_AND',
  TYPE_OR = 'TYPE_OR',
}
