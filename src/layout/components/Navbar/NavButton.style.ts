import styled from 'styled-components';

/**
 * Nav Primary Button
 * TODO(gxy): to be organized in the future
 */
export const NavPrimaryButton = styled.button`
  color: #fff;
  padding: 5px 20px;
  border: 1px solid #f96b18;
  background: #f96b18;
  border-radius: 56px;
  font-weight: bold;
  font-size: 14px;
  line-height: 20px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  &:hover,
  &:active {
    background: #de5a21;
    border-color: #de5a21;
  }

  &.disabled,
  &[disabled] {
    cursor: not-allowed;
    opacity: 0.7;
    box-shadow: none;

    > * {
      pointer-events: none;
    }
  }
`;

export const NavTextButton = styled(NavPrimaryButton)`
  padding: 3px 10px;
  border-color: transparent;
  background-color: transparent;
  color: #333333;
  &:hover,
  &:active {
    background-color: #f4f5f9;
    border-color: #f4f5f9;
  }
  > .icon {
    margin-right: 4px;
  }
`;

export const IconButtonView = styled.button`
  border: none;
  background-color: transparent;
  padding: 4px;
  border-radius: 16px;
  color: #333;
  cursor: pointer;
  &:hover {
    color: #f96b18;
    fill: #f96b18;
  }
  &:active {
    color: #f96b18;
    fill: #f96b18;
    background: #fff7f0;
  }
`;
