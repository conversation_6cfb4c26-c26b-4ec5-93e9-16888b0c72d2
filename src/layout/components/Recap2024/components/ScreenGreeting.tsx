import { SvgIcon } from '@moego/business-components';
import { Condition } from '@moego/finance-utils';
import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { memo } from 'react';
import Image2024AnnualGreetingPng from '../../../../assets/image/2024-annual-greeting.png';
import SvgIconArrowRotateSvg from '../../../../assets/svg/icon-arrow-rotate.svg';
import { companyMapBox, currentCompanyIdBox } from '../../../../store/company/company.boxes';
import { selectCurrentStaff } from '../../../../store/staff/staff.selectors';
import { ScreenType, defaultBgColor } from '../Recap2024.utils';
import { type SummaryData } from '../use2024Summary';

export interface ScreenGreetingProps {
  className?: string;
  isBiz: boolean;
  summaryData: SummaryData;
  onChange2Biz?: () => void;
  onChange2Staff?: () => void;
}

export const ScreenGreeting = memo<ScreenGreetingProps>(function ScreenGreeting(props) {
  const { className, isBiz, onChange2Biz, onChange2Staff } = props;

  const [staff, companyMap, currentCompanyId] = useSelector(selectCurrentStaff(), companyMapBox, currentCompanyIdBox);
  const isOwner = staff.isOwner();
  const companyName = companyMap.mustGetItem(currentCompanyId).companyName;
  const staffName = staff.fullName();

  return (
    <div
      className={classNames(className, 'moe-pt-[40px] moe-flex moe-flex-col moe-h-full moe-select-none')}
      style={{ backgroundColor: defaultBgColor[ScreenType.greeting] }}
    >
      <div className="moe-text-[20px] moe-leading-[20px] moe-text-[#202020] moe-text-center">
        👋 {isBiz ? companyName : staffName}
      </div>
      <Condition if={isOwner}>
        <div className="moe-text-center">
          <div
            className="moe-inline-flex moe-flex-col moe-items-center moe-mt-[8px] moe-cursor-pointer"
            onClick={isBiz ? onChange2Staff : onChange2Biz}
          >
            <div className="moe-text-[#fff] moe-text-center">
              <SvgIcon className="moe-mr-[4px]" src={SvgIconArrowRotateSvg} size={16} color="#fff" />
              <span className="moe-text-[16px] moe-leading-[16px] moe-font-semibold">switch to</span>
            </div>
            <div className="moe-mt-[4px] moe-text-[16px] moe-leading-[16px] moe-font-semibold moe-text-[#202020] moe-text-center moe-underline">
              @{isBiz ? staffName : companyName}
            </div>
          </div>
        </div>
      </Condition>
      <div
        className="moe-mt-[20px] moe-flex-1 moe-bg-cover moe-bg-no-repeat moe-bg-top"
        style={{ backgroundImage: `url(${Image2024AnnualGreetingPng})` }}
      />
    </div>
  );
});
