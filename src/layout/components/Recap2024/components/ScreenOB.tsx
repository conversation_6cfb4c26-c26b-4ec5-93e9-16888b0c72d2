import { Condition } from '@moego/finance-utils';
import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { memo } from 'react';
import Image2024AnnualObPng from '../../../../assets/image/2024-annual-ob.png';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentCompany } from '../../../../store/company/company.selectors';
import { useImgAspectRatioStyle } from '../../../../utils/hooks/useImgAspectRatioStyle';
import { ScreenType, defaultBgColor } from '../Recap2024.utils';
import { type SummaryData } from '../use2024Summary';
import { LogoWhite } from './LogoWhite';
import { OBHome } from './OBHome';
import { PrettyNum } from './PrettyNum';

export interface ScreenOBProps {
  className?: string;
  summaryData: SummaryData;
}

export const ScreenOB = memo<ScreenOBProps>(function ScreenOB(props) {
  const { className, summaryData } = props;
  const { onlineBookingSummary } = summaryData;
  const [business, company] = useSelector(selectCurrentBusiness(), selectCurrentCompany());
  const imgStyle = useImgAspectRatioStyle(318, 694 / 558);
  const amountRevenue = onlineBookingSummary?.recoveredRevenue ?? 0;
  const abandonedBookingsRecovered = onlineBookingSummary?.abandonedBookingsRecovered ?? 0;
  const revenueStr = business.formatAmount(amountRevenue);

  return (
    <div
      className={classNames(className, 'moe-pt-[20px] moe-flex moe-flex-col moe-h-full moe-select-none')}
      style={{ backgroundColor: defaultBgColor[ScreenType.ob] }}
    >
      <div
        className="moe-flex moe-justify-end moe-items-start moe-self-end moe-bg-right moe-bg-contain moe-bg-no-repeat"
        style={{ backgroundImage: `url(${Image2024AnnualObPng})`, ...imgStyle }}
      >
        <OBHome className="moe-w-[214px] moe-pt-[105px] moe-pl-[10px] moe-rotate-[7.5deg]" />
      </div>
      <div className="moe-mx-[28px] moe-text-[32px] moe-leading-[32px] moe-text-[#fff] moe-font-bold">
        You’re a digital natural!
      </div>
      <div className="moe-mt-[18px] moe-mx-[28px] moe-text-[18px] moe-leading-[24px] moe-text-[#fff] moe-font-normal">
        You received
      </div>
      <PrettyNum className="moe-mt-[12px] moe-mx-[28px]" value={onlineBookingSummary?.obRequestCnt ?? 0} />
      <div className="moe-mt-[10px] moe-mx-[28px] moe-text-[20px] moe-leading-[28px] moe-text-[#fff] moe-font-bold">
        online booking requests
      </div>
      <Condition if={company.isGrowthPlus && abandonedBookingsRecovered > 0}>
        <div className="moe-mt-[12px] moe-mx-[28px] moe-text-[18px] moe-leading-[24px] moe-text-[#fff] moe-font-normal">
          With the Abandoned Booking List, you also recovered{' '}
          <span className="moe-bg-[#202020] moe-px-[2px]">{abandonedBookingsRecovered}</span> bookings and&nbsp;
          <span className="moe-bg-[#202020] moe-px-[2px]">{revenueStr}</span> in revenue. Great job!
        </div>
      </Condition>
      <LogoWhite className="moe-pb-[20px]" />
    </div>
  );
});
