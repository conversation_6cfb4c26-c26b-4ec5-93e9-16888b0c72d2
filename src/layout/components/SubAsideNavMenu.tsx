import { MinorChevronDownOutlined, MinorChevronUpOutlined } from '@moego/icons-react';
import { cn } from '@moego/ui';
import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import SvgPricingIconCrownSvg from '../../assets/svg/pricing-icon-crown.svg';
import { CollapseSimple } from '../../components/Collapse/CollapseSimple';
import { FixedWrapper } from '../../components/Collapse/CollapseSimple.style';
import { Condition } from '../../components/Condition';
import { SvgIcon } from '../../components/Icon/Icon';
import { WithPricingEnableUpgradeCondition } from '../../components/Pricing/WithPricingComponents';
import { Switch } from '../../components/SwitchCase';
import { hasPermissions } from '../../components/WithFeature/useNewAccountStructure';
import { type SettingsLeftNavItem } from '../../container/settings/Settings/types';
import { selectCurrentPermissions } from '../../store/business/role.selectors';
import { selectPricingPermission } from '../../store/company/company.selectors';
import { memo } from '../../utils/react';

export interface SubAsideNavMenuProps<T> {
  navList: SettingsLeftNavItem<T>[];
  className?: string;
  activeId: T;
  onClick: (
    item: SettingsLeftNavItem<T>,
    options?: {
      parent: SettingsLeftNavItem<T>;
    },
  ) => void;
}

interface SubAsideNavMenuItemProps<T> {
  isActive: boolean;
  item: SettingsLeftNavItem<T>;
  onClick: (item: SettingsLeftNavItem<T>) => void;
}

const StyledCollapseSimple = styled(CollapseSimple)`
  position: relative;
  &:last-of-type ::after {
    display: none;
  }
  ${FixedWrapper} {
    flex: 1;
    justify-content: space-between;
  }
  .collapse-title {
    font-size: 14px;
    color: #333;
    line-height: 18px;
    font-weight: 700;
  }
  .ant-collapse .ant-collapse-item .ant-collapse-content-box {
    padding-top: 8px;
  }
`;

const SubAsideNavMenuItem = memo(function LeftNavItem<T>(props: SubAsideNavMenuItemProps<T>) {
  const { item, onClick, isActive } = props;
  return (
    <div
      className={classNames(
        'moe-text-[14px] moe-font-regular moe-leading-[20px] moe-cursor-pointer moe-pl-[16px] moe-relative moe-ml-[-1px]',
        {
          'moe-text-tertiary': !isActive,
          'moe-text-brand': isActive,
        },
      )}
      onClick={() => onClick(item)}
    >
      {item.title}
      <Condition if={isActive}>
        <div className="moe-bg-brand-bold moe-w-[2px] moe-h-[20px] moe-rounded-[2px] moe-absolute moe-left-0 moe-top-0"></div>
      </Condition>
    </div>
  );
});

/**
 * The secondary menu navigation placed on the left side of the main page.
 */
export const SubAsideNavMenu = memo(function <T>(props: SubAsideNavMenuProps<T>) {
  const { className, navList, onClick, activeId } = props;
  const [permissions, pricingPermissions] = useSelector(selectCurrentPermissions(), selectPricingPermission());
  // 可以展开的节点
  const expandableNodeMap = useMemo(() => {
    return navList.reduce((map, item) => (item.childList ? map.set(item.id, true) : map), new Map<T, boolean>());
  }, [navList]);

  const [expandedNodeMap, setExpandedNodeMap] = useState(expandableNodeMap);

  useEffect(() => {
    setExpandedNodeMap(expandableNodeMap);
  }, [navList]);

  const handleToggleExpand = (item: SettingsLeftNavItem<T>) => {
    if (!item.childList || item.childList.length === 0) {
      onClick(item);
      return;
    }
    const newExpandedNodeMap = new Map(expandedNodeMap);
    newExpandedNodeMap.set(item.id, !newExpandedNodeMap.get(item.id));
    setExpandedNodeMap(newExpandedNodeMap);
  };

  return (
    // 需要指定 sticky 和 高度才能吸顶；同时 leftNav 不能高过父容器
    <div className={cn('moe-flex moe-flex-col moe-sticky moe-top-[-22px] moe-w-[216px] moe-flex-shrink-0', className)}>
      {navList.map((item, index) => {
        const isExpand = expandedNodeMap.get(item.id) || item.id === activeId;
        const hasChild = item.childList && item.childList.length > 0;
        const isChildActive = hasChild && item.childList?.some((childItem) => childItem.id === activeId);

        // 用于控制是否展示皇冠：如果没传值，说明不需要权限控制；如果传值，则需要判断权限
        const hasCurrentPricingPermission = item?.pricingPermission
          ? pricingPermissions.enable.has(item.pricingPermission)
          : true;
        const crown = (
          <Condition if={!hasCurrentPricingPermission}>
            <SvgIcon src={SvgPricingIconCrownSvg} size={14} color={'#FFD029'} className="moe-ml-[4px]" />
          </Condition>
        );

        return (
          <React.Fragment key={`${item.id}_${index}`}>
            <Condition if={hasPermissions(item.permissions, permissions) && !item.hidden}>
              <div className={'moe-mb-[4px] moe-overflow-hidden moe-font-manrope'} key={`${item.id}_${index}`}>
                <Condition if={hasChild}>
                  <StyledCollapseSimple
                    defaultExpand={true}
                    expand={isExpand}
                    customHeader={() => (
                      <WithPricingEnableUpgradeCondition permission={item?.pricingPermission}>
                        {(onCapture) => (
                          <div
                            className={cn(
                              'moe-py-[8px] moe-px-[16px] moe-text-[14px] moe-leading-[20px] moe-cursor-pointer moe-flex moe-items-center moe-gap-x-[4px]',
                              {
                                'moe-text-tertiary moe-font-normal': !isChildActive,
                                'moe-text-brand moe-font-bold': isChildActive,
                              },
                            )}
                            onClick={() => {
                              onCapture ? onCapture() : handleToggleExpand(item);
                            }}
                          >
                            {item.title}
                            {crown}
                            <Switch>
                              <Switch.Case if={hasChild && isExpand}>
                                <MinorChevronUpOutlined />
                              </Switch.Case>
                              <Switch.Case else>
                                <MinorChevronDownOutlined />
                              </Switch.Case>
                            </Switch>
                          </div>
                        )}
                      </WithPricingEnableUpgradeCondition>
                    )}
                  >
                    <div className="moe-pl-[16px] moe-overflow-hidden">
                      <div className="moe-border-0 moe-border-solid moe-border-l moe-border-l-[#E6E6E6] moe-gap-y-[16px] moe-flex moe-flex-col">
                        {/* TODO(dori): 看下二级菜单是否需要 pricing permission 控制 */}
                        {item.childList
                          ?.filter((item) => !item.hidden)
                          .map((childItem, index) => {
                            return (
                              <SubAsideNavMenuItem
                                item={childItem}
                                onClick={(childItem) =>
                                  onClick(childItem, {
                                    parent: item,
                                  })
                                }
                                isActive={childItem.id === activeId}
                                key={`${childItem.id}_${index}`}
                              />
                            );
                          })}
                      </div>
                    </div>
                  </StyledCollapseSimple>
                </Condition>
                <Condition if={!hasChild}>
                  <WithPricingEnableUpgradeCondition permission={item?.pricingPermission}>
                    {(onCapture) => (
                      <div
                        className={classNames(
                          'moe-py-[8px] moe-px-[16px] moe-text-[14px]  moe-leading-[20px] moe-cursor-pointer',
                          {
                            'moe-text-tertiary moe-font-regular': !isExpand,
                            'moe-text-brand moe-font-bold': isExpand,
                          },
                        )}
                        onClick={() => {
                          onCapture ? onCapture() : onClick(item);
                        }}
                      >
                        {item.title}
                        {crown}
                      </div>
                    )}
                  </WithPricingEnableUpgradeCondition>
                </Condition>
              </div>
            </Condition>
          </React.Fragment>
        );
      })}
    </div>
  );
});
