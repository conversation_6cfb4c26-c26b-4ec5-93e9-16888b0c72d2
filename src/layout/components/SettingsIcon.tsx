import React from 'react';
import SvgNavSettingsSvg from '../../assets/svg/nav-settings.svg';
import { useQuickBookNewFeature } from '../../container/settings/Settings/IntegrationSetting/QuickBookSync/hooks/useQuickBookNewFeature';
import { LeftNavIcon } from './LeftNavIcon';

export const SettingsIcon = () => {
  const [isNewQuickBook] = useQuickBookNewFeature();
  const hasNew = isNewQuickBook;
  return <LeftNavIcon src={SvgNavSettingsSvg} dotVisible={hasNew} />;
};
