import React from 'react';
import IconNotificationLandingSvg from '../../../../../assets/icon/notification-landing.svg';
import { PATH_FINANCE_CAPITAL } from '../../../../../router/paths';
import { switchBusiness } from '../../../../../store/business/business.actions';
import {
  type ActivityNotificationType,
  MoeGoNotification,
  type NotificationRecord,
  type NotificationType,
} from '../../../../../store/notification/notification.boxes';
import { isNormal } from '../../../../../store/utils/identifier';
import { notificationPopoverVisibleBox } from '../../Notification.boxes';
import { BaseNotificationItemRenderer, type INotificationRendererMeta } from '../base';

export class FinanceCapitalOfferCreated<PERSON>enderer extends BaseNotificationItemRenderer<ActivityNotificationType.FINANCE_CAPITAL_OFFER_CREATED> {
  match(item: NotificationRecord<NotificationType>) {
    return item.is(MoeGoNotification.FINANCE_CAPITAL_OFFER_CREATED);
  }
  render(meta: INotificationRendererMeta<ActivityNotificationType.FINANCE_CAPITAL_OFFER_CREATED>) {
    const { item, dispatch, business, history } = meta;
    return {
      icon: <img src={IconNotificationLandingSvg} />,
      title: 'Your pre-qualify offer has been approved!',
      body: (
        <div>
          Congratulations! You received an offer of {business.formatAmount(item.extra.amount)}. Come to finalize the
          offer details now to access your exclusive funds!
        </div>
      ),
      onClick: async () => {
        if (isNormal(item.businessId)) {
          await dispatch(switchBusiness(Number(item.businessId)));
        }
        dispatch([
          notificationPopoverVisibleBox.setState({
            dropdownVisible: false,
            selectVisible: false,
          }),
        ]);
        history.push(PATH_FINANCE_CAPITAL.build());
      },
    };
  }
}
