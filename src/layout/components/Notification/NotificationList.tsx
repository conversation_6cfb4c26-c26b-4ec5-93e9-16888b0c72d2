/*
 * @since 2020-11-19 14:52:03
 * <AUTHOR> <<EMAIL>>
 */

import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useRef } from 'react';
import { Loading } from '../../../components/Loading/Loading';
import { MarkAllAsRead } from '../../../components/MarkAllAsRead/MarkAllAsRead';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { currentCompanyIdBox } from '../../../store/company/company.boxes';
import { getNotificationList, readAllNotification } from '../../../store/notification/notification.actions';
import { type NotificationListTab } from '../../../store/notification/notification.boxes';
import { selectNotifications } from '../../../store/notification/notification.selectors';
import { type LoadMoreMode } from '../../../store/utils/PagedLoadMore';
import { isNormal } from '../../../store/utils/identifier';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { NotificationItem } from './NotificationItem';
import { NotificationListView } from './NotificationList.style';

export interface NotificationListProps {
  className?: string;
  tab: NotificationListTab;
  visible: boolean;
  locationId: string;
}

export const NotificationList = memo<NotificationListProps>(({ className, tab, visible, locationId }) => {
  const [notifications, businessId, companyId] = useSelector(
    selectNotifications(tab),
    currentBusinessIdBox,
    currentCompanyIdBox,
  );

  const listRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const hasError = useRef(false);
  const loadList = useSerialCallback((loadMode: LoadMoreMode) => {
    return dispatch(getNotificationList({ tab, loadMode, locationId })).catch(() => (hasError.current = true));
  });

  const loadMore = () => {
    if (
      isNormal(businessId) &&
      visible &&
      !loadList.isBusy() &&
      notifications.hasMore &&
      listRef.current &&
      listRef.current.scrollHeight < listRef.current.clientHeight + listRef.current.scrollTop + 20 &&
      !hasError.current
    ) {
      loadList('APPEND');
    } else if (hasError.current) {
      hasError.current = false;
    }
  };
  useEffect(() => {
    if (isNormal(companyId) && visible) {
      loadList('RESET');
    }
  }, [businessId, companyId, locationId, visible]);

  useEffect(() => {
    loadMore();
  }, [visible, notifications.data.size, loadList.isBusy(), businessId, companyId]);

  const handleReadAll = useSerialCallback(async () => {
    await dispatch(readAllNotification({ tab, locationId }));
  });
  useEffect(() => {
    listRef.current!.scrollTop = 0;
  }, [notifications.data.first()]);
  return (
    <NotificationListView className={className}>
      <div className="notification-list" ref={listRef} onScroll={loadMore}>
        {notifications.data.map((id) => (
          <NotificationItem id={id} tab={tab} key={id} />
        ))}
        {loadList.isBusy() && <Loading loading={true} height={40} className="!moe-my-l" />}
        {!notifications.hasMore && !loadList.isBusy() && <div className="no-more">No more notifications</div>}
      </div>
      <div className="notification-footer">
        <MarkAllAsRead onClick={handleReadAll} svgSize={24} />
      </div>
    </NotificationListView>
  );
});
