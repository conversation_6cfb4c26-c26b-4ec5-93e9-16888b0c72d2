import { SvgIcon } from '@moego/business-components';
import React from 'react';
import { default as ReferralIconCredits } from '../../../assets/svg/icon-referral-credits.svg';
import { default as ReferralIconDiscount } from '../../../assets/svg/icon-referral-discount.svg';
import { default as ReferralIconRewards } from '../../../assets/svg/icon-referral-rewards.svg';
import { ReferralDescriptionContentView } from './ReferralDescriptionContent.style';

interface Props {}

export function ReferralDescriptionContent({}: Props) {
  return (
    <ReferralDescriptionContentView>
      <h1>How does it work?</h1>
      <p className="title">What does my friend get?</p>
      <Item
        item={[
          '30% off their first month',
          'when your friends subscribe to one of our premium plans',
          ReferralIconDiscount,
        ]}
        color="#F96B18"
      />
      <p className="title">What do I get?</p>
      {descriptions.map((i) => (
        <Item item={i} key={i[0]} color="#F96B18" gap />
      ))}
      <p className="tips">* Access the referral center in MoeGo mobile app to learn more</p>
      <div className="policy">
        <a href="/referral-policy.html" target="_blank">
          Terms & Policy
        </a>
      </div>
    </ReferralDescriptionContentView>
  );
}

const descriptions = [
  ['$25 reward credits', 'when your friend is on board', ReferralIconCredits],
  ['More bonus rewards', 'when your friend stays for 3 months', ReferralIconRewards],
];

interface ItemProps {
  item: (typeof descriptions)[number];
  color: string;
  gap?: boolean;
}

function Item({ item: [title, description, icon], color, gap }: ItemProps) {
  return (
    <div className="item" style={gap ? { marginBottom: '8px' } : undefined}>
      <SvgIcon src={icon} color={color} />
      <div>
        <p className="item-title">{title}</p>
        <p className="item-description">{description}</p>
      </div>
    </div>
  );
}
