import { isString } from 'lodash';
import React, { memo } from 'react';

export interface TextProps {
  className?: string;
  children: React.ReactNode;
}

/**
 * 入参是字符串则渲染为 div 文本，否则直接渲染 children
 *
 * 不过多数情况下，建议直接使用 @moego/ui 自带的 Text 组件
 */
export const TextOrChildren = memo(function Text(props: TextProps) {
  const { className = '', children } = props;
  return isString(children) ? <div className={className}>{children}</div> : <>{children}</>;
});
