import {
  type FetchQueryOptions,
  queryOptions,
  type UseInfiniteQueryOptions,
  useInfiniteQuery,
  useQuery,
  type UseQueryOptions,
  useSuspenseQuery,
  type UseSuspenseQueryOptions,
  type QueryFunctionContext,
} from '@tanstack/react-query';
import { type PickRequired } from '../../types/advanced';
import { queryClient } from '../queryClient';

/**
 * `queryKey` 和 `queryFn` 支持定义 params 参数，其他参数保持不变
 *
 * `createQueryOptions` 和直接 `(params) => queryOptions()` 的另一个主要区别在于能便利的创建一个稳定引用的 `select` 函数
 *
 * The select function will only re-run if:
 * - the select function itself changed referentially
 * - data changed
 * @see https://tanstack.com/query/latest/docs/framework/react/guides/render-optimizations#select
 */
export function createQueryOptions<
  TQueryFnData = unknown,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends readonly unknown[] = readonly unknown[],
  TParams = unknown,
>(
  config: Omit<ReturnType<typeof queryOptions<TQueryFnData, TError, TData, TQueryKey>>, 'queryFn' | 'queryKey'> & {
    queryKey: (params: TParams) => TQueryKey;
    queryFn: (params: TParams, ctx: QueryFunctionContext<TQueryKey, any>) => Promise<TQueryFnData>;
  },
) {
  const { queryKey, queryFn, ...rest } = config;
  const getOptions = (params: TParams) => {
    const options = queryOptions<TQueryFnData, TError, TData, TQueryKey>({
      ...rest,
      queryFn: (ctx) => queryFn(params, ctx),
      queryKey: queryKey(params),
    });

    type Options = PickRequired<typeof options, 'queryKey' | 'queryFn'>;

    return options as typeof config.select extends undefined
      ? Omit<Options, 'select'>
      : PickRequired<Options, 'select'>;
  };

  const invalidateQueries = (params: TParams) => queryClient.invalidateQueries(getOptions(params));

  const resetQueries = (params: TParams) => queryClient.resetQueries(getOptions(params));

  const cancelQueries = (params: TParams) => queryClient.cancelQueries(getOptions(params));

  function useQueryHook<TNewData = TData>(
    params: TParams,
    extraOptions?: Omit<UseQueryOptions<TQueryFnData, TError, TNewData, TQueryKey>, 'queryKey' | 'queryFn'> & {
      select?: (data: TQueryFnData) => TNewData;
    },
  ): ReturnType<typeof useQuery<TQueryFnData, TError, TNewData, TQueryKey>>;
  function useQueryHook(
    params: TParams,
    extraOptions?: Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'queryKey' | 'queryFn'>,
  ): ReturnType<typeof useQuery<TQueryFnData, TError, TData, TQueryKey>>;
  function useQueryHook(
    params: TParams,
    extraOptions?: Omit<UseQueryOptions<TQueryFnData, TError, any, TQueryKey>, 'queryKey' | 'queryFn'>,
  ) {
    const options = getOptions(params);
    return useQuery({ ...options, ...extraOptions });
  }

  function useSuspenseQueryHook<TNewData = TData>(
    params: TParams,
    extraOptions?: Omit<UseSuspenseQueryOptions<TQueryFnData, TError, TNewData, TQueryKey>, 'queryKey' | 'queryFn'> & {
      select?: (data: TQueryFnData) => TNewData;
    },
  ): ReturnType<typeof useSuspenseQuery<TQueryFnData, TError, TNewData, TQueryKey>>;
  function useSuspenseQueryHook(
    params: TParams,
    extraOptions?: Omit<UseSuspenseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'queryKey' | 'queryFn'>,
  ): ReturnType<typeof useSuspenseQuery<TQueryFnData, TError, TData, TQueryKey>>;
  function useSuspenseQueryHook(
    params: TParams,
    extraOptions?: Omit<UseSuspenseQueryOptions<TQueryFnData, TError, any, TQueryKey>, 'queryKey' | 'queryFn'>,
  ) {
    const options = getOptions(params);
    return useSuspenseQuery({ ...options, ...extraOptions });
  }

  const useInfiniteQueryHook = (
    params: TParams,
    extraOptions?: Omit<UseInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'queryKey' | 'queryFn'>,
  ) => {
    const options = getOptions(params);
    return useInfiniteQuery({ ...options, ...extraOptions } as any);
  };

  const prefetchQuery = async (
    params: TParams,
    extraOptions?: Omit<FetchQueryOptions<TQueryFnData, TError, TQueryFnData, TQueryKey>, 'queryKey' | 'queryFn'>,
  ) => {
    const options = getOptions(params);
    await queryClient.prefetchQuery({ ...options, ...extraOptions });
  };

  const fetchQuery = async (
    params: TParams,
    extraOptions?: Omit<FetchQueryOptions<TQueryFnData, TError, TQueryFnData, TQueryKey>, 'queryKey' | 'queryFn'>,
  ): Promise<TData> => {
    const options = getOptions(params);
    const data = await queryClient.fetchQuery({ ...options, ...extraOptions });
    return 'select' in options && options.select ? options.select(data) : (data as TData);
  };

  /**
   * `ensureQueryData` 通常用于平替 `store.select()`
   *
   * 所以 `staleTime` 默认设置为 `STALE_TIME.HOUR1`
   *
   * 如果需要新鲜数据，可使用 `fetchQuery`
   *
   * @default staleTime: STALE_TIME.HOUR1
   */
  const ensureQueryData = async (
    params: TParams,
    extraOptions?: Omit<FetchQueryOptions<TQueryFnData, TError, TQueryFnData, TQueryKey>, 'queryKey' | 'queryFn'>,
  ): Promise<TData> => {
    const options = getOptions(params);
    const data = await queryClient.ensureQueryData({
      ...options,
      ...extraOptions,
    });
    return 'select' in options && options.select ? options.select(data) : (data as TData);
  };

  getOptions.invalidateQueries = invalidateQueries;
  getOptions.resetQueries = resetQueries;
  getOptions.cancelQueries = cancelQueries;
  getOptions.useQuery = useQueryHook;
  getOptions.useSuspenseQuery = useSuspenseQueryHook;
  getOptions.useInfiniteQuery = useInfiniteQueryHook;
  getOptions.prefetchQuery = prefetchQuery;
  getOptions.fetchQuery = fetchQuery;
  getOptions.ensureQueryData = ensureQueryData;

  return getOptions;
}
