import { http } from '../../middleware/api';
import { BFFPaymentClient } from '../../middleware/bff';
import { createQueryOptions } from '../utils/createQueryOptions';

export const queryPayment = {
  listBriefs: createQueryOptions({
    queryKey: (params) => ['payment', 'listPaymentBriefs', params],
    queryFn: ({ orderIds }: { orderIds: string[] }) => BFFPaymentClient.listPaymentBriefs({ orderIds }),
  }),
  paymentSummary: createQueryOptions({
    queryKey: (params) => ['payment', 'paymentSummary', params],
    queryFn: ({ customerId }: { customerId: number }) => http.open('POST/payment/payment/sum', { customerId }),
    select: (data) => data.data,
  }),
};
