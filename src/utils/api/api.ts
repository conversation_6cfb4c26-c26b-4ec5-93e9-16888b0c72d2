/**
 * API Definition Typing Utils
 */
import { type Overwrite } from 'utility-types';

/**
 * 针对存量改不动的 API 定义做的兼容转换，支持业务平滑迭代
 * 用于兼容旧 swagger / 过去不依赖 api definition 自定义的 typing
 *
 * 策略：
 * 1. 将表达 int64 id 的 string | number 转换为 number
 * 2. 将 object 的属性全部转换为必选，原因是旧的 swagger 生成的定义必选的，但 open api 生成的结果会存在 optional，导致 http.open 的返回值无法直接替代原 http.swagger 返回值
 */
export type IdToNumberAndRequired<T> = string | number extends T
  ? number
  : T extends { [key: string]: any }
    ? Required<{ [Key in keyof T]: IdToNumberAndRequired<T[Key]> }>
    : undefined extends T
      ? Exclude<T, undefined>
      : T;

/**
 * 强行指定类型为 IdToNumberAndRequired 后的结果，值不变
 */
export const id2NumberAndRequired = <T>(data: T): IdToNumberAndRequired<T> => {
  return data as IdToNumberAndRequired<T>;
};

/**
 * 覆盖 object 返回值的特定字段类型，
 * 针对 enum -> number / string 的场景
 */
export const overwriteReturnType = <InputObject extends object, U extends object>(data: InputObject) =>
  data as unknown as Overwrite<InputObject, U>;

/**
 * 覆盖 object 参数的特定字段的类型，
 * 主要针对的是 number / string -> enum 的场景，兼容原特定字段的参数差异
 */
export const overwriteParamsType = <TargetObject extends object, U extends object>(
  data: Overwrite<TargetObject, U>,
) => {
  return data as TargetObject;
};

/**
 * 针对复杂类型支持创建类型转换器，减少频繁的 typing 引用
 */
export const createOverwriteParamsTypeTransformer =
  <TargetObject extends object, U extends object>() =>
  (data: Overwrite<TargetObject, U>) => {
    return data as TargetObject;
  };
