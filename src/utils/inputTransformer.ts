import { type Transformer } from '@moego/ui/dist/esm/types/common';
import { isNil } from 'lodash';

export const booleanToNumber: Transformer<boolean, number> = {
  input: (value?: boolean) => {
    return (value ? 1 : 0) as number;
  },
  output: (value: number) => {
    return value === 1;
  },
};

export const numberToBoolean: Transformer<number, boolean> = {
  input: (value?: number) => {
    return value === 1;
  },
  output: (value: boolean) => {
    return value ? 1 : 0;
  },
};

export const numbersToStrings: Transformer<number[], string[]> = {
  input: (value?: number[]) => {
    if (isNil(value)) {
      return value;
    }
    return value.map((item) => String(item));
  },
  output: (value: string[]) => value.map((item) => Number(item)),
};
