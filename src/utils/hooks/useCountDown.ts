/*
 * @since 2021-07-19 17:11:18
 * <AUTHOR> <<EMAIL>>
 */

import { T_SECOND } from 'monofile-utilities/lib/consts';
import { useEffect, useRef } from 'react';
import { useUpdate } from 'react-use';

export interface CountDownState {
  remain: number | undefined;
  start: (timeout: number, step?: number) => void;
  stop: () => void;
  timer: ReturnType<typeof setInterval> | undefined;
}

export function useCountDown(timeout?: number, step: number = T_SECOND): CountDownState {
  const update = useUpdate();
  const countDown = useRef<CountDownState>();

  const internalStop = (shouldUpdate: boolean) => {
    if (countDown.current!.timer !== void 0) {
      clearInterval(countDown.current!.timer);
      countDown.current!.timer = void 0;
      countDown.current!.remain = void 0;
      shouldUpdate && update();
    }
  };

  const internalStart = (timeout: number, step: number = T_SECOND, shouldUpdate: boolean) => {
    internalStop(false);
    countDown.current!.remain = timeout;
    countDown.current!.timer = setInterval(() => {
      countDown.current!.remain! -= step;
      if (!countDown.current!.remain) {
        internalStop(false);
      }
      update();
    }, step);
    shouldUpdate && update();
  };

  if (countDown.current === void 0) {
    countDown.current = {
      remain: void 0,
      start: (timeout: number, step: number = T_SECOND) => internalStart(timeout, step, true),
      stop: () => internalStop(true),
      timer: void 0,
    };
    timeout && internalStart(timeout, step, false);
  }

  useEffect(() => () => internalStop(false), []);
  return countDown.current!;
}
