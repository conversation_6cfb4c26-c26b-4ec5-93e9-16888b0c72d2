import { useSelector } from 'amos';
import { useCallback, useMemo, useState } from 'react';
import { currentBusinessIdBox } from '../../store/business/business.boxes';
import { useBool } from './useBool';

/**
 * 循环值 根据localStorage中的状态循环值
 * @param key 存储localStorage的字符串key
 * @param rotating
 * @returns
 * @deprecated use useMetaData or useEnableFeature instead
 */
export function useBizRelatedRotatingValue<T = any>(key: string, rotating: [T, T]): [T, () => void] {
  const [bool, setRead] = useBizRelatedOnceBool(key);
  return [bool ? rotating[1] : rotating[0], setRead];
}

/**
 * 两个状态的持久化工具
 * @param key 存储localStorage的字符串key
 * @returns [boolean setBoolean] 第一次必定是false，重置成true后，一直为true
 * @deprecated use useMetaData or useEnableFeature instead
 */
export function useBizRelatedOnceBool(key: string): [boolean, () => void] {
  const [businessId] = useSelector(currentBusinessIdBox);
  const finalKey = useMemo(() => `MOE-BIZ${businessId}-ONCE-${key}`, [businessId, key]);
  const storedValue = useMemo(() => !!window.localStorage.getItem(finalKey), []);
  const hasSetted = useBool(storedValue);
  const setRead = useCallback(() => {
    window.localStorage.setItem(finalKey, '1');
    hasSetted.open();
  }, [finalKey]);
  return [hasSetted.value, setRead];
}

type StateType = Record<string, string | number>;
/**
 * 与useBizRelatedOnceBool类似，但是可以存储多个值
 * @deprecated use useMetaData or useEnableFeature instead
 */
export function useBizRelatedOnceBoolMulti(key: string): [StateType, (newVal: string | number) => void] {
  const [businessId] = useSelector(currentBusinessIdBox);
  const finalKey = useMemo(() => `MOE-BIZ${businessId}-ONCE-${key}`, [businessId, key]);
  const [state, setState] = useState<StateType>(JSON.parse(window.localStorage.getItem(finalKey) || '{}'));

  const setter = useCallback(
    (newVal: string | number) => {
      const newState = { ...state, [newVal]: 1 };
      window.localStorage.setItem(finalKey, JSON.stringify(newState));
      setState(newState);
    },
    [finalKey, state],
  );
  return [state, setter];
}
