import { useHistory } from 'react-router-dom';
import { PATH_HOME } from '../../router/paths';

/**
 * 页面回退到上一页，能确保如果无法回退就跳转到指定页面
 */
export const useGoBack = (fallbackPath = PATH_HOME.build()) => {
  const history = useHistory();
  // 从浏览器直接输入 url 进入页面时, history.length 为 2, 此时返回直接回到浏览器默认页了
  // 所以用 > 2 比较保险
  const canGoBack = history.length > 2;

  const handleRoute = () => {
    if (canGoBack) {
      history.goBack();
      return;
    }
    history.push(fallbackPath);
  };

  return handleRoute;
};
