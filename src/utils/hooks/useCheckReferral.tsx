import { useStore } from 'amos';
import { useHistory } from 'react-router';
import { toastApi } from '../../components/Toast/Toast';
import { http } from '../../middleware/api';
import { PATH_REFEREE, PATH_SELECT_PLAN } from '../../router/paths';
import { selectCurrentCompany } from '../../store/company/company.selectors';
import { useRouteQueryV2 } from '../RoutePath';
import { promiseWrapper } from '../utils';
import { useBool } from './useBool';
import { useReferralInvalidModal } from './useReferral';

async function handlerReferee(referralCode: string, companyId: number) {
  const [error, result] = await promiseWrapper(
    http.open(
      'GET/business/referral/code',
      {
        code: referralCode,
        companyId,
      },
      { autoToast: false },
    ),
  );

  if (error || !result) {
    toastApi.error('Get referral info error. Maybe retry later.');
    return;
  }

  return result;
}

export const useCheckReferral = () => {
  const query = useRouteQueryV2(PATH_REFEREE);
  const isReferee = !!query.referralCode;
  const invalidModal = useReferralInvalidModal();
  const history = useHistory();
  const checking = useBool(false);
  const store = useStore();

  const goSelectPlan = () => {
    history.push(PATH_SELECT_PLAN.queried({ ...query, internalSource: 'referral' }));
  };

  const check = async (companyId?: number) => {
    if (!companyId) {
      companyId = store.select(selectCurrentCompany()).companyId;
    }
    if (isReferee) {
      checking.open();
      const refereeResult = await handlerReferee(query.referralCode!, companyId);
      checking.close();
      if (!refereeResult) {
        return;
      }

      const { isValid, isPurchased, isSubscribing } = refereeResult;
      // 购买过，无法被 referral
      if (isPurchased) {
        // 订阅中。无法被 referral
        if (isSubscribing) {
          invalidModal.current.show?.();
        } else {
          // 订阅过，但是取消订阅。无法被 referral，但是可以正常购买
          invalidModal.current.show?.({ text: 'Upgrade anyway', handler: goSelectPlan });
        }
        return;
      }

      if (isValid) {
        goSelectPlan();
      } else {
        invalidModal.current.show?.();
      }
      return;
    }
  };

  return {
    check,
    checking: checking.value,
    haveReferralCode: isReferee,
    referralCode: query.referralCode,
  };
};
