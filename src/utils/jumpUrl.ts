// 跳转系统的url
export enum MoegoSystem {
  MOEGO = 'go',
  MOEGO_ENTERPRISE = 'enterprise',
}

const GO_GREY_HOST_REG = /^https:\/\/([^.]*-grey-)?go\.[^.]+\.moego\.(dev|pet)$/;

export function getJumpUrl({ from, target, query }: { from: MoegoSystem; target: MoegoSystem; query?: string }) {
  const { hostname } = window.location;

  let newHostname: string = '';
  // t2的grey模式
  if (GO_GREY_HOST_REG.test(location.origin) && hostname.indexOf('grey') > -1) {
    newHostname = hostname.replace(`grey-${from}`, `grey-${target}`);
  } else {
    newHostname = hostname.replace(from, target);
  }

  return `${window.location.protocol}//${newHostname}${query}`;
}
