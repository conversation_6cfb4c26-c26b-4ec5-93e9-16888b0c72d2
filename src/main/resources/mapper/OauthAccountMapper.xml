<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.account.repository.mapper.OauthAccountMapper">

    <resultMap id="BaseResultMap" type="com.moego.svc.account.repository.entity.OauthAccount">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="accountId" column="account_id" jdbcType="BIGINT"/>
            <result property="provider" column="provider" jdbcType="SMALLINT"/>
            <result property="openId" column="open_id" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="firstName" column="first_name" jdbcType="VARCHAR"/>
            <result property="lastName" column="last_name" jdbcType="VARCHAR"/>
            <result property="avatarPath" column="avatar_path" jdbcType="VARCHAR"/>
            <result property="extraData" column="extra_data" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="SMALLINT"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_id,provider,
        open_id,email,first_name,
        last_name,avatar_path,extra_data,
        status,created_at,updated_at
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from oauth_account
        where  id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByAccountId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from   oauth_account
        where  account_id = #{accountId,jdbcType=BIGINT}
        and    status = 1
        <if test="provider != null">
          and provider = #{provider,jdbcType=SMALLINT}
        </if>
    </select>

    <select id="selectByOpenIdAndProviderForUpdate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from   oauth_account
        where  open_id = #{openId,jdbcType=VARCHAR}
        and    provider = #{provider,jdbcType=SMALLINT}
        and    status = 1
        for update
    </select>

    <update id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update oauth_account
        set status = 2
            updated_at = statement_timestamp()
        where   id = #{id,jdbcType=BIGINT}
        and status != 2
    </update>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.account.repository.entity.OauthAccount" useGeneratedKeys="true">
        insert into oauth_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="accountId != null">account_id,</if>
                <if test="provider != null">provider,</if>
                <if test="openId != null">open_id,</if>
                <if test="email != null">email,</if>
                <if test="firstName != null">first_name,</if>
                <if test="lastName != null">last_name,</if>
                <if test="avatarPath != null">avatar_path,</if>
                <if test="extraData != null">extra_data,</if>
                <if test="status != null">status,</if>
                created_at,
                updated_at
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="accountId != null">#{accountId,jdbcType=BIGINT},</if>
                <if test="provider != null">#{provider,jdbcType=SMALLINT},</if>
                <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
                <if test="email != null">#{email,jdbcType=VARCHAR},</if>
                <if test="firstName != null">#{firstName,jdbcType=VARCHAR},</if>
                <if test="lastName != null">#{lastName,jdbcType=VARCHAR},</if>
                <if test="avatarPath != null">#{avatarPath,jdbcType=VARCHAR},</if>
                <if test="extraData != null">#{extraData,jdbcType=VARCHAR},</if>
                <if test="status != null">#{status,jdbcType=SMALLINT},</if>
                statement_timestamp(),
                statement_timestamp()
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.account.repository.entity.OauthAccount">
        update oauth_account
        <set>
                <if test="accountId != null">
                    account_id = #{accountId,jdbcType=BIGINT},
                </if>
                <if test="provider != null">
                    provider = #{provider,jdbcType=SMALLINT},
                </if>
                <if test="openId != null">
                    open_id = #{openId,jdbcType=VARCHAR},
                </if>
                <if test="email != null">
                    email = #{email,jdbcType=VARCHAR},
                </if>
                <if test="firstName != null">
                    first_name = #{firstName,jdbcType=VARCHAR},
                </if>
                <if test="lastName != null">
                    last_name = #{lastName,jdbcType=VARCHAR},
                </if>
                <if test="avatarPath != null">
                    avatar_path = #{avatarPath,jdbcType=VARCHAR},
                </if>
                <if test="extraData != null">
                    extra_data = #{extraData,jdbcType=VARCHAR},
                </if>
                <if test="status != null">
                    status = #{status,jdbcType=SMALLINT},
                </if>
                updated_at = statement_timestamp()
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
