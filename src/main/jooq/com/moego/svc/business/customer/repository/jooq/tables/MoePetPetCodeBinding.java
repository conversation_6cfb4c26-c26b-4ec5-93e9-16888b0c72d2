/*
 * This file is generated by jOOQ.
 */
package com.moego.svc.business.customer.repository.jooq.tables;


import com.moego.svc.business.customer.repository.jooq.Indexes;
import com.moego.svc.business.customer.repository.jooq.Keys;
import com.moego.svc.business.customer.repository.jooq.MoeCustomer;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoePetPetCodeBindingRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Function5;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Records;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.SelectField;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MoePetPetCodeBinding extends TableImpl<MoePetPetCodeBindingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>moe_customer.moe_pet_pet_code_binding</code>
     */
    public static final MoePetPetCodeBinding MOE_PET_PET_CODE_BINDING = new MoePetPetCodeBinding();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MoePetPetCodeBindingRecord> getRecordType() {
        return MoePetPetCodeBindingRecord.class;
    }

    /**
     * The column <code>moe_customer.moe_pet_pet_code_binding.id</code>.
     */
    public final TableField<MoePetPetCodeBindingRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>moe_customer.moe_pet_pet_code_binding.pet_id</code>.
     */
    public final TableField<MoePetPetCodeBindingRecord, Integer> PET_ID = createField(DSL.name("pet_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column
     * <code>moe_customer.moe_pet_pet_code_binding.pet_code_id</code>.
     */
    public final TableField<MoePetPetCodeBindingRecord, Integer> PET_CODE_ID = createField(DSL.name("pet_code_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>moe_customer.moe_pet_pet_code_binding.comment</code>.
     */
    public final TableField<MoePetPetCodeBindingRecord, String> COMMENT = createField(DSL.name("comment"), SQLDataType.VARCHAR(512).defaultValue(DSL.inline("", SQLDataType.VARCHAR)), this, "");

    /**
     * The column
     * <code>moe_customer.moe_pet_pet_code_binding.binding_time</code>.
     */
    public final TableField<MoePetPetCodeBindingRecord, LocalDateTime> BINDING_TIME = createField(DSL.name("binding_time"), SQLDataType.LOCALDATETIME(0).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.LOCALDATETIME)), this, "");

    private MoePetPetCodeBinding(Name alias, Table<MoePetPetCodeBindingRecord> aliased) {
        this(alias, aliased, null);
    }

    private MoePetPetCodeBinding(Name alias, Table<MoePetPetCodeBindingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>moe_customer.moe_pet_pet_code_binding</code>
     * table reference
     */
    public MoePetPetCodeBinding(String alias) {
        this(DSL.name(alias), MOE_PET_PET_CODE_BINDING);
    }

    /**
     * Create an aliased <code>moe_customer.moe_pet_pet_code_binding</code>
     * table reference
     */
    public MoePetPetCodeBinding(Name alias) {
        this(alias, MOE_PET_PET_CODE_BINDING);
    }

    /**
     * Create a <code>moe_customer.moe_pet_pet_code_binding</code> table
     * reference
     */
    public MoePetPetCodeBinding() {
        this(DSL.name("moe_pet_pet_code_binding"), null);
    }

    public <O extends Record> MoePetPetCodeBinding(Table<O> child, ForeignKey<O, MoePetPetCodeBindingRecord> key) {
        super(child, key, MOE_PET_PET_CODE_BINDING);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : MoeCustomer.MOE_CUSTOMER;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.MOE_PET_PET_CODE_BINDING_IDX_PET_CODE_ID, Indexes.MOE_PET_PET_CODE_BINDING_INDEX_PETID);
    }

    @Override
    public Identity<MoePetPetCodeBindingRecord, Integer> getIdentity() {
        return (Identity<MoePetPetCodeBindingRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<MoePetPetCodeBindingRecord> getPrimaryKey() {
        return Keys.KEY_MOE_PET_PET_CODE_BINDING_PRIMARY;
    }

    @Override
    public MoePetPetCodeBinding as(String alias) {
        return new MoePetPetCodeBinding(DSL.name(alias), this);
    }

    @Override
    public MoePetPetCodeBinding as(Name alias) {
        return new MoePetPetCodeBinding(alias, this);
    }

    @Override
    public MoePetPetCodeBinding as(Table<?> alias) {
        return new MoePetPetCodeBinding(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public MoePetPetCodeBinding rename(String name) {
        return new MoePetPetCodeBinding(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public MoePetPetCodeBinding rename(Name name) {
        return new MoePetPetCodeBinding(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public MoePetPetCodeBinding rename(Table<?> name) {
        return new MoePetPetCodeBinding(name.getQualifiedName(), null);
    }

    // -------------------------------------------------------------------------
    // Row5 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row5<Integer, Integer, Integer, String, LocalDateTime> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Function)}.
     */
    public <U> SelectField<U> mapping(Function5<? super Integer, ? super Integer, ? super Integer, ? super String, ? super LocalDateTime, ? extends U> from) {
        return convertFrom(Records.mapping(from));
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Class,
     * Function)}.
     */
    public <U> SelectField<U> mapping(Class<U> toType, Function5<? super Integer, ? super Integer, ? super Integer, ? super String, ? super LocalDateTime, ? extends U> from) {
        return convertFrom(toType, Records.mapping(from));
    }
}
