/*
 * This file is generated by jOOQ.
 */
package com.moego.svc.business.customer.repository.jooq.tables;


import com.moego.svc.business.customer.repository.jooq.Keys;
import com.moego.svc.business.customer.repository.jooq.MoeCustomer;
import com.moego.svc.business.customer.repository.jooq.tables.records.CustomerCreationSettingRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Function6;
import org.jooq.Identity;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Records;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.SelectField;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CustomerCreationSetting extends TableImpl<CustomerCreationSettingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>moe_customer.customer_creation_setting</code>
     */
    public static final CustomerCreationSetting CUSTOMER_CREATION_SETTING = new CustomerCreationSetting();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CustomerCreationSettingRecord> getRecordType() {
        return CustomerCreationSettingRecord.class;
    }

    /**
     * The column <code>moe_customer.customer_creation_setting.id</code>.
     */
    public final TableField<CustomerCreationSettingRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

    /**
     * The column
     * <code>moe_customer.customer_creation_setting.company_id</code>.
     */
    public final TableField<CustomerCreationSettingRecord, Long> COMPANY_ID = createField(DSL.name("company_id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column
     * <code>moe_customer.customer_creation_setting.enable_creation_from_sms</code>.
     */
    public final TableField<CustomerCreationSettingRecord, Integer> ENABLE_CREATION_FROM_SMS = createField(DSL.name("enable_creation_from_sms"), SQLDataType.TINYINT.nullable(false).defaultValue(DSL.inline("1", SQLDataType.TINYINT)), this, "", Converter.ofNullable(Byte.class, Integer.class, Integer::valueOf, Integer::byteValue));

    /**
     * The column
     * <code>moe_customer.customer_creation_setting.enable_creation_from_call</code>.
     */
    public final TableField<CustomerCreationSettingRecord, Integer> ENABLE_CREATION_FROM_CALL = createField(DSL.name("enable_creation_from_call"), SQLDataType.TINYINT.nullable(false).defaultValue(DSL.inline("1", SQLDataType.TINYINT)), this, "", Converter.ofNullable(Byte.class, Integer.class, Integer::valueOf, Integer::byteValue));

    /**
     * The column
     * <code>moe_customer.customer_creation_setting.created_at</code>.
     */
    public final TableField<CustomerCreationSettingRecord, LocalDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "");

    /**
     * The column
     * <code>moe_customer.customer_creation_setting.updated_at</code>.
     */
    public final TableField<CustomerCreationSettingRecord, LocalDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.LOCALDATETIME(0).nullable(false), this, "");

    private CustomerCreationSetting(Name alias, Table<CustomerCreationSettingRecord> aliased) {
        this(alias, aliased, null);
    }

    private CustomerCreationSetting(Name alias, Table<CustomerCreationSettingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>moe_customer.customer_creation_setting</code>
     * table reference
     */
    public CustomerCreationSetting(String alias) {
        this(DSL.name(alias), CUSTOMER_CREATION_SETTING);
    }

    /**
     * Create an aliased <code>moe_customer.customer_creation_setting</code>
     * table reference
     */
    public CustomerCreationSetting(Name alias) {
        this(alias, CUSTOMER_CREATION_SETTING);
    }

    /**
     * Create a <code>moe_customer.customer_creation_setting</code> table
     * reference
     */
    public CustomerCreationSetting() {
        this(DSL.name("customer_creation_setting"), null);
    }

    public <O extends Record> CustomerCreationSetting(Table<O> child, ForeignKey<O, CustomerCreationSettingRecord> key) {
        super(child, key, CUSTOMER_CREATION_SETTING);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : MoeCustomer.MOE_CUSTOMER;
    }

    @Override
    public Identity<CustomerCreationSettingRecord, Long> getIdentity() {
        return (Identity<CustomerCreationSettingRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<CustomerCreationSettingRecord> getPrimaryKey() {
        return Keys.KEY_CUSTOMER_CREATION_SETTING_PRIMARY;
    }

    @Override
    public List<UniqueKey<CustomerCreationSettingRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_CUSTOMER_CREATION_SETTING_UK_COMPANY_ID);
    }

    @Override
    public CustomerCreationSetting as(String alias) {
        return new CustomerCreationSetting(DSL.name(alias), this);
    }

    @Override
    public CustomerCreationSetting as(Name alias) {
        return new CustomerCreationSetting(alias, this);
    }

    @Override
    public CustomerCreationSetting as(Table<?> alias) {
        return new CustomerCreationSetting(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public CustomerCreationSetting rename(String name) {
        return new CustomerCreationSetting(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CustomerCreationSetting rename(Name name) {
        return new CustomerCreationSetting(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public CustomerCreationSetting rename(Table<?> name) {
        return new CustomerCreationSetting(name.getQualifiedName(), null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Long, Integer, Integer, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Function)}.
     */
    public <U> SelectField<U> mapping(Function6<? super Long, ? super Long, ? super Integer, ? super Integer, ? super LocalDateTime, ? super LocalDateTime, ? extends U> from) {
        return convertFrom(Records.mapping(from));
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Class,
     * Function)}.
     */
    public <U> SelectField<U> mapping(Class<U> toType, Function6<? super Long, ? super Long, ? super Integer, ? super Integer, ? super LocalDateTime, ? super LocalDateTime, ? extends U> from) {
        return convertFrom(toType, Records.mapping(from));
    }
}
