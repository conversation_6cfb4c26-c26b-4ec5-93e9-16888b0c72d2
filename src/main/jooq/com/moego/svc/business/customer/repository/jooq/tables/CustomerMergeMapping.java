/*
 * This file is generated by jOOQ.
 */
package com.moego.svc.business.customer.repository.jooq.tables;


import com.moego.svc.business.customer.repository.jooq.Indexes;
import com.moego.svc.business.customer.repository.jooq.Keys;
import com.moego.svc.business.customer.repository.jooq.MoeCustomer;
import com.moego.svc.business.customer.repository.jooq.tables.records.CustomerMergeMappingRecord;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Function5;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Records;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.SelectField;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CustomerMergeMapping extends TableImpl<CustomerMergeMappingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>moe_customer.customer_merge_mapping</code>
     */
    public static final CustomerMergeMapping CUSTOMER_MERGE_MAPPING = new CustomerMergeMapping();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CustomerMergeMappingRecord> getRecordType() {
        return CustomerMergeMappingRecord.class;
    }

    /**
     * The column <code>moe_customer.customer_merge_mapping.id</code>.
     */
    public final TableField<CustomerMergeMappingRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

    /**
     * The column <code>moe_customer.customer_merge_mapping.company_id</code>.
     */
    public final TableField<CustomerMergeMappingRecord, Long> COMPANY_ID = createField(DSL.name("company_id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column
     * <code>moe_customer.customer_merge_mapping.source_customer_id</code>.
     */
    public final TableField<CustomerMergeMappingRecord, Long> SOURCE_CUSTOMER_ID = createField(DSL.name("source_customer_id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column
     * <code>moe_customer.customer_merge_mapping.target_customer_id</code>.
     */
    public final TableField<CustomerMergeMappingRecord, Long> TARGET_CUSTOMER_ID = createField(DSL.name("target_customer_id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>moe_customer.customer_merge_mapping.process_id</code>.
     */
    public final TableField<CustomerMergeMappingRecord, Long> PROCESS_ID = createField(DSL.name("process_id"), SQLDataType.BIGINT.nullable(false), this, "");

    private CustomerMergeMapping(Name alias, Table<CustomerMergeMappingRecord> aliased) {
        this(alias, aliased, null);
    }

    private CustomerMergeMapping(Name alias, Table<CustomerMergeMappingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>moe_customer.customer_merge_mapping</code> table
     * reference
     */
    public CustomerMergeMapping(String alias) {
        this(DSL.name(alias), CUSTOMER_MERGE_MAPPING);
    }

    /**
     * Create an aliased <code>moe_customer.customer_merge_mapping</code> table
     * reference
     */
    public CustomerMergeMapping(Name alias) {
        this(alias, CUSTOMER_MERGE_MAPPING);
    }

    /**
     * Create a <code>moe_customer.customer_merge_mapping</code> table reference
     */
    public CustomerMergeMapping() {
        this(DSL.name("customer_merge_mapping"), null);
    }

    public <O extends Record> CustomerMergeMapping(Table<O> child, ForeignKey<O, CustomerMergeMappingRecord> key) {
        super(child, key, CUSTOMER_MERGE_MAPPING);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : MoeCustomer.MOE_CUSTOMER;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.CUSTOMER_MERGE_MAPPING_IDX_COMPANY_ID, Indexes.CUSTOMER_MERGE_MAPPING_IDX_PROCESS_ID);
    }

    @Override
    public Identity<CustomerMergeMappingRecord, Long> getIdentity() {
        return (Identity<CustomerMergeMappingRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<CustomerMergeMappingRecord> getPrimaryKey() {
        return Keys.KEY_CUSTOMER_MERGE_MAPPING_PRIMARY;
    }

    @Override
    public List<UniqueKey<CustomerMergeMappingRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_CUSTOMER_MERGE_MAPPING_UK_MERGE_RELATION);
    }

    @Override
    public CustomerMergeMapping as(String alias) {
        return new CustomerMergeMapping(DSL.name(alias), this);
    }

    @Override
    public CustomerMergeMapping as(Name alias) {
        return new CustomerMergeMapping(alias, this);
    }

    @Override
    public CustomerMergeMapping as(Table<?> alias) {
        return new CustomerMergeMapping(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public CustomerMergeMapping rename(String name) {
        return new CustomerMergeMapping(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public CustomerMergeMapping rename(Name name) {
        return new CustomerMergeMapping(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public CustomerMergeMapping rename(Table<?> name) {
        return new CustomerMergeMapping(name.getQualifiedName(), null);
    }

    // -------------------------------------------------------------------------
    // Row5 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row5<Long, Long, Long, Long, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Function)}.
     */
    public <U> SelectField<U> mapping(Function5<? super Long, ? super Long, ? super Long, ? super Long, ? super Long, ? extends U> from) {
        return convertFrom(Records.mapping(from));
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Class,
     * Function)}.
     */
    public <U> SelectField<U> mapping(Class<U> toType, Function5<? super Long, ? super Long, ? super Long, ? super Long, ? super Long, ? extends U> from) {
        return convertFrom(toType, Records.mapping(from));
    }
}
