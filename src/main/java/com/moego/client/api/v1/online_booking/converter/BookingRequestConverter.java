package com.moego.client.api.v1.online_booking.converter;

import com.moego.idl.client.online_booking.v1.AppointmentSummaryItem;
import com.moego.idl.client.online_booking.v1.GetAppointmentDetailResult;
import com.moego.idl.client.online_booking.v1.ReschedulePetFeedingMedicationParams;
import com.moego.idl.client.online_booking.v1.UpdateAppointmentParams;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.service.online_booking.v1.CreateFeedingRequestList;
import com.moego.idl.service.online_booking.v1.CreateMedicationRequestList;
import com.moego.idl.service.online_booking.v1.UpdateBoardingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBoardingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateDaycareAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateDaycareServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateGroomingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateGroomingServiceDetailRequest;
import com.moego.idl.utils.v1.StringListValue;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.stream.Collectors;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        uses = AppointmentConverter.class)
public interface BookingRequestConverter {

    BookingRequestConverter INSTANCE = Mappers.getMapper(BookingRequestConverter.class);

    @Mapping(target = "appointmentId", ignore = true)
    @Mapping(target = "bookingRequestId", source = "id")
    @Mapping(target = "isBookingRequest", expression = "java(true)")
    @Mapping(target = "mainCareType", source = "serviceTypeInclude", qualifiedByName = "toMainCareType")
    AppointmentSummaryItem toAppointmentSummary(BookingRequestModel model);

    @Mapping(target = "appointmentId", ignore = true)
    @Mapping(target = "bookingRequestId", source = "id")
    @Mapping(target = "isBookingRequest", expression = "java(true)")
    @Mapping(target = "mainCareType", source = "serviceTypeInclude", qualifiedByName = "toMainCareType")
    GetAppointmentDetailResult.AppointmentItem toAppointmentDetail(BookingRequestModel model);

    static UpdateBookingRequestRequest toUpdateBookingRequestRequest(
            ReschedulePetFeedingMedicationParams request, BookingRequestModel bookingRequest) {
        // petDetailId -> schedule
        var boardingSchedules = request.getSchedulesList().stream()
                .filter(k -> k.getCareType() == ServiceItemType.BOARDING)
                .collect(Collectors.toMap(
                        ReschedulePetFeedingMedicationParams.PetScheduleDef::getPetDetailId, k -> k, (k1, k2) -> k1));

        var daycareSchedules = request.getSchedulesList().stream()
                .filter(k -> k.getCareType() == ServiceItemType.DAYCARE)
                .collect(Collectors.toMap(
                        ReschedulePetFeedingMedicationParams.PetScheduleDef::getPetDetailId, k -> k, (k1, k2) -> k1));

        var builder = UpdateBookingRequestRequest.newBuilder().setId(request.getBookingRequestId());

        for (var petService : bookingRequest.getServicesList()) {
            switch (petService.getServiceCase()) {
                case BOARDING -> {
                    var boardingDetail = petService.getBoarding().getService();
                    var tmp = boardingSchedules.get(boardingDetail.getId());
                    if (tmp != null) {
                        builder.addServiceDetails(UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                                .setUpdate(toBoardingUpdateService(tmp, boardingDetail.getId()))
                                .build());
                    }
                }
                case DAYCARE -> {
                    var daycareDetail = petService.getDaycare().getService();
                    var tmp = daycareSchedules.get(daycareDetail.getId());
                    if (tmp != null) {
                        builder.addServiceDetails(UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                                .setUpdate(toDaycareUpdateService(tmp, daycareDetail.getId()))
                                .build());
                    }
                }
                default -> {}
            }
        }
        return builder.build();
    }

    static UpdateBookingRequestRequest toUpdateBookingRequestRequest(
            UpdateAppointmentParams request, BookingRequestModel bookingRequest) {
        var updateBuilder = UpdateBookingRequestRequest.newBuilder();
        updateBuilder.setId(request.getBookingRequestId());

        for (var petAndServices : request.getPetAndServicesList()) {
            for (var serviceDetail : petAndServices.getServicesList()) {
                updateBuilder.addServiceDetails(buildUpdateServiceDetailRequest(serviceDetail));
            }
            for (var addOnDetail : petAndServices.getAddOnsList()) {
                updateBuilder.addServiceDetails(buildUpdateAddOnDetailRequest(bookingRequest, addOnDetail));
            }
        }

        for (var delete : request.getDeleteList()) {
            if (delete.getResourceCase() == UpdateAppointmentParams.Delete.ResourceCase.BOOKING_REQUEST) {
                var updateRequest =
                        switch (delete.getBookingRequest().getResourceCase()) {
                            case SERVICE -> UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                                    .setDelete(buildDeleteServiceDetail(
                                            delete.getBookingRequest().getService()))
                                    .build();
                            case ADD_ON -> UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                                    .setUpdate(buildDeleteAddOnDetail(
                                            delete.getBookingRequest().getAddOn(), bookingRequest))
                                    .build();
                            default -> throw ExceptionUtil.bizException(
                                    Code.CODE_PARAMS_ERROR, "Unsupported booking request resource type");
                        };
                updateBuilder.addServiceDetails(updateRequest);
            }
        }

        return updateBuilder.build();
    }

    private static UpdateBookingRequestRequest.ServiceDetail buildUpdateAddOnDetailRequest(
            BookingRequestModel bookingRequest, UpdateAppointmentParams.UpdateAddOnDetailParams addOnDetail) {
        return switch (addOnDetail.getCareType()) {
            case BOARDING -> UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                    .setUpdate(buildUpdateBoardingAddOnDetail(
                            addOnDetail, mustGetBoardingServiceDetailId(bookingRequest, addOnDetail.getPetDetailId())))
                    .build();
            case DAYCARE -> UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                    .setUpdate(buildUpdateDaycareAddOnDetail(
                            addOnDetail, mustGetDaycareServiceDetailId(bookingRequest, addOnDetail.getPetDetailId())))
                    .build();
            case GROOMING -> UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                    .setUpdate(buildUpdateGroomingAddOnDetail(
                            addOnDetail, mustGetGroomingServiceDetailId(bookingRequest, addOnDetail.getPetDetailId())))
                    .build();
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported care type: " + addOnDetail.getCareType());
        };
    }

    private static UpdateBookingRequestRequest.ServiceDetail buildUpdateServiceDetailRequest(
            UpdateAppointmentParams.UpdateServiceDetailParams serviceDetail) {
        return switch (serviceDetail.getCareType()) {
            case BOARDING -> UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                    .setUpdate(UpdateBookingRequestRequest.Service.newBuilder()
                            .setBoarding(UpdateBookingRequestRequest.BoardingService.newBuilder()
                                    .setService(toUpdateBoardingServiceDetailRequest(serviceDetail))))
                    .build();
            case DAYCARE -> UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                    .setUpdate(UpdateBookingRequestRequest.Service.newBuilder()
                            .setDaycare(UpdateBookingRequestRequest.DaycareService.newBuilder()
                                    .setService(toUpdateDaycareServiceDetailRequest(serviceDetail))))
                    .build();
            case GROOMING -> UpdateBookingRequestRequest.ServiceDetail.newBuilder()
                    .setUpdate(UpdateBookingRequestRequest.Service.newBuilder()
                            .setGrooming(UpdateBookingRequestRequest.GroomingService.newBuilder()
                                    .setService(toUpdateGroomingServiceDetailRequest(serviceDetail))))
                    .build();
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unsupported care type");
        };
    }

    private static UpdateBookingRequestRequest.Service buildUpdateGroomingAddOnDetail(
            UpdateAppointmentParams.UpdateAddOnDetailParams addOnDetail, long groomingServiceDetailId) {
        var builder = UpdateBookingRequestRequest.Service.newBuilder();
        builder.setGrooming(UpdateBookingRequestRequest.GroomingService.newBuilder()
                .setService(UpdateGroomingServiceDetailRequest.newBuilder().setId(groomingServiceDetailId))
                .addAddonsV2(UpdateBookingRequestRequest.GroomingService.Addon.newBuilder()
                        .setUpdate(UpdateBookingRequestRequest.GroomingService.Addon.Update.newBuilder()
                                .setAddon(toUpdateGroomingAddOnDetailRequest(addOnDetail)))));
        return builder.build();
    }

    private static UpdateGroomingAddOnDetailRequest toUpdateGroomingAddOnDetailRequest(
            UpdateAppointmentParams.UpdateAddOnDetailParams addOnDetail) {
        var builder = UpdateGroomingAddOnDetailRequest.newBuilder();
        builder.setId(addOnDetail.getPetDetailId());
        if (addOnDetail.hasStartDate()) {
            builder.setStartDate(addOnDetail.getStartDate());
        }
        if (addOnDetail.hasStartTime()) {
            builder.setStartTime(addOnDetail.getStartTime());
        }
        if (addOnDetail.hasEndTime()) {
            builder.setEndTime(addOnDetail.getEndTime());
        }
        return builder.build();
    }

    private static UpdateBookingRequestRequest.Service buildUpdateDaycareAddOnDetail(
            UpdateAppointmentParams.UpdateAddOnDetailParams addOnDetail, long daycareServiceDetailId) {
        var builder = UpdateBookingRequestRequest.Service.newBuilder();
        builder.setDaycare(UpdateBookingRequestRequest.DaycareService.newBuilder()
                .setService(UpdateDaycareServiceDetailRequest.newBuilder().setId(daycareServiceDetailId))
                .addAddonsV2(UpdateBookingRequestRequest.DaycareService.Addon.newBuilder()
                        .setUpdate(UpdateBookingRequestRequest.DaycareService.Addon.Update.newBuilder()
                                .setAddon(toUpdateDaycareAddOnDetailRequest(addOnDetail)))));
        return builder.build();
    }

    private static UpdateBookingRequestRequest.Service buildUpdateBoardingAddOnDetail(
            UpdateAppointmentParams.UpdateAddOnDetailParams addOnDetail, long boardingServiceDetailId) {
        var builder = UpdateBookingRequestRequest.Service.newBuilder();
        builder.setBoarding(UpdateBookingRequestRequest.BoardingService.newBuilder()
                .setService(UpdateBoardingServiceDetailRequest.newBuilder().setId(boardingServiceDetailId))
                .addAddonsV2(UpdateBookingRequestRequest.BoardingService.Addon.newBuilder()
                        .setUpdate(UpdateBookingRequestRequest.BoardingService.Addon.Update.newBuilder()
                                .setAddon(toUpdateBoardingAddOnDetailRequest(addOnDetail)))));
        return builder.build();
    }

    private static UpdateGroomingServiceDetailRequest toUpdateGroomingServiceDetailRequest(
            UpdateAppointmentParams.UpdateServiceDetailParams serviceDetail) {
        var builder = UpdateGroomingServiceDetailRequest.newBuilder();
        builder.setId(serviceDetail.getPetDetailId());
        if (serviceDetail.hasDateType()) {
            builder.setDateType(serviceDetail.getDateType());
        }
        if (serviceDetail.hasStartDate()) {
            builder.setStartDate(serviceDetail.getStartDate());
        }
        if (serviceDetail.hasStartTime()) {
            builder.setStartTime(serviceDetail.getStartTime());
        }
        if (serviceDetail.hasEndDate()) {
            builder.setEndDate(serviceDetail.getEndDate());
        }
        if (serviceDetail.hasEndTime()) {
            builder.setEndTime(serviceDetail.getEndTime());
        }
        return builder.build();
    }

    private static UpdateBookingRequestRequest.Service buildDeleteAddOnDetail(
            UpdateAppointmentParams.Delete.BookingRequest.AddOn addOnDetail, BookingRequestModel bookingRequest) {
        var builder = UpdateBookingRequestRequest.Service.newBuilder();

        switch (addOnDetail.getIdCase()) {
            case GROOMING_ADD_ON_DETAIL_ID -> {
                var groomingServiceDetailId =
                        mustGetGroomingServiceDetailId(bookingRequest, addOnDetail.getGroomingAddOnDetailId());
                builder.setGrooming(buildDeleteGroomingAddOnDetail(addOnDetail, groomingServiceDetailId));
            }
            case BOARDING_ADD_ON_DETAIL_ID -> {
                var boardingServiceDetailId =
                        mustGetBoardingServiceDetailId(bookingRequest, addOnDetail.getBoardingAddOnDetailId());
                builder.setBoarding(buildDeleteBoardingAddOnDetail(addOnDetail, boardingServiceDetailId));
            }
            case DAYCARE_ADD_ON_DETAIL_ID -> {
                var daycareServiceDetailId =
                        mustGetDaycareServiceDetailId(bookingRequest, addOnDetail.getDaycareAddOnDetailId());
                builder.setDaycare(buildDeleteDaycareAddOnDetail(addOnDetail, daycareServiceDetailId));
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unsupported add-on detail id type");
        }

        return builder.build();
    }

    private static UpdateBookingRequestRequest.BoardingService buildDeleteBoardingAddOnDetail(
            UpdateAppointmentParams.Delete.BookingRequest.AddOn addOnDetail, long boardingServiceDetailId) {
        return UpdateBookingRequestRequest.BoardingService.newBuilder()
                .setService(UpdateBoardingServiceDetailRequest.newBuilder()
                        .setId(boardingServiceDetailId)
                        .build())
                .addAddonsV2(UpdateBookingRequestRequest.BoardingService.Addon.newBuilder()
                        .setDelete(UpdateBookingRequestRequest.BoardingService.Addon.Delete.newBuilder()
                                .setId(addOnDetail.getBoardingAddOnDetailId())
                                .build())
                        .build())
                .build();
    }

    private static UpdateBookingRequestRequest.DaycareService buildDeleteDaycareAddOnDetail(
            UpdateAppointmentParams.Delete.BookingRequest.AddOn addOnDetail, long daycareServiceDetailId) {
        return UpdateBookingRequestRequest.DaycareService.newBuilder()
                .setService(UpdateDaycareServiceDetailRequest.newBuilder()
                        .setId(daycareServiceDetailId)
                        .build())
                .addAddonsV2(UpdateBookingRequestRequest.DaycareService.Addon.newBuilder()
                        .setDelete(UpdateBookingRequestRequest.DaycareService.Addon.Delete.newBuilder()
                                .setId(addOnDetail.getDaycareAddOnDetailId())
                                .build())
                        .build())
                .build();
    }

    private static UpdateBookingRequestRequest.GroomingService buildDeleteGroomingAddOnDetail(
            UpdateAppointmentParams.Delete.BookingRequest.AddOn addOnDetail, long groomingServiceDetailId) {
        return UpdateBookingRequestRequest.GroomingService.newBuilder()
                .setService(UpdateGroomingServiceDetailRequest.newBuilder()
                        .setId(groomingServiceDetailId)
                        .build())
                .addAddonsV2(UpdateBookingRequestRequest.GroomingService.Addon.newBuilder()
                        .setDelete(UpdateBookingRequestRequest.GroomingService.Addon.Delete.newBuilder()
                                .setId(addOnDetail.getGroomingAddOnDetailId())
                                .build())
                        .build())
                .build();
    }

    private static long mustGetBoardingServiceDetailId(BookingRequestModel bookingRequest, long boardingAddOnDetailId) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .filter(service -> service.getBoarding().getAddonsList().stream()
                        .anyMatch(addOn -> addOn.getId() == boardingAddOnDetailId))
                .findFirst()
                .map(BookingRequestModel.Service::getBoarding)
                .map(BookingRequestModel.BoardingService::getService)
                .map(BoardingServiceDetailModel::getId)
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "Boarding service not found for boarding add-on detail id: " + boardingAddOnDetailId));
    }

    private static long mustGetDaycareServiceDetailId(BookingRequestModel bookingRequest, long daycareAddOnDetailId) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .filter(service -> service.getDaycare().getAddonsList().stream()
                        .anyMatch(addOn -> addOn.getId() == daycareAddOnDetailId))
                .findFirst()
                .map(BookingRequestModel.Service::getDaycare)
                .map(BookingRequestModel.DaycareService::getService)
                .map(DaycareServiceDetailModel::getId)
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "Daycare service not found for daycare add-on detail id: " + daycareAddOnDetailId));
    }

    private static long mustGetGroomingServiceDetailId(BookingRequestModel bookingRequest, long groomingAddOnDetailId) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .filter(service -> service.getGrooming().getAddonsList().stream()
                        .anyMatch(addOn -> addOn.getId() == groomingAddOnDetailId))
                .findFirst()
                .map(BookingRequestModel.Service::getGrooming)
                .map(BookingRequestModel.GroomingService::getService)
                .map(GroomingServiceDetailModel::getId)
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "Grooming service not found for grooming add-on detail id: " + groomingAddOnDetailId));
    }

    private static UpdateBookingRequestRequest.ServiceDetail.Delete buildDeleteServiceDetail(
            UpdateAppointmentParams.Delete.BookingRequest.Service serviceDetail) {
        var builder = UpdateBookingRequestRequest.ServiceDetail.Delete.newBuilder();
        switch (serviceDetail.getIdCase()) {
            case GROOMING_SERVICE_DETAIL_ID -> builder.setGrooming(serviceDetail.getGroomingServiceDetailId());
            case DAYCARE_SERVICE_DETAIL_ID -> builder.setDaycare(serviceDetail.getDaycareServiceDetailId());
            case BOARDING_SERVICE_DETAIL_ID -> builder.setBoarding(serviceDetail.getBoardingServiceDetailId());
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unsupported service detail id type");
        }
        return builder.build();
    }

    private static UpdateDaycareServiceDetailRequest toUpdateDaycareServiceDetailRequest(
            UpdateAppointmentParams.UpdateServiceDetailParams params) {
        var builder = UpdateDaycareServiceDetailRequest.newBuilder().setId(params.getPetDetailId());
        if (params.hasDateType()) {
            builder.setSpecificDates(StringListValue.newBuilder()
                    .addAllValues(params.getSpecificDatesList())
                    .build());
        }
        if (params.hasStartTime()) {
            builder.setStartTime(params.getStartTime());
        }
        if (params.hasEndTime()) {
            builder.setEndTime(params.getEndTime());
        }
        return builder.build();
    }

    private static UpdateBoardingServiceDetailRequest toUpdateBoardingServiceDetailRequest(
            UpdateAppointmentParams.UpdateServiceDetailParams params) {
        var builder = UpdateBoardingServiceDetailRequest.newBuilder().setId(params.getPetDetailId());
        if (params.hasStartDate()) {
            builder.setStartDate(params.getStartDate());
        }
        if (params.hasEndDate()) {
            builder.setEndDate(params.getEndDate());
        }
        if (params.hasStartTime()) {
            builder.setStartTime(params.getStartTime());
        }
        if (params.hasEndTime()) {
            builder.setEndTime(params.getEndTime());
        }
        return builder.build();
    }

    private static UpdateDaycareAddOnDetailRequest toUpdateDaycareAddOnDetailRequest(
            UpdateAppointmentParams.UpdateAddOnDetailParams params) {
        var builder = UpdateDaycareAddOnDetailRequest.newBuilder().setId(params.getPetDetailId());
        if (params.hasDateType()) {
            if (params.getDateType() == PetDetailDateType.PET_DETAIL_DATE_EVERYDAY) {
                builder.setIsEveryday(true);
            } else {
                builder.setSpecificDates(StringListValue.newBuilder()
                        .addAllValues(params.getSpecificDatesList())
                        .build());
            }
        }
        if (params.hasQuantityPerDay()) {
            builder.setQuantityPerDay(params.getQuantityPerDay());
        }
        return builder.build();
    }

    private static UpdateBoardingAddOnDetailRequest toUpdateBoardingAddOnDetailRequest(
            UpdateAppointmentParams.UpdateAddOnDetailParams params) {
        var builder = UpdateBoardingAddOnDetailRequest.newBuilder().setId(params.getPetDetailId());
        if (params.hasDateType()) {
            builder.setDateType(params.getDateType());
            if (params.getDateType() == PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
                builder.setSpecificDates(StringListValue.newBuilder()
                        .addAllValues(params.getSpecificDatesList())
                        .build());
            }
        }
        if (params.hasQuantityPerDay()) {
            builder.setQuantityPerDay(params.getQuantityPerDay());
        }
        if (params.hasStartDate()) {
            builder.setStartDate(params.getStartDate());
        }
        return builder.build();
    }

    private static UpdateBookingRequestRequest.Service toBoardingUpdateService(
            ReschedulePetFeedingMedicationParams.PetScheduleDef def, Long petDetailId) {
        return UpdateBookingRequestRequest.Service.newBuilder()
                .setBoarding(UpdateBookingRequestRequest.BoardingService.newBuilder()
                        .setService(UpdateBoardingServiceDetailRequest.newBuilder()
                                .setId(petDetailId)
                                .build())
                        .setFeedingsUpsert(CreateFeedingRequestList.newBuilder()
                                .addAllValues(def.getFeedingsList().stream()
                                        .map(FeedingConverter::toCreateFeedingRequest)
                                        .toList())
                                .build())
                        .setMedicationsUpsert(CreateMedicationRequestList.newBuilder()
                                .addAllValues(def.getMedicationsList().stream()
                                        .map(MedicationConverter::toCreateMedicationRequest)
                                        .toList())
                                .build())
                        .build())
                .build();
    }

    private static UpdateBookingRequestRequest.Service toDaycareUpdateService(
            ReschedulePetFeedingMedicationParams.PetScheduleDef def, Long petDetailId) {
        return UpdateBookingRequestRequest.Service.newBuilder()
                .setDaycare(UpdateBookingRequestRequest.DaycareService.newBuilder()
                        .setService(UpdateDaycareServiceDetailRequest.newBuilder()
                                .setId(petDetailId)
                                .build())
                        .setFeedingsUpsert(CreateFeedingRequestList.newBuilder()
                                .addAllValues(def.getFeedingsList().stream()
                                        .map(FeedingConverter::toCreateFeedingRequest)
                                        .toList())
                                .build())
                        .setMedicationsUpsert(CreateMedicationRequestList.newBuilder()
                                .addAllValues(def.getMedicationsList().stream()
                                        .map(MedicationConverter::toCreateMedicationRequest)
                                        .toList())
                                .build())
                        .build())
                .build();
    }
}
