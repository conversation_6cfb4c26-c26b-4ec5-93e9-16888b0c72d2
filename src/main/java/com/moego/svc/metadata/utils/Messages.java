/*
 * @since 2023-04-14 11:59:03
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.metadata.utils;

import lombok.AllArgsConstructor;

public class Messages {

    public static final String NAME_IS_ALREADY_IN_USE = "the name is already in use.";
    public static final String META_KEY_NOT_FOUND = "the meta key was not found.";
    public static final String UNKNOWN_OWNER_TYPE = "unknown owner type.";
    public static final Two INVALID_PERMISSION_LEVEL = new Two("invalid permission level %s for owner type %s.");
    public static final String START_SHOULD_BEFORE_END = "start at time should before end at time.";

    public static final String META_VALUE_NOT_FOUND = "the meta value was not found.";

    @AllArgsConstructor
    public static class Two {

        private final String template;

        public String format(Object a, Object b) {
            return String.format(template, a, b);
        }
    }
}
