package com.moego.svc.organization.client;

import com.moego.idl.models.offering.v1.ServiceCategoryModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.offering.v1.CreateServiceRequest;
import com.moego.idl.service.offering.v1.GetServiceListRequest;
import com.moego.idl.service.offering.v1.GetServiceListResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class OfferClient {
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementStub;

    public List<ServiceCategoryModel> listCategory(int templateCompanyId, List<Long> templateBusinessIds) {
        GetServiceListResponse serviceList = serviceManagementStub.getServiceList(GetServiceListRequest.newBuilder()
                .addAllBusinessIds(templateBusinessIds)
                .setTokenCompanyId(templateCompanyId)
                .build());
        if (serviceList.getCategoryListCount() == 0) {
            return null;
        }
        return serviceList.getCategoryListList();
    }

    // 获取service列表 包括service 和 add on
    public List<ServiceCategoryModel> getServiceList(Long companyId, ServiceType serviceType) {
        List<ServiceCategoryModel> inactiveServiceList = serviceManagementStub
                .getServiceList(GetServiceListRequest.newBuilder()
                        .setTokenCompanyId(companyId)
                        .setInactive(true) // 获取inactive类型的服务列表
                        .setServiceType(serviceType)
                        .build())
                .getCategoryListList();

        List<ServiceCategoryModel> activeServiceList = serviceManagementStub
                .getServiceList(GetServiceListRequest.newBuilder()
                        .setTokenCompanyId(companyId)
                        .setInactive(false) // 获取active类型的服务列表
                        .setServiceType(serviceType)
                        .build())
                .getCategoryListList();

        return Stream.concat(inactiveServiceList.stream(), activeServiceList.stream())
                .collect(Collectors.toList());
    }

    public void addService(CreateServiceRequest request) {
        serviceManagementStub.createService(request);
    }
}
