package com.moego.svc.organization.client;

import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.dto.InitDataGroomingResultDto;
import com.moego.server.grooming.params.GroomingInitDataParam;
import com.moego.svc.organization.dto.init.InitCompanyDTO;
import com.moego.svc.organization.dto.init.InitCustomerDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class GroomingClient {
    private final IGroomingAppointmentClient groomingAppointmentClient;

    public InitDataGroomingResultDto initGroomingModule(
            InitCompanyDTO initCompanyDTO, InitCustomerDTO customerResultDto) {
        try {
            // grooming模块初始化
            GroomingInitDataParam initDataParam = new GroomingInitDataParam();
            initDataParam.setBusinessId(initCompanyDTO.getBusinessId().intValue());
            initDataParam.setCompanyId(initCompanyDTO.getCompanyId());
            initDataParam.setTaxId(initCompanyDTO.getTaxId().intValue());
            initDataParam.setCustomerId(customerResultDto.getCustomerId().intValue());
            initDataParam.setOwnerStaffId(initCompanyDTO.getOwnerStaffId().intValue());
            initDataParam.setMiniPetId(customerResultDto.getMiniPetId().intValue());
            initDataParam.setMaxPetId(customerResultDto.getMaxPetId().intValue());
            return groomingAppointmentClient.groomingInitData(initDataParam);
        } catch (Exception e) {
            log.error("init grooming error:" + e.getMessage());
            return null;
        }
    }
}
