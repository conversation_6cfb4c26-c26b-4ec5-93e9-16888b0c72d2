package com.moego.svc.organization.helper;

import com.moego.common.enums.CompanyQuestionConst;

public class CompanyQuestionHelper {

    public static int getIntValueFromPetPerMonth(String petPerMonth) {
        return switch (petPerMonth) {
            case CompanyQuestionConst.PET_PER_MONTH_1000_PLUS -> 5;
            case CompanyQuestionConst.PET_PER_MONTH_500_1000 -> 4;
            case CompanyQuestionConst.PET_PER_MONTH_200_500 -> 3;
            case CompanyQuestionConst.PET_PER_MONTH_100_200 -> 2;
            case CompanyQuestionConst.PET_PER_MONTH_0_100 -> 1;
            default -> 0;
        };
    }

    public static int getIntValueFromLocationCount(String totalLocation) {
        return switch (totalLocation) {
            case CompanyQuestionConst.TOTAL_LOCATION_50_PLUS -> 5;
            case CompanyQuestionConst.TOTAL_LOCATION_11_50 -> 4;
            case CompanyQuestionConst.TOTAL_LOCATION_3_10 -> 3;
            case CompanyQuestionConst.TOTAL_LOCATION_2 -> 2;
            case CompanyQuestionConst.TOTAL_LOCATION_1 -> 1;
            default -> 0;
        };
    }

    public static int getIntValueFromVanCount(String totalVan) {
        return switch (totalVan) {
            case CompanyQuestionConst.TOTAL_VAN_100_PLUS -> 5;
            case CompanyQuestionConst.TOTAL_VAN_21_100 -> 4;
            case CompanyQuestionConst.TOTAL_VAN_5_20 -> 3;
            case CompanyQuestionConst.TOTAL_VAN_2_4 -> 2;
            case CompanyQuestionConst.TOTAL_VAN_1 -> 1;
            default -> 0;
        };
    }
}
