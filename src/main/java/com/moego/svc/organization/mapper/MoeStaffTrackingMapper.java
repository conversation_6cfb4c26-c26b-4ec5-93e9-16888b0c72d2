package com.moego.svc.organization.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.organization.dto.staff.StaffTrackingIndexDTO;
import com.moego.svc.organization.mapper.base.BaseMoeStaffTrackingMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeStaffTrackingMapper extends BaseMoeStaffTrackingMapper, DynamicDataSource<MoeStaffTrackingMapper> {
    // distinct 查询 x 之前的数据
    List<StaffTrackingIndexDTO> queryStaffTrackingBeforeDays(@Param("beforeDays") Integer beforeDays);

    // 物理删除 x 天之前的数据
    void deleteTrackingCode(
            @Param("companyId") Long companyId,
            @Param("staffId") Long staffId,
            @Param("beforeDays") Integer beforeDays);
}
