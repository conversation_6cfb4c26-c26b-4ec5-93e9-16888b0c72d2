package com.moego.svc.account.adapter.grpc;

import com.google.protobuf.util.Durations;
import com.moego.idl.models.account.v1.AccountImpersonateApprovalInstanceModel;
import com.moego.svc.account.repository.jooq.tables.records.AccountImpersonateApprovalInstanceRecord;
import com.moego.svc.account.utils.TimeUtils;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        uses = {TimeUtils.class},
        imports = {Durations.class})
public interface AccountImpersonateApprovalInstanceConverter {

    @Mapping(target = "maxAge", expression = "java(Durations.fromSeconds(record.getMaxAge()))")
    AccountImpersonateApprovalInstanceModel toModel(AccountImpersonateApprovalInstanceRecord record);

    @Mapping(target = "maxAge", expression = "java(Durations.toSeconds(model.getMaxAge()))")
    AccountImpersonateApprovalInstanceRecord toRecord(AccountImpersonateApprovalInstanceModel model);
}
