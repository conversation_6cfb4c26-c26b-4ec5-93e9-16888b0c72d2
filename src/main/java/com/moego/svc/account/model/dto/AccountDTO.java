package com.moego.svc.account.model.dto;

import com.moego.svc.account.model.enums.AccountStatusEnum;
import com.moego.svc.account.utils.Namespace;
import java.time.OffsetDateTime;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AccountDTO {

    private long id;

    private String email;

    private String phoneNumber;

    private String firstName;

    private String lastName;

    private String avatarPath;

    private String password;

    private AccountStatusEnum status;

    private Namespace namespace;

    private OffsetDateTime createdAt;

    private OffsetDateTime updatedAt;
}
