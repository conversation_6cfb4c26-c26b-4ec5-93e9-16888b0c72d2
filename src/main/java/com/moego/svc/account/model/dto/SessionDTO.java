package com.moego.svc.account.model.dto;

import com.moego.svc.account.model.enums.SessionStatusEnum;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SessionDTO {

    private long id;

    private long accountId;

    private String token;

    private String refererLink;

    private long refererSessionId;

    private String ip;

    private String userAgent;

    private String deviceId;

    private String impersonator;

    private Map<String, Object> sessionData;

    private SessionStatusEnum status;

    private boolean renewable;

    private Duration maxAge;

    private String source;

    private OffsetDateTime lastAccessedAt;

    private OffsetDateTime createdAt;

    private OffsetDateTime updatedAt;
}
