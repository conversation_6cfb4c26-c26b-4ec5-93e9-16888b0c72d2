package com.moego.svc.account.model.enums;

import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

public enum EncryptionAlgorithmEnum {
    MD5("MD5"),
    HMAC_SHA256("HmacSHA256");

    @Getter
    private final String name;

    EncryptionAlgorithmEnum(String name) {
        this.name = name;
    }

    public static EncryptionAlgorithmEnum of(String name) {
        return Arrays.stream(values())
                .filter(e -> Objects.equals(name, e.name))
                .findAny()
                .orElse(null);
    }
}
