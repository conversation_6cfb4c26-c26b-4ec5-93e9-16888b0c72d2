package com.moego.svc.customer.service;

import com.moego.common.distributed.LockManager;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.PhoneUtil;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.ListCustomerAddressRequest;
import com.moego.idl.service.customer.v1.InitPetInput;
import com.moego.idl.service.customer.v1.InitPetInputPet;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.params.CommonIdsParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.client.ICustomerAccountClient;
import com.moego.server.customer.client.ICustomerContactClient;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.client.IPetVaccineClient;
import com.moego.server.customer.dto.ClientAccountInfoDTO;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.dto.ob.BookOnlineConfigDTO;
import com.moego.server.grooming.dto.ob.BookOnlineGalleryDTO;
import com.moego.server.grooming.dto.ob.BookOnlineProfileDTO;
import com.moego.server.message.client.IMessageClient;
import com.moego.server.message.dto.BusinessTwilioNumberDTO;
import com.moego.svc.customer.adapter.convert.AddressConvert;
import com.moego.svc.customer.adapter.convert.LinkBusinessConvert;
import com.moego.svc.customer.adapter.convert.PetConvert;
import com.moego.svc.customer.adapter.convert.PetVaccineConvert;
import com.moego.svc.customer.model.dto.AddressDTO;
import com.moego.svc.customer.model.dto.LinkBusinessDTO;
import com.moego.svc.customer.model.dto.PetDTO;
import com.moego.svc.customer.model.dto.PetVaccineDTO;
import com.moego.svc.customer.utils.JsonUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class LinkBusinessService {
    private final ICustomerCustomerClient iCustomerCustomerClient;
    private final ICustomerAccountClient iCustomerAccountClient;
    private final ICustomerContactClient iCustomerContactClient;
    private final IPetClient iPetClient;
    private final IPetVaccineClient iPetVaccineClient;
    private final AddressService addressService;
    private final PetMetadataService petMetadataService;
    private final PetBreedService petBreedService;
    private final PetService petService;
    private final PetVaccineService petVaccineService;
    private final IBusinessBusinessClient businessClient;
    private final IGroomingOnlineBookingClient onlineBookingClient;
    private final LockManager lockManager;
    private final IMessageClient messageClient;

    private final BusinessCustomerAddressServiceBlockingStub businessCustomerAddressServiceBlockingStub;

    public void linkCustomerAndPet(Long accountId, Long needLinkCustomerId, String needLinkCustomerCode) {
        log.info(
                "[client-portal-link] link start. accountId:{}, needLinkCustomerId:{}, needLinkCustomerCode:{}",
                accountId,
                needLinkCustomerId,
                needLinkCustomerCode);
        Integer customerId = null;
        if (Objects.nonNull(needLinkCustomerId)) {
            customerId = Math.toIntExact(needLinkCustomerId);
        }
        if (Objects.nonNull(needLinkCustomerCode)) {
            customerId = iCustomerCustomerClient.getCustomerIdByCustomerCode(needLinkCustomerCode);
        }

        if (Objects.isNull(customerId)) {
            log.error(
                    "[client-portal-link] customerId is null. customerCode:{}, accountId:{}",
                    needLinkCustomerCode,
                    accountId);
            return;
        }

        // // 检查 customer 对应的 phoneNumber 是否一致
        // boolean phoneNumberIsInconsistent = checkPhoneNumber(accountId, customerId);
        // if (phoneNumberIsInconsistent) {
        //     return;
        // }

        // 查询当前 accountId 绑定过的数据
        List<BaseBusinessCustomerIdDTO> linkList = iCustomerCustomerClient.getLinkBusinessList(accountId);

        // 如果绑定过当前的 customerId，则跳过
        boolean customerIdAlreadyLinked =
                linkList.stream().map(BaseBusinessCustomerIdDTO::getCustomerId).anyMatch(customerId::equals);
        if (customerIdAlreadyLinked) {
            log.info(
                    "[client-portal-link] customer already linked. customerId:{}, accountId:{}", customerId, accountId);
            return;
        }

        boolean isNewCustomer = linkList.isEmpty() && noProfile(accountId);
        link(accountId, customerId, isNewCustomer);

        log.info(
                "[client-portal-link] link end. accountId:{}, needLinkCustomerId:{}, needLinkCustomerCode:{}",
                accountId,
                needLinkCustomerId,
                needLinkCustomerCode);
    }

    private void link(Long accountId, Integer customerId, boolean isNewCustomer) {
        String lockKey = lockManager.getResourceKey(LockManager.CLIENT_PORTAL_LINK, accountId);
        String lockValue = CommonUtil.getUuid();

        try {
            if (!lockManager.lock(lockKey, lockValue)) {
                throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "get lock failed for " + lockKey);
            }

            // 如果曾经没有绑定过，则为新用户，初始化并进行全绑定
            if (isNewCustomer) {
                log.info("[client-portal-link] new customer link. customerId:{}, accountId:{}", customerId, accountId);
                initNewCustomerAndLinkAll(accountId, customerId);
                return;
            }

            // 如果曾经绑定过，则为老用户，进行绑定
            log.info("[client-portal-link] existing customer link. customerId:{}, accountId:{}", customerId, accountId);
            linkCustomerAndPet(accountId, Collections.singletonList(customerId));
        } catch (Exception e) {
            log.error(
                    "[client-portal-link] link customer error. customerId:{}, accountId:{}", customerId, accountId, e);
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "link error for accountId: " + accountId);
        } finally {
            lockManager.unlock(lockKey, lockValue);
        }
    }

    private boolean checkPhoneNumber(Long accountId, Integer customerId) {
        boolean phoneNumberIsInconsistent = true;

        // 获取 customer 手机号
        CustomerInfoDto customerInfoDto = iCustomerCustomerClient.queryCustomerWithOwnPhone(customerId);
        if (Objects.isNull(customerInfoDto) || customerInfoDto.getPhoneNumber().isEmpty()) {
            log.info(
                    "[client-portal-link] customer phone is empty. customerId:{}, accountId:{}", customerId, accountId);
            return phoneNumberIsInconsistent;
        }

        // 获取 business 地区
        Integer businessId = customerInfoDto.getBusinessId();
        MoeBusinessDto businessInfo = businessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId).build());
        if (Objects.isNull(businessInfo)) {
            log.info(
                    "[client-portal-link] business info is empty. customerId:{}, accountId:{}, businessId:{}",
                    customerId,
                    accountId,
                    businessId);
            return phoneNumberIsInconsistent;
        }

        // 将 customer 手机号转为 E164 格式
        String phoneNumber = PhoneUtil.toE164Number(customerInfoDto.getPhoneNumber(), businessInfo.getCountry());

        // 匹配手机号
        ClientAccountInfoDTO accountInfoDTO = iCustomerAccountClient.queryAccount(Math.toIntExact(accountId));
        phoneNumberIsInconsistent = !Objects.equals(phoneNumber, accountInfoDTO.getPhoneNumber());
        if (phoneNumberIsInconsistent) {
            log.info(
                    "[client-portal-link] customer phone is inconsistent. customerId:{}, accountId:{}",
                    customerId,
                    accountId);
        }

        return phoneNumberIsInconsistent;
    }

    private boolean noProfile(Long accountId) {
        List<AddressDTO> addressList = addressService.getAddressList(accountId);
        List<PetDTO> petList = petService.getPetList(accountId);

        return CollectionUtils.isEmpty(addressList) && CollectionUtils.isEmpty(petList);
    }

    private void initNewCustomerAndLinkAll(Long accountId, Integer customerId) {
        // 创建用户数据
        initCustomer(accountId, customerId);
        // 创建宠物数据
        initPet(accountId, customerId);

        // 查询所有相同 phone 的 customerId 列表，添加绑定
        ClientAccountInfoDTO accountInfoDTO = iCustomerAccountClient.queryAccount(Math.toIntExact(accountId));
        String phoneNumber = accountInfoDTO.getPhoneNumber();
        List<Integer> needBindingCustomerIdList =
                iCustomerContactClient.getOtherCustomerIdListByPhoneNumber(phoneNumber, customerId);
        linkCustomerAndPet(accountId, needBindingCustomerIdList);
    }

    private void linkCustomerAndPet(Long accountId, List<Integer> customerIdList) {
        if (Objects.isNull(accountId) || customerIdList.isEmpty()) {
            return;
        }
        // 在旧库 moe_business_customer 关联 accountId
        boolean operateResult = iCustomerCustomerClient.linkCustomer(accountId, customerIdList);
        if (Boolean.FALSE.equals(operateResult)) {
            log.info(
                    "[client-portal-link] customer in same business already linked. accountId:{},  customerId:{}",
                    accountId,
                    customerIdList);
            return;
        }
        // 在旧库 moe_customer_pet 关联新库的 petId
        iPetClient.linkPet(bindPetId(accountId, customerIdList));
    }

    /**
     * get bind pet map
     *
     * @param accountId      account id
     * @param customerIdList customer id list
     * @return business pet id - client pet id
     */
    private Map<Integer, Long> bindPetId(Long accountId, List<Integer> customerIdList) {
        Map<Integer, Long> needBindingPetIdMap = new HashMap<>();
        Map<String, PetDTO> platformPetMap = petService.getPetMap(accountId);
        List<InitPetInput> initPetInputList = new ArrayList<>();

        List<CustomerPetDetailDTO> pets = iPetClient.getCustomerPetListByCustomerId(customerIdList);
        if (CollectionUtils.isEmpty(pets)) {
            return Map.of();
        }
        List<Integer> businessIds = pets.stream()
                .map(CustomerPetDetailDTO::getBusinessId)
                .distinct()
                .toList();
        Map<Integer, MoeBusinessDto> businessMap = businessClient.getOnlyBusinessInfoBatch(businessIds);
        pets.forEach(businessPet -> {
            // 如果 pet name + pet breed 相同，则进行关联
            String matchKey = petService.getMatchKey(businessPet.getPetName(), businessPet.getBreed());
            PetDTO platformPet = platformPetMap.get(matchKey);
            if (Objects.isNull(platformPet)) {
                InitPetInputPet pet = PetConvert.INSTANCE.dtoToInput(businessPet);
                pet.toBuilder()
                        .setWeightUnit(
                                businessMap.get(businessPet.getBusinessId()).getUnitOfWeight());
                InitPetInput.Builder builder = InitPetInput.newBuilder().setPet(pet);
                Map<Integer, List<VaccineBindingRecordDto>> vaccines =
                        iPetVaccineClient.getVaccineInfoByPetIdList(List.of(businessPet.getPetId()));
                List<VaccineBindingRecordDto> records = vaccines.get(businessPet.getPetId());
                if (!CollectionUtils.isEmpty(records)) {
                    builder.addAllVaccine(PetVaccineConvert.INSTANCE.dtoToInput(records));
                }
                initPetInputList.add(builder.build());
            } else {
                needBindingPetIdMap.put(businessPet.getPetId(), platformPet.getId());
            }
        });
        petService.initPet(accountId, initPetInputList);
        return needBindingPetIdMap;
    }

    private void initPet(Long accountId, Integer customerId) {
        // 获取 pet info，写入新库
        Map<Integer, Long> petIdMap = new HashMap<>();
        Map<String, Integer> metadataMap = petMetadataService.getAllSettingMap();
        Map<String, Integer> breedMap = petBreedService.getAllBreedMap();
        List<CustomerPetDetailDTO> pets =
                iPetClient.getCustomerPetListByCustomerId(Collections.singletonList(customerId));
        if (CollectionUtils.isEmpty(pets)) {
            return;
        }
        List<Integer> businessIds = pets.stream()
                .map(CustomerPetDetailDTO::getBusinessId)
                .distinct()
                .toList();
        Map<Integer, MoeBusinessDto> businessMap = businessClient.getOnlyBusinessInfoBatch(businessIds);
        pets.forEach(existPet -> {
            PetDTO newPet = PetConvert.INSTANCE.dtoToDto(existPet);
            if (PetService.DEFAULT_PET_TYPE.contains(existPet.getPetTypeId())) {
                newPet.setPetTypeMetadataId(existPet.getPetTypeId());
            } else {
                newPet.setPetTypeMetadataId(PetType.PET_TYPE_OTHER_VALUE);
            }
            newPet.setBehaviorMetadataId(metadataMap.getOrDefault(existPet.getBehavior(), 0));
            newPet.setHairLengthMetadataId(metadataMap.getOrDefault(existPet.getHairLength(), 0));
            newPet.setFixedMetadataId(metadataMap.getOrDefault(existPet.getFixed(), 0));
            String breed = existPet.getBreed();
            if (!breedMap.containsKey(breed)) {
                int customizeBreedId = petBreedService.saveCustomizeBreed(breed);
                breedMap.putIfAbsent(breed, customizeBreedId);
            }
            newPet.setBreedId(breedMap.getOrDefault(breed, 0));
            MoeBusinessDto business = businessMap.get(existPet.getBusinessId());
            newPet.setWeightUnitMetadataId(metadataMap.getOrDefault(business.getUnitOfWeight(), 0));

            Long petId = petService.savePet(accountId, newPet);
            petIdMap.put(existPet.getPetId(), petId);
        });

        // 获取 vaccine 信息
        iPetVaccineClient.getVaccineByCustomerId(customerId).forEach(customerPetVaccineDto -> {
            Long petId = petIdMap.get(customerPetVaccineDto.getPetId());
            if (Objects.isNull(petId)) {
                return;
            }
            List<PetVaccineDTO> petVaccineDTOList = customerPetVaccineDto.getVaccineList().stream()
                    .filter(vaccineInfoDto -> metadataMap.containsKey(vaccineInfoDto.getVaccineName()))
                    .map(vaccineInfoDto -> {
                        PetVaccineDTO petVaccineDTO = new PetVaccineDTO();
                        petVaccineDTO.setVaccineMetadataId(metadataMap.get(vaccineInfoDto.getVaccineName()));
                        petVaccineDTO.setDocumentUrlList(JsonUtil.toBean(vaccineInfoDto.getDocumentUrls(), List.class));
                        petVaccineDTO.setExpirationDate(vaccineInfoDto.getExpirationDate());
                        return petVaccineDTO;
                    })
                    .toList();
            petVaccineService.batchInsert(petId, petVaccineDTOList);
        });
    }

    private void initCustomer(Long accountId, Integer customerId) {
        // 获取 address list，写入新库
        var request = ListCustomerAddressRequest.newBuilder()
                .setCustomerId(customerId)
                .build();
        businessCustomerAddressServiceBlockingStub
                .listCustomerAddress(request)
                .getAddressesList()
                .forEach(address -> {
                    AddressDTO addressDTO = AddressConvert.INSTANCE.modelToDto(address);
                    addressService.saveAddress(accountId, addressDTO);
                });
    }

    public List<LinkBusinessDTO> getLinkBusiness(Long accountId) {
        List<BaseBusinessCustomerIdDTO> linkBusinessList = iCustomerCustomerClient.getLinkBusinessList(accountId);
        if (CollectionUtils.isEmpty(linkBusinessList)) {
            return Collections.emptyList();
        }
        List<Integer> businessIdList = linkBusinessList.stream()
                .map(BaseBusinessCustomerIdDTO::getBusinessId)
                .distinct()
                .toList();
        // Business info
        CommonIdsParams commonIdsParams = new CommonIdsParams();
        commonIdsParams.setIds(businessIdList);
        List<OBBusinessInfoDTO> businessInfoDTOList = businessClient.getBusinessInfoListForOB(commonIdsParams);
        Map<Integer, OBBusinessInfoDTO> businessInfoDTOMap =
                businessInfoDTOList.stream().collect(Collectors.toMap(OBBusinessInfoDTO::getId, Function.identity()));
        // Online booking profile
        List<BookOnlineProfileDTO> profileDTOList = onlineBookingClient.listBookOnlineProfile(businessIdList);
        Map<Integer, BookOnlineProfileDTO> profileDTOMap = profileDTOList.stream()
                .collect(Collectors.toMap(BookOnlineProfileDTO::getBusinessId, Function.identity()));
        // Online booking gallery image
        List<BookOnlineGalleryDTO> galleryDTOList =
                onlineBookingClient.listGalleryMaxSortImageByBusinessId(businessIdList);
        Map<Integer, BookOnlineGalleryDTO> galleryDTOMap = galleryDTOList.stream()
                .collect(Collectors.toMap(BookOnlineGalleryDTO::getBusinessId, Function.identity()));
        // Online booking config
        List<BookOnlineConfigDTO> configDTOList = onlineBookingClient.listBookOnlineConfig(businessIdList);
        Map<Integer, BookOnlineConfigDTO> businessConfigDTOMap = configDTOList.stream()
                .collect(Collectors.toMap(BookOnlineConfigDTO::getBusinessId, Function.identity()));
        // Business twilio number
        List<BusinessTwilioNumberDTO> twilioNumberDTOList = messageClient.listBusinessTwilioNumber(businessIdList);
        Map<Integer, BusinessTwilioNumberDTO> businessTwilioNumberDTOMap = twilioNumberDTOList.stream()
                .collect(Collectors.toMap(BusinessTwilioNumberDTO::getBusinessId, Function.identity()));
        return businessIdList.stream()
                .filter(businessId -> Objects.nonNull(businessConfigDTOMap.get(businessId)))
                .map(businessId -> new LinkBusinessDTO()
                        .setBusinessInfo(businessInfoDTOMap.get(businessId))
                        .setObProfile(LinkBusinessConvert.INSTANCE.build(profileDTOMap.get(businessId)))
                        .setObConfig(LinkBusinessConvert.INSTANCE.build(businessConfigDTOMap.get(businessId)))
                        .setObGalleryFirstImage(LinkBusinessConvert.INSTANCE.build(galleryDTOMap.get(businessId)))
                        .setBusinessTwilio(
                                LinkBusinessConvert.INSTANCE.build(businessTwilioNumberDTOMap.get(businessId))))
                .sorted((l1, l2) ->
                        l2.getObConfig().getIsEnable() - l1.getObConfig().getIsEnable())
                .toList();
    }
}
