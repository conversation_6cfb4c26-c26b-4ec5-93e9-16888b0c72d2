package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.svc.online.booking.mapper.GroomingServiceDetailDynamicSqlSupport.groomingServiceDetail;
import static com.moego.svc.online.booking.mapper.GroomingServiceDetailMapper.updateSelectiveColumns;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.GroomingServiceDetail;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.helper.params.MustGetCustomizedServiceParam;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import com.moego.svc.online.booking.mapper.GroomingServiceDetailMapper;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class GroomingServiceDetailService {

    private final GroomingServiceDetailMapper groomingServiceDetailMapper;
    private final GroomingAutoAssignService groomingAutoAssignService;
    private final ServiceHelper serviceHelper;
    private final BookingRequestMapper bookingRequestMapper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    @Nullable
    public GroomingServiceDetail get(long id) {
        return groomingServiceDetailMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Must get existed record by id, not include deleted record, throw exception if not found.
     *
     * @param id groom service detail id
     * @return groom service detail
     */
    public GroomingServiceDetail mustGet(long id) {
        var entity = get(id);
        if (entity == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "GroomingServiceDetail not found: " + id);
        }
        return entity;
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(GroomingServiceDetail entity) {

        populate(entity);

        groomingServiceDetailMapper.insertSelective(entity);

        return entity.getId();
    }

    private void populate(GroomingServiceDetail entity) {

        check(entity);

        if (entity.getServiceTime() == null || entity.getServicePrice() == null) {

            var bookingRequest = bookingRequestMapper
                    .selectByPrimaryKey(entity.getBookingRequestId())
                    .orElseThrow(() -> ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR, "BookingRequest not found: " + entity.getBookingRequestId()));

            var builder = MustGetCustomizedServiceParam.builder();
            builder.serviceId(entity.getServiceId());
            builder.companyId(bookingRequest.getCompanyId());
            builder.businessId(bookingRequest.getBusinessId());
            builder.petId(entity.getPetId());
            if (isNormal(entity.getStaffId())) {
                builder.staffId(entity.getStaffId());
            }

            var service = serviceHelper.mustGetCustomizedService(builder.build());

            entity.setServiceTime(service.getDuration());
            entity.setServicePrice(BigDecimal.valueOf(service.getPrice()));
            entity.setTaxId(service.getTaxId());
        }

        int dayCount = 0;

        if (entity.getStartTime() != null && entity.getServiceTime() != null) {
            dayCount = (entity.getStartTime() + entity.getServiceTime()) / 1440;
            var endTime = (entity.getStartTime() + entity.getServiceTime()) % 1440;
            entity.setEndTime(endTime);
        }

        if (entity.getStartDate() != null) {
            entity.setEndDate(
                    LocalDate.parse(entity.getStartDate()).plusDays(dayCount).toString());
        }
    }

    private static void check(GroomingServiceDetail entity) {
        if (!isNormal(entity.getBookingRequestId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is invalid");
        }
        if (!isNormal(entity.getPetId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petId is invalid");
        }
        if (!isNormal(entity.getServiceId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "serviceId is invalid");
        }
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return groomingServiceDetailMapper.update(c -> c.set(groomingServiceDetail.deletedAt)
                .equalTo(new Date())
                .where(groomingServiceDetail.id, isEqualTo(id))
                .and(groomingServiceDetail.deletedAt, isNull()));
    }

    /**
     * List boarding service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestId bookingRequestId
     * @return list of boarding service detail
     */
    public List<GroomingServiceDetail> listByBookingRequestId(long bookingRequestId) {
        return groomingServiceDetailMapper.select(
                c -> c.where(groomingServiceDetail.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(groomingServiceDetail.deletedAt, isNull()));
    }

    /**
     * List boarding service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of bookingRequestId
     * @return list of boarding service detail
     */
    public List<GroomingServiceDetail> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return List.of();
        }
        return groomingServiceDetailMapper.select(
                c -> c.where(groomingServiceDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(groomingServiceDetail.deletedAt, isNull()));
    }

    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        GroomingServiceDetail update = new GroomingServiceDetail();
        update.setDeletedAt(new Date());
        return groomingServiceDetailMapper.update(c -> updateSelectiveColumns(update, c)
                .where(groomingServiceDetail.id, isIn(ids))
                .and(groomingServiceDetail.deletedAt, isNull()));
    }

    public int update(GroomingServiceDetail detail) {

        detail.setUpdatedAt(new Date());

        return groomingServiceDetailMapper.update(c -> updateSelectiveColumns(detail, c)
                .where(groomingServiceDetail.id, isEqualTo(detail.getId()))
                .and(groomingServiceDetail.deletedAt, isNull()));
    }

}
