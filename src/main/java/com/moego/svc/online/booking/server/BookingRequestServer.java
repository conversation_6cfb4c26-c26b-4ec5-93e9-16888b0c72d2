package com.moego.svc.online.booking.server;

import static com.moego.common.enums.ServiceItemEnum.getMainServiceItemType;
import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.utils.PricingRuleUtils.getUsingPricingRuleService;
import static java.lang.Math.toIntExact;
import static java.util.Comparator.comparing;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsFirst;
import static java.util.Comparator.nullsLast;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.springframework.util.CollectionUtils.firstElement;
import static org.springframework.util.CollectionUtils.isEmpty;
import static org.springframework.util.CollectionUtils.lastElement;

import com.google.protobuf.Int64Value;
import com.google.protobuf.util.Timestamps;
import com.google.type.Date;
import com.moego.common.dto.notificationDto.NotificationExtraOBReqestDto;
import com.moego.common.dto.notificationDto.NotificationExtraWaitlistSignedUpDto;
import com.moego.common.enums.AppointmentEventEnum;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.params.CustomerIdsParams;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.appointment.v1.AppointmentCreateForOnlineBookingDef;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteCreateDef;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.AppointmentSource;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedEvaluationDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.models.online_booking.v1.AcceptClientType;
import com.moego.idl.models.online_booking.v1.AutomationConditionDef;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.PetToLodgingDef;
import com.moego.idl.models.online_booking.v1.ProfileUpdateCondition;
import com.moego.idl.models.online_booking.v1.VaccineStatusCondition;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.CreateAppointmentForOnlineBookingRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo;
import com.moego.idl.service.offering.v2.CalculatePricingRuleRequest;
import com.moego.idl.service.offering.v2.PricingRuleServiceGrpc;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestV2Request;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestV2Response;
import com.moego.idl.service.online_booking.v1.AutoAssignRequest;
import com.moego.idl.service.online_booking.v1.AutoAssignResponse;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.CheckWaitlistAvailableTaskRequest;
import com.moego.idl.service.online_booking.v1.CheckWaitlistAvailableTaskResponse;
import com.moego.idl.service.online_booking.v1.CountBookingRequestByFilterRequest;
import com.moego.idl.service.online_booking.v1.CountBookingRequestByFilterResponse;
import com.moego.idl.service.online_booking.v1.CountBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.CountBookingRequestsResponse;
import com.moego.idl.service.online_booking.v1.CreateBoardingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.CreateDaycareAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateGroomingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.DeclineBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.DeclineBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.GetAutoAssignRequest;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.GetBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.ListBookingRequestIdRequest;
import com.moego.idl.service.online_booking.v1.ListBookingRequestIdResponse;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsResponse;
import com.moego.idl.service.online_booking.v1.ListWaitlistsRequest;
import com.moego.idl.service.online_booking.v1.ListWaitlistsResponse;
import com.moego.idl.service.online_booking.v1.MoveBookingRequestToWaitlistRequest;
import com.moego.idl.service.online_booking.v1.MoveBookingRequestToWaitlistResponse;
import com.moego.idl.service.online_booking.v1.PreviewBookingRequestPricingRequest;
import com.moego.idl.service.online_booking.v1.PreviewBookingRequestPricingResponse;
import com.moego.idl.service.online_booking.v1.ReplaceBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.ReplaceBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.RetryFailedEventsRequest;
import com.moego.idl.service.online_booking.v1.RetryFailedEventsResponse;
import com.moego.idl.service.online_booking.v1.SyncBookingRequestFromAppointmentRequest;
import com.moego.idl.service.online_booking.v1.SyncBookingRequestFromAppointmentResponse;
import com.moego.idl.service.online_booking.v1.TriggerBookingRequestAutoAcceptedRequest;
import com.moego.idl.service.online_booking.v1.TriggerBookingRequestAutoAcceptedResponse;
import com.moego.idl.service.online_booking.v1.UpdateBoardingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestStatusRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestStatusResponse;
import com.moego.idl.service.online_booking.v1.UpdateDaycareAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateGroomingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.WaitlistExtra;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.Tx;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.grooming.api.IBookOnlineDepositService;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.api.IOBService;
import com.moego.server.grooming.api.IOrderDecouplingFlowMarkerService;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.dto.ob.OBRequestSyncDTO;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.server.message.api.INotificationService;
import com.moego.server.message.params.OnlineBookWaitingNotifyParams;
import com.moego.server.message.params.notification.NotificationWaitlistSignedUpParams;
import com.moego.server.message.params.notification.ob.NotificationOBRequestReceivedParams;
import com.moego.server.payment.api.IPaymentPreAuthService;
import com.moego.server.payment.api.IPaymentRefundService;
import com.moego.server.payment.api.IPaymentStripeService;
import com.moego.server.payment.params.AppointmentEventParams;
import com.moego.server.payment.params.CreateRefundByPaymentIdParams;
import com.moego.svc.online.booking.dto.BookingRequestFilterDTO;
import com.moego.svc.online.booking.entity.BoardingAddOnDetail;
import com.moego.svc.online.booking.entity.BoardingServiceDetail;
import com.moego.svc.online.booking.entity.BoardingServiceWaitlist;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.entity.BookingRequestAppointmentMapping;
import com.moego.svc.online.booking.entity.DaycareServiceDetail;
import com.moego.svc.online.booking.entity.DaycareServiceWaitlist;
import com.moego.svc.online.booking.entity.EvaluationTestDetail;
import com.moego.svc.online.booking.entity.GroomingAddOnDetail;
import com.moego.svc.online.booking.entity.GroomingServiceDetail;
import com.moego.svc.online.booking.eventbus.OnlineBookingProducer;
import com.moego.svc.online.booking.helper.BusinessHelper;
import com.moego.svc.online.booking.helper.CustomerHelper;
import com.moego.svc.online.booking.helper.OfferingHelper;
import com.moego.svc.online.booking.helper.OrderHelper;
import com.moego.svc.online.booking.helper.OrganizationHelper;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.helper.VaccineHelper;
import com.moego.svc.online.booking.helper.WaitlistRedisHelper;
import com.moego.svc.online.booking.listener.SyncBookingRequestListener;
import com.moego.svc.online.booking.mapstruct.AutoAssignConverter;
import com.moego.svc.online.booking.mapstruct.AutomationConverter;
import com.moego.svc.online.booking.mapstruct.BoardingAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.BoardingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.BookingRequestConverter;
import com.moego.svc.online.booking.mapstruct.BookingRequestNoteConverter;
import com.moego.svc.online.booking.mapstruct.DaycareAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.DaycareServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.DogWalkingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.EvaluationTestDetailConverter;
import com.moego.svc.online.booking.mapstruct.FeedingConverter;
import com.moego.svc.online.booking.mapstruct.GroomingAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.GroomingAutoAssignConverter;
import com.moego.svc.online.booking.mapstruct.GroomingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.GroupClassServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.MedicationConverter;
import com.moego.svc.online.booking.mapstruct.PageConverter;
import com.moego.svc.online.booking.service.AutoAssignService;
import com.moego.svc.online.booking.service.AutomationSettingService;
import com.moego.svc.online.booking.service.BoardingAddOnDetailService;
import com.moego.svc.online.booking.service.BoardingServiceDetailService;
import com.moego.svc.online.booking.service.BookingRequestAppointmentMappingService;
import com.moego.svc.online.booking.service.BookingRequestModifyService;
import com.moego.svc.online.booking.service.BookingRequestNoteService;
import com.moego.svc.online.booking.service.BookingRequestService;
import com.moego.svc.online.booking.service.DaycareAddOnDetailService;
import com.moego.svc.online.booking.service.DaycareServiceDetailService;
import com.moego.svc.online.booking.service.DogWalkingServiceDetailService;
import com.moego.svc.online.booking.service.EvaluationTestDetailService;
import com.moego.svc.online.booking.service.FeedingMedicationService;
import com.moego.svc.online.booking.service.FeedingService;
import com.moego.svc.online.booking.service.FulfillmentService;
import com.moego.svc.online.booking.service.GroomingAddOnDetailService;
import com.moego.svc.online.booking.service.GroomingAutoAssignService;
import com.moego.svc.online.booking.service.GroomingServiceDetailService;
import com.moego.svc.online.booking.service.GroupClassServiceDetailService;
import com.moego.svc.online.booking.service.LodgingService;
import com.moego.svc.online.booking.service.MedicationService;
import com.moego.svc.online.booking.service.MembershipService;
import com.moego.svc.online.booking.service.OBAvailableDateTimeService;
import com.moego.svc.online.booking.service.PetDetailService;
import com.moego.svc.online.booking.service.WaitlistService;
import com.moego.svc.online.booking.utils.PricingRuleUtils;
import com.moego.svc.online.booking.utils.PricingRuleUtils.FulfillmentLineItem;
import com.moego.svc.online.booking.utils.ProtobufUtil;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingRequestServer extends BookingRequestServiceGrpc.BookingRequestServiceImplBase {

    private final BookingRequestService bookingRequestService;
    private final WaitlistService waitlistService;
    private final WaitlistRedisHelper waitlistRedisHelper;
    private final BookingRequestNoteService bookingRequestNoteService;
    private final DaycareServiceDetailService daycareServiceDetailService;
    private final BoardingServiceDetailService boardingServiceDetailService;
    private final EvaluationTestDetailService evaluationTestDetailService;
    private final DaycareAddOnDetailService daycareAddOnDetailService;
    private final BoardingAddOnDetailService boardingAddOnDetailService;
    private final FeedingService feedingService;
    private final MedicationService medicationService;
    private final DogWalkingServiceDetailService dogWalkingServiceDetailService;
    private final GroomingServiceDetailService groomingServiceDetailService;
    private final GroomingAddOnDetailService groomingAddOnDetailService;
    private final GroomingAutoAssignService groomingAutoAssignService;
    private final SyncBookingRequestListener syncBookingRequestListener;
    private final StringRedisTemplate stringRedisTemplate;
    private final BookingRequestModifyService bookingRequestModifyService;
    private final FeedingMedicationService feedingMedicationService;
    private final AutoAssignService autoAssignService;
    private final LodgingService lodgingService;
    private final PetDetailService petDetailService;
    private final OnlineBookingProducer onlineBookingProducer;
    private final IBookOnlineDepositService depositApi;
    private final IPaymentRefundService refundApi;
    private final BookingRequestAppointmentMappingService bookingRequestAppointmentMappingService;
    private final IPaymentStripeService paymentStripeApi;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final OrderHelper orderHelper;
    private final IPaymentPreAuthService preAuthApi;
    private final OrganizationHelper organizationHelper;
    private final ServiceHelper serviceHelper;
    private final VaccineHelper vaccineHelper;
    private final IOBService obApi;
    private final AutomationSettingService automationSettingService;
    private final CustomerHelper customerHelper;
    private final INotificationService notificationApi;
    private final ICustomerProfileRequestService profileRequestApi;
    private final IBookOnlineQuestionService bookOnlineQuestionApi;
    private final IOrderDecouplingFlowMarkerService orderDecouplingFlowMarkerServiceApi;
    private final MembershipService membershipService;
    private final GroupClassServiceDetailService groupClassServiceDetailService;
    private final FulfillmentService fulfillmentService;
    private final OfferingHelper offeringHelper;
    private final PricingRuleServiceGrpc.PricingRuleServiceBlockingStub pricingRuleStub;
    private final BusinessHelper businessHelper;
    private final OBAvailableDateTimeService obAvailableDateTimeService;

    @Override
    public void createBookingRequest(CreateBookingRequestRequest request, StreamObserver<Int64Value> responseObserver) {

        // 1. 创建 BookingRequest
        long id = createBookingRequest(request);
        onlineBookingProducer.pushOnlineBookingSubmittedEvent(id);
        if (request.getNeedCreateOrder()) {
            createOrder(request, id);
        }
        responseObserver.onNext(Int64Value.of(id));
        responseObserver.onCompleted();
    }

    private long createBookingRequest(CreateBookingRequestRequest request) {

        Long id = Tx.doInTransaction(() -> {
            var bookingRequestId = bookingRequestService.insert(toBookingRequest(request));

            for (var service : request.getServicesList()) {
                addService(bookingRequestId, service);
            }

            if (request.hasComment() && StringUtils.hasText(request.getComment())) {
                bookingRequestNoteService.saveBookingRequestNote(
                        request.getCompanyId(), request.getCustomerId(), bookingRequestId, request.getComment());
            }

            return bookingRequestId;
        });

        // 这个操作需要异步并行查询 service detail 数据，需要在事务外执行
        refreshBookingRequest(id);

        return id;
    }

    @Override
    public void getBookingRequest(
            GetBookingRequestRequest request, StreamObserver<GetBookingRequestResponse> responseObserver) {
        var bookingRequestModel = bookingRequestService.getBookingRequestModel(request);
        if (bookingRequestModel == null) {
            responseObserver.onNext(GetBookingRequestResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var builder = bookingRequestModel.toBuilder();

        var note = bookingRequestNoteService.getBookingRequestNoteByBookingRequestId(
                bookingRequestModel.getCompanyId(), request.getId());
        if (note != null) {
            builder.setComment(BookingRequestNoteConverter.INSTANCE.entityToModel(note));
        }

        responseObserver.onNext(GetBookingRequestResponse.newBuilder()
                .setBookingRequest(builder.build())
                .build());
        responseObserver.onCompleted();
    }

    /**
     * List services by booking request ids.
     *
     * @param bookingRequests booking requests
     * @param models          associated models
     * @return bookingRequestId -> list of services
     */
    private Map<Long, List<BookingRequestModel.Service>> listServices(
            List<BookingRequest> bookingRequests, Set<BookingRequestAssociatedModel> models) {
        return bookingRequestService.listServiceDetail(bookingRequests, models);
    }

    private static String toDateString(Date date) {
        return ProtobufUtil.toLocalDate(date).toString();
    }

    @Override
    public void listBookingRequests(
            ListBookingRequestsRequest request, StreamObserver<ListBookingRequestsResponse> responseObserver) {
        BookingRequestFilterDTO filter = convertBookingRequestToFilter(request);
        // 判断是否内存排序
        boolean isQueryOnlyAvailable = request.hasIsWaitlistAvailable() && request.getIsWaitlistAvailable();
        boolean isOrderPriceDesc = request.hasOrderPriceDesc() && request.getOrderPriceDesc();

        Pagination pagination = PageConverter.INSTANCE.toPagination(request.getPagination());
        if (isQueryOnlyAvailable || isOrderPriceDesc) {
            pagination = Pagination.ALL;
        }

        // query booking request models
        Pair<List<BookingRequest>, Pagination> pair = bookingRequestService.listByBusinessFilter(filter, pagination);
        Set<BookingRequestAssociatedModel> models = new HashSet<>(request.getAssociatedModelsList());
        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(pair.getFirst(), models);
        List<BookingRequestModel> bookingRequestModelList = pair.getFirst().stream()
                .map(bookingRequest -> BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                        .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .build())
                .collect(Collectors.toCollection(ArrayList::new));
        pagination = pair.getSecond();
        if (!request.getStatusesList().contains(BookingRequestStatus.WAIT_LIST)) {
            responseObserver.onNext(ListBookingRequestsResponse.newBuilder()
                    .addAllBookingRequests(bookingRequestModelList)
                    .addAllWaitlistExtras(List.of())
                    .setPagination(PageConverter.INSTANCE.toResponse(pagination))
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // query isAvailable
        var waitlistExtras = waitlistService.generateWaitlistExtras(bookingRequestModelList);

        // filter bookingRequestModelList to waitlist available list
        if (isQueryOnlyAvailable) {
            var availableWaitlistIds = waitlistExtras.stream()
                    .filter(WaitlistExtra::getIsAvailable)
                    .map(WaitlistExtra::getId)
                    .toList();
            bookingRequestModelList = bookingRequestModelList.stream()
                    .filter(waitlist -> availableWaitlistIds.contains(waitlist.getId()))
                    .collect(Collectors.toCollection(ArrayList::new));
        }
        // order by list
        if (isOrderPriceDesc) {
            bookingRequestModelList.sort(
                    Comparator.comparingDouble((BookingRequestModel brm) -> brm.getServicesList().stream()
                                    .mapToDouble(s -> {
                                        double sum = 0;
                                        if (s.hasDaycare()) {
                                            sum += s.getDaycare().getService().getServicePrice();
                                        }
                                        if (s.hasBoarding()) {
                                            sum += s.getBoarding().getService().getServicePrice();
                                        }
                                        return sum;
                                    })
                                    .sum())
                            .reversed());
        }
        // 根据分页值手动分页
        if (isQueryOnlyAvailable || isOrderPriceDesc) {
            var paginationOrigin = PageConverter.INSTANCE.toPagination(request.getPagination());
            var pageNum = paginationOrigin.pageNum();
            var pageSize = paginationOrigin.pageSize();
            int total = bookingRequestModelList.size();
            int fromIndex = Math.min((pageNum - 1) * pageSize, total);
            int toIndex = Math.min(fromIndex + pageSize, total);
            bookingRequestModelList = bookingRequestModelList.subList(fromIndex, toIndex);
            pagination = pagination.toBuilder()
                    .pageNum(pageNum)
                    .pageSize(pageSize)
                    .total(total)
                    .build();
        }
        // query comment
        bookingRequestModelList = bookingRequestNoteService.fillWaitlistModelComments(bookingRequestModelList);

        responseObserver.onNext(ListBookingRequestsResponse.newBuilder()
                .addAllBookingRequests(bookingRequestModelList)
                .addAllWaitlistExtras(waitlistExtras)
                .setPagination(PageConverter.INSTANCE.toResponse(pagination))
                .build());
        responseObserver.onCompleted();
    }

    private static BookingRequestFilterDTO convertBookingRequestToFilter(ListBookingRequestsRequest request) {
        var filter = new BookingRequestFilterDTO()
                .setBusinessIds(Stream.concat(Stream.of(request.getBusinessId()), request.getBusinessIdsList().stream())
                        .filter(id -> id != 0)
                        .toList())
                .setStatuses(request.getStatusesList().stream()
                        .map(BookingRequestStatus::getNumber)
                        .toList())
                .setStartDate(request.hasStartDate() ? toDateString(request.getStartDate()) : null)
                .setEndDate(request.hasEndDate() ? toDateString(request.getEndDate()) : null)
                .setLatestEndDate(request.hasLatestEndDate() ? toDateString(request.getLatestEndDate()) : null)
                .setOrderBys(request.getOrderBysList())
                .setServiceItems(request.getServiceItemsList())
                .setServiceTypeIncludes(request.getServiceTypeIncludesList())
                .setCompanyId(request.hasCompanyId() ? request.getCompanyId() : null)
                .setCustomerIds(request.getCustomerIdList())
                .setAppointmentIds(request.getAppointmentIdsList())
                .setPaymentStatuses(request.getPaymentStatusesList())
                .setIsWaitlistExpired(request.hasIsWaitlistExpired() && request.getIsWaitlistExpired());
        // 这里是 source 和 sources 两个字段的判断
        if (request.hasSource() || request.getSourcesCount() > 0) {
            Set<BookingRequestModel.Source> sources = new HashSet<>(request.getSourcesList());
            if (request.hasSource()) {
                sources.add(request.getSource());
            }
            filter.setSources(sources.stream().toList());
        }
        if (isEmpty(filter.getSources())) {
            filter.setSources(List.of(BookingRequestModel.Source.OB, BookingRequestModel.Source.MEMBERSHIP));
        }
        if (request.hasKeywords()) {
            filter.setKeyword(request.getKeywords());
        }
        return filter;
    }

    @Override
    public void listWaitlists(ListWaitlistsRequest request, StreamObserver<ListWaitlistsResponse> responseObserver) {
        var filter = convertBookingRequestToFilter(BookingRequestConverter.INSTANCE.convertRequest(request));
        filter.setStatuses(List.of(BookingRequestStatus.WAIT_LIST_VALUE));
        // 判断是否内存排序
        boolean isQueryOnlyAvailable = request.hasIsWaitlistAvailable() && request.getIsWaitlistAvailable();
        boolean isOrderPriceDesc = request.hasOrderPriceDesc() && request.getOrderPriceDesc();

        Pagination pagination = PageConverter.INSTANCE.toPagination(request.getPagination());
        if (isQueryOnlyAvailable || isOrderPriceDesc) {
            pagination = Pagination.ALL;
        }

        // query booking request models
        Pair<List<BookingRequest>, Pagination> pair = bookingRequestService.listByBusinessFilter(filter, pagination);
        Set<BookingRequestAssociatedModel> models = new HashSet<>(request.getAssociatedModelsList());
        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(pair.getFirst(), models);
        List<BookingRequestModel> bookingRequestModelList = pair.getFirst().stream()
                .map(bookingRequest -> BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                        .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .build())
                .collect(Collectors.toCollection(ArrayList::new));
        pagination = pair.getSecond();

        // query isAvailable
        var waitlistExtras = waitlistService.generateWaitlistExtras(bookingRequestModelList);

        // filter bookingRequestModelList to waitlist available list
        if (isQueryOnlyAvailable) {
            var availableWaitlistIds = waitlistExtras.stream()
                    .filter(WaitlistExtra::getIsAvailable)
                    .map(WaitlistExtra::getId)
                    .toList();
            bookingRequestModelList = bookingRequestModelList.stream()
                    .filter(waitlist -> availableWaitlistIds.contains(waitlist.getId()))
                    .collect(Collectors.toCollection(ArrayList::new));
        }
        // order by list
        if (isOrderPriceDesc) {
            bookingRequestModelList.sort(
                    Comparator.comparingDouble((BookingRequestModel brm) -> brm.getServicesList().stream()
                                    .mapToDouble(s -> {
                                        double sum = 0;
                                        if (s.hasDaycare()) {
                                            sum += s.getDaycare().getService().getServicePrice();
                                        }
                                        if (s.hasBoarding()) {
                                            sum += s.getBoarding().getService().getServicePrice();
                                        }
                                        return sum;
                                    })
                                    .sum())
                            .reversed());
        }
        // 根据分页值手动分页
        if (isQueryOnlyAvailable || isOrderPriceDesc) {
            var paginationOrigin = PageConverter.INSTANCE.toPagination(request.getPagination());
            var pageNum = paginationOrigin.pageNum();
            var pageSize = paginationOrigin.pageSize();
            int total = bookingRequestModelList.size();
            int fromIndex = Math.min((pageNum - 1) * pageSize, total);
            int toIndex = Math.min(fromIndex + pageSize, total);
            bookingRequestModelList = bookingRequestModelList.subList(fromIndex, toIndex);
            pagination = pagination.toBuilder()
                    .pageNum(pageNum)
                    .pageSize(pageSize)
                    .total(total)
                    .build();
        }
        // query comment
        bookingRequestModelList = bookingRequestNoteService.fillWaitlistModelComments(bookingRequestModelList);

        responseObserver.onNext(ListWaitlistsResponse.newBuilder()
                .addAllBookingRequests(bookingRequestModelList)
                .addAllWaitlistExtras(waitlistExtras)
                .setPagination(PageConverter.INSTANCE.toResponse(pagination))
                .build());
        responseObserver.onCompleted();
    }

    private void addService(long bookingRequestId, CreateBookingRequestRequest.Service service) {
        switch (service.getServiceCase()) {
            case GROOMING -> addGroomingService(service.getGrooming(), bookingRequestId);
            case BOARDING -> addBoardingService(service.getBoarding(), bookingRequestId);
            case DAYCARE -> addDaycareService(service.getDaycare(), bookingRequestId);
            case EVALUATION -> addEvaluationService(service.getEvaluation(), bookingRequestId);
            case DOG_WALKING -> addDogWalkingService(service.getDogWalking(), bookingRequestId);
            case GROUP_CLASS -> addGroupClassService(service.getGroupClass(), bookingRequestId);
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Invalid service type: " + service.getServiceCase());
        }
    }

    private void addGroupClassService(CreateBookingRequestRequest.GroupClassService groupClass, long bookingRequestId) {
        var serviceDetail = GroupClassServiceDetailConverter.INSTANCE.createRequestToEntity(groupClass.getService());
        serviceDetail.setBookingRequestId(bookingRequestId);
        groupClassServiceDetailService.insert(serviceDetail);
    }

    private void addDogWalkingService(CreateBookingRequestRequest.DogWalkingService dogWalking, long bookingRequestId) {
        var dogWalkingServiceDetail =
                DogWalkingServiceDetailConverter.INSTANCE.createRequestToEntity(dogWalking.getService());
        dogWalkingServiceDetail.setBookingRequestId(bookingRequestId);
        dogWalkingServiceDetailService.insert(dogWalkingServiceDetail);
    }

    private void addGroomingService(CreateBookingRequestRequest.GroomingService grooming, long bookingRequestId) {
        GroomingServiceDetail groomingServiceDetail =
                GroomingServiceDetailConverter.INSTANCE.createRequestToEntity(grooming.getService());
        groomingServiceDetail.setBookingRequestId(bookingRequestId);

        var serviceDetailId = groomingServiceDetailService.insert(groomingServiceDetail);

        for (var addon : grooming.getAddonsV2List()) {
            addGroomingAddon(addon, bookingRequestId, serviceDetailId);
        }
    }

    private void addEvaluationService(CreateBookingRequestRequest.EvaluationService evaluation, long bookingRequestId) {
        EvaluationTestDetail evaluationTestDetail =
                EvaluationTestDetailConverter.INSTANCE.createRequestToEntity(evaluation.getService());
        evaluationTestDetail.setBookingRequestId(bookingRequestId);
        evaluationTestDetailService.insert(evaluationTestDetail);
    }

    private void addDaycareService(CreateBookingRequestRequest.DaycareService daycare, long bookingRequestId) {
        long serviceDetailId = daycareServiceDetailService.insert(buildDaycareServiceDetail(daycare, bookingRequestId));

        for (var addon : daycare.getAddonsList()) {
            addDaycareAddon(addon, bookingRequestId, serviceDetailId);
        }

        var feedings = new ArrayList<>(daycare.getFeedingsList());
        var medications = new ArrayList<>(daycare.getMedicationsList());
        if (daycare.hasFeeding()) {
            feedings.add(daycare.getFeeding());
        }
        if (daycare.hasMedication()) {
            medications.add(daycare.getMedication());
        }
        feedingService.insertMultiple(FeedingConverter.INSTANCE.createRequestToEntity(
                feedings, bookingRequestId, serviceDetailId, ServiceItemType.DAYCARE));
        medicationService.insertMultiple(MedicationConverter.INSTANCE.createRequestToEntity(
                medications, bookingRequestId, serviceDetailId, ServiceItemType.DAYCARE));

        if (daycare.hasWaitlist()) {
            var waitlist = daycare.getWaitlist();
            var daycareServiceWaitlist = new DaycareServiceWaitlist();
            daycareServiceWaitlist.setBookingRequestId(bookingRequestId);
            daycareServiceWaitlist.setServiceDetailId(serviceDetailId);
            daycareServiceWaitlist.setSpecificDates(waitlist.getSpecificDatesList().stream()
                    .map(ProtobufUtil::toLocalDate)
                    .toList());
            waitlistService.insertDaycareServiceWaitlist(daycareServiceWaitlist);
        }
    }

    private static DaycareServiceDetail buildDaycareServiceDetail(
            CreateBookingRequestRequest.DaycareService daycare, long bookingRequestId) {
        DaycareServiceDetail daycareServiceDetail =
                DaycareServiceDetailConverter.INSTANCE.createRequestToEntity(daycare.getService());
        daycareServiceDetail.setBookingRequestId(bookingRequestId);

        if (daycare.hasWaitlist()) {
            // 当 service 为 waitlist 时，使用 waitlist 的 specificDates
            // 有一些逻辑依赖（比如创建 order）service 的 specificDates，所以这里要保证它们不为空
            var dates = daycare.getWaitlist().getSpecificDatesList().stream()
                    .map(ProtobufUtil::toLocalDate)
                    .toList();
            daycareServiceDetail.setSpecificDates(JsonUtil.toJson(dates));
        }

        return daycareServiceDetail;
    }

    private void addBoardingService(CreateBookingRequestRequest.BoardingService boarding, long bookingRequestId) {
        long serviceDetailId =
                boardingServiceDetailService.insert(buildBoardingServiceDetail(boarding, bookingRequestId));

        for (var addon : boarding.getAddonsList()) {
            addBoardingAddon(addon, bookingRequestId, serviceDetailId);
        }

        var feedings = new ArrayList<>(boarding.getFeedingsList());
        var medications = new ArrayList<>(boarding.getMedicationsList());
        if (boarding.hasFeeding()) {
            feedings.add(boarding.getFeeding());
        }
        if (boarding.hasMedication()) {
            medications.add(boarding.getMedication());
        }
        feedingService.insertMultiple(FeedingConverter.INSTANCE.createRequestToEntity(
                feedings, bookingRequestId, serviceDetailId, ServiceItemType.BOARDING));
        medicationService.insertMultiple(MedicationConverter.INSTANCE.createRequestToEntity(
                medications, bookingRequestId, serviceDetailId, ServiceItemType.BOARDING));

        if (boarding.hasWaitlist()) {
            var waitlist = boarding.getWaitlist();
            var boardingServiceWaitlist = new BoardingServiceWaitlist();
            boardingServiceWaitlist.setBookingRequestId(bookingRequestId);
            boardingServiceWaitlist.setServiceDetailId(serviceDetailId);
            boardingServiceWaitlist.setStartDate(ProtobufUtil.toLocalDate(waitlist.getStartDate()));
            boardingServiceWaitlist.setEndDate(ProtobufUtil.toLocalDate(waitlist.getEndDate()));
            waitlistService.insertBoardingServiceWaitlist(boardingServiceWaitlist);
        }
    }

    private static BoardingServiceDetail buildBoardingServiceDetail(
            CreateBookingRequestRequest.BoardingService boarding, long bookingRequestId) {
        BoardingServiceDetail boardingServiceDetail =
                BoardingServiceDetailConverter.INSTANCE.createRequestToEntity(boarding.getService());
        boardingServiceDetail.setBookingRequestId(bookingRequestId);

        if (boarding.hasWaitlist()) {
            // 当 service 为 waitlist 时，使用 waitlist 的 startDate 和 endDate
            // 有一些逻辑依赖（比如创建 order）service 的 startDate 和 endDate，所以这里要保证它们不为空
            var waitlist = boarding.getWaitlist();
            boardingServiceDetail.setStartDate(
                    ProtobufUtil.toLocalDate(waitlist.getStartDate()).toString());
            boardingServiceDetail.setEndDate(
                    ProtobufUtil.toLocalDate(waitlist.getEndDate()).toString());
        }

        return boardingServiceDetail;
    }

    private static BookingRequest toBookingRequest(CreateBookingRequestRequest request) {
        BookingRequest entity = BookingRequestConverter.INSTANCE.createRequestToEntity(request);

        if (hasWaitlist(request)) {
            entity.setStatus(BookingRequestStatus.WAIT_LIST);
        }

        return entity;
    }

    private static boolean hasWaitlist(CreateBookingRequestRequest request) {
        return request.getServicesList().stream().anyMatch(e -> switch (e.getServiceCase()) {
            case BOARDING -> e.getBoarding().hasWaitlist();
            case DAYCARE -> e.getDaycare().hasWaitlist();
                // Add other service types here
            default -> false;
        });
    }

    private record DatePoint(@Nullable String date, @Nullable Integer time) {
        public static DatePoint max(List<DatePoint> datePoints) {
            return datePoints.stream()
                    .max(comparing(DatePoint::date, nullsFirst(naturalOrder()))
                            .thenComparing(DatePoint::time, nullsFirst(naturalOrder())))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Can't find max date time"));
        }

        public static DatePoint min(List<DatePoint> datePoints) {
            return datePoints.stream()
                    .min(comparing(DatePoint::date, nullsLast(naturalOrder()))
                            .thenComparing(DatePoint::time, nullsLast(naturalOrder())))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Can't find min date time"));
        }
    }

    @Override
    public void updateBookingRequestStatus(
            UpdateBookingRequestStatusRequest request,
            StreamObserver<UpdateBookingRequestStatusResponse> responseObserver) {
        int affectedRows = bookingRequestService.updateStatus(request.getId(), request.getStatus());

        responseObserver.onNext(UpdateBookingRequestStatusResponse.newBuilder()
                .setUpdatedResult(affectedRows != 0)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateBookingRequest(
            UpdateBookingRequestRequest request, StreamObserver<UpdateBookingRequestResponse> responseObserver) {

        var bookingRequest = bookingRequestService.mustGetBookingRequestModel(buildGetBookingRequestRequest(request));

        Tx.doInTransaction(() -> {
            // 1) Update service details
            for (var service : request.getServicesList()) {
                updateService(bookingRequest.getId(), service);
            }
            for (var serviceDetail : request.getServiceDetailsList()) {
                updateServiceDetail(bookingRequest.getId(), serviceDetail);
            }

            // 2) Update comment
            if (request.hasComment()) {
                updateComment(request.getId(), request.getComment());
            }

            // 3) Update booking request
            updateBookingRequest(request);
        });

        if (!isEmpty(request.getServicesList()) || !isEmpty(request.getServiceDetailsList())) {

            refreshBookingRequest(bookingRequest.getId());

            // 4) Update order
            updateOrder(bookingRequest.getId());
        }

        responseObserver.onNext(UpdateBookingRequestResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private static GetBookingRequestRequest buildGetBookingRequestRequest(UpdateBookingRequestRequest request) {
        var builder = GetBookingRequestRequest.newBuilder();
        builder.setId(request.getId());
        if (request.hasCompanyId()) {
            builder.setCompanyId(request.getCompanyId());
        }
        if (request.hasBusinessId()) {
            builder.setBusinessId(request.getBusinessId());
        }
        builder.addAllAssociatedModels(
                List.of(BookingRequestAssociatedModel.SERVICE, BookingRequestAssociatedModel.ADD_ON));
        return builder.build();
    }

    @Nullable
    private static Integer calculateServiceTypeInclude(List<BookingRequestModel.Service> serviceDetails) {
        var serviceItemTypes = serviceDetails.stream()
                .map(service -> switch (service.getServiceCase()) {
                    case BOARDING -> ServiceItemEnum.BOARDING;
                    case DAYCARE -> ServiceItemEnum.DAYCARE;
                    case GROOMING -> ServiceItemEnum.GROOMING;
                    case EVALUATION -> ServiceItemEnum.EVALUATION;
                    case DOG_WALKING -> ServiceItemEnum.DOG_WALKING;
                    case GROUP_CLASS -> ServiceItemEnum.GROUP_CLASS;
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unsupported service type: " + service.getServiceCase());
                })
                .distinct()
                .map(ServiceItemEnum::getServiceItem)
                .toList();

        return !serviceItemTypes.isEmpty() ? ServiceItemEnum.convertBitValueList(serviceItemTypes) : null;
    }

    private void updateOrder(long bookingRequestId) {
        orderHelper.updateOrderForBookingRequest(bookingRequestId);
    }

    private void updateBookingRequest(UpdateBookingRequestRequest request) {
        var bookingRequest = BookingRequestConverter.INSTANCE.updateRequestToEntity(request);
        bookingRequestService.update(bookingRequest);
    }

    private void updateComment(long bookingRequestId, String comment) {
        var bookingRequest = bookingRequestService.get(bookingRequestId);
        if (bookingRequest != null) {
            bookingRequestNoteService.saveBookingRequestNote(
                    bookingRequest.getCompanyId(), bookingRequest.getCustomerId(), bookingRequestId, comment);
        }
    }

    private void updateServiceDetail(long bookingRequestId, UpdateBookingRequestRequest.ServiceDetail serviceDetail) {
        var action = serviceDetail.getActionCase();
        switch (action) {
            case ADD -> addService(bookingRequestId, serviceDetail.getAdd());
            case UPDATE -> updateService(bookingRequestId, serviceDetail.getUpdate());
            case DELETE -> deleteService(serviceDetail.getDelete());
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported action: " + action);
        }
    }

    private void deleteService(UpdateBookingRequestRequest.ServiceDetail.Delete delete) {
        switch (delete.getServiceCase()) {
            case GROOMING -> groomingServiceDetailService.delete(delete.getGrooming());
            case BOARDING -> boardingServiceDetailService.delete(delete.getBoarding());
            case DAYCARE -> daycareServiceDetailService.delete(delete.getDaycare());
            case EVALUATION -> evaluationTestDetailService.delete(delete.getEvaluation());
            case DOG_WALKING -> dogWalkingServiceDetailService.delete(delete.getDogWalking());
            case GROUP_CLASS -> groupClassServiceDetailService.delete(delete.getGroupClass());
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported service: " + delete.getServiceCase());
        }
    }

    @Override
    public void replaceBookingRequest(
            ReplaceBookingRequestRequest request, StreamObserver<ReplaceBookingRequestResponse> responseObserver) {

        Tx.doInTransaction(() -> {
            var bookingRequest = BookingRequestConverter.INSTANCE.replaceRequestToEntity(request);
            bookingRequestService.update(bookingRequest);

            replaceSaveService(request.getId(), request.getServicesList());

            if (StringUtils.hasText(request.getStartDate())) {
                waitlistService.updateWaitlistByStartDateEndDate(
                        request.getId(), request.getStartDate(), request.getEndDate());
            }

            if (request.hasComment()) {
                updateComment(request.getId(), request.getComment());
            }
        });

        ThreadPool.execute(() -> {
            if (!isEmpty(request.getServicesList())) {
                refreshBookingRequest(request.getId());
            }
        });

        responseObserver.onNext(ReplaceBookingRequestResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void replaceSaveService(long bookingRequestId, List<ReplaceBookingRequestRequest.Service> serviceList) {
        if (isEmpty(serviceList)) {
            return;
        }
        List<ReplaceBookingRequestRequest.BoardingService> boardingServiceList = new ArrayList<>();
        List<ReplaceBookingRequestRequest.DaycareService> daycareServicesList = new ArrayList<>();
        // boarding && daycare
        for (var service : serviceList) {
            if (service.hasBoarding()) {
                boardingServiceList.add(service.getBoarding());
            }
            if (service.hasDaycare()) {
                daycareServicesList.add(service.getDaycare());
            }
        }
        if (isEmpty(boardingServiceList) && isEmpty(daycareServicesList)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Please select at least one service: boarding or daycare.");
        }

        replaceBoardingService(bookingRequestId, boardingServiceList);
        replaceDaycareService(bookingRequestId, daycareServicesList);
    }

    private static String getPetServiceKey(long petId, long serviceId) {
        return petId + "_" + serviceId;
    }

    private void replaceBoardingService(
            long bookingRequestId, List<ReplaceBookingRequestRequest.BoardingService> newBoardingServiceList) {
        // 获取当前 booking request 的 boarding service detail
        var existedBoardingServiceDetails = boardingServiceDetailService.listByBookingRequestId(bookingRequestId);
        if (isEmpty(newBoardingServiceList) && isEmpty(existedBoardingServiceDetails)) {
            return;
        }
        // key 等于 petid+serviceid
        Map<String, BoardingServiceDetail> existedBoardingServiceDetailMap = existedBoardingServiceDetails.stream()
                .collect(toMap(e -> getPetServiceKey(e.getPetId(), e.getServiceId()), Function.identity()));

        // 遍历新的服务列表
        for (ReplaceBookingRequestRequest.BoardingService newService : newBoardingServiceList) {
            String key = getPetServiceKey(
                    newService.getService().getPetId(), newService.getService().getServiceId());
            BoardingServiceDetail existedService = existedBoardingServiceDetailMap.get(key);
            // convert to bean
            BoardingServiceDetail boardingServiceDetail =
                    BoardingServiceDetailConverter.INSTANCE.createRequestToEntity(newService.getService());
            Long serviceDetailId;
            if (existedService == null) {
                // 新增服务
                boardingServiceDetail.setBookingRequestId(bookingRequestId);
                boardingServiceDetailService.insert(boardingServiceDetail);
                serviceDetailId = boardingServiceDetail.getId();
            } else {
                // 更新服务
                boardingServiceDetail.setId(existedService.getId());
                boardingServiceDetailService.update(boardingServiceDetail);
                serviceDetailId = existedService.getId();
                // 从已存在map中移除，剩下的就是要删除的
                existedBoardingServiceDetailMap.remove(key);
            }
            // 有 waitlist 就保存
            if (newService.hasWaitlist()) {
                var waitlist = newService.getWaitlist();
                waitlistService.updateBoardingWaitlist(
                        bookingRequestId,
                        serviceDetailId,
                        ProtobufUtil.toLocalDate(waitlist.getStartDate()),
                        ProtobufUtil.toLocalDate(waitlist.getEndDate()));
            }
        }
        existedBoardingServiceDetailMap.values().stream()
                .mapToLong(BoardingServiceDetail::getId)
                .forEach(boardingServiceDetailService::delete);
    }

    private void replaceDaycareService(
            long bookingRequestId, List<ReplaceBookingRequestRequest.DaycareService> daycareServicesList) {
        // 获取当前 booking request 的 daycare service detail
        var existedDaycareServiceDetails = daycareServiceDetailService.listByBookingRequestId(bookingRequestId);
        if (isEmpty(daycareServicesList) && isEmpty(existedDaycareServiceDetails)) {
            return;
        }
        // key 等于 petid+serviceid
        Map<String, DaycareServiceDetail> existedDaycareServiceDetailMap = existedDaycareServiceDetails.stream()
                .collect(toMap(e -> getPetServiceKey(e.getPetId(), e.getServiceId()), Function.identity()));

        // 遍历新的服务列表
        for (ReplaceBookingRequestRequest.DaycareService newService : daycareServicesList) {
            String key = getPetServiceKey(
                    newService.getService().getPetId(), newService.getService().getServiceId());
            DaycareServiceDetail existedService = existedDaycareServiceDetailMap.get(key);
            // convert to bean
            var daycareServiceDetail =
                    DaycareServiceDetailConverter.INSTANCE.createRequestToEntity(newService.getService());
            Long serviceDetailId;
            if (existedService == null) {
                // 新增服务
                daycareServiceDetail.setBookingRequestId(bookingRequestId);
                daycareServiceDetailService.insert(daycareServiceDetail);
                serviceDetailId = daycareServiceDetail.getId();
            } else {
                // 更新服务
                daycareServiceDetail.setId(existedService.getId());
                daycareServiceDetailService.update(daycareServiceDetail);
                serviceDetailId = existedService.getId();
                // 从已存在map中移除，剩下的就是要删除的
                existedDaycareServiceDetailMap.remove(key);
            }
            // 有 waitlist 就保存
            if (newService.hasWaitlist()) {
                waitlistService.updateDaycareWaitlist(
                        bookingRequestId,
                        serviceDetailId,
                        newService.getWaitlist().getSpecificDatesList());
            }
        }
        existedDaycareServiceDetailMap.values().stream()
                .mapToLong(DaycareServiceDetail::getId)
                .forEach(daycareServiceDetailService::delete);
    }

    private void updateService(Long bookingRequestId, UpdateBookingRequestRequest.Service service) {
        switch (service.getServiceCase()) {
            case BOARDING -> updateBoardingService(bookingRequestId, service.getBoarding());
            case DAYCARE -> updateDaycareService(bookingRequestId, service.getDaycare());
            case GROOMING -> updateGroomingService(bookingRequestId, service.getGrooming());
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Invalid service type: " + service.getServiceCase());
        }
    }

    private void updateGroomingService(Long bookingRequestId, UpdateBookingRequestRequest.GroomingService grooming) {
        var serviceDetailToUpdate =
                GroomingServiceDetailConverter.INSTANCE.updateRequestToEntity(grooming.getService());
        groomingServiceDetailService.update(serviceDetailToUpdate);

        for (var addon : grooming.getAddonsList()) {
            var groomingAddOnDetail = GroomingAddOnDetailConverter.INSTANCE.updateRequestToEntity(addon);
            groomingAddOnDetailService.update(groomingAddOnDetail);
        }

        for (var addon : grooming.getAddonsV2List()) {
            switch (addon.getActionCase()) {
                case ADD -> addGroomingAddon(
                        addon.getAdd().getAddon(), bookingRequestId, serviceDetailToUpdate.getId());
                case UPDATE -> updateGroomingAddon(addon.getUpdate().getAddon());
                case DELETE -> deleteGroomingAddon(addon.getDelete().getId());
                default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported action: " + addon.getActionCase());
            }
        }

        if (grooming.hasAutoAssign()) {
            var groomingAutoAssign =
                    GroomingAutoAssignConverter.INSTANCE.upsertRequestToEntity(grooming.getAutoAssign());
            // TODO 未来支持多个 auto assign 记录时，需要根据 service detail id 更新
            groomingAutoAssign.setGroomingServiceDetailId(0L);
            groomingAutoAssignService.upsertByGroomingServiceDetailId(groomingAutoAssign);
        }
    }

    private void updateBoardingService(Long bookingRequestId, UpdateBookingRequestRequest.BoardingService boarding) {
        var serviceDetailToUpdate =
                BoardingServiceDetailConverter.INSTANCE.updateRequestToEntity(boarding.getService());
        boardingServiceDetailService.update(serviceDetailToUpdate);

        for (var addon : boarding.getAddonsList()) {
            var addOnDetailToUpdate = BoardingAddOnDetailConverter.INSTANCE.updateRequestToEntity(addon);
            boardingAddOnDetailService.update(addOnDetailToUpdate);
        }

        for (var addon : boarding.getAddonsV2List()) {
            switch (addon.getActionCase()) {
                case ADD -> addBoardingAddon(
                        addon.getAdd().getAddon(), bookingRequestId, serviceDetailToUpdate.getId());
                case UPDATE -> updateBoardingAddon(addon.getUpdate().getAddon());
                case DELETE -> deleteBoardingAddon(addon.getDelete().getId());
                default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported action: " + addon.getActionCase());
            }
        }

        long serviceDetailId = boarding.getService().getId();
        if (boarding.hasFeedingsUpsert()) {
            var toDelIds = feedingService.listByBookingRequestId(bookingRequestId).stream()
                    .filter(k -> k.getServiceDetailId().equals(serviceDetailId))
                    .map(k -> k.getId())
                    .toList();
            feedingService.delete(toDelIds);
            feedingService.insertMultiple(FeedingConverter.INSTANCE.createRequestToEntity(
                    boarding.getFeedingsUpsert().getValuesList(),
                    bookingRequestId,
                    serviceDetailId,
                    ServiceItemType.BOARDING));
        }
        if (boarding.hasMedicationsUpsert()) {
            var toDelIds = medicationService.listByBookingRequestId(bookingRequestId).stream()
                    .filter(k -> k.getServiceDetailId().equals(serviceDetailId))
                    .map(k -> k.getId())
                    .toList();
            medicationService.delete(toDelIds);
            medicationService.insertMultiple(MedicationConverter.INSTANCE.createRequestToEntity(
                    boarding.getMedicationsUpsert().getValuesList(),
                    bookingRequestId,
                    serviceDetailId,
                    ServiceItemType.BOARDING));
        }
        if (boarding.hasWaitlist()) {
            waitlistService.updateBoardingWaitlist(
                    bookingRequestId,
                    serviceDetailId,
                    ProtobufUtil.toLocalDate(boarding.getWaitlist().getStartDate()),
                    ProtobufUtil.toLocalDate(boarding.getWaitlist().getEndDate()));
        }
    }

    private void addBoardingAddon(CreateBoardingAddOnDetailRequest addon, long bookingRequestId, long serviceDetailId) {
        var boardingAddOnDetail = BoardingAddOnDetailConverter.INSTANCE.createRequestToEntity(addon);
        boardingAddOnDetail.setBookingRequestId(bookingRequestId);
        boardingAddOnDetail.setServiceDetailId(serviceDetailId);
        boardingAddOnDetailService.insert(boardingAddOnDetail);
    }

    private void updateBoardingAddon(UpdateBoardingAddOnDetailRequest update) {
        var addOnDetailToUpdate = BoardingAddOnDetailConverter.INSTANCE.updateRequestToEntity(update);
        boardingAddOnDetailService.update(addOnDetailToUpdate);
    }

    private void deleteBoardingAddon(long boardingAddOnDetailId) {
        boardingAddOnDetailService.delete(boardingAddOnDetailId);
    }

    private void addDaycareAddon(CreateDaycareAddOnDetailRequest addon, long bookingRequestId, long serviceDetailId) {
        var daycareAddOnDetail = DaycareAddOnDetailConverter.INSTANCE.createRequestToEntity(addon);
        daycareAddOnDetail.setBookingRequestId(bookingRequestId);
        daycareAddOnDetail.setServiceDetailId(serviceDetailId);
        daycareAddOnDetailService.insert(daycareAddOnDetail);
    }

    private void updateDaycareAddon(UpdateDaycareAddOnDetailRequest update) {
        var addOnDetailToUpdate = DaycareAddOnDetailConverter.INSTANCE.updateRequestToEntity(update);
        daycareAddOnDetailService.update(addOnDetailToUpdate);
    }

    private void deleteDaycareAddon(long daycareAddOnDetailId) {
        daycareAddOnDetailService.delete(daycareAddOnDetailId);
    }

    private void addGroomingAddon(CreateGroomingAddOnDetailRequest addon, long bookingRequestId, long serviceDetailId) {
        var groomingAddOnDetail = GroomingAddOnDetailConverter.INSTANCE.createRequestToEntity(addon);
        groomingAddOnDetail.setBookingRequestId(bookingRequestId);
        groomingAddOnDetail.setServiceDetailId(serviceDetailId);
        groomingAddOnDetailService.insert(groomingAddOnDetail);
    }

    private void updateGroomingAddon(UpdateGroomingAddOnDetailRequest update) {
        var addOnDetailToUpdate = GroomingAddOnDetailConverter.INSTANCE.updateRequestToEntity(update);
        groomingAddOnDetailService.update(addOnDetailToUpdate);
    }

    private void deleteGroomingAddon(long groomingAddOnDetailId) {
        groomingAddOnDetailService.delete(groomingAddOnDetailId);
    }

    private void updateDaycareService(Long bookingRequestId, UpdateBookingRequestRequest.DaycareService daycare) {
        var serviceDetailToUpdate = DaycareServiceDetailConverter.INSTANCE.updateRequestToEntity(daycare.getService());
        daycareServiceDetailService.update(serviceDetailToUpdate);

        for (var addon : daycare.getAddonsList()) {
            var addOnDetailToUpdate = DaycareAddOnDetailConverter.INSTANCE.updateRequestToEntity(addon);
            daycareAddOnDetailService.update(addOnDetailToUpdate);
        }

        for (var addon : daycare.getAddonsV2List()) {
            switch (addon.getActionCase()) {
                case ADD -> addDaycareAddon(addon.getAdd().getAddon(), bookingRequestId, serviceDetailToUpdate.getId());
                case UPDATE -> updateDaycareAddon(addon.getUpdate().getAddon());
                case DELETE -> deleteDaycareAddon(addon.getDelete().getId());
                default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported action: " + addon.getActionCase());
            }
        }

        long serviceDetailId = daycare.getService().getId();
        if (daycare.hasFeedingsUpsert()) {
            var toDelIds = feedingService.listByBookingRequestId(bookingRequestId).stream()
                    .filter(k -> k.getServiceDetailId().equals(serviceDetailId))
                    .map(k -> k.getId())
                    .toList();
            feedingService.delete(toDelIds);
            feedingService.insertMultiple(FeedingConverter.INSTANCE.createRequestToEntity(
                    daycare.getFeedingsUpsert().getValuesList(),
                    bookingRequestId,
                    serviceDetailId,
                    ServiceItemType.DAYCARE));
        }
        if (daycare.hasMedicationsUpsert()) {
            var toDelIds = medicationService.listByBookingRequestId(bookingRequestId).stream()
                    .filter(k -> k.getServiceDetailId().equals(serviceDetailId))
                    .map(k -> k.getId())
                    .toList();
            medicationService.delete(toDelIds);
            medicationService.insertMultiple(MedicationConverter.INSTANCE.createRequestToEntity(
                    daycare.getMedicationsUpsert().getValuesList(),
                    bookingRequestId,
                    serviceDetailId,
                    ServiceItemType.DAYCARE));
        }
        if (daycare.hasWaitlist()) {
            waitlistService.updateDaycareWaitlist(
                    bookingRequestId,
                    serviceDetailId,
                    daycare.getWaitlist().getSpecificDates().getDatesList());
        }
    }

    @Override
    public void retryFailedEvents(
            RetryFailedEventsRequest request, StreamObserver<RetryFailedEventsResponse> responseObserver) {
        List<String> pops = stringRedisTemplate.opsForSet().pop(OBRequestSyncDTO.FAILED_KEY, 100);
        if (!isEmpty(pops)) {
            log.info("Retry failed events: {}", pops);
            pops.forEach(pop -> {
                try {
                    BookingRequestEventParams params = JsonUtil.toBean(pop, BookingRequestEventParams.class);
                    syncBookingRequestListener.processEvent(params);
                } catch (Exception e) {
                    log.error("Failed to process failed event: {}", pop, e);
                    stringRedisTemplate.opsForSet().add(OBRequestSyncDTO.FAILED_KEY, pop);
                }
            });
        }

        responseObserver.onNext(RetryFailedEventsResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getAutoAssign(GetAutoAssignRequest request, StreamObserver<GetAutoAssignResponse> responseObserver) {
        responseObserver.onNext(getAutoAssignResponse(request));
        responseObserver.onCompleted();
    }

    private GetAutoAssignResponse getAutoAssignResponse(GetAutoAssignRequest request) {
        var bookingRequestModel = mustGetBookingRequestModel(request.getCompanyId(), request.getId());

        var result = GetAutoAssignResponse.newBuilder();
        doLodgingAssign(bookingRequestModel, result);
        doStaffAssign(bookingRequestModel, result);

        return result.build();
    }

    /**
     * auto assign lodging. Support boarding only
     */
    void doLodgingAssign(BookingRequestModel bookingRequestModel, GetAutoAssignResponse.Builder result) {
        // 获取待分配的 pet service 信息
        List<BoardingServiceDetailModel> boardingServiceDetails =
                PetDetailService.getBoardingServiceDetails(bookingRequestModel);
        List<DaycareServiceDetailModel> daycareServiceDetails =
                PetDetailService.getDaycareServiceDetails(bookingRequestModel);
        // 没有待分配的信息，直接返回
        if (isEmpty(daycareServiceDetails) && isEmpty(boardingServiceDetails)) {
            return;
        }

        long companyId = bookingRequestModel.getCompanyId();
        long businessId = bookingRequestModel.getBusinessId();

        // 获取 pet、service 信息，用于 lodging available 判断
        Map<Long, BusinessCustomerPetInfoModel> petMap =
                autoAssignService.getPetMap(companyId, PetDetailService.getPetIds(bookingRequestModel));
        List<BusinessPetSizeModel> petSizeList = autoAssignService.getPetSizeList(companyId);
        Map<Long, ServiceBriefView> serviceMap =
                serviceHelper.listService(PetDetailService.getServiceIds(bookingRequestModel));

        // 获取 lodging 信息
        List<LodgingUnitModel> lodgingUnitList = lodgingService.getLodgingUnit(companyId, businessId);
        List<LodgingTypeModel> lodgingTypeList = lodgingService.getLodgingTypeByUnits(lodgingUnitList);
        Map<Long, LodgingUnitModel> lodgingUnitMap =
                lodgingUnitList.stream().collect(toMap(LodgingUnitModel::getId, Function.identity()));
        Map<Long, LodgingTypeModel> lodgingTypeMap =
                lodgingTypeList.stream().collect(toMap(LodgingTypeModel::getId, Function.identity()));

        // 获取 lodging 使用信息
        List<LodgingAssignInfo> assignInfoList = lodgingService.getLodgingAssignInfo(
                companyId, businessId, bookingRequestModel.getStartDate(), bookingRequestModel.getEndDate());

        // 根据 feature flag 控制在计算 lodging 占用上是否过滤 boarding 的 checkout day
        boolean excludeBoardingLastDay = lodgingService.enableNewLodgingOccupancy(companyId);

        List<PetToLodgingDef> petLodgings = AutoAssignService.autoAssign(
                bookingRequestModel,
                lodgingTypeList,
                lodgingUnitList,
                serviceMap,
                petSizeList,
                petMap,
                assignInfoList,
                excludeBoardingLastDay);

        result.addAllBoardingAssignRequires(AutoAssignConverter.INSTANCE.buildAssignRequire(
                        PetDetailService.getBoardingServiceDetails(bookingRequestModel),
                        PetDetailService.getDaycareServiceDetails(bookingRequestModel)))
                .addAllPetToLodgings(petLodgings)
                .addAllLodgings(AutoAssignConverter.INSTANCE.buildAutoAssignLodgingDetail(
                        petLodgings.stream()
                                .map(PetToLodgingDef::getLodgingUnitId)
                                .distinct()
                                .toList(),
                        lodgingUnitMap,
                        lodgingTypeMap));
    }

    /**
     * auto assign staff. Support evaluation only
     */
    void doStaffAssign(BookingRequestModel bookingRequestModel, GetAutoAssignResponse.Builder result) {
        // 获取待分配的 pet service 信息
        var evaluationServiceDetails = PetDetailService.getEvaluationServiceDetails(bookingRequestModel);
        // 没有待分配的信息，直接返回
        if (isEmpty(evaluationServiceDetails)) {
            return;
        }

        long companyId = bookingRequestModel.getCompanyId();
        long businessId = bookingRequestModel.getBusinessId();

        // 1. 获取 business 可用的 staff
        var staffs = organizationHelper.listStaffForBusiness(companyId, businessId, true);

        // 2. 获取 evaluation 详情
        var evaluations = serviceHelper.getEvaluationByIds(
                evaluationServiceDetails.stream()
                        .map(EvaluationTestDetailModel::getEvaluationId)
                        .toList(),
                businessId);

        result.addAllEvaluationAssignRequires(
                        AutoAssignConverter.INSTANCE.evaluationToAssignRequire(evaluationServiceDetails, evaluations))
                .addAllEvaluationPetToStaffs(
                        AutoAssignService.autoAssignToStaff(evaluationServiceDetails, evaluations, staffs));
    }

    @Override
    public void acceptBookingRequest(
            AcceptBookingRequestRequest request, StreamObserver<AcceptBookingRequestResponse> responseObserver) {
        responseObserver.onNext(getAcceptBookingRequestResponse(request));
        responseObserver.onCompleted();
    }

    private AcceptBookingRequestResponse getAcceptBookingRequestResponse(AcceptBookingRequestRequest request) {
        Long companyId = request.getCompanyId();
        Long businessId = request.getBusinessId();
        Long staffId = request.hasStaffId() ? request.getStaffId() : 0;

        var bookingRequestModel = mustGetBookingRequestModel(companyId, request.getId());

        // get last lodging unit id
        List<PetToLodgingDef> petToLodgingsList = request.getPetToLodgingsList();
        if (Objects.equals(
                ServiceItemEnum.DAYCARE, getMainServiceItemType(bookingRequestModel.getServiceTypeInclude()))) {
            // daycare service 优先 assign last lodging
            List<PetToLodgingDef> petToLastLodgingList = getLastLodgingUnitId(
                    bookingRequestModel.getServicesList(), companyId, businessId, bookingRequestModel.getCustomerId());
            if (!isEmpty(petToLastLodgingList)) {
                Map<Long, PetToLodgingDef> merged = new HashMap<>();
                Stream.concat(petToLodgingsList.stream(), petToLastLodgingList.stream())
                        .forEach(def -> merged.put(def.getPetId(), def));
                petToLodgingsList = new ArrayList<>(merged.values());
            }
        }

        List<Long> appointmentIds = bookingRequestModifyService.createAppointment(
                bookingRequestModel,
                companyId,
                businessId,
                staffId,
                AppointmentStatus.UNCONFIRMED,
                petToLodgingsList,
                request.getPetToStaffsList(),
                request.getEvaluationPetToStaffsList(),
                request.getPetToServicesList());

        // set booking request status
        int affectedRows = bookingRequestService.updateStatus(request.getId(), BookingRequestStatus.SCHEDULED);

        feedingMedicationService.syncPetDetailDef(companyId, bookingRequestModel);
        onlineBookingProducer.pushOnlineBookingAcceptedEvent(bookingRequestModel);

        afterBookingRequestAccepted(appointmentIds, bookingRequestModel);

        return AcceptBookingRequestResponse.newBuilder()
                .setResult(!isEmpty(appointmentIds) && affectedRows != 0)
                .setAppointmentId(appointmentIds.get(0))
                .build();
    }

    /*private*/ void afterBookingRequestAccepted(List<Long> appointmentIds, BookingRequestModel bookingRequest) {

        if (ObjectUtils.isEmpty(appointmentIds)) {
            return;
        }

        // 添加 booking request 和 appointment 的关联
        addBookingRequestAppointmentMappings(appointmentIds, bookingRequest.getId());

        try {

            // 将 appointment id 回写到 OB deposit，用于向后兼容
            updateOBDeposit(bookingRequest.getId(), appointmentIds);

            // 处理 payment
            // prepay: 直接扣款
            // preauth: 写入 PreAuthRecord 记录，capture 金额
            // NOTE: 这个操作依赖 addBookingRequestAppointmentMappings 这一步
            handlePayment(bookingRequest.getBusinessId(), appointmentIds.get(0), bookingRequest.getId());

        } catch (Exception e) {

            // 如果出现异常，需要删除 booking request 和 appointment 的关联记录
            deleteBookingRequestAppointmentMappings(bookingRequest.getId());

            throw e;
        }
    }

    private void handlePayment(long businessId, long appointmentId, long bookingRequestId) {

        var deposit = depositApi.getOBDepositByBookingRequestId(Math.toIntExact(businessId), bookingRequestId);
        if (deposit == null) {
            return;
        }

        if (Objects.equals(deposit.getDepositType(), DepositPaymentTypeEnum.PrePay)) {

            // Accept booking request 之后需要真正 capture payment
            paymentStripeApi.capturePaymentIntent(deposit.getPaymentId());

        } else if (Objects.equals(deposit.getDepositType(), DepositPaymentTypeEnum.PreAuth)) {
            var appointment = mustGetAppointment(appointmentId);
            // Accept booking request 之后需要写入 pre-auth record
            insertPreAuthRecord(appointment, deposit);
        }
    }

    private AppointmentModel mustGetAppointment(long appointmentId) {
        var resp = appointmentStub.getAppointment(GetAppointmentRequest.newBuilder()
                .setAppointmentId(appointmentId)
                .build());
        if (!resp.hasAppointment()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Appointment not found: " + appointmentId);
        }
        return resp.getAppointment();
    }

    private void insertPreAuthRecord(AppointmentModel appointment, BookOnlineDepositDTO deposit) {

        var preauthInfo = deposit.getPreauthInfo();
        if (preauthInfo == null) {
            return;
        }

        var order = orderHelper.getBySource(OrderSourceType.APPOINTMENT, appointment.getId(), true);
        if (order == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Order not found: " + appointment.getId());
        }

        var param = new AppointmentEventParams();
        param.setBusinessId(Math.toIntExact(appointment.getBusinessId()));
        param.setCompanyId(appointment.getCompanyId());
        param.setTicketId(Math.toIntExact(appointment.getId()));
        param.setInvoiceId(Math.toIntExact(order.getOrder().getId()));
        param.setPreAuthAmount(BigDecimal.valueOf(order.getOrder().getSubTotalAmount()));
        param.setPreAuthPaymentMethod(preauthInfo.getPaymentMethodId());
        param.setPreAuthCardNumber(preauthInfo.getCardNumber());
        param.setCustomerId(Math.toIntExact(appointment.getCustomerId()));
        param.setApptDateStr(appointment.getAppointmentDate());
        param.setApptTime(appointment.getAppointmentStartTime());
        param.setPreAuthStatus(true);
        param.setEvent(AppointmentEventEnum.CREATE_SINGLE.name());
        param.setAppointmentSource(AppointmentSource.ONLINE_BOOKING_VALUE);
        param.setReleasePreAuth(false);
        param.setTicketIds(List.of());

        // 依赖于 booking request 和 appointment 的关联关系
        // See com.moego.server.payment.service.PreAuthService.isBookingRequest
        preAuthApi.create(param);
    }

    private void updateOBDeposit(long bookingRequestId, List<Long> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return;
        }

        var deposit = depositApi.getOBDepositByBookingRequestId(null, bookingRequestId);
        if (deposit == null) {
            return;
        }

        // 只有 daycare 一个 booking request 可能会创建多个 appointment
        // 但是 daycare 只付了一天的钱，所以这里逻辑是闭环的：一个 OB deposit 对应一个 booking request 和一个 appointment
        var appointmentId = appointmentIds.get(0);

        depositApi.update(IBookOnlineDepositService.UpdateParam.builder()
                .id(deposit.getId())
                .groomingId(Math.toIntExact(appointmentId))
                .build());
    }

    private void deleteBookingRequestAppointmentMappings(Long bookingRequestId) {
        bookingRequestAppointmentMappingService.deleteByBookingRequestId(bookingRequestId);
    }

    private void addBookingRequestAppointmentMappings(List<Long> appointmentIds, Long bookingRequestId) {
        for (var appointmentId : appointmentIds) {
            var insertBean = new BookingRequestAppointmentMapping();
            insertBean.setBookingRequestId(bookingRequestId);
            insertBean.setAppointmentId(appointmentId);
            bookingRequestAppointmentMappingService.insert(insertBean);
        }
    }

    private List<PetToLodgingDef> getLastLodgingUnitId(
            List<BookingRequestModel.Service> serviceDetails, Long companyId, Long businessId, Long customerId) {
        Map<Long /* pet id */, Long /* service id */> petServiceMap = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .map(BookingRequestModel.Service::getDaycare)
                .map(BookingRequestModel.DaycareService::getService)
                .collect(toMap(
                        DaycareServiceDetailModel::getPetId, DaycareServiceDetailModel::getServiceId, (a, b) -> b));

        List<PetToLodgingDef> petToLodgingsList = petDetailService
                .getLastLodgingUnitId(companyId, businessId, customerId, petServiceMap.keySet(), petServiceMap.values())
                .entrySet()
                .stream()
                .map(entry -> PetToLodgingDef.newBuilder()
                        .setPetId(entry.getKey())
                        .setLodgingUnitId(entry.getValue())
                        .build())
                .toList();

        Set<Long> lodgingUnitIds = petToLodgingsList.stream()
                .map(PetToLodgingDef::getLodgingUnitId)
                .collect(Collectors.toSet());

        Set<Long> existUnitIds = lodgingService.getLodgingUnitByUnitIds(companyId, businessId, lodgingUnitIds).stream()
                .map(LodgingUnitModel::getId)
                .collect(Collectors.toSet());

        return petToLodgingsList.stream()
                .filter(entry -> existUnitIds.contains(entry.getLodgingUnitId()))
                .toList();
    }

    @Override
    public void declineBookingRequest(
            DeclineBookingRequestRequest request, StreamObserver<DeclineBookingRequestResponse> responseObserver) {
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();
        long staffId = request.hasStaffId() ? request.getStaffId() : 0;

        var bookingRequestModel = mustGetBookingRequestModel(companyId, request.getId());

        // create appointment with decline status
        List<Long> appointmentIds = bookingRequestModifyService.createAppointment(
                bookingRequestModel,
                companyId,
                businessId,
                staffId,
                AppointmentStatus.CANCELED,
                List.of(),
                List.of(),
                List.of(),
                List.of());

        // set booking request status
        int affectedRows = bookingRequestService.updateStatus(request.getId(), BookingRequestStatus.DECLINED);

        ThreadPool.execute(() -> refund(businessId, request.getId()));

        responseObserver.onNext(DeclineBookingRequestResponse.newBuilder()
                .setResult(!isEmpty(appointmentIds) && affectedRows != 0)
                .setAppointmentId(appointmentIds.get(0))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void acceptBookingRequestV2(
            AcceptBookingRequestV2Request request, StreamObserver<AcceptBookingRequestV2Response> responseObserver) {

        var beforeStatus = bookingRequestService.mustGet(request.getId()).getStatus();

        int affectedRows = bookingRequestService.updateStatus(request.getId(), BookingRequestStatus.SCHEDULED);
        if (affectedRows == 0) {
            responseObserver.onNext(
                    AcceptBookingRequestV2Response.newBuilder().setResult(false).build());
            responseObserver.onCompleted();
            return;
        }

        List<Long> appointmentIds;
        try {
            var bookingRequestModel =
                    mustGetBookingRequestModel(request.hasCompanyId() ? request.getCompanyId() : null, request.getId());

            if (ServiceItemEnum.GROUP_CLASS.isIncludedIn(bookingRequestModel.getServiceTypeInclude())) {
                appointmentIds = fulfillmentService.createFulfillment(bookingRequestModel);
            } else {
                appointmentIds = createAppointments(request, bookingRequestModel);

                feedingMedicationService.syncPetDetailDef(bookingRequestModel.getCompanyId(), bookingRequestModel);
                onlineBookingProducer.pushOnlineBookingAcceptedEvent(bookingRequestModel);
            }
            afterBookingRequestAccepted(appointmentIds, bookingRequestModel);
        } catch (Exception e) {

            // accept 失败需要回滚状态
            bookingRequestService.updateStatus(request.getId(), beforeStatus);

            throw e;
        }

        responseObserver.onNext(AcceptBookingRequestV2Response.newBuilder()
                .setResult(!isEmpty(appointmentIds))
                .setAppointmentId(appointmentIds.get(0))
                .build());
        responseObserver.onCompleted();
    }

    private BookingRequest mustGetBookingRequest(
            @Nullable Long companyId, @Nullable Long businessId, long bookingRequestId) {
        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
        if (companyId != null && !Objects.equals(companyId, bookingRequest.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        if (businessId != null && !Objects.equals(businessId, bookingRequest.getBusinessId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        return bookingRequest;
    }

    private BookingRequestModel mustGetBookingRequestModel(@Nullable Long companyId, long bookingRequestId) {
        var bookingRequestModel = bookingRequestService.mustGetBookingRequestModel(GetBookingRequestRequest.newBuilder()
                .setId(bookingRequestId)
                .addAllAssociatedModels(List.of(
                        BookingRequestAssociatedModel.SERVICE,
                        BookingRequestAssociatedModel.ADD_ON,
                        BookingRequestAssociatedModel.FEEDING,
                        BookingRequestAssociatedModel.MEDICATION))
                .build());
        if (companyId != null && !Objects.equals(companyId, bookingRequestModel.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        return bookingRequestModel;
    }

    private List<Long> createAppointments(AcceptBookingRequestV2Request request, BookingRequestModel bookingRequest) {

        var appointmentIds = new ArrayList<Long>();

        // 特别注意：boarding/daycare 里面有 evaluation，如果 evaluation 对应 service 的 is_evaluation_required_for_ob 为 true
        // 这类 evaluation 叫做 mandatory evaluation.
        // 在 accept booking request 时：
        // - mandatory evaluation 需要放在一起单独创建一个 appointment
        // - non-mandatory evaluation 则需要和对应的 boarding/daycare service 放在一起创建 appointment

        var mandatoryEvaluations = filterMandatoryEvaluation(bookingRequest.getServicesList());
        var normalServiceDetails = bookingRequest.getServicesList().stream()
                .filter(not(mandatoryEvaluations::contains))
                .toList();

        switch (getMainServiceItemType(bookingRequest.getServiceTypeInclude())) {
            case BOARDING -> {
                appointmentIds.add(createAppointmentForBoarding(request, normalServiceDetails, bookingRequest));

                if (!mandatoryEvaluations.isEmpty()) {
                    // 注意：新增的 evaluation (createEvaluationRequests) 应该和 boarding 一起创建
                    // 为 mandatory evaluation 创建 appointment 时需要去掉 createEvaluationRequests
                    var newRequest =
                            request.toBuilder().clearCreateEvaluationRequests().build();
                    appointmentIds.add(
                            createAppointmentForEvaluation(newRequest, mandatoryEvaluations, bookingRequest));
                }
            }
            case DAYCARE -> {
                // daycare 会按照 date 拆分为多个 appointments
                appointmentIds.addAll(createAppointmentsForDaycare(request, normalServiceDetails, bookingRequest));

                if (!mandatoryEvaluations.isEmpty()) {
                    // 注意：新增的 evaluation (createEvaluationRequests) 应该随着 daycare 一起创建
                    // 为 mandatory evaluation 创建 appointment 时需要去掉 createEvaluationRequests
                    var newRequest =
                            request.toBuilder().clearCreateEvaluationRequests().build();
                    appointmentIds.add(
                            createAppointmentForEvaluation(newRequest, mandatoryEvaluations, bookingRequest));
                }
            }
            case GROOMING -> {
                appointmentIds.add(
                        createAppointmentForGrooming(request, bookingRequest.getServicesList(), bookingRequest));
            }
            case EVALUATION -> {
                appointmentIds.add(
                        createAppointmentForEvaluation(request, bookingRequest.getServicesList(), bookingRequest));
            }
            default -> throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Unsupported service type: " + getMainServiceItemType(bookingRequest.getServiceTypeInclude()));
        }

        return appointmentIds;
    }

    private long createAppointmentForEvaluation(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequestModel bookingRequest) {

        var petDetailDefs = new ArrayList<PetDetailDef>();

        // evaluation 会出现的 case 只有 evaluation
        petDetailDefs.addAll(buildPetDetailDefsForEvaluation(request, serviceDetails, null));

        var req = buildCreateAppointmentForOnlineBookingRequest(request, bookingRequest, petDetailDefs);

        return appointmentStub.createAppointmentForOnlineBooking(req).getAppointmentId();
    }

    private long createAppointmentForGrooming(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequestModel bookingRequest) {

        var petDetailDefs = new ArrayList<PetDetailDef>();

        // grooming 会出现的 case 只有 grooming
        petDetailDefs.addAll(buildPetDetailDefsForGrooming(request, serviceDetails, bookingRequest, null));

        var req = buildCreateAppointmentForOnlineBookingRequest(request, bookingRequest, petDetailDefs);

        return appointmentStub.createAppointmentForOnlineBooking(req).getAppointmentId();
    }

    private long createAppointmentForBoarding(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequestModel bookingRequest) {

        var petDetailDefs = new ArrayList<PetDetailDef>();

        // boarding 会出现的 case 只有 boarding + grooming + evaluation
        petDetailDefs.addAll(buildPetDetailDefsForBoarding(request, serviceDetails, bookingRequest));
        petDetailDefs.addAll(buildPetDetailDefsForGrooming(request, serviceDetails, bookingRequest, null));
        petDetailDefs.addAll(buildPetDetailDefsForEvaluation(request, serviceDetails, null));

        var req = buildCreateAppointmentForOnlineBookingRequest(request, bookingRequest, petDetailDefs);

        return appointmentStub.createAppointmentForOnlineBooking(req).getAppointmentId();
    }

    private List<BookingRequestModel.Service> filterMandatoryEvaluation(
            List<BookingRequestModel.Service> serviceDetails) {
        var serviceIds = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(e -> e.getEvaluation().getService().getServiceId())
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());

        var serviceIdToService = serviceHelper.listService(serviceIds);

        return serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .filter(e -> isNormal(e.getEvaluation().getService().getServiceId()))
                .filter(e -> {
                    var service = serviceIdToService.get(
                            e.getEvaluation().getService().getServiceId());
                    return service != null
                            && service.getIsEvaluationRequired()
                            && service.getIsEvaluationRequiredForOb();
                })
                .toList();
    }

    private List<PetDetailDef> buildPetDetailDefsForEvaluation(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            @Nullable LocalDate date // 只 build 指定日期的 evaluation
            ) {

        var bookingRequest = bookingRequestService.mustGet(request.getId());

        var petIdToEvaluationList = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .collect(groupingBy(BookingRequestServer::getPetId));

        var result = new ArrayList<PetDetailDef>();

        for (var en : petIdToEvaluationList.entrySet()) {
            var petId = en.getKey();
            var evaluationServices = en.getValue();
            var petDetailBuilder = PetDetailDef.newBuilder().setPetId(petId);
            for (var evaluation : evaluationServices) {
                if (date == null
                        || Objects.equals(
                                evaluation.getEvaluation().getService().getStartDate(), date.toString())) {
                    processEvaluation(
                            petDetailBuilder, evaluation.getEvaluation(), request, bookingRequest.getBusinessId());
                }
            }
            if (!petDetailBuilder.getEvaluationsList().isEmpty()) {
                result.add(petDetailBuilder.build());
            }
        }

        // 处理 create evaluation request
        var petIdToCreateEvaluationRequests = request.getCreateEvaluationRequestsList().stream()
                .collect(groupingBy(AcceptBookingRequestV2Request.CreateEvaluationRequest::getPetId));
        for (var en : petIdToCreateEvaluationRequests.entrySet()) {
            var petId = en.getKey();
            var createEvaluationRequests = en.getValue();
            var petDetailBuilder = PetDetailDef.newBuilder().setPetId(petId);
            for (var createEvaluationRequest : createEvaluationRequests) {
                if (date == null
                        || Objects.equals(ProtobufUtil.toLocalDate(createEvaluationRequest.getStartDate()), date)) {
                    petDetailBuilder.addEvaluations(
                            buildSelectedEvaluationDef(createEvaluationRequest, bookingRequest.getBusinessId()));
                }
            }
            if (!petDetailBuilder.getEvaluationsList().isEmpty()) {
                result.add(petDetailBuilder.build());
            }
        }

        return result;
    }

    private SelectedEvaluationDef buildSelectedEvaluationDef(
            AcceptBookingRequestV2Request.CreateEvaluationRequest createEvaluationRequest, long businessId) {

        var evaluationBuilder = SelectedEvaluationDef.newBuilder();
        evaluationBuilder.setServiceId(createEvaluationRequest.getEvaluationId());
        evaluationBuilder.setStartDate(
                ProtobufUtil.toLocalDate(createEvaluationRequest.getStartDate()).toString());
        evaluationBuilder.setStartTime(createEvaluationRequest.getStartTime());
        if (createEvaluationRequest.hasStaffId()) {
            evaluationBuilder.setStaffId(createEvaluationRequest.getStaffId());
        }
        if (createEvaluationRequest.hasLodgingId()) {
            evaluationBuilder.setLodgingId(createEvaluationRequest.getLodgingId());
        }

        var evaluationBriefView = mustGetEvaluation(createEvaluationRequest.getEvaluationId(), businessId);
        evaluationBuilder.setServiceTime(evaluationBriefView.getDuration());
        evaluationBuilder.setServicePrice(evaluationBriefView.getPrice());
        evaluationBuilder.setEndTime(evaluationBuilder.getStartTime() + evaluationBriefView.getDuration());

        return evaluationBuilder.build();
    }

    private static List<PetDetailDef> buildPetDetailDefsForGrooming(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequestModel bookingRequest,
            @Nullable LocalDate date // 只 build 指定日期的 grooming
            ) {

        var petIdToGroomingList = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .collect(groupingBy(BookingRequestServer::getPetId));

        var result = new ArrayList<PetDetailDef>();

        for (var en : petIdToGroomingList.entrySet()) {
            var petId = en.getKey();
            var groomingServices = en.getValue();
            var petDetailBuilder = PetDetailDef.newBuilder().setPetId(petId);
            for (var grooming : groomingServices) {
                if (date == null
                        || Objects.equals(grooming.getGrooming().getService().getStartDate(), date.toString())) {
                    processGrooming(petDetailBuilder, grooming.getGrooming(), request, bookingRequest);
                }
            }
            if (!petDetailBuilder.getServicesList().isEmpty()) {
                result.add(petDetailBuilder.build());
            }
        }

        return result;
    }

    private static List<PetDetailDef> buildPetDetailDefsForBoarding(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequestModel bookingRequest) {

        var petIdToBoardingList = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .collect(groupingBy(BookingRequestServer::getPetId));

        var result = new ArrayList<PetDetailDef>();

        for (var en : petIdToBoardingList.entrySet()) {
            var petId = en.getKey();
            var boardingServices = en.getValue();
            var petDetailBuilder = PetDetailDef.newBuilder().setPetId(petId);
            for (var boarding : boardingServices) {
                processBoarding(petDetailBuilder, boarding.getBoarding(), request, bookingRequest);
            }
            result.add(petDetailBuilder.build());
        }

        return result;
    }

    private List<Long> createAppointmentsForDaycare(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequestModel bookingRequest) {

        // 将相同日期不同 pet 的 daycare 服务合并到同一个 appointment
        var dateToDaycareList = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .flatMap(e -> e.getDaycare().getService().getSpecificDatesList().stream()
                        .map(it -> Map.entry(LocalDate.parse(it), e)))
                .collect(groupingBy(Map.Entry::getKey, TreeMap::new, mapping(Map.Entry::getValue, toList())));

        if (dateToDaycareList.isEmpty()) {
            return List.of();
        }

        var result = new ArrayList<Long>();
        var iterator = dateToDaycareList.entrySet().iterator();

        // 这里特别注意 prepay 的场景，daycare prepay 时只付第一天的钱
        // 为了避免并发问题，这里不能直接并发创建，而是要等第一天的 appointment 创建完成之后再创建后续的 appointment

        // 同步创建第一天的 appointment
        var firstEntry = iterator.next();
        var firstDate = firstEntry.getKey();
        var firstDaycareServices = firstEntry.getValue();
        var firstAppointmentId =
                createDaycareAppointment(request, serviceDetails, bookingRequest, firstDaycareServices, firstDate);
        result.add(firstAppointmentId);

        // 并发创建后续日期的 appointment
        var futures = new ArrayList<CompletableFuture<Long>>();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            var date = entry.getKey();
            var daycareServices = entry.getValue();
            futures.add(CompletableFuture.supplyAsync(
                    () -> createDaycareAppointment(request, serviceDetails, bookingRequest, daycareServices, date),
                    ThreadPool.getSubmitExecutor()));
        }

        for (var future : futures) {
            result.add(future.join());
        }

        return result;
    }

    private long createDaycareAppointment(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequestModel bookingRequest,
            List<BookingRequestModel.Service> daycareServices,
            LocalDate date) {

        var petDetailDefs = new ArrayList<PetDetailDef>();

        // daycare 会出现的 case 只有 daycare + grooming + evaluation
        petDetailDefs.addAll(buildPetDetailDefsForDaycare(request, daycareServices, bookingRequest, date));
        petDetailDefs.addAll(buildPetDetailDefsForGrooming(request, serviceDetails, bookingRequest, date));
        petDetailDefs.addAll(buildPetDetailDefsForEvaluation(request, serviceDetails, date));

        var req = buildCreateAppointmentForOnlineBookingRequest(request, bookingRequest, petDetailDefs);
        return appointmentStub.createAppointmentForOnlineBooking(req).getAppointmentId();
    }

    private static List<PetDetailDef> buildPetDetailDefsForDaycare(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> daycareServices,
            BookingRequestModel bookingRequest,
            LocalDate date) {

        var daycarePetDetailDefs = new ArrayList<PetDetailDef>();

        for (var daycareService : daycareServices) {
            daycarePetDetailDefs.add(
                    buildPetDetailDefForDaycare(daycareService.getDaycare(), request, bookingRequest, date));
        }

        return daycarePetDetailDefs;
    }

    private static CreateAppointmentForOnlineBookingRequest buildCreateAppointmentForOnlineBookingRequest(
            AcceptBookingRequestV2Request request,
            BookingRequestModel bookingRequest,
            List<PetDetailDef> petDetailDefs) {
        var builder = CreateAppointmentForOnlineBookingRequest.newBuilder();
        builder.setCompanyId(bookingRequest.getCompanyId());
        builder.setBusinessId(bookingRequest.getBusinessId());
        if (request.hasStaffId()) {
            builder.setStaffId(request.getStaffId());
        }
        builder.setBookingRequestId(bookingRequest.getId());
        builder.setBookingRequestIdentifier(bookingRequest.getId() + "." + bookingRequest.getStartDate());
        builder.setAppointment(buildAppointmentCreateForOnlineBookingDef(bookingRequest));
        if (StringUtils.hasText(bookingRequest.getAdditionalNote())) {
            builder.addNotes(buildAppointmentNoteCreateDef(bookingRequest));
        }

        builder.addAllPetDetails(petDetailDefs);

        return builder.build();
    }

    private static long getPetId(BookingRequestModel.Service serviceDetail) {
        return switch (serviceDetail.getServiceCase()) {
            case GROOMING -> serviceDetail.getGrooming().getService().getPetId();
            case BOARDING -> serviceDetail.getBoarding().getService().getPetId();
            case DAYCARE -> serviceDetail.getDaycare().getService().getPetId();
            case EVALUATION -> serviceDetail.getEvaluation().getService().getPetId();
            case DOG_WALKING -> serviceDetail.getDogWalking().getService().getPetId();
            default -> 0L;
        };
    }

    private static void processGrooming(
            PetDetailDef.Builder petDetailBuilder,
            BookingRequestModel.GroomingService grooming,
            AcceptBookingRequestV2Request request,
            BookingRequestModel bookingRequest) {
        var serviceBuilder =
                BookingRequestConverter.INSTANCE.groomingToSelectedServiceDef(grooming.getService()).toBuilder();
        serviceBuilder.setStartDate(
                getStartDate(grooming.getService(), bookingRequest.getStartDate(), bookingRequest.getEndDate()));

        var specifiedService = request.getGroomingServicesList().stream()
                .filter(e -> Objects.equals(e.getId(), grooming.getService().getId()))
                .findFirst()
                .orElse(null);
        if (specifiedService != null) {
            if (specifiedService.hasStaffId()) {
                serviceBuilder.setStaffId(specifiedService.getStaffId());
            }
            if (specifiedService.hasStartTime()) {
                serviceBuilder.setStartTime(specifiedService.getStartTime());
                serviceBuilder.setEndTime(
                        specifiedService.getStartTime() + grooming.getService().getServiceTime());
            }
        }

        petDetailBuilder.addServices(serviceBuilder.build());

        for (var addon : grooming.getAddonsList()) {
            var addonBuilder =
                    BookingRequestConverter.INSTANCE
                            .groomingAddOnToSelectedServiceAddOnDef(addon, addon.getStartDate())
                            .toBuilder();

            var specifiedAddon = request.getGroomingAddonsList().stream()
                    .filter(e -> Objects.equals(e.getId(), addon.getId()))
                    .findFirst()
                    .orElse(null);
            if (specifiedAddon != null) {
                if (specifiedAddon.hasStaffId()) {
                    addonBuilder.setStaffId(specifiedAddon.getStaffId());
                }
                if (specifiedAddon.hasStartTime()) {
                    addonBuilder.setStartTime(specifiedAddon.getStartTime());
                }
            }

            petDetailBuilder.addAddOns(addonBuilder.build());
        }
    }

    private static void processBoarding(
            PetDetailDef.Builder petDetailBuilder,
            BookingRequestModel.BoardingService boarding,
            AcceptBookingRequestV2Request request,
            BookingRequestModel bookingRequest) {
        var serviceBuilder = BookingRequestConverter.INSTANCE
                .boardingToSelectedServiceDef(
                        boarding.getService(),
                        boarding.getService().getLodgingId(),
                        boarding.getFeedingsList(),
                        boarding.getMedicationsList())
                .toBuilder();

        var specifiedService = request.getBoardingServicesList().stream()
                .filter(e -> Objects.equals(e.getId(), boarding.getService().getId()))
                .findFirst()
                .orElse(null);
        if (specifiedService != null) {
            if (specifiedService.hasLodgingId()) {
                serviceBuilder.setLodgingId(specifiedService.getLodgingId());
            }
        }

        petDetailBuilder.addServices(serviceBuilder.build());

        for (var addon : boarding.getAddonsList()) {
            var addonBuilder = BookingRequestConverter.INSTANCE
                    .boardingAddOnToSelectedServiceAddOnDef(
                            addon,
                            bookingRequest.getStartDate(),
                            bookingRequest.getStartTime(),
                            boarding.getService().getServiceId())
                    .toBuilder();

            var specifiedAddon = request.getBoardingAddonsList().stream()
                    .filter(e -> Objects.equals(e.getId(), addon.getId()))
                    .findFirst()
                    .orElse(null);
            if (specifiedAddon != null) {
                if (specifiedAddon.hasStartTime()) {
                    addonBuilder.setStartTime(specifiedAddon.getStartTime());
                }
                if (specifiedAddon.hasStaffId()) {
                    addonBuilder.setStaffId(specifiedAddon.getStaffId());
                }
            }

            petDetailBuilder.addAddOns(addonBuilder.build());
        }
    }

    private static String getStartDate(
            GroomingServiceDetailModel grooming, String bookingRequestStartDate, String bookingRequestEndDate) {
        if (grooming.hasStartDate()) {
            return grooming.getStartDate();
        }
        switch (grooming.getDateType()) {
            case PET_DETAIL_DATE_FIRST_DAY -> {
                return bookingRequestStartDate;
            }
            case PET_DETAIL_DATE_LAST_DAY -> {
                return bookingRequestEndDate;
            }
            default -> {
                return bookingRequestStartDate;
            }
        }
    }

    private static PetDetailDef buildPetDetailDefForDaycare(
            BookingRequestModel.DaycareService daycare,
            AcceptBookingRequestV2Request request,
            BookingRequestModel bookingRequest,
            LocalDate date) {

        var petDetailBuilder =
                PetDetailDef.newBuilder().setPetId(daycare.getService().getPetId());

        var serviceBuilder = BookingRequestConverter.INSTANCE
                .daycareToSelectedServiceDef(
                        daycare.getService(),
                        date.toString(),
                        date.toString(),
                        0L,
                        daycare.getFeedingsList(),
                        daycare.getMedicationsList())
                .toBuilder()
                .clearSpecificDates()
                .addSpecificDates(date.toString());

        var specifiedService = request.getDaycareServicesList().stream()
                .filter(e -> Objects.equals(e.getId(), daycare.getService().getId()))
                .findFirst()
                .orElse(null);
        if (specifiedService != null) {
            if (specifiedService.hasLodgingId()) {
                serviceBuilder.setLodgingId(specifiedService.getLodgingId());
            }
        }

        petDetailBuilder.addServices(serviceBuilder.build());

        for (var addon : daycare.getAddonsList()) {

            if (!addon.getIsEveryday() && !addon.getSpecificDatesList().contains(date.toString())) {
                continue;
            }

            var addonBuilder = BookingRequestConverter.INSTANCE
                    .daycareAddOnToSelectedServiceAddOnDef(
                            addon,
                            date.toString(),
                            bookingRequest.getStartTime(),
                            daycare.getService().getServiceId(),
                            List.of(date.toString()))
                    .toBuilder();

            var specifiedAddon = request.getDaycareAddonsList().stream()
                    .filter(e -> Objects.equals(e.getId(), addon.getId()))
                    .findFirst()
                    .orElse(null);
            if (specifiedAddon != null) {
                if (specifiedAddon.hasStartTime()) {
                    addonBuilder.setStartTime(specifiedAddon.getStartTime());
                }
                if (specifiedAddon.hasStaffId()) {
                    addonBuilder.setStaffId(specifiedAddon.getStaffId());
                    // require staff 的 daycare add-on 只可能和 daycare service 同一天
                    // @see https://moego.atlassian.net/browse/CS-30026
                    addonBuilder
                            .setAddonDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                            .setStartDate(date.toString())
                            .clearSpecificDates();
                }
            }

            petDetailBuilder.addAddOns(addonBuilder.build());
        }

        return petDetailBuilder.build();
    }

    private void processEvaluation(
            PetDetailDef.Builder petDetailBuilder,
            BookingRequestModel.EvaluationService evaluation,
            AcceptBookingRequestV2Request request,
            long businessId) {
        var evaluationBuilder =
                BookingRequestConverter.INSTANCE.evaluationToSelectedServiceDef(evaluation.getService()).toBuilder();

        var specifiedEvaluation = request.getEvaluationServicesList().stream()
                .filter(e -> Objects.equals(e.getId(), evaluation.getService().getId()))
                .findFirst()
                .orElse(null);
        if (specifiedEvaluation != null) {
            if (specifiedEvaluation.hasStaffId()) {
                evaluationBuilder.setStaffId(specifiedEvaluation.getStaffId());
            }
            if (specifiedEvaluation.hasEvaluationId()) {
                var evaluationModel = mustGetEvaluation(specifiedEvaluation.getEvaluationId(), businessId);
                evaluationBuilder.setServiceId(evaluationModel.getId());
                evaluationBuilder.setServicePrice(evaluationModel.getPrice());
                evaluationBuilder.setServiceTime(evaluationModel.getDuration());
            }
        }

        petDetailBuilder.addEvaluations(evaluationBuilder.build());
    }

    private EvaluationBriefView mustGetEvaluation(long evaluationId, long businessId) {
        var evaluation = serviceHelper
                .getEvaluationByIds(List.of(evaluationId), businessId)
                .get(evaluationId);
        if (evaluation == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Evaluation not found: " + evaluationId);
        }
        return evaluation;
    }

    private static AppointmentNoteCreateDef buildAppointmentNoteCreateDef(BookingRequestModel bookingRequest) {
        return AppointmentNoteCreateDef.newBuilder()
                .setNote(bookingRequest.getAdditionalNote())
                .setType(AppointmentNoteType.ADDITIONAL)
                .build();
    }

    private static AppointmentCreateForOnlineBookingDef buildAppointmentCreateForOnlineBookingDef(
            BookingRequestModel bookingRequest) {
        var appointmentBuilder = AppointmentCreateForOnlineBookingDef.newBuilder();
        appointmentBuilder.setCustomerId(bookingRequest.getCustomerId());
        appointmentBuilder.setStatus(AppointmentStatus.UNCONFIRMED);
        appointmentBuilder.setCreatedAt(Timestamps.fromDate(new java.util.Date()));
        return appointmentBuilder.build();
    }

    private void refund(long businessId, long bookingRequestId) {
        var deposit = depositApi.getOBDepositByBookingRequestId(Math.toIntExact(businessId), bookingRequestId);
        if (deposit == null) {
            return;
        }

        var params = new CreateRefundByPaymentIdParams();
        params.setPaymentId(deposit.getPaymentId());
        params.setReason("cancel for online booking decline");
        refundApi.createRefundByPaymentId(Math.toIntExact(businessId), params);
    }

    @Override
    public void countBookingRequests(
            CountBookingRequestsRequest request, StreamObserver<CountBookingRequestsResponse> responseObserver) {
        int count = bookingRequestService.countBookingRequest(
                BookingRequestConverter.INSTANCE.countRequestToFilterDTO(request));
        responseObserver.onNext(
                CountBookingRequestsResponse.newBuilder().setCount(count).build());
        responseObserver.onCompleted();
    }

    @Override
    public void listBookingRequestId(
            ListBookingRequestIdRequest request, StreamObserver<ListBookingRequestIdResponse> responseObserver) {
        var appointmentIdToBookingRequestId =
                bookingRequestAppointmentMappingService.listByAppointmentIds(request.getAppointmentIdsList()).stream()
                        .collect(toMap(
                                BookingRequestAppointmentMapping::getAppointmentId,
                                BookingRequestAppointmentMapping::getBookingRequestId,
                                (o, n) -> o));

        responseObserver.onNext(ListBookingRequestIdResponse.newBuilder()
                .putAllAppointmentIdToBookingRequestId(appointmentIdToBookingRequestId)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void syncBookingRequestFromAppointment(
            SyncBookingRequestFromAppointmentRequest request,
            StreamObserver<SyncBookingRequestFromAppointmentResponse> responseObserver) {

        for (var appointmentId : request.getAppointmentIdList()) {
            var params = new BookingRequestEventParams();
            params.setAppointmentId(Math.toIntExact(appointmentId));
            params.setEvent(BookingRequestEventParams.BookingRequestEvent.SUBMITTED);
            syncBookingRequestListener.processEvent(params);
        }

        responseObserver.onNext(SyncBookingRequestFromAppointmentResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void triggerBookingRequestAutoAccepted(
            TriggerBookingRequestAutoAcceptedRequest request,
            StreamObserver<TriggerBookingRequestAutoAcceptedResponse> responseObserver) {
        boolean isAutoAccepted;
        var bookingRequest = bookingRequestService.mustGet(request.getId());
        if (bookingRequest.getStatus() == BookingRequestStatus.WAIT_LIST) {
            isAutoAccepted = false;
            // waitlist notification
            ThreadPool.execute(() -> sendOBWaitlistSignedUpNotification(bookingRequest));

        } else {
            // booking request notification
            // auto accept if possible
            isAutoAccepted = autoAccept(request.getId());

            // sync C-end update to B-end
            if (isAutoAccepted) {
                ThreadPool.execute(() -> syncCustomerProfile(request.getId()));
            }

            // send notification
            ThreadPool.execute(() -> sendOBBookingRequestNotification(request.getId(), isAutoAccepted));
        }

        responseObserver.onNext(TriggerBookingRequestAutoAcceptedResponse.newBuilder()
                .setIsAutoAccepted(isAutoAccepted)
                .build());
        responseObserver.onCompleted();
    }

    private void syncCustomerProfile(long bookingRequestId) {
        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
        if (bookingRequest.getStatus() == BookingRequestStatus.WAIT_LIST) {
            return;
        }

        var businessId = toIntExact(bookingRequest.getBusinessId());
        var customerId = toIntExact(bookingRequest.getCustomerId());

        var customerProfileRequest = profileRequestApi.getCustomerProfileRequest(businessId, customerId);
        if (customerProfileRequest == null) {
            return;
        }

        var existingPets = Optional.ofNullable(customerProfileRequest.getPets()).stream()
                .flatMap(Collection::stream)
                .filter(e -> isNormal(e.getPetId()))
                .toList();
        customerProfileRequest.setPets(existingPets);

        profileRequestApi.updateCustomerAndProfileRequest(customerProfileRequest, true);

        var dto = new BookOnlineQuestionSaveDTO();
        dto.setCompanyId(bookingRequest.getCompanyId());
        dto.setBusinessId(businessId);
        dto.setCustomerId(customerId);
        dto.setClientCustomQuestionMap(customerProfileRequest.getClient().getCustomQuestions());
        dto.setPetCustomQuestionMap(existingPets.stream()
                .collect(toMap(
                        CustomerProfileRequestDTO.PetProfileDTO::getPetId,
                        pet -> Optional.ofNullable(pet.getCustomQuestions()).orElse(Map.of()))));
        bookOnlineQuestionApi.upsertCustomerQuestionSave(dto, true);
    }

    private boolean autoAccept(long bookingRequestId) {

        var bookingRequestModel = mustGetBookingRequestModel(null, bookingRequestId);
        if (bookingRequestModel.getStatus() != BookingRequestStatus.SUBMITTED) {
            return false;
        }

        // check service type, only boarding and daycare can be auto accepted
        int serviceTypeInclude = bookingRequestModel.getServiceTypeInclude();
        ServiceItemType serviceItemType = null;
        if (ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)) {
            serviceItemType = ServiceItemType.BOARDING;
        } else if (ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude)) {
            serviceItemType = ServiceItemType.DAYCARE;
        }
        if (Objects.isNull(serviceItemType)) {
            log.info("Auto accept failed, service type not match. bookingRequestId: {}", bookingRequestId);
            return false;
        }

        // 两种情况不能 auto accept：
        // 1. 有 require staff 的 addon，需要手动 staff
        // 2. grooming service 还没有 staff 或者 time
        boolean hasRequiredStaffAddon = hasRequiredStaffAddOn(bookingRequestModel);
        if (hasRequiredStaffAddon || hasIncompleteGroomingService(bookingRequestModel)) {
            return false;
        }

        Set<Long> obRequestPetIds = bookingRequestModel.getServicesList().stream()
                .map(BookingRequestServer::getPetId)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());

        boolean canAutoAccept = canAutoAccept(bookingRequestModel, serviceItemType, obRequestPetIds);

        if (!canAutoAccept) {
            return false;
        }

        // auto assign for boarding & daycare
        List<PetToLodgingDef> petToLodgingsList = List.of();
        if (ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)
                || ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude)) {
            GetAutoAssignResponse autoAssignResponse = getAutoAssignResponse(GetAutoAssignRequest.newBuilder()
                    .setId(bookingRequestId)
                    .setCompanyId(bookingRequestModel.getCompanyId())
                    .setBusinessId(bookingRequestModel.getBusinessId())
                    .build());
            petToLodgingsList = autoAssignResponse.getPetToLodgingsList();
            log.info("Auto assign result: {}", JsonUtil.toJson(autoAssignResponse));

            if (ObjectUtils.isEmpty(petToLodgingsList) && ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)) {
                log.info("Auto assign failed, petToLodgingsList is empty. bookingRequestId: {}", bookingRequestId);
                return false;
            }

            Set<Long> petIds =
                    petToLodgingsList.stream().map(PetToLodgingDef::getPetId).collect(Collectors.toSet());
            boolean petHasNoLodging = bookingRequestModel.getServicesList().stream()
                    .filter(BookingRequestModel.Service::hasBoarding)
                    .map(service -> service.getBoarding().getService().getPetId())
                    .distinct()
                    .anyMatch(not(petIds::contains));
            if (petHasNoLodging) {
                log.info("Auto assign failed, some pet has no lodging room. bookingRequestId: {}", bookingRequestId);
                return false;
            }
        }

        // auto accept
        AcceptBookingRequestResponse acceptBookingRequestResponse =
                getAcceptBookingRequestResponse(AcceptBookingRequestRequest.newBuilder()
                        .setId(bookingRequestId)
                        .setCompanyId(bookingRequestModel.getCompanyId())
                        .setBusinessId(bookingRequestModel.getBusinessId())
                        .addAllPetToLodgings(petToLodgingsList)
                        .addAllPetToStaffs(List.of())
                        .build());
        if (!acceptBookingRequestResponse.getResult()) {
            log.info("Auto accept failed when scheduling this appointment. bookingRequestId: {}", bookingRequestId);
            return false;
        }

        log.info("Auto accept success. bookingRequestId: {}", bookingRequestId);
        return true;
    }

    private static boolean hasIncompleteGroomingService(BookingRequestModel bookingRequestModel) {
        var groomings = bookingRequestModel.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .toList();

        for (var grooming : groomings) {
            var service = grooming.getGrooming().getService();
            if (!isNormal(service.getStaffId()) || !service.hasStartTime()) {
                return true;
            }
        }

        return false;
    }

    private void sendOBBookingRequestNotification(long bookingRequestId, boolean isAutoAccepted) {

        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);

        // web 站内 + mobile notification
        sendOBReceivedNotification(bookingRequest);

        // 发送 'Online booking -> Notification -> When client submitted booking request -> Business receive' 通知
        sendOBSubmittedNotification(bookingRequest, isAutoAccepted);
    }

    private void sendOBWaitlistSignedUpNotification(BookingRequest bookingRequest) {
        var extra = new NotificationExtraWaitlistSignedUpDto();
        extra.setWaitlistId(bookingRequest.getId());
        extra.setCustomerId(bookingRequest.getCustomerId());
        extra.setServiceItemType(ServiceItemEnum.convertBitValueList(bookingRequest.getServiceTypeInclude()));
        extra.setStartDate(bookingRequest.getStartDate());
        extra.setEndDate(bookingRequest.getEndDate());

        var param = new NotificationWaitlistSignedUpParams();
        param.setBusinessId(Math.toIntExact(bookingRequest.getBusinessId()));
        param.setIsSendMobilePush(false); // 不给 mobile 发通知，mobile 还没有适配 BD
        param.setIsNotifyBusinessAllStaff(true);
        param.setWebPushDto(extra);
        notificationApi.sendNotificationWaitlistSignedUp(param);
    }

    private void sendOBSubmittedNotification(BookingRequest bookingRequest, boolean isAutoAccepted) {
        OnlineBookWaitingNotifyParams onlineBookWaitingNotifyParams = new OnlineBookWaitingNotifyParams();
        onlineBookWaitingNotifyParams.setCompanyId(bookingRequest.getCompanyId());
        onlineBookWaitingNotifyParams.setBusinessId(toIntExact(bookingRequest.getBusinessId()));
        onlineBookWaitingNotifyParams.setBookingRequestId(bookingRequest.getId());
        if (isAutoAccepted) {
            onlineBookWaitingNotifyParams.setType(OnlineBookWaitingNotifyParams.TYPE_ACCEPT);
        } else {
            onlineBookWaitingNotifyParams.setType(OnlineBookWaitingNotifyParams.TYPE_SUBMIT);
        }
        notificationApi.bookOnlineNotify(onlineBookWaitingNotifyParams);
    }

    private void sendOBReceivedNotification(BookingRequest bookingRequest) {
        var customer = customerHelper.mustGetCustomer(bookingRequest.getCustomerId());

        NotificationExtraOBReqestDto extra = new NotificationExtraOBReqestDto();
        extra.setGroomingId(0);
        extra.setAppointmentDate(bookingRequest.getStartDate());
        extra.setAppointmentStartTime(bookingRequest.getStartTime());
        extra.setNoStartTime(false);
        extra.setAppointmentEndTime(bookingRequest.getEndTime());
        extra.setCustomerId(toIntExact(bookingRequest.getCustomerId()));
        extra.setCustomerFirstName(customer.getFirstName());
        extra.setCustomerLastName(customer.getLastName());
        extra.setStaffId(0);
        extra.setBookingRequestId(bookingRequest.getId());
        extra.setServices(List.of());

        NotificationOBRequestReceivedParams param = new NotificationOBRequestReceivedParams();
        param.setBusinessId(toIntExact(bookingRequest.getBusinessId()));
        param.setIsSendMobilePush(false); // 不给 mobile 发通知，mobile 还没有适配 BD
        param.setWebPushDto(extra);
        notificationApi.sendNotificationOBRequestReceived(param);
    }

    private boolean hasRequiredStaffAddOn(BookingRequestModel bookingRequest) {
        List<Long> addOnIds = bookingRequest.getServicesList().stream()
                .flatMap(service -> switch (service.getServiceCase()) {
                    case GROOMING -> Optional.of(service.getGrooming().getAddonsList()).orElse(List.of()).stream()
                            .map(GroomingAddOnDetailModel::getAddOnId);
                    case BOARDING -> Optional.of(service.getBoarding().getAddonsList()).orElse(List.of()).stream()
                            .map(BoardingAddOnDetailModel::getAddOnId);
                    case DAYCARE -> Optional.of(service.getDaycare().getAddonsList()).orElse(List.of()).stream()
                            .map(DaycareAddOnDetailModel::getAddOnId);
                    default -> Stream.empty();
                })
                .distinct()
                .toList();

        if (isEmpty(addOnIds)) {
            return false;
        }

        boolean hasRequiredStaffAddOn = serviceHelper.listService(addOnIds).values().stream()
                .anyMatch(service -> ServiceType.ADDON.equals(service.getType()) && service.getRequireDedicatedStaff());
        if (hasRequiredStaffAddOn) {
            log.info("Auto accept failed, add on required staff. bookingRequestId: {}", bookingRequest.getId());
            return true;
        }

        return false;
    }

    private boolean canAutoAccept(
            BookingRequestModel bookingRequest, ServiceItemType serviceItemType, Collection<Long> obRequestPetIds) {

        if (cannotAutoAcceptForEvaluation(bookingRequest)) {
            return false;
        }

        long companyId = bookingRequest.getCompanyId();
        long businessId = bookingRequest.getBusinessId();
        long customerId = bookingRequest.getCustomerId();

        var automationSetting = AutomationConverter.INSTANCE.toModel(
                automationSettingService.getAutomationSetting(companyId, businessId, serviceItemType));

        if (!automationSetting.getEnableAutoAccept()) {
            log.info("Auto accept failed, auto accept not enabled. bookingRequestId: {}", bookingRequest.getId());
            return false;
        }

        AutomationConditionDef autoAcceptCondition = automationSetting.getAutoAcceptCondition();
        AcceptClientType acceptClientType = autoAcceptCondition.getAcceptClientType();
        ProfileUpdateCondition profileUpdatesCondition = autoAcceptCondition.getProfileUpdateCondition();
        VaccineStatusCondition vaccineStatusCondition = autoAcceptCondition.getVaccineStatusCondition();

        if (AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH.equals(acceptClientType)
                && ProfileUpdateCondition.PROFILE_UPDATE_CONDITION_ALL.equals(profileUpdatesCondition)
                && VaccineStatusCondition.VACCINE_STATUS_CONDITION_ALL.equals(vaccineStatusCondition)) {
            return true;
        }

        boolean canAutoAccept =
                switch (acceptClientType) {
                    case ACCEPT_CLIENT_TYPE_NEW -> {
                        boolean isNewCustomer = bookingRequest.getAttr().getIsNewVisitor();
                        if (!isNewCustomer) {
                            log.info(
                                    "Auto accept failed, customer is existing. bookingRequestId: {}",
                                    bookingRequest.getId());
                        }
                        yield isNewCustomer;
                    }
                    case ACCEPT_CLIENT_TYPE_EXISTING -> {
                        boolean isExistingCustomer = !bookingRequest.getAttr().getIsNewVisitor();
                        if (!isExistingCustomer) {
                            log.info(
                                    "Auto accept failed, customer is new. bookingRequestId: {}",
                                    bookingRequest.getId());
                        }
                        yield isExistingCustomer;
                    }
                    default -> true;
                };
        if (!canAutoAccept) {
            return false;
        }

        var customerUpdateDTO = obApi.listCustomerHasRequestUpdate(
                        new CustomerIdsParams(toIntExact(businessId), List.of(toIntExact(customerId))))
                .get(toIntExact(customerId));
        canAutoAccept = switch (profileUpdatesCondition) {
            case PROFILE_UPDATE_CONDITION_WITHOUT_UPDATE -> {
                boolean noUpdate = !customerUpdateDTO.hasRequestUpdate();
                if (!noUpdate) {
                    log.info(
                            "Auto accept failed, profile has been updated. bookingRequestId: {}",
                            bookingRequest.getId());
                }
                yield noUpdate;
            }
            default -> true;};
        if (!canAutoAccept) {
            return false;
        }

        List<CustomerProfileRequestDTO.PetProfileDTO> pets = customerUpdateDTO.mergedProfile().getPets().stream()
                .filter(pet -> obRequestPetIds.contains(pet.getPetId().longValue()))
                .toList();
        return switch (vaccineStatusCondition) {
            case VACCINE_STATUS_CONDITION_NO_MISSING_OR_EXPIRED -> {
                boolean notExpired = vaccineHelper.vaccineNotExpired(pets, bookingRequest.getEndDate());
                if (!notExpired) {
                    log.info("Auto accept failed, vaccine is expired. bookingRequestId: {}", bookingRequest.getId());
                    yield false;
                }
                boolean notMissing = vaccineHelper.vaccineNotMissing(pets, companyId, serviceItemType);
                if (!notMissing) {
                    log.info("Auto accept failed, vaccine is missing. bookingRequestId: {}", bookingRequest.getId());
                }
                yield notMissing;
            }
            default -> true;
        };
    }

    private static boolean cannotAutoAcceptForEvaluation(BookingRequestModel bookingRequest) {
        // 当 booking request 的 services 数量大于 1 且有 evaluation 时，不能 auto accept
        // 因为 evaluation 需要单独创建一个 appointment
        // 当只有一个 evaluation 时，可以 auto accept（向后兼容）
        return bookingRequest.getServicesCount() > 1
                && bookingRequest.getServicesList().stream().anyMatch(BookingRequestModel.Service::hasEvaluation);
    }

    /*private*/ void refreshBookingRequest(long bookingRequestId) {
        // 1) Update booking request service_type_include
        // 在查询 service details 时，会依赖 service_type_include 的值，所以先更新
        refreshBookingRequestServiceTypeInclude(bookingRequestId);

        var bookingRequestModel = mustGetBookingRequestModel(null, bookingRequestId);

        // 1.1) 时间修正
        // evaluation 场景下，end time 不能超过 business 设置的 working hour
        var flushBookingRequestModule = refreshDateForEvaluationServiceDetails(bookingRequestModel.getServicesList());
        if (flushBookingRequestModule) {
            bookingRequestModel = mustGetBookingRequestModel(null, bookingRequestId);
        }

        // 2) Update booking request date/time 数据
        refreshBookingRequestDateTime(bookingRequestModel);

        // 3）为某些 DateType（first day/last day）设置 start date / end date
        refreshDateBasedOnDateType(bookingRequestModel.getServicesList());
    }

    private void refreshBookingRequestServiceTypeInclude(long bookingRequestId) {
        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequestId);
        updateBean.setServiceTypeInclude(bookingRequestService.getServiceTypeInclude(bookingRequestId));
        bookingRequestService.update(updateBean);
    }

    private void refreshBookingRequestDateTime(BookingRequestModel bookingRequestModel) {
        var updateBean = reCalculateBookingRequestTime(bookingRequestModel.getServicesList());
        updateBean.setId(bookingRequestModel.getId());
        bookingRequestService.update(updateBean);
    }

    private void refreshDateBasedOnDateType(List<BookingRequestModel.Service> serviceDetails) {

        var bookingRequestDateTime = reCalculateBookingRequestTime(serviceDetails);

        // 这里暂时需要处理的 DateType 类型有 First Day 和 Last Day
        // 只有 grooming service 和 boarding add-on 可以选用这些 DateType
        // 所以只用处理 grooming service 和 boarding add-on 即可

        refreshDateForGroomingServiceDetails(serviceDetails, bookingRequestDateTime);

        refreshDateForBoardingServiceDetails(serviceDetails, bookingRequestDateTime);
    }

    private boolean refreshDateForEvaluationServiceDetails(List<BookingRequestModel.Service> serviceDetails) {
        Set<Long> serviceIds = new HashSet<>();
        String evaluationStartDate = null;
        Long bookingRequestId = null;
        List<Long> evaluationIdList = new ArrayList<>();
        for (BookingRequestModel.Service serviceDetail : serviceDetails) {
            switch (serviceDetail.getServiceCase()) {
                case BOARDING -> serviceIds.add(
                        serviceDetail.getBoarding().getService().getId());

                case DAYCARE -> serviceIds.add(
                        serviceDetail.getDaycare().getService().getId());

                case EVALUATION -> {
                    evaluationStartDate =
                            serviceDetail.getEvaluation().getService().getStartDate();
                    bookingRequestId =
                            serviceDetail.getEvaluation().getService().getBookingRequestId();
                    evaluationIdList.add(
                            serviceDetail.getEvaluation().getService().getId());
                }
                case GROOMING, DOG_WALKING, GROUP_CLASS, SERVICE_NOT_SET -> {}
            }
        }

        if (bookingRequestId != null && StringUtils.hasText(evaluationStartDate)) {
            var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
            var localDateStartDate = LocalDate.parse(evaluationStartDate);
            // 需要计算出最大 end time
            var evaluationArrivalTimeRange = obAvailableDateTimeService
                    .getCareTypeAvailableTimeRange(
                            bookingRequest.getCompanyId(),
                            bookingRequest.getBusinessId(),
                            localDateStartDate,
                            localDateStartDate,
                            ServiceItemType.EVALUATION,
                            serviceIds.stream().toList())
                    .getFirst();
            int maxEndTime = evaluationArrivalTimeRange.get(localDateStartDate).stream()
                    .mapToInt(DayTimeRangeDef::getEndTime)
                    .max()
                    .orElse(0);
            if (maxEndTime > 0) {
                evaluationTestDetailService.updateMaxEndTime(bookingRequestId, evaluationIdList, maxEndTime);
                return true;
            }
        }
        return false;
    }

    private void refreshDateForBoardingServiceDetails(
            List<BookingRequestModel.Service> serviceDetails, BookingRequest bookingRequestDateTime) {
        var boardingServiceDetails = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .map(BookingRequestModel.Service::getBoarding)
                .toList();

        for (var boardingServiceDetail : boardingServiceDetails) {
            doRefreshDateForBoardingAddons(boardingServiceDetail, bookingRequestDateTime);
        }
    }

    private void doRefreshDateForBoardingAddons(
            BookingRequestModel.BoardingService boardingServiceDetail, BookingRequest bookingRequestDateTime) {
        for (var boardingAddOnDetail : boardingServiceDetail.getAddonsList()) {
            var updateBean = new BoardingAddOnDetail();
            updateBean.setId(boardingAddOnDetail.getId());

            switch (boardingAddOnDetail.getDateType()) {
                case PET_DETAIL_DATE_LAST_DAY -> {
                    if (StringUtils.hasText(bookingRequestDateTime.getStartDate())) {
                        updateBean.setStartDate(LocalDate.parse(bookingRequestDateTime.getEndDate()));
                    }
                }
                case PET_DETAIL_DATE_FIRST_DAY -> {
                    if (StringUtils.hasText(bookingRequestDateTime.getStartDate())) {
                        updateBean.setStartDate(LocalDate.parse(bookingRequestDateTime.getStartDate()));
                    }
                }
                default -> {}
            }

            boardingAddOnDetailService.update(updateBean);
        }
    }

    private void refreshDateForGroomingServiceDetails(
            List<BookingRequestModel.Service> serviceDetails, BookingRequest bookingRequestDateTime) {
        var groomingServiceDetails = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .map(BookingRequestModel.Service::getGrooming)
                .toList();

        for (var groomingServiceDetail : groomingServiceDetails) {

            doRefreshDateForGroomingService(bookingRequestDateTime, groomingServiceDetail.getService());

            doRefreshDateForGroomingAddons(groomingServiceDetail);
        }
    }

    private void doRefreshDateForGroomingAddons(BookingRequestModel.GroomingService groomingServiceDetail) {
        var groomingServiceDetailId = groomingServiceDetail.getService().getId();
        var groomingService = Optional.ofNullable(groomingServiceDetailService.get(groomingServiceDetailId))
                .orElseThrow(() -> bizException(
                        Code.CODE_PARAMS_ERROR, "Grooming service detail not found: " + groomingServiceDetailId));

        for (var groomingAddOnDetail : groomingServiceDetail.getAddonsList()) {
            var updateBean = new GroomingAddOnDetail();
            updateBean.setId(groomingAddOnDetail.getId());
            updateBean.setStartDate(groomingService.getStartDate());
            updateBean.setEndDate(groomingService.getEndDate());
            groomingAddOnDetailService.update(updateBean);
        }
    }

    private void doRefreshDateForGroomingService(
            BookingRequest bookingRequestDateTime, GroomingServiceDetailModel groomingService) {
        var updateBean = new GroomingServiceDetail();
        updateBean.setId(groomingService.getId());

        switch (groomingService.getDateType()) {
            case PET_DETAIL_DATE_LAST_DAY -> {
                if (StringUtils.hasText(bookingRequestDateTime.getEndDate())) {
                    updateBean.setStartDate(bookingRequestDateTime.getEndDate());
                }
                if (StringUtils.hasText(bookingRequestDateTime.getEndDate())) {
                    updateBean.setEndDate(bookingRequestDateTime.getEndDate());
                }
            }
            case PET_DETAIL_DATE_FIRST_DAY -> {
                if (StringUtils.hasText(bookingRequestDateTime.getStartDate())) {
                    updateBean.setStartDate(bookingRequestDateTime.getStartDate());
                }
                if (StringUtils.hasText(bookingRequestDateTime.getStartDate())) {
                    updateBean.setEndDate(bookingRequestDateTime.getStartDate());
                }
            }
            default -> {}
        }

        groomingServiceDetailService.update(updateBean);
    }

    static BookingRequest reCalculateBookingRequestTime(List<BookingRequestModel.Service> services) {
        // First, collect all boarding and daycare date ranges to establish the valid date range
        var validDates = new TreeSet<String>();

        for (var service : services) {
            switch (service.getServiceCase()) {
                case BOARDING -> {
                    var boarding = service.getBoarding().getService();
                    String startDate = boarding.getStartDate();
                    String endDate = boarding.getEndDate();
                    if (StringUtils.hasText(startDate) && StringUtils.hasText(endDate)) {
                        LocalDate.parse(startDate).datesUntil(LocalDate.parse(endDate).plusDays(1))
                                .map(LocalDate::toString)
                                .forEach(validDates::add);
                    }
                }
                case DAYCARE -> {
                    var daycare = service.getDaycare().getService();
                    List<String> dates = daycare.getSpecificDatesList();
                    validDates.addAll(dates);
                }
                default -> {} // Only boarding and daycare define the valid date range
            }
        }

        // If no boarding or daycare services exist, fall back to original logic
        if (validDates.isEmpty()) {
            return calculateTimeRangeFromAllServices(services);
        }

        // Now collect time points from all services, but only include dates within the valid range
        List<DatePoint> starts = new ArrayList<>();
        List<DatePoint> ends = new ArrayList<>();

        for (var service : services) {
            switch (service.getServiceCase()) {
                case GROOMING -> {
                    var grooming = service.getGrooming().getService();
                    String startDate = grooming.getStartDate();
                    String endDate = grooming.getEndDate();
                    if (StringUtils.hasText(startDate) && validDates.contains(startDate)) {
                        var startTime = grooming.hasStartTime() ? grooming.getStartTime() : null;
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate) && validDates.contains(endDate)) {
                        var endTime = grooming.hasEndTime() ? grooming.getEndTime() : null;
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case BOARDING -> {
                    var boarding = service.getBoarding().getService();
                    String startDate = boarding.getStartDate();
                    String endDate = boarding.getEndDate();
                    if (StringUtils.hasText(startDate)) {
                        Integer startTime = boarding.getStartTime();
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate)) {
                        Integer endTime = boarding.getEndTime();
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case DAYCARE -> {
                    var daycare = service.getDaycare().getService();
                    List<String> dates =
                            daycare.getSpecificDatesList().stream().sorted().toList();
                    String startDate = firstElement(dates);
                    String endDate = lastElement(dates);
                    Integer startTime = daycare.getStartTime();
                    Integer endTime = daycare.getEndTime();
                    if (StringUtils.hasText(startDate)) {
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate)) {
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case EVALUATION -> {
                    var evaluation = service.getEvaluation().getService();
                    String startDate = evaluation.getStartDate();
                    String endDate = evaluation.getEndDate();
                    if (StringUtils.hasText(startDate) && validDates.contains(startDate)) {
                        Integer startTime = evaluation.getStartTime();
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate) && validDates.contains(endDate)) {
                        Integer endTime = evaluation.getEndTime();
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case DOG_WALKING -> {
                    var dogWalking = service.getDogWalking().getService();
                    String startDate = dogWalking.getStartDate();
                    String endDate = dogWalking.getEndDate();
                    if (StringUtils.hasText(startDate) && validDates.contains(startDate)) {
                        Integer startTime = dogWalking.getStartTime();
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate) && validDates.contains(endDate)) {
                        Integer endTime = dogWalking.getEndTime();
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case GROUP_CLASS -> {
                    var groupClass = service.getGroupClass().getService();
                    List<String> dates = groupClass.getSpecificDatesList();
                    // Only include dates that are within the valid date range
                    List<String> validServiceDates =
                            dates.stream().filter(validDates::contains).sorted().toList();

                    if (!validServiceDates.isEmpty()) {
                        String startDate = firstElement(validServiceDates);
                        String endDate = lastElement(validServiceDates);
                        Integer startTime = groupClass.getStartTime();
                        Integer endTime = groupClass.getEndTime();
                        starts.add(new DatePoint(startDate, startTime));
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                default -> {
                    // no-op
                }
            }
        }

        if (starts.isEmpty() || ends.isEmpty()) {
            return new BookingRequest();
        }

        DatePoint min = DatePoint.min(starts);
        DatePoint max = DatePoint.max(ends);

        var updateBean = new BookingRequest();
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        return updateBean;
    }

    /**
     * Add all dates in the range [startDate, endDate] to the given set
     */
    private static void addDateRangeToSet(Set<String> dateSet, String startDate, String endDate) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            LocalDate current = start;
            while (!current.isAfter(end)) {
                dateSet.add(current.toString());
                current = current.plusDays(1);
            }
        } catch (Exception e) {
            // If date parsing fails, skip this range
        }
    }

    private static BookingRequest calculateTimeRangeFromAllServices(List<BookingRequestModel.Service> services) {
        List<DatePoint> starts = new ArrayList<>();
        List<DatePoint> ends = new ArrayList<>();

        for (var service : services) {
            switch (service.getServiceCase()) {
                case GROOMING -> {
                    var grooming = service.getGrooming().getService();
                    String startDate = grooming.getStartDate();
                    String endDate = grooming.getEndDate();
                    if (StringUtils.hasText(startDate)) {
                        var startTime = grooming.hasStartTime() ? grooming.getStartTime() : null;
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate)) {
                        var endTime = grooming.hasEndTime() ? grooming.getEndTime() : null;
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case BOARDING -> {
                    var boarding = service.getBoarding().getService();
                    String startDate = boarding.getStartDate();
                    String endDate = boarding.getEndDate();
                    if (StringUtils.hasText(startDate)) {
                        Integer startTime = boarding.getStartTime();
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate)) {
                        Integer endTime = boarding.getEndTime();
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case DAYCARE -> {
                    var daycare = service.getDaycare().getService();
                    List<String> dates =
                            daycare.getSpecificDatesList().stream().sorted().toList();
                    String startDate = firstElement(dates);
                    String endDate = lastElement(dates);
                    Integer startTime = daycare.getStartTime();
                    Integer endTime = daycare.getEndTime();
                    if (StringUtils.hasText(startDate)) {
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate)) {
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case EVALUATION -> {
                    var evaluation = service.getEvaluation().getService();
                    String startDate = evaluation.getStartDate();
                    String endDate = evaluation.getEndDate();
                    if (StringUtils.hasText(startDate)) {
                        Integer startTime = evaluation.getStartTime();
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate)) {
                        Integer endTime = evaluation.getEndTime();
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case DOG_WALKING -> {
                    var dogWalking = service.getDogWalking().getService();
                    String startDate = dogWalking.getStartDate();
                    String endDate = dogWalking.getEndDate();
                    if (StringUtils.hasText(startDate)) {
                        Integer startTime = dogWalking.getStartTime();
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate)) {
                        Integer endTime = dogWalking.getEndTime();
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                case GROUP_CLASS -> {
                    var groupClass = service.getGroupClass().getService();
                    List<String> dates =
                            groupClass.getSpecificDatesList().stream().sorted().toList();
                    String startDate = firstElement(dates);
                    String endDate = lastElement(dates);
                    Integer startTime = groupClass.getStartTime();
                    Integer endTime = groupClass.getEndTime();
                    if (StringUtils.hasText(startDate)) {
                        starts.add(new DatePoint(startDate, startTime));
                    }
                    if (StringUtils.hasText(endDate)) {
                        ends.add(new DatePoint(endDate, endTime));
                    }
                }
                default -> {
                    // no-op
                }
            }
        }

        if (starts.isEmpty() || ends.isEmpty()) {
            return new BookingRequest();
        }

        DatePoint min = DatePoint.min(starts);
        DatePoint max = DatePoint.max(ends);

        var updateBean = new BookingRequest();
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        return updateBean;
    }

    @Override
    public void autoAssign(AutoAssignRequest request, StreamObserver<AutoAssignResponse> responseObserver) {

        var bookingRequestModel = mustGetBookingRequestModel(
                request.hasCompanyId() ? request.getCompanyId() : null, request.getBookingRequestId());

        var assignResult = assign(bookingRequestModel);

        responseObserver.onNext(buildAutoAssignResponse(assignResult, bookingRequestModel.getServicesList()));
        responseObserver.onCompleted();
    }

    private static AutoAssignResponse buildAutoAssignResponse(
            GetAutoAssignResponse assignResult, List<BookingRequestModel.Service> serviceDetails) {
        var builder = AutoAssignResponse.newBuilder();

        var boardingServiceDetailIdToLodgingId = buildAssignResultForBoardingService(assignResult, serviceDetails);

        for (var en : boardingServiceDetailIdToLodgingId.entrySet()) {
            var boardingServiceDetailId = en.getKey();
            var lodgingId = en.getValue();

            var boardingBuilder = AutoAssignResponse.BoardingService.newBuilder();
            boardingBuilder.setId(boardingServiceDetailId);
            if (isNormal(lodgingId)) {
                boardingBuilder.setLodgingId(lodgingId);
            }

            builder.addBoardingServices(boardingBuilder.build());
        }

        var evaluationServiceDetailIdToStaffId = buildAssignResultForEvaluationService(assignResult, serviceDetails);

        for (var en : evaluationServiceDetailIdToStaffId.entrySet()) {
            var evaluationServiceDetailId = en.getKey();
            var staffId = en.getValue();

            var evaluationBuilder = AutoAssignResponse.EvaluationService.newBuilder();
            evaluationBuilder.setId(evaluationServiceDetailId);
            if (isNormal(staffId)) {
                evaluationBuilder.setStaffId(staffId);
            }

            builder.addEvaluationServices(evaluationBuilder.build());
        }

        return builder.build();
    }

    /**
     * @return evaluation_test_detail id -> staff id
     */
    private static Map<Long, Long> buildAssignResultForEvaluationService(
            GetAutoAssignResponse assignResult, List<BookingRequestModel.Service> serviceDetails) {
        // assignResult 是 pet id -> staff id
        // 由于一个 pet 不会存在多个 evaluation service，使用 pet id 可以找到对应的 evaluation_test_detail id
        return assignResult.getEvaluationPetToStaffsList().stream()
                .map(e -> {
                    var evaluationService = serviceDetails.stream()
                            .filter(BookingRequestModel.Service::hasEvaluation)
                            .map(it -> it.getEvaluation().getService())
                            .filter(it -> Objects.equals(it.getPetId(), e.getPetId()))
                            .findFirst()
                            .orElse(null);
                    return evaluationService != null ? Map.entry(evaluationService.getId(), e.getStaffId()) : null;
                })
                .filter(Objects::nonNull)
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue, (o, n) -> o));
    }

    /**
     * @return boarding_service_detail id -> lodging unit id
     */
    private static Map<Long, Long> buildAssignResultForBoardingService(
            GetAutoAssignResponse assignResult, List<BookingRequestModel.Service> serviceDetails) {
        // assignResult 是 pet id -> lodging id
        // 由于一个 pet 不会存在多个 boarding service，使用 pet id 可以找到对应的 boarding_service_detail id
        return assignResult.getPetToLodgingsList().stream()
                .map(e -> {
                    var boardingService = serviceDetails.stream()
                            .filter(BookingRequestModel.Service::hasBoarding)
                            .map(it -> it.getBoarding().getService())
                            .filter(it -> Objects.equals(it.getPetId(), e.getPetId()))
                            .findFirst()
                            .orElse(null);
                    return boardingService != null ? Map.entry(boardingService.getId(), e.getLodgingUnitId()) : null;
                })
                .filter(Objects::nonNull)
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue, (o, n) -> o));
    }

    private GetAutoAssignResponse assign(BookingRequestModel bookingRequestModel) {
        var builder = GetAutoAssignResponse.newBuilder();
        doLodgingAssign(bookingRequestModel, builder);
        doStaffAssign(bookingRequestModel, builder);
        return builder.build();
    }

    private long createOrder(CreateBookingRequestRequest req, long bookingRequestId) {

        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);

        var orderId = orderHelper.createOrderForBookingRequest(bookingRequestId);

        // 标记 order 是通过新流程创建的，这样才能在 payment 回调里判断是否需要进行 auto accept 和 send notification 等逻辑
        orderDecouplingFlowMarkerServiceApi.insertOrderDecouplingFlowMarker(orderId);

        // membership info for obc submit boarding&daycare
        if (req.hasMembership()) {
            ThreadPool.execute(() -> membershipService.applyMemberships(
                    bookingRequest.getBusinessId(),
                    bookingRequest.getCompanyId(),
                    0L,
                    orderId,
                    req.getMembership().getMembershipIdsList()));
        }

        return orderId;
    }

    @Override
    public void checkWaitlistAvailableTask(
            CheckWaitlistAvailableTaskRequest request,
            StreamObserver<CheckWaitlistAvailableTaskResponse> responseObserver) {
        ThreadPool.execute(this::startCheckWaitlistAvailableTask);
        responseObserver.onNext(CheckWaitlistAvailableTaskResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void moveBookingRequestToWaitlist(
            MoveBookingRequestToWaitlistRequest request,
            StreamObserver<MoveBookingRequestToWaitlistResponse> responseObserver) {

        var bookingRequest = mustGetBookingRequest(
                request.hasCompanyId() ? request.getCompanyId() : null,
                request.hasBusinessId() ? request.getBusinessId() : null,
                request.getBookingRequestId());

        if (bookingRequest.getStatus() == BookingRequestStatus.WAIT_LIST) {
            responseObserver.onNext(MoveBookingRequestToWaitlistResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 1. 在 service detail level 添加 waitlist 信息
        var mainServiceItemType = getMainServiceItemType(bookingRequest.getServiceTypeInclude());

        switch (mainServiceItemType) {
            case BOARDING -> addWaitlistForBoarding(bookingRequest);
            case DAYCARE -> addWaitlistForDaycare(bookingRequest);
            default -> throw bizException(Code.CODE_PARAMS_ERROR, mainServiceItemType + " not support waitlist");
        }

        // 2. 更新 booking request status
        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setStatus(BookingRequestStatus.WAIT_LIST);
        updateBean.setAttr(buildAttr(bookingRequest));
        updateBean.setCreatedAt(new java.util.Date());
        bookingRequestService.update(updateBean);

        responseObserver.onNext(MoveBookingRequestToWaitlistResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private static BookingRequestModel.Attr buildAttr(BookingRequest bookingRequest) {
        var builder = bookingRequest.getAttr().toBuilder();
        var staffId = AuthContext.get().staffId();
        if (isNormal(staffId)) {
            builder.setCreatedByStaffId(staffId);
        }
        return builder.build();
    }

    private void addWaitlistForBoarding(BookingRequest bookingRequest) {
        var boardingServices = boardingServiceDetailService.listByBookingRequestId(bookingRequest.getId());

        for (var boardingServiceDetail : boardingServices) {
            if (!StringUtils.hasText(boardingServiceDetail.getStartDate())
                    || !StringUtils.hasText(boardingServiceDetail.getEndDate())) {
                continue;
            }

            var boardingServiceWaitlist = new BoardingServiceWaitlist();
            boardingServiceWaitlist.setBookingRequestId(boardingServiceDetail.getBookingRequestId());
            boardingServiceWaitlist.setServiceDetailId(boardingServiceDetail.getId());
            boardingServiceWaitlist.setStartDate(LocalDate.parse(boardingServiceDetail.getStartDate()));
            boardingServiceWaitlist.setEndDate(LocalDate.parse(boardingServiceDetail.getEndDate()));

            waitlistService.insertBoardingServiceWaitlist(boardingServiceWaitlist);
        }
    }

    private void addWaitlistForDaycare(BookingRequest bookingRequest) {
        var daycareServices = daycareServiceDetailService.listByBookingRequestId(bookingRequest.getId());

        for (var daycareServiceDetail : daycareServices) {
            if (!StringUtils.hasText(daycareServiceDetail.getSpecificDates())) {
                continue;
            }

            var daycareServiceWaitlist = new DaycareServiceWaitlist();
            daycareServiceWaitlist.setBookingRequestId(daycareServiceDetail.getBookingRequestId());
            daycareServiceWaitlist.setServiceDetailId(daycareServiceDetail.getId());
            daycareServiceWaitlist.setSpecificDates(
                    JsonUtil.toList(daycareServiceDetail.getSpecificDates(), LocalDate.class));

            waitlistService.insertDaycareServiceWaitlist(daycareServiceWaitlist);
        }
    }

    private void startCheckWaitlistAvailableTask() {
        List<Pair<Long, Long>> waitlistToCheck = waitlistRedisHelper.getAllWaitlistToCheck();
        log.info("checkWaitlistAvailability count {}", waitlistToCheck.size());

        for (Pair<Long, Long> ids : waitlistToCheck) {
            long companyId = ids.getFirst();
            long businessId = ids.getSecond();
            try {
                // 检查 bd waitlist available
                var bdTrueList = bdWaitlistAvailableCheck(companyId, businessId);
                if (!isEmpty(bdTrueList)) {
                    waitlistService.sendNotification(businessId, bdTrueList.get(0), null);
                }
            } catch (Exception e) {
                log.error("Failed to handle waitlist logic for companyId={}, businessId={}", companyId, businessId, e);
            }
        }
    }

    private List<BookingRequestModel> bdWaitlistAvailableCheck(long companyId, long businessId) {
        String ORDER_BY_COLUMN = "startDate";
        // boarding daycare 检查
        BookingRequestFilterDTO filter = new BookingRequestFilterDTO()
                .setCompanyId(companyId)
                .setBusinessIds(Collections.singletonList(businessId))
                .setStartDate(businessHelper.getBusinessCurrentDate(businessId))
                .setStatuses((Collections.singletonList(BookingRequestStatus.WAIT_LIST_VALUE)))
                .setIsWaitlistExpired(false)
                .setOrderBys(Collections.singletonList(OrderBy.newBuilder()
                        .setFieldName(ORDER_BY_COLUMN)
                        .setAsc(false)
                        .build()));

        List<BookingRequestModel> tureList = new ArrayList<>();

        var pagination = Pagination.builder().pageSize(100).build();
        for (int pageNum = 1; pageNum < 100; pageNum++) {
            pagination = pagination.toBuilder().pageNum(pageNum).build();

            Pair<List<BookingRequest>, Pagination> pair =
                    bookingRequestService.listByBusinessFilter(filter, pagination);
            if (isEmpty(pair.getFirst())) {
                break;
            }
            Map<Long, List<BookingRequestModel.Service>> servicesMap =
                    listServices(pair.getFirst(), Collections.singleton(BookingRequestAssociatedModel.SERVICE));

            List<BookingRequestModel> bookingRequestModelList = pair.getFirst().stream()
                    .map(bookingRequest -> BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                            .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                            .build())
                    .toList();

            var waitlistExtras = waitlistService.generateWaitlistExtras(bookingRequestModelList);
            Map<Long, WaitlistExtra> extraMapById =
                    waitlistExtras.stream().collect(toMap(WaitlistExtra::getId, Function.identity(), (o, n) -> o));

            for (var bookingRequestModel : bookingRequestModelList) {
                var waitlistExtra = extraMapById.get(bookingRequestModel.getId());
                var isSendNotification = waitlistRedisHelper.sendCheckCompare(
                        waitlistExtra.getIsAvailable(), bookingRequestModel.getId(), null);
                if (isSendNotification) {
                    tureList.add(bookingRequestModel);
                }
            }
        }

        return tureList;
    }

    @Override
    public void previewBookingRequestPricing(
            PreviewBookingRequestPricingRequest request,
            StreamObserver<PreviewBookingRequestPricingResponse> responseObserver) {
        var customizedServices = listCustomizedServices(request);

        var serviceAmountsWithPricingRule = calculateServiceAmountsWithPricingRule(request, customizedServices);

        var addOnAmounts = PricingRuleUtils.convertAddOns(request.getPetServicesList(), customizedServices);

        var lineItems = Stream.concat(
                        PricingRuleUtils.processServiceLineItems(serviceAmountsWithPricingRule).stream(),
                        PricingRuleUtils.processSamePetServices(addOnAmounts).stream())
                .toList();

        responseObserver.onNext(PreviewBookingRequestPricingResponse.newBuilder()
                .addAllLineItems(lineItems)
                .build());
        responseObserver.onCompleted();
    }

    private List<FulfillmentLineItem> calculateServiceAmountsWithPricingRule(
            PreviewBookingRequestPricingRequest request, List<ServiceWithCustomizedInfo> customizedServices) {

        var serviceAmounts = PricingRuleUtils.convertServices(request.getPetServicesList(), customizedServices);

        var petDetails = listPetDetailWithPricingRuleApplied(request.getCompanyId(), serviceAmounts);
        if (petDetails.isEmpty()) {
            return serviceAmounts;
        }

        return getUsingPricingRuleService(serviceAmounts, petDetails);
    }

    private List<PetDetailCalculateResultDef> listPetDetailWithPricingRuleApplied(
            long companyId, List<FulfillmentLineItem> items) {

        var pricingRuleEligibleServices = items.stream()
                .filter(PricingRuleUtils::isPricingRuleEligible)
                .map(item -> PetDetailCalculateDef.newBuilder()
                        .setPetId(item.petId())
                        .setServiceId(item.service().getId())
                        .setServicePrice(item.price().doubleValue())
                        .setServiceDate(item.date())
                        .build())
                .toList();

        if (pricingRuleEligibleServices.isEmpty()) {
            return List.of();
        }

        return pricingRuleStub
                .calculatePricingRule(CalculatePricingRuleRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllPetDetails(pricingRuleEligibleServices)
                        .build())
                .getPetDetailsList();
    }

    private List<ServiceWithCustomizedInfo> listCustomizedServices(PreviewBookingRequestPricingRequest request) {
        var conditions = request.getPetServicesList().stream()
                .flatMap(petServiceDetail -> petServiceDetail.getServiceDetailsList().stream()
                        .flatMap(service -> PricingRuleUtils.getQueryConditionStream(
                                request.getBusinessId(), service, petServiceDetail)))
                .toList();

        return offeringHelper.listCustomizedService(request.getCompanyId(), conditions);
    }

    @Override
    public void countBookingRequestByFilter(
            CountBookingRequestByFilterRequest request,
            StreamObserver<CountBookingRequestByFilterResponse> responseObserver) {
        // 获取 evaluation 是否在使用中
        var evaluationInUseMap = evaluationTestDetailService.countBookingRequestByFilter(
                request.getCompanyId(), request.getEvaluationIdsList());

        CountBookingRequestByFilterResponse.Builder responseBuilder =
                CountBookingRequestByFilterResponse.newBuilder().putAllEvaluationInUse(evaluationInUseMap);

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }
}
