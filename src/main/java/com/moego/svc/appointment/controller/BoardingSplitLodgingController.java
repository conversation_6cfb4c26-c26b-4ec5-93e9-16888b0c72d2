package com.moego.svc.appointment.controller;

import com.moego.idl.service.appointment.v1.BoardingSplitLodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsRequest;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.appointment.converter.BoardingSplitLodgingConverter;
import com.moego.svc.appointment.service.BoardingSplitLodgingService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class BoardingSplitLodgingController
        extends BoardingSplitLodgingServiceGrpc.BoardingSplitLodgingServiceImplBase {

    private final BoardingSplitLodgingService boardingSplitLodgingService;

    @Override
    public void listBoardingSplitLodgings(
            final ListBoardingSplitLodgingsRequest request,
            final StreamObserver<ListBoardingSplitLodgingsResponse> responseObserver) {

        var results = boardingSplitLodgingService.getBoardingSplitLodgings(request.getAppointmentIdsList());

        responseObserver.onNext(ListBoardingSplitLodgingsResponse.newBuilder()
                .addAllBoardingSplitLodgings(BoardingSplitLodgingConverter.INSTANCE.poToModel(results))
                .build());
        responseObserver.onCompleted();
    }
}
