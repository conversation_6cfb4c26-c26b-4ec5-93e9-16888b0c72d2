package com.moego.svc.appointment.controller;

import com.moego.idl.models.appointment.v1.InvoiceDepositModel;
import com.moego.idl.service.appointment.v1.GetInvoiceDepositListRequest;
import com.moego.idl.service.appointment.v1.GetInvoiceDepositListResponse;
import com.moego.idl.service.appointment.v1.InvoiceDepositServiceGrpc;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.appointment.converter.InvoiceDepositConverter;
import com.moego.svc.appointment.domain.MoeInvoiceDeposit;
import com.moego.svc.appointment.service.MoeInvoiceDepositService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class InvoiceDepositController extends InvoiceDepositServiceGrpc.InvoiceDepositServiceImplBase {

    private final MoeInvoiceDepositService invoiceDepositService;

    @Override
    public void getInvoiceDepositList(
            GetInvoiceDepositListRequest request, StreamObserver<GetInvoiceDepositListResponse> responseObserver) {
        List<MoeInvoiceDeposit> depositList =
                invoiceDepositService.getDepositByInvoiceIdList(request.getInvoiceIdsList());
        Map<Long, InvoiceDepositModel> depositMap = depositList.stream()
                .collect(Collectors.toMap(
                        invoice -> invoice.getInvoiceId().longValue(), InvoiceDepositConverter.INSTANCE::toModel));

        responseObserver.onNext(GetInvoiceDepositListResponse.newBuilder()
                .putAllInvoiceDeposit(depositMap)
                .build());
        responseObserver.onCompleted();
    }
}
