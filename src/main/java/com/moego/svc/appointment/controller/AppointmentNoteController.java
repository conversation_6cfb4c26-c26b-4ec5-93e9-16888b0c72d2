package com.moego.svc.appointment.controller;

import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.AppointmentNoteServiceGrpc;
import com.moego.idl.service.appointment.v1.CreateAppointmentNoteRequest;
import com.moego.idl.service.appointment.v1.CreateAppointmentNoteResponse;
import com.moego.idl.service.appointment.v1.GetAppointmentNoteListRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentNoteListResponse;
import com.moego.idl.service.appointment.v1.GetAppointmentNoteRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentNoteResponse;
import com.moego.idl.service.appointment.v1.GetCustomerLastNoteRequest;
import com.moego.idl.service.appointment.v1.GetCustomerLastNoteResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentNotesRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentNotesResponse;
import com.moego.idl.service.appointment.v1.UpdateAppointmentNoteRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentNoteResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.appointment.converter.GroomingNoteConverter;
import com.moego.svc.appointment.domain.MoeGroomingNote;
import com.moego.svc.appointment.service.AppointmentServiceProxy;
import com.moego.svc.appointment.service.NoteService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class AppointmentNoteController extends AppointmentNoteServiceGrpc.AppointmentNoteServiceImplBase {

    private final NoteService noteService;
    private final AppointmentServiceProxy appointmentService;

    @Override
    public void getAppointmentNoteList(
            GetAppointmentNoteListRequest request, StreamObserver<GetAppointmentNoteListResponse> responseObserver) {

        List<MoeGroomingNote> noteList = noteService.getNoteByAppointmentId(request.getAppointmentIdsList());

        responseObserver.onNext(GetAppointmentNoteListResponse.newBuilder()
                .addAllNotes(GroomingNoteConverter.INSTANCE.domainToModelList(noteList))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getCustomerLastNote(
            GetCustomerLastNoteRequest request, StreamObserver<GetCustomerLastNoteResponse> responseObserver) {

        MoeGroomingNote note =
                noteService.getLastNoteByCustomerId(request.getCompanyId(), request.getCustomerId(), request.getType());

        var builder = GetCustomerLastNoteResponse.newBuilder();
        if (note != null) {
            builder.setNote(GroomingNoteConverter.INSTANCE.domainToModel(note));
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAppointmentNote(
            GetAppointmentNoteRequest request, StreamObserver<GetAppointmentNoteResponse> responseObserver) {
        MoeGroomingNote note = Optional.ofNullable(noteService.getNoteById(Math.toIntExact(request.getId())))
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR));
        responseObserver.onNext(GetAppointmentNoteResponse.newBuilder()
                .setNote(GroomingNoteConverter.INSTANCE.domainToModel(note))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createAppointmentNote(
            CreateAppointmentNoteRequest request, StreamObserver<CreateAppointmentNoteResponse> responseObserver) {
        var appointment = Optional.ofNullable(appointmentService.get(request.getAppointmentId()))
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_APPOINTMENT_NOT_FOUND));

        MoeGroomingNote note = new MoeGroomingNote();
        note.setCompanyId(appointment.getCompanyId());
        note.setBusinessId(appointment.getBusinessId());
        note.setCustomerId(appointment.getCustomerId());
        note.setGroomingId(appointment.getId());
        note.setCreateBy(Math.toIntExact(request.getAccountId()));
        note.setUpdateBy(Math.toIntExact(request.getAccountId()));
        note.setCreateTime(DateUtil.get10Timestamp());
        note.setUpdateTime(DateUtil.get10Timestamp());
        note.setType((byte) request.getNote().getTypeValue());
        note.setNote(request.getNote().getNote());
        long noteId = noteService.insert(note);

        responseObserver.onNext(
                CreateAppointmentNoteResponse.newBuilder().setId(noteId).build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAppointmentNote(
            UpdateAppointmentNoteRequest request, StreamObserver<UpdateAppointmentNoteResponse> responseObserver) {
        Integer id = Math.toIntExact(request.getNote().getId());
        Integer staffId = Math.toIntExact(request.getStaffId());
        MoeGroomingNote note = Optional.ofNullable(noteService.getNoteById(id))
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR));
        note.setUpdateBy(staffId);
        note.setUpdateTime(DateUtil.get10Timestamp());
        note.setNote(request.getNote().getNote());
        noteService.update(note);
        responseObserver.onNext(UpdateAppointmentNoteResponse.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    public void listAppointmentNotes(
            ListAppointmentNotesRequest request, StreamObserver<ListAppointmentNotesResponse> responseObserver) {

        List<MoeGroomingNote> noteList = noteService.listAppointmentNotes(
                request.getCompanyId(),
                request.getFilter().getCustomerIdsList(),
                request.getFilter().getBusinessIdsList(),
                request.getFilter().getTypesList(),
                request.getFilter().getAppointmentIdsList());
        responseObserver.onNext(ListAppointmentNotesResponse.newBuilder()
                .addAllNotes(GroomingNoteConverter.INSTANCE.domainToModelList(noteList))
                .build());
        responseObserver.onCompleted();
    }
}
