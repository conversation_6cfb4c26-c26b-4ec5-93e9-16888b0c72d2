package com.moego.svc.appointment.domain;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Database Table Remarks:
 *   boarding lodging split
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table boarding_split_lodging
 */
public class BoardingSplitLodging {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   appointment id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   pet detail id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.pet_detail_id")
    private Long petDetailId;

    /**
     * Database Column Remarks:
     *   pet id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.pet_id")
    private Long petId;

    /**
     * Database Column Remarks:
     *   start datetime
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.start_date_time")
    private LocalDateTime startDateTime;

    /**
     * Database Column Remarks:
     *   end datetime
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.end_date_time")
    private LocalDateTime endDateTime;

    /**
     * Database Column Remarks:
     *   lodging id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.lodging_id")
    private Long lodgingId;

    /**
     * Database Column Remarks:
     *   price
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.price")
    private BigDecimal price;

    /**
     * Database Column Remarks:
     *   currency
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.currency")
    private String currency;

    /**
     * Database Column Remarks:
     *   created at
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.created_at")
    private LocalDateTime createdAt;

    /**
     * Database Column Remarks:
     *   lodging is applicable
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.is_applicable")
    private Boolean isApplicable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.pet_detail_id")
    public Long getPetDetailId() {
        return petDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.pet_detail_id")
    public void setPetDetailId(Long petDetailId) {
        this.petDetailId = petDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.start_date_time")
    public LocalDateTime getStartDateTime() {
        return startDateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.start_date_time")
    public void setStartDateTime(LocalDateTime startDateTime) {
        this.startDateTime = startDateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.end_date_time")
    public LocalDateTime getEndDateTime() {
        return endDateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.end_date_time")
    public void setEndDateTime(LocalDateTime endDateTime) {
        this.endDateTime = endDateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.lodging_id")
    public Long getLodgingId() {
        return lodgingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.lodging_id")
    public void setLodgingId(Long lodgingId) {
        this.lodgingId = lodgingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.price")
    public BigDecimal getPrice() {
        return price;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.price")
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.currency")
    public String getCurrency() {
        return currency;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.currency")
    public void setCurrency(String currency) {
        this.currency = currency;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.is_applicable")
    public Boolean getIsApplicable() {
        return isApplicable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_split_lodging.is_applicable")
    public void setIsApplicable(Boolean isApplicable) {
        this.isApplicable = isApplicable;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appointmentId=").append(appointmentId);
        sb.append(", petDetailId=").append(petDetailId);
        sb.append(", petId=").append(petId);
        sb.append(", startDateTime=").append(startDateTime);
        sb.append(", endDateTime=").append(endDateTime);
        sb.append(", lodgingId=").append(lodgingId);
        sb.append(", price=").append(price);
        sb.append(", currency=").append(currency);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", isApplicable=").append(isApplicable);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BoardingSplitLodging other = (BoardingSplitLodging) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getPetDetailId() == null ? other.getPetDetailId() == null : this.getPetDetailId().equals(other.getPetDetailId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getStartDateTime() == null ? other.getStartDateTime() == null : this.getStartDateTime().equals(other.getStartDateTime()))
            && (this.getEndDateTime() == null ? other.getEndDateTime() == null : this.getEndDateTime().equals(other.getEndDateTime()))
            && (this.getLodgingId() == null ? other.getLodgingId() == null : this.getLodgingId().equals(other.getLodgingId()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getCurrency() == null ? other.getCurrency() == null : this.getCurrency().equals(other.getCurrency()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getIsApplicable() == null ? other.getIsApplicable() == null : this.getIsApplicable().equals(other.getIsApplicable()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_split_lodging")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getPetDetailId() == null) ? 0 : getPetDetailId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getStartDateTime() == null) ? 0 : getStartDateTime().hashCode());
        result = prime * result + ((getEndDateTime() == null) ? 0 : getEndDateTime().hashCode());
        result = prime * result + ((getLodgingId() == null) ? 0 : getLodgingId().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getCurrency() == null) ? 0 : getCurrency().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getIsApplicable() == null) ? 0 : getIsApplicable().hashCode());
        return result;
    }
}