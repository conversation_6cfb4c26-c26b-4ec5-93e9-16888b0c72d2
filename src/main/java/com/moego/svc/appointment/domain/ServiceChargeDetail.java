package com.moego.svc.appointment.domain;

import com.moego.idl.models.offering.v1.ServiceOverrideType;
import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table service_charge_detail
 */
public class ServiceChargeDetail {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   foreign key to service_charge table
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.service_charge_id")
    private Long serviceChargeId;

    /**
     * Database Column Remarks:
     *   price of the service charge
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.price")
    private BigDecimal price;

    /**
     * Database Column Remarks:
     *   currency of the price
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.currency")
    private String currency;

    /**
     * Database Column Remarks:
     *   0-no override, 1-override by business
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.price_override_type")
    private ServiceOverrideType priceOverrideType;

    /**
     * Database Column Remarks:
     *   foreign key to moe_business_tax table
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.tax_id")
    private Integer taxId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.deleted_at")
    private LocalDateTime deletedAt;

    /**
     * Database Column Remarks:
     *   Order line item id, 0 if not created from order
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.order_line_item_id")
    private Long orderLineItemId;

    /**
     * Database Column Remarks:
     *   The total price of this service charge
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.total_price")
    private BigDecimal totalPrice;

    /**
     * Database Column Remarks:
     *   The quantity of this apply service charge
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.quantity")
    private Integer quantity;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.service_charge_id")
    public Long getServiceChargeId() {
        return serviceChargeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.service_charge_id")
    public void setServiceChargeId(Long serviceChargeId) {
        this.serviceChargeId = serviceChargeId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.price")
    public BigDecimal getPrice() {
        return price;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.price")
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.currency")
    public String getCurrency() {
        return currency;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.currency")
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.price_override_type")
    public ServiceOverrideType getPriceOverrideType() {
        return priceOverrideType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.price_override_type")
    public void setPriceOverrideType(ServiceOverrideType priceOverrideType) {
        this.priceOverrideType = priceOverrideType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.tax_id")
    public Integer getTaxId() {
        return taxId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.tax_id")
    public void setTaxId(Integer taxId) {
        this.taxId = taxId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.deleted_at")
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.deleted_at")
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.order_line_item_id")
    public Long getOrderLineItemId() {
        return orderLineItemId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.order_line_item_id")
    public void setOrderLineItemId(Long orderLineItemId) {
        this.orderLineItemId = orderLineItemId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.total_price")
    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.total_price")
    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.quantity")
    public Integer getQuantity() {
        return quantity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: service_charge_detail.quantity")
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appointmentId=").append(appointmentId);
        sb.append(", serviceChargeId=").append(serviceChargeId);
        sb.append(", price=").append(price);
        sb.append(", currency=").append(currency);
        sb.append(", priceOverrideType=").append(priceOverrideType);
        sb.append(", taxId=").append(taxId);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", orderLineItemId=").append(orderLineItemId);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", quantity=").append(quantity);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ServiceChargeDetail other = (ServiceChargeDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getServiceChargeId() == null ? other.getServiceChargeId() == null : this.getServiceChargeId().equals(other.getServiceChargeId()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getCurrency() == null ? other.getCurrency() == null : this.getCurrency().equals(other.getCurrency()))
            && (this.getPriceOverrideType() == null ? other.getPriceOverrideType() == null : this.getPriceOverrideType().equals(other.getPriceOverrideType()))
            && (this.getTaxId() == null ? other.getTaxId() == null : this.getTaxId().equals(other.getTaxId()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getOrderLineItemId() == null ? other.getOrderLineItemId() == null : this.getOrderLineItemId().equals(other.getOrderLineItemId()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: service_charge_detail")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getServiceChargeId() == null) ? 0 : getServiceChargeId().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getCurrency() == null) ? 0 : getCurrency().hashCode());
        result = prime * result + ((getPriceOverrideType() == null) ? 0 : getPriceOverrideType().hashCode());
        result = prime * result + ((getTaxId() == null) ? 0 : getTaxId().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getOrderLineItemId() == null) ? 0 : getOrderLineItemId().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        return result;
    }
}