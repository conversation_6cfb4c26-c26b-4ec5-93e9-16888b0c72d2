package com.moego.svc.appointment.mapper.mysql;

import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AppointmentPetMedicationDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_pet_medication")
    public static final AppointmentPetMedication appointmentPetMedication = new AppointmentPetMedication();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.id")
    public static final SqlColumn<Long> id = appointmentPetMedication.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.company_id")
    public static final SqlColumn<Long> companyId = appointmentPetMedication.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.appointment_id")
    public static final SqlColumn<Long> appointmentId = appointmentPetMedication.appointmentId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.pet_detail_id")
    public static final SqlColumn<Long> petDetailId = appointmentPetMedication.petDetailId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.pet_id")
    public static final SqlColumn<Long> petId = appointmentPetMedication.petId;

    /**
     * Database Column Remarks:
     *   such as 1.2, 1/2, 1 etc.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.medication_amount")
    public static final SqlColumn<String> medicationAmount = appointmentPetMedication.medicationAmount;

    /**
     * Database Column Remarks:
     *   pet_metadata.metadata_value, metadata_name = 7
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.medication_unit")
    public static final SqlColumn<String> medicationUnit = appointmentPetMedication.medicationUnit;

    /**
     * Database Column Remarks:
     *   medication name, user input
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.medication_name")
    public static final SqlColumn<String> medicationName = appointmentPetMedication.medicationName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.created_at")
    public static final SqlColumn<Date> createdAt = appointmentPetMedication.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.updated_at")
    public static final SqlColumn<Date> updatedAt = appointmentPetMedication.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.deleted_at")
    public static final SqlColumn<Date> deletedAt = appointmentPetMedication.deletedAt;

    /**
     * Database Column Remarks:
     *   1-EVERYDAY_EXCEPT_CHECKOUT_DATE; 2-EVERYDAY_INCLUDE_CHECKOUT_DATE; 3-SPECIFIC_DATE
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.date_type")
    public static final SqlColumn<FeedingMedicationScheduleDateType> dateType = appointmentPetMedication.dateType;

    /**
     * Database Column Remarks:
     *   specific_date, yyyy-mm-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.specific_dates")
    public static final SqlColumn<String> specificDates = appointmentPetMedication.specificDates;

    /**
     * Database Column Remarks:
     *   medication note, user input
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_medication.medication_note")
    public static final SqlColumn<String> medicationNote = appointmentPetMedication.medicationNote;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_pet_medication")
    public static final class AppointmentPetMedication extends AliasableSqlTable<AppointmentPetMedication> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petDetailId = column("pet_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<String> medicationAmount = column("medication_amount", JDBCType.VARCHAR);

        public final SqlColumn<String> medicationUnit = column("medication_unit", JDBCType.VARCHAR);

        public final SqlColumn<String> medicationName = column("medication_name", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<FeedingMedicationScheduleDateType> dateType = column("date_type", JDBCType.INTEGER, "com.moego.svc.appointment.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler");

        public final SqlColumn<String> specificDates = column("specific_dates", JDBCType.VARCHAR);

        public final SqlColumn<String> medicationNote = column("medication_note", JDBCType.LONGVARCHAR);

        public AppointmentPetMedication() {
            super("appointment_pet_medication", AppointmentPetMedication::new);
        }
    }
}