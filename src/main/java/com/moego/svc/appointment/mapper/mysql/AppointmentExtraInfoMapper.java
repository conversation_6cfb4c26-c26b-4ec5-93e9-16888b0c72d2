package com.moego.svc.appointment.mapper.mysql;

import static com.moego.svc.appointment.mapper.mysql.AppointmentExtraInfoDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.AppointmentExtraInfo;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface AppointmentExtraInfoMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonInsertMapper<AppointmentExtraInfo>, CommonUpdateMapper, DynamicDataSource<AppointmentExtraInfoMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    BasicColumn[] selectList = BasicColumn.columnList(id, appointmentId, isNewOrder);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AppointmentExtraInfoResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="appointment_id", property="appointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="is_new_order", property="isNewOrder", jdbcType=JdbcType.BIT)
    })
    List<AppointmentExtraInfo> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AppointmentExtraInfoResult")
    Optional<AppointmentExtraInfo> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, appointmentExtraInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, appointmentExtraInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default int insertMultiple(Collection<AppointmentExtraInfo> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, appointmentExtraInfo, c ->
            c.map(id).toProperty("id")
            .map(appointmentId).toProperty("appointmentId")
            .map(isNewOrder).toProperty("isNewOrder")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default int insertSelective(AppointmentExtraInfo row) {
        return MyBatis3Utils.insert(this::insert, row, appointmentExtraInfo, c ->
            c.map(id).toPropertyWhenPresent("id", row::getId)
            .map(appointmentId).toPropertyWhenPresent("appointmentId", row::getAppointmentId)
            .map(isNewOrder).toPropertyWhenPresent("isNewOrder", row::getIsNewOrder)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default Optional<AppointmentExtraInfo> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, appointmentExtraInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default List<AppointmentExtraInfo> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, appointmentExtraInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default List<AppointmentExtraInfo> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, appointmentExtraInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default Optional<AppointmentExtraInfo> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, appointmentExtraInfo, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    static UpdateDSL<UpdateModel> updateAllColumns(AppointmentExtraInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(appointmentId).equalTo(row::getAppointmentId)
                .set(isNewOrder).equalTo(row::getIsNewOrder);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AppointmentExtraInfo row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
                .set(isNewOrder).equalToWhenPresent(row::getIsNewOrder);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_extra_info")
    default int updateByPrimaryKeySelective(AppointmentExtraInfo row) {
        return update(c ->
            c.set(appointmentId).equalToWhenPresent(row::getAppointmentId)
            .set(isNewOrder).equalToWhenPresent(row::getIsNewOrder)
            .where(id, isEqualTo(row::getId))
        );
    }
}