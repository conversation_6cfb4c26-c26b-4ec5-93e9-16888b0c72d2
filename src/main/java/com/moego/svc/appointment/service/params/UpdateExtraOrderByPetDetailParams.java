package com.moego.svc.appointment.service.params;

import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/25
 */
@Data
@Builder
public class UpdateExtraOrderByPetDetailParams {
    private Long extraOrderId;
    private MoeGroomingAppointment appointment;
    private MoeGroomingPetDetail beforePetDetail;
    private MoeGroomingPetDetail afterPetDetail;
    private List<Long> addedPetDetailIds;
    private List<MoeGroomingPetDetail> deletedPetDetails;
}
