package com.moego.svc.appointment.service.params;

import lombok.Data;

@Data
public class UpdatePetPlaygroupParams {
    private Long id;

    private Long playgroupId;

    private Long appointmentId;

    private Long petId;

    private String date;

    // 更新到表里的 sort 值（sort 不一定按照 1 递增，所以需先由 index 确定具体 sort）
    private Integer sort;

    // 更新 pet playgroup sort 时要放置的下标位置（从 1 开始）
    private Integer index;
}
