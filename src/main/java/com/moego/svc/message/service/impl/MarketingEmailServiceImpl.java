package com.moego.svc.message.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.protobuf.Timestamp;
import com.microtripit.mandrillapp.lutung.view.MandrillMessage;
import com.microtripit.mandrillapp.lutung.view.MandrillMessageStatus;
import com.microtripit.mandrillapp.lutung.view.MandrillSubaccountInfo;
import com.moego.idl.models.engagement.v1.DomainVerifyStatus;
import com.moego.idl.models.engagement.v1.SenderEmailModel;
import com.moego.idl.models.engagement.v1.SenderEmailUsageType;
import com.moego.idl.models.enterprise.v1.Campaign;
import com.moego.idl.models.enterprise.v1.CampaignTemplatePushRecord;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.message.v1.EmailEventType;
import com.moego.idl.models.message.v1.MarketingEmailApptBriefView;
import com.moego.idl.models.message.v1.MarketingEmailModel;
import com.moego.idl.models.message.v1.MarketingEmailModelBriefView;
import com.moego.idl.models.message.v1.MarketingEmailRecipientModel;
import com.moego.idl.models.message.v1.MarketingEmailStatus;
import com.moego.idl.models.message.v1.MarketingEmailTemplateModel;
import com.moego.idl.models.message.v1.MarketingEmailTemplateModelBriefView;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.engagement.v1.EmailServiceGrpc;
import com.moego.idl.service.engagement.v1.GetSenderEmailRequest;
import com.moego.idl.service.engagement.v1.GetSenderEmailResponse;
import com.moego.idl.service.enterprise.v1.CampaignServiceGrpc;
import com.moego.idl.service.enterprise.v1.ListTemplatePushRecordsRequest;
import com.moego.idl.service.message.v1.GetAppointmentsAfterEmailResponse;
import com.moego.idl.service.message.v1.GetAvailableEmailsCountResponse;
import com.moego.idl.service.message.v1.GetEmailListResponse;
import com.moego.idl.service.message.v1.GetMarketingEmailTemplateListResponse;
import com.moego.idl.service.message.v1.GetRecipientListResponse;
import com.moego.idl.service.message.v1.MarketingCampaignsSummaryResponse;
import com.moego.idl.utils.v1.PaginationRequest;
import com.moego.idl.utils.v1.PaginationResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.ClientListRequestWithParamsSnapshot;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.dto.CustomerAppointmentWithAmountList;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentWithAmountDTO;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.svc.message.adapter.converter.ModelConverter;
import com.moego.svc.message.client.BusinessClient;
import com.moego.svc.message.mapper.MoeBusinessMessageControlMapper;
import com.moego.svc.message.mapper.MoeMarketingEmailDetailMapper;
import com.moego.svc.message.mapper.MoeMarketingEmailTemplateMapper;
import com.moego.svc.message.mapper.MoeMessageRecipientMapper;
import com.moego.svc.message.mapperbean.MoeBusinessMessageControl;
import com.moego.svc.message.mapperbean.MoeMarketingEmailDetail;
import com.moego.svc.message.mapperbean.MoeMarketingEmailTemplate;
import com.moego.svc.message.mapperbean.MoeMessageRecipient;
import com.moego.svc.message.mapperbean.MoeMessageRecipientExample;
import com.moego.svc.message.model.Attachment;
import com.moego.svc.message.model.Email;
import com.moego.svc.message.model.EmailUser;
import com.moego.svc.message.model.params.EmailSenderVO;
import com.moego.svc.message.model.params.GetEmailListVO;
import com.moego.svc.message.model.params.GetRecipientListVO;
import com.moego.svc.message.model.params.MassEmailSendVO;
import com.moego.svc.message.service.MarketingEmailService;
import com.moego.svc.message.service.render.EmailRender;
import com.moego.svc.message.service.storage.MessageStorage;
import com.moego.svc.message.service.third.MandrillService;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class MarketingEmailServiceImpl implements MarketingEmailService {

    @Autowired
    private MessageStorage messageStorage;

    @Autowired
    private BusinessClient businessClient;

    @Autowired
    private MandrillService mandrillService;

    @Autowired
    private MoeMarketingEmailDetailMapper moeMarketingEmailDetailMapper;

    @Autowired
    private MoeMessageRecipientMapper moeMessageRecipientMapper;

    @Autowired
    private MoeBusinessMessageControlMapper moeBusinessMessageControlMapper;

    @Autowired
    private MoeMarketingEmailTemplateMapper moeMarketingEmailTemplateMapper;

    @Autowired
    private ICustomerCustomerClient customerClient;

    @Autowired
    private IGroomingAppointmentClient groomingAppointmentClient;

    @Autowired
    private CampaignServiceGrpc.CampaignServiceBlockingStub enterpriseCampaignClient;

    @Autowired
    private BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub customerService;

    @Value("${mandrill.send.from.emailPrefix}")
    private String emailPrefix;

    @Value("${mandrill.send.from.emailSuffix}")
    private String emailSuffix;

    @Value("${mandrill.send.sub-account.prefix}")
    private String subAccountPrefix;

    @Autowired
    private EmailRender emailRender;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private FeatureFlagApi featureFlagApi;

    @Autowired
    private EmailServiceGrpc.EmailServiceBlockingStub engagementEmailService;

    @Override
    public Long massEmailSend(MassEmailSendVO params) {
        // Step1. save new email
        params.getEmail()
                .setRecipients(getRecipients(
                        params.getCompanyId(),
                        params.getBusinessId(),
                        params.getStaffId(),
                        params.getFilterClientListParams(),
                        params.getExcludeCustomerIds(),
                        params.getIncludeCustomerIds()));
        params.setSenderMap(getSenderMap(params.getCompanyId()));
        Long messageId = messageStorage.saveNewEmail(
                params, calculateCreditCost(params.getEmail().getRecipients().size()));

        // Step2. call mandrill API to send email async
        if (!params.isScheduled()) {
            sendEmailAsync(params.getEmail(), params.getCompanyId(), messageId);
        }

        return messageId;
    }

    @Override
    public GetEmailListResponse getEmailList(GetEmailListVO params) {
        PageHelper.startPage(params.getPageId().intValue(), params.getPageSize().intValue());
        List<MoeMarketingEmailDetail> emails = moeMarketingEmailDetailMapper.findPageByBusinessIdOrCompanyId(
                migrateHelper.isMigrate(params.getCompanyId()) ? null : params.getBusinessId(),
                params.getCompanyId(),
                params.getStatus(),
                params.getSubject(),
                new RowBounds());

        List<MarketingEmailModelBriefView> emailList = new ArrayList<>();
        for (var email : emails) {
            boolean hasUnreadReply = moeMessageRecipientMapper.HasUnreadReply(email.getId());
            MarketingEmailModelBriefView emailBrief = ModelConverter.toMarketingEmailBrief(email);
            emailList.add(
                    emailBrief.toBuilder().setHasUnreadReply(hasUnreadReply).build());
        }
        return GetEmailListResponse.newBuilder()
                .addAllList(emailList)
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNo(params.getPageId())
                        .setPageSize(params.getPageSize())
                        .setTotal(new PageInfo<>(emails).getTotal()))
                .build();
    }

    @Override
    public MarketingEmailModel getEmailDetail(Long companyId, Long businessId, Long emailId) {
        MoeMarketingEmailDetail email = moeMarketingEmailDetailMapper.selectByPrimaryKey(emailId);
        if (migrateHelper.isMigrate(companyId)) {
            if (!email.getCompanyId().equals(companyId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
        } else {
            if (!email.getBusinessId().equals(businessId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
        }

        boolean hasUnreadReply = moeMessageRecipientMapper.HasUnreadReply(emailId);

        return MarketingEmailModel.newBuilder()
                .setId(emailId)
                .setSubject(email.getSubject())
                .setContent(email.getContent())
                .setStatus(MarketingEmailStatus.values()[email.getStatus()])
                .setRecipientsNumber(email.getRecipientCount())
                .setDeliveredNumber(email.getDeliveredCount())
                .setOpenedNumber(email.getOpenedCount())
                .setClickedNumber(email.getClickedCount())
                .setRepliedNumber(email.getRepliedCount())
                .setSendAt(Timestamp.newBuilder().setSeconds(email.getSentAt()))
                .setClientFilter(email.getClientFilter())
                .setHasUnreadReply(hasUnreadReply)
                .build();
    }

    @Override
    public void handleEmailEvent(String mandrillMessageId, EmailEventType eventType, String replyContent) {
        messageStorage.updateRecipientStatus(mandrillMessageId, eventType, replyContent);
    }

    @Override
    public void sendNow(Long companyId, Long businessId, Long emailId) {
        // Step1. check email
        MoeMarketingEmailDetail email = moeMarketingEmailDetailMapper.selectByPrimaryKey(emailId);
        if (email == null) {
            log.error("Email not found, companyId={}, emailId={}", companyId, emailId);
            throw ExceptionUtil.bizException(Code.CODE_EMAIL_NOT_FOUND);
        }
        if (migrateHelper.isMigrate(companyId)) {
            if (!email.getCompanyId().equals(companyId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
        } else {
            if (!email.getBusinessId().equals(businessId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
        }

        // Step2. update email send status to 'send now'
        messageStorage.setEmailToSendNow(companyId, emailId);

        // Step3. send email async
        Email e = new Email();
        e.setSubject(email.getSubject());
        e.setContent(email.getContent());
        e.setAttachments(new Gson().fromJson(email.getAttachments(), new TypeToken<List<Attachment>>() {}.getType()));
        sendEmailAsync(e, companyId, emailId);
    }

    @Override
    public void sendTestEmail(Email email, Long businessId, String businessName) {
        MandrillMessage m = new MandrillMessage();
        m.setSubject(emailRender.getRenderedSubjectForTestEmail(email.getSubject(), businessName));
        m.setFromName(businessName);
        m.setFromEmail(getSenderEmail(businessId, businessName));
        m.setAttachments(downloadAttachment(email));
        m.setHtml(emailRender.getRenderedContentForTestEmail(email, businessId));
        if (Objects.nonNull(businessId) && businessId > 0) { // 注入自定义 sender
            Optional<SenderEmailModel> sender = customizedEmailSender(businessId);
            if (sender.isPresent()) {
                m.setFromEmail(sender.get().getEmail());
                m.setFromName(sender.get().getName());
            }
        }
        MandrillMessage.Recipient to = new MandrillMessage.Recipient();
        to.setName(email.getRecipients().get(0).name());
        to.setEmail(email.getRecipients().get(0).email());
        m.setTo(Collections.singletonList(to));

        mandrillService.sendEmail(m);
    }

    @Override
    public void rescheduleEmail(Long companyId, Long businessId, Long emailId, Long sendTime) {
        int affectRows = moeMarketingEmailDetailMapper.updateEmailSendAt(
                emailId, companyId, migrateHelper.isMigrate(companyId) ? null : businessId, sendTime);
        if (sendTime < Instant.now().getEpochSecond() && affectRows > 0) {
            sendNow(companyId, businessId, emailId);
        }
    }

    @Override
    public GetRecipientListResponse getRecipientList(GetRecipientListVO params) {
        PageHelper.startPage(params.getPageId().intValue(), params.getPageSize().intValue());
        List<MoeMessageRecipient> recipients = moeMessageRecipientMapper.selectByEmail(
                migrateHelper.isMigrate(params.getCompanyId()) ? null : params.getBusinessId(),
                params.getCompanyId(),
                params.getEmailId(),
                params.getRecipientStatusFilter(),
                new RowBounds());

        List<MarketingEmailRecipientModel> recipientList = new ArrayList<>();
        for (var recipient : recipients) {
            recipientList.add(ModelConverter.toMarketingEmailRecipient(recipient));
        }
        return GetRecipientListResponse.newBuilder()
                .addAllRecipients(recipientList)
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNo(params.getPageId())
                        .setPageSize(params.getPageSize())
                        .setTotal(new PageInfo<>(recipients).getTotal()))
                .build();
    }

    @Override
    public void cancelScheduledEmail(Long companyId, Long businessId, Long emailId) {
        MoeMarketingEmailDetail email = moeMarketingEmailDetailMapper.selectByPrimaryKey(emailId);
        if (email == null) {
            log.error("Email not found, businessId={}, emailId={}", businessId, emailId);
            throw ExceptionUtil.bizException(Code.CODE_EMAIL_NOT_FOUND);
        }
        if (migrateHelper.isMigrate(companyId)) {
            if (!email.getCompanyId().equals(companyId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
        } else {
            if (!email.getBusinessId().equals(businessId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
        }

        messageStorage.cancelScheduledEmail(companyId, emailId, calculateCreditCost(email.getRecipientCount()));
    }

    @Override
    public GetAvailableEmailsCountResponse getAvailableEmailsCount(Long companyId) {
        MoeBusinessMessageControl control = moeBusinessMessageControlMapper.selectByCompanyId(companyId);
        if (control == null) {
            return GetAvailableEmailsCountResponse.newBuilder()
                    .setAvailableEmails(0)
                    .setUsedEmails(0)
                    .build();
        }
        return GetAvailableEmailsCountResponse.newBuilder()
                .setAvailableEmails(control.getAvailableEmails())
                .setUsedEmails(control.getUsedEmails())
                .build();
    }

    @Override
    public Long saveDraft(Long businessId, Long companyId, Email email, Long draftId, String clientFilter) {
        MoeMarketingEmailDetail emailDetail = new MoeMarketingEmailDetail();
        emailDetail.setBusinessId(businessId);
        emailDetail.setCompanyId(companyId);
        emailDetail.setSubject(email.getSubject());
        emailDetail.setContent(email.getContent());
        emailDetail.setStatus(MarketingEmailStatus.MARKETING_EMAIL_STATUS_DRAFT.ordinal());
        emailDetail.setUpdateTime(Instant.now().getEpochSecond());
        emailDetail.setClientFilter(clientFilter);
        if (draftId != null && draftId > 0) {
            MoeMarketingEmailDetail existed = moeMarketingEmailDetailMapper.selectByPrimaryKey(draftId);
            if (existed == null || !existed.getCompanyId().equals(companyId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
            if (!existed.getStatus().equals(MarketingEmailStatus.MARKETING_EMAIL_STATUS_DRAFT_VALUE)) {
                throw ExceptionUtil.bizException(
                        Code.CODE_INVALID_EMAIL_STATUS,
                        "This campaign has been sent or scheduled, cannot be saved as a draft, please cancel it first.");
            }
            emailDetail.setId(draftId);
            moeMarketingEmailDetailMapper.updateByPrimaryKeySelective(emailDetail);
            return draftId;
        }
        moeMarketingEmailDetailMapper.insertSelective(emailDetail);
        return emailDetail.getId();
    }

    @Override
    public GetMarketingEmailTemplateListResponse getMarketingEmailTemplateList(
            Long companyId, PaginationRequest request) {
        var rsp = enterpriseCampaignClient.listTemplatePushRecords(ListTemplatePushRecordsRequest.newBuilder()
                .setFilter(ListTemplatePushRecordsRequest.Filter.newBuilder()
                        .addCompanyIds(companyId)
                        .addTypes(Campaign.Type.EMAIL)
                        .build())
                .build());
        log.error("get push records: {}", rsp);
        PageHelper.startPage((int) request.getPageNo(), (int) request.getPageSize());
        var ids = rsp.getRecordsList().stream()
                .map(CampaignTemplatePushRecord::getInternalTemplateId)
                .toList();
        log.error("get push template ids: {}", ids);
        List<MoeMarketingEmailTemplate> templates =
                moeMarketingEmailTemplateMapper.listByIdsOrEnterpriseIds(ids, List.of(0L));
        log.error("list all templates: {}", templates);
        List<MarketingEmailTemplateModelBriefView> templateList = new ArrayList<>();
        for (var template : templates) {
            log.error("template: {}", template.toString());
            templateList.add(MarketingEmailTemplateModelBriefView.newBuilder()
                    .setId(template.getId())
                    .setName(template.getName())
                    .setPictureUrl(template.getImageUrl())
                    .setDescription(template.getDescription())
                    .setSubject(template.getSubject())
                    .setType(MarketingEmailTemplateModel.MarketingEmailTemplateType.valueOf(template.getType()))
                    .setEnterpriseId(template.getEnterpriseId())
                    .build());
        }
        log.error("build templates list: {}", templateList);
        return GetMarketingEmailTemplateListResponse.newBuilder()
                .addAllTemplates(templateList)
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNo(request.getPageNo())
                        .setPageSize(request.getPageSize())
                        .setTotal(new PageInfo<>(templates).getTotal()))
                .build();
    }

    @Override
    public MarketingEmailTemplateModel getMarketingEmailTemplate(Long templateId) {
        MoeMarketingEmailTemplate template = moeMarketingEmailTemplateMapper.selectByPrimaryKey(templateId);
        return MarketingEmailTemplateModel.newBuilder()
                .setId(template.getId())
                .setName(template.getName())
                .setPictureUrl(template.getImageUrl())
                .setContent(template.getContent())
                .setDescription(template.getDescription())
                .setSubject(template.getSubject())
                .setClientFilter(template.getClientFilter())
                .build();
    }

    @Override
    public void deleteEmail(Long companyId, Long businessId, Long emailId) {
        moeMarketingEmailDetailMapper.deleteEmailDraft(
                companyId, migrateHelper.isMigrate(companyId) ? null : businessId, emailId);
    }

    @Override
    public String viewReply(Long companyId, Long businessId, Long recipientId) {
        MoeMessageRecipient recipient = moeMessageRecipientMapper.selectByPrimaryKey(recipientId);
        if (recipient == null) {
            throw ExceptionUtil.bizException(Code.CODE_EMAIL_NOT_FOUND);
        }
        if (migrateHelper.isMigrate(companyId)) {
            if (!recipient.getCompanyId().equals(companyId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
        } else {
            if (!recipient.getBusinessId().equals(businessId)) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
            }
        }

        MoeMessageRecipient readRecipient = new MoeMessageRecipient();
        readRecipient.setId(recipientId);
        readRecipient.setIsReplyRead(true);
        moeMessageRecipientMapper.updateByPrimaryKeySelective(readRecipient);

        return recipient.getRepliedContent();
    }

    @Override
    public void triggerScheduleEmail() {
        // list emails that are about to send (sent_at <= now and sent_at >= now - 5 min)
        Long from = Instant.now().getEpochSecond() - 5 * 60;
        Long to = Instant.now().getEpochSecond();
        List<MoeMarketingEmailDetail> emails = moeMarketingEmailDetailMapper.findEmailsToSend(from, to);

        for (var email : emails) {
            sendNow(email.getCompanyId(), email.getBusinessId(), email.getId());
        }
    }

    @Override
    public int calculateCreditCost(int recipientCount) {
        if (recipientCount <= 200) return 1;
        else if (recipientCount <= 500) return 2;
        else return ((recipientCount / 500) * 2 + 2);
    }

    @Override
    public GetAppointmentsAfterEmailResponse getAppointmentsAfterEmail(
            Long companyId, Long businessId, Long emailId, PaginationRequest req) {
        // get email detail
        MoeMarketingEmailDetail emailDetail = moeMarketingEmailDetailMapper.selectByPrimaryKey(emailId);
        // get client ids
        MoeMessageRecipientExample example = new MoeMessageRecipientExample();
        var criteria = example.createCriteria();
        // https://moego.atlassian.net/browse/CRM-4373
        // 优化口径为 campaign 发出后 7 天内打开过 email 的 recipient
        criteria.andEmailIdEqualTo(emailId)
                .andOpenedEqualTo(true)
                .andOpenTimeBetween(emailDetail.getSentAt(), emailDetail.getSentAt() + 7 * 24 * 60 * 60);
        if (migrateHelper.isMigrate(companyId)) {
            criteria.andCompanyIdEqualTo(companyId);
        } else {
            criteria.andBusinessIdEqualTo(businessId);
        }
        List<MoeMessageRecipient> recipientList = moeMessageRecipientMapper.selectByExample(example);

        if (recipientList == null || recipientList.isEmpty()) {
            return GetAppointmentsAfterEmailResponse.newBuilder()
                    .setPagination(PaginationResponse.newBuilder()
                            .setPageNo(req.getPageNo())
                            .setPageSize(req.getPageSize())
                            .setTotal(0))
                    .build();
        }

        List<Long> clientIds =
                recipientList.stream().map(MoeMessageRecipient::getCustomerId).toList();

        Map<Long, MoeMessageRecipient> recipientMap = recipientList.stream()
                .collect(Collectors.toMap(
                        MoeMessageRecipient::getCustomerId, Function.identity(), (oldValue, newValue) -> newValue));

        // get appointments
        CommonIdsParams params = new CommonIdsParams();
        params.setIds(clientIds.stream().map(Long::intValue).toList());
        List<CustomerGroomingAppointmentWithAmountDTO> list = new ArrayList<>();
        // 仅能通过内存分页
        int pageSize = 1000;
        int total = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Integer pageNum = 1; ; pageNum++) {
            var rsp = groomingAppointmentClient.getAppointmentIdsCreatedBetween(
                    businessId.intValue(),
                    emailDetail.getSentAt(),
                    emailDetail.getSentAt() + 2 * 7 * 24 * 60 * 60,
                    params,
                    pageNum,
                    pageSize);
            if (CollectionUtils.isEmpty(rsp.getAppts())) {
                break;
            }
            var validAppts = rsp.getAppts().stream()
                    .filter(appt -> {
                        var recipient = recipientMap.get(appt.getCustomerId().longValue());
                        return appt.getCreateTime() >= recipient.getOpenTime()
                                && appt.getCreateTime() < recipient.getOpenTime() + 7 * 24 * 60 * 60;
                    })
                    .toList();
            if (!CollectionUtils.isEmpty(validAppts)) {
                list.addAll(validAppts);
                total += validAppts.size();
                for (var appt : validAppts) {
                    totalAmount = totalAmount.add(appt.getAmount());
                }
            }
            if (rsp.getCount() <= (long) pageNum * pageSize) {
                break;
            }
        }
        if (total == 0) {
            return GetAppointmentsAfterEmailResponse.getDefaultInstance();
        }
        // create time desc
        list.sort(Comparator.comparing(
                CustomerGroomingAppointmentWithAmountDTO::getCreateTime, Comparator.reverseOrder()));
        var startIndex = (req.getPageNo() - 1) * pageSize;
        var endIndex = Math.min(req.getPageNo() * pageSize, total);

        List<MarketingEmailApptBriefView> apptList = list.subList((int) startIndex, (int) endIndex).stream()
                .map(appt -> MarketingEmailApptBriefView.newBuilder()
                        .setId(appt.getApptId())
                        .setCustomerEmail(recipientMap
                                .get(appt.getCustomerId().longValue())
                                .getRecipientEmail())
                        .setCustomerName(recipientMap
                                .get(appt.getCustomerId().longValue())
                                .getRecipientName())
                        .setRevenue(appt.getAmount().toPlainString())
                        .setCreatedTime(Timestamp.newBuilder()
                                .setSeconds(appt.getCreateTime())
                                .build())
                        .build())
                .toList();

        return GetAppointmentsAfterEmailResponse.newBuilder()
                .addAllAppointments(apptList)
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNo(req.getPageNo())
                        .setPageSize(req.getPageSize())
                        .setTotal(total)
                        .build())
                .setTotalExpected(MoneyUtils.toGoogleMoney(totalAmount))
                .build();
    }

    @Override
    public void retryForWaitingEmails() {
        Long begin = Instant.now().getEpochSecond() - 60 * 60 * 24;
        Long end = Instant.now().getEpochSecond() - 60 * 60 * 6;
        List<MoeMarketingEmailDetail> emails = moeMarketingEmailDetailMapper.findEmailsToRetry(begin, end);
        emails.stream().parallel().forEach(email -> {
            if (email.getRecipientCount().equals(email.getDeliveredCount())) {
                return;
            }
            Email e = new Email();
            e.setSubject(email.getSubject());
            e.setContent(email.getContent());
            e.setAttachments(
                    new Gson().fromJson(email.getAttachments(), new TypeToken<List<Attachment>>() {}.getType()));
            sendEmail(e, email.getCompanyId(), email.getId());
        });
    }

    @Override
    public void removeEmailFromRejectedList(Long companyId, Long businessId, Long customerId) {
        CustomerIdListParams params = new CustomerIdListParams();
        params.setIdList(List.of(customerId.intValue()));
        List<MoeBusinessCustomerDTO> customerList = customerClient.queryCustomerList(params);
        if (customerList == null || customerList.isEmpty()) {
            log.error("Customer not found, customerId: {}", customerId);
            return;
        }

        MandrillSubaccountInfo subAccountInfo =
                getEmailSubAccountInfo(businessId, businessClient.getLocationName(companyId, businessId));
        if (subAccountInfo == null) {
            log.error("get email sub account info failed, businessId={}", businessId);
            return;
        }

        mandrillService.removeFromDenyList(customerList.get(0).getEmail(), subAccountInfo.getId());
    }

    @Override
    public MarketingCampaignsSummaryResponse getMarketingCampaignsSummary(
            Long companyId, Long businessId, Long sentAtFrom, Long sentAtTo) {
        List<MoeMarketingEmailDetail> emails =
                moeMarketingEmailDetailMapper.findEmailsSent(companyId, businessId, sentAtFrom, sentAtTo);
        if (emails.isEmpty()) {
            return MarketingCampaignsSummaryResponse.newBuilder().build();
        }
        List<Long> emailIdList =
                emails.stream().map(MoeMarketingEmailDetail::getId).toList();
        Long sentAtMin = emails.stream()
                .mapToLong(MoeMarketingEmailDetail::getSentAt)
                .min()
                .orElse(sentAtFrom);
        long sentAtMax = emails.stream()
                .mapToLong(MoeMarketingEmailDetail::getSentAt)
                .max()
                .orElse(sentAtTo);

        // get client ids
        List<MoeMessageRecipient> recipientList = moeMessageRecipientMapper.selectCustomerInfoByEmails(emailIdList);
        List<Long> clientIds = recipientList.stream()
                .map(MoeMessageRecipient::getCustomerId)
                .distinct()
                .toList();
        Map<Long, List<MoeMessageRecipient>> emailRecipientMap =
                recipientList.stream().collect(Collectors.groupingBy(MoeMessageRecipient::getEmailId));

        // get appointments
        CommonIdsParams params = new CommonIdsParams();
        params.setIds(clientIds.stream().map(Long::intValue).toList());
        List<CustomerGroomingAppointmentWithAmountDTO> allCustomerApptsWithAmount = new ArrayList<>();
        int pageSize = 1000;
        for (Integer pageNum = 1; ; pageNum++) {
            CustomerAppointmentWithAmountList list = groomingAppointmentClient.getAppointmentIdsCreatedBetween(
                    businessId.intValue(), sentAtMin, sentAtMax + 2 * 7 * 24 * 60 * 60, params, pageNum, pageSize);
            if (!CollectionUtils.isEmpty(list.getAppts())) {
                allCustomerApptsWithAmount.addAll(list.getAppts());
            }
            if (list.getCount() <= (long) pageNum * pageSize) {
                break;
            }
        }

        AtomicInteger totalEmailsSentCnt = new AtomicInteger();
        AtomicInteger totalEmailsOpenedCnt = new AtomicInteger();
        AtomicInteger totalContributedBookingsCnt = new AtomicInteger();

        emails.forEach(email -> {
            totalEmailsSentCnt.addAndGet(email.getRecipientCount());
            totalEmailsOpenedCnt.addAndGet(email.getOpenedCount());
            AtomicInteger contributedBookingsCnt = new AtomicInteger();
            emailRecipientMap.getOrDefault(email.getId(), List.of()).forEach(v -> {
                if (v.getCustomerId() == null) {
                    return;
                }
                for (CustomerGroomingAppointmentWithAmountDTO appt : allCustomerApptsWithAmount) {
                    if (!appt.getCustomerId().equals(v.getCustomerId().intValue())) {
                        continue;
                    }
                    if (appt.getCreateTime() < email.getSentAt()
                            || appt.getCreateTime() > email.getSentAt() + 2 * 7 * 24 * 60 * 60) {
                        continue;
                    }
                    contributedBookingsCnt.getAndIncrement();
                }
            });
            totalContributedBookingsCnt.addAndGet(contributedBookingsCnt.get());
        });
        return MarketingCampaignsSummaryResponse.newBuilder()
                .setEmailSentCnt(totalEmailsSentCnt.get())
                .setEmailOpenedCnt(totalEmailsOpenedCnt.get())
                .setContributeBookingCnt(totalContributedBookingsCnt.get())
                .build();
    }

    private String getSenderEmail(Long businessId, String senderName) {
        MoeMarketingEmailDetail emailDetail = moeMarketingEmailDetailMapper.findSenderEmailByBusinessId(businessId);
        if (emailDetail == null || emailDetail.getSenderEmail().isEmpty()) {
            String invalidEmailCharacterSet = "[^a-zA-Z0-9._-]";
            return emailPrefix + senderName.replaceAll(invalidEmailCharacterSet, "") + emailSuffix;
        }
        return emailDetail.getSenderEmail();
    }

    private List<MandrillMessage.MessageContent> downloadAttachment(Email email) {
        List<MandrillMessage.MessageContent> attachments = new ArrayList<>();
        try {
            for (var attachment : email.getAttachments()) {
                URL url = new URL(attachment.url());
                URLConnection connection = url.openConnection();
                connection.connect();

                // Download the attachment
                BufferedInputStream inputStream = new BufferedInputStream(connection.getInputStream());
                BufferedOutputStream outputStream = new BufferedOutputStream(new FileOutputStream(attachment.name()));
                byte[] buffer = new byte[1024];
                int bytesRead = 0;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                inputStream.close();
                outputStream.close();

                byte[] fileContent = Files.readAllBytes(new File(attachment.name()).toPath());

                // Encode the attachment content as Base64 string
                String encodedFileContent = Base64.getEncoder().encodeToString(fileContent);

                MandrillMessage.MessageContent attachmentContent = new MandrillMessage.MessageContent();
                attachmentContent.setName(attachment.name());
                attachmentContent.setType(attachment.type());
                attachmentContent.setContent(encodedFileContent);
                attachments.add(attachmentContent);
            }
        } catch (Exception e) {
            log.error("download attachment failed, email={}", new JSONObject(email).toString(), e);
        }
        return attachments;
    }

    private void sendEmail(Email email, Long companyId, Long emailId) {
        Long lastId = 0L;
        Map<Long, MandrillSubaccountInfo> subAccountInfoMap = new HashMap<>();
        while (true) {
            MoeMessageRecipient r = moeMessageRecipientMapper.selectNextRecipient(emailId, lastId);
            if (r == null) {
                break;
            }
            lastId = r.getId();

            // mandrill message id is not null means the email has been sent
            if (r.getMandrillMessageId() != null && !r.getMandrillMessageId().isEmpty()) {
                continue;
            }

            MandrillSubaccountInfo subAccountInfo = subAccountInfoMap.computeIfAbsent(
                    r.getBusinessId(), k -> getEmailSubAccountInfo(r.getBusinessId(), r.getSenderEmail()));

            MandrillMessage m = new MandrillMessage();
            m.setSubject(emailRender.getRenderedSubject(email.getSubject(), r.getBusinessId(), r.getCustomerId()));
            m.setFromName(r.getSenderName());
            m.setFromEmail(r.getSenderEmail());
            m.setAttachments(downloadAttachment(email));

            Optional<SenderEmailModel> sender = customizedEmailSender(r.getBusinessId());
            if (sender.isPresent()) {
                m.setFromEmail(sender.get().getEmail());
                m.setFromName(sender.get().getName());
            }

            m.setHtml(
                    emailRender.getRenderedContent(email, companyId, r.getBusinessId(), r.getCustomerId(), r.getId()));

            MandrillMessage.Recipient to = new MandrillMessage.Recipient();
            to.setName(r.getRecipientName());
            to.setEmail(r.getRecipientEmail());
            m.setTo(Collections.singletonList(to));

            m.setSubaccount(subAccountInfo.getId());

            try {
                MandrillMessageStatus status = mandrillService.sendEmail(m);
                messageStorage.updateSendResult(status, r.getId());

                if (status.getStatus().equals(MandrillService.MANDRILL_EMAIL_STATUS_REJECTED)
                        && status.getRejectReason()
                                .equals(MandrillService.MANDRILL_EMAIL_REJECTED_REASON_UNSUBSCRIBE)) {
                    ThreadPool.execute(() -> customerClient.unsubscribeMarketingEmail(
                            r.getBusinessId().intValue(), r.getCustomerId().intValue()));
                }
            } catch (Exception e) {
                log.error("send email failed, recipientId = {}", r.getId(), e);
            }
        }
    }

    private void sendEmailAsync(Email email, Long companyId, Long emailId) {
        ThreadPool.execute(() -> {
            sendEmail(email, companyId, emailId);
        });
    }

    private List<EmailUser> getRecipients(
            Long companyId,
            Long businessId,
            Long staffId,
            String recipientFilter,
            List<Long> excludeCustomerIds,
            List<Long> includeCustomerIds) {
        var recipientList = new ArrayList<EmailUser>();
        var existedCustomerIds = new HashSet<Long>();
        if (StringUtils.hasText(recipientFilter)) {
            customerClient
                    .getSmartClientListV2ByParamsSnapshot(
                            new ClientListRequestWithParamsSnapshot(companyId, businessId, staffId, recipientFilter))
                    .getPhoneNumberMap()
                    .values()
                    .forEach(d -> {
                        recipientList.add(new EmailUser(
                                d.getFirstName() + " " + d.getLastName(),
                                d.getEmail(),
                                d.getCustomerId().longValue(),
                                d.getPreferredBusinessId().longValue()));
                        existedCustomerIds.add(d.getCustomerId().longValue());
                    });
        }
        if (!CollectionUtils.isEmpty(includeCustomerIds)) {
            customerService
                    .batchGetCustomerInfo(BatchGetCustomerInfoRequest.newBuilder()
                            .addAllIds(includeCustomerIds)
                            .build())
                    .getCustomersList()
                    .stream()
                    .filter(c -> !existedCustomerIds.contains(c.getId()) && StringUtils.hasText(c.getEmail()))
                    .forEach(c -> recipientList.add(new EmailUser(
                            c.getFirstName() + " " + c.getLastName(),
                            c.getEmail(),
                            c.getId(),
                            c.getPreferredBusinessId())));
        }
        return recipientList.stream()
                .filter(r ->
                        CollectionUtils.isEmpty(excludeCustomerIds) || !excludeCustomerIds.contains(r.customerId()))
                .toList();
    }

    public Map<Long, EmailSenderVO> getSenderMap(Long companyId) {
        List<LocationBriefView> locations = businessClient.getLocationList(companyId);
        return locations.stream()
                .collect(Collectors.toMap(
                        LocationBriefView::getId,
                        location -> new EmailSenderVO(
                                location.getName(), getSenderEmail(location.getId(), location.getName()))));
    }

    private MandrillSubaccountInfo getEmailSubAccountInfo(Long businessId, String businessName) {
        String subAccountId = getMandrillSubAccountId(businessId);
        Integer quota = 2000; // Set quota to 2000 emails per hour

        return mandrillService.createIfNotExist(subAccountId, businessName, "", quota);
    }

    private String getMandrillSubAccountId(Long businessId) {
        return String.format("%s%s", subAccountPrefix, businessId.toString());
    }

    @Override
    public Long insertMarketingEmailTemplate(MoeMarketingEmailTemplate moeMessageTemplate) {
        moeMarketingEmailTemplateMapper.insertSelective(moeMessageTemplate);

        return moeMessageTemplate.getId();
    }

    @Override
    public Long updateMarketingEmailTemplate(MoeMarketingEmailTemplate moeMessageTemplate) {
        moeMarketingEmailTemplateMapper.updateByPrimaryKeySelective(moeMessageTemplate);
        return moeMessageTemplate.getId();
    }

    private Optional<SenderEmailModel> customizedEmailSender(long businessID) {
        if (!customizedEmailDomainEnable(businessID)) {
            return Optional.empty();
        }

        GetSenderEmailResponse getSenderResp = engagementEmailService.getSenderEmail(
                GetSenderEmailRequest.newBuilder().setBusinessId(businessID).build());
        if (DomainVerifyStatus.VERIFY_STATUS_VERIFIED
                != getSenderResp.getSenderEmail().getVerifyStatus()) {
            return Optional.empty();
        }
        if (getSenderResp.getSenderEmailUsageType() != SenderEmailUsageType.SENDER_EMAIL_USAGE_TYPE_CUSTOMIZE) {
            return Optional.empty();
        }
        if (Strings.isNullOrEmpty(getSenderResp.getSenderEmail().getEmail())) {
            return Optional.empty();
        }

        return Optional.of(getSenderResp.getSenderEmail());
    }

    private boolean customizedEmailDomainEnable(long businessID) {
        return featureFlagApi.isOn(
                FeatureFlags.CUSTOMIZED_EMAIL_DOMAIN,
                FeatureFlagContext.builder().business(businessID).build());
    }
}
