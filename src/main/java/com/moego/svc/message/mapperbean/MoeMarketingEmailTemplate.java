package com.moego.svc.message.mapperbean;

public class MoeMarketingEmailTemplate {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.subject
     *
     * @mbg.generated
     */
    private String subject;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.image_url
     *
     * @mbg.generated
     */
    private String imageUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.type
     *
     * @mbg.generated
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.enterprise_id
     *
     * @mbg.generated
     */
    private Long enterpriseId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.content
     *
     * @mbg.generated
     */
    private String content;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_marketing_email_template.client_filter
     *
     * @mbg.generated
     */
    private String clientFilter;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.id
     *
     * @return the value of moe_marketing_email_template.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.id
     *
     * @param id the value for moe_marketing_email_template.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.name
     *
     * @return the value of moe_marketing_email_template.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.name
     *
     * @param name the value for moe_marketing_email_template.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.subject
     *
     * @return the value of moe_marketing_email_template.subject
     *
     * @mbg.generated
     */
    public String getSubject() {
        return subject;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.subject
     *
     * @param subject the value for moe_marketing_email_template.subject
     *
     * @mbg.generated
     */
    public void setSubject(String subject) {
        this.subject = subject == null ? null : subject.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.description
     *
     * @return the value of moe_marketing_email_template.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.description
     *
     * @param description the value for moe_marketing_email_template.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.image_url
     *
     * @return the value of moe_marketing_email_template.image_url
     *
     * @mbg.generated
     */
    public String getImageUrl() {
        return imageUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.image_url
     *
     * @param imageUrl the value for moe_marketing_email_template.image_url
     *
     * @mbg.generated
     */
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl == null ? null : imageUrl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.sort
     *
     * @return the value of moe_marketing_email_template.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.sort
     *
     * @param sort the value for moe_marketing_email_template.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.type
     *
     * @return the value of moe_marketing_email_template.type
     *
     * @mbg.generated
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.type
     *
     * @param type the value for moe_marketing_email_template.type
     *
     * @mbg.generated
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.enterprise_id
     *
     * @return the value of moe_marketing_email_template.enterprise_id
     *
     * @mbg.generated
     */
    public Long getEnterpriseId() {
        return enterpriseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.enterprise_id
     *
     * @param enterpriseId the value for moe_marketing_email_template.enterprise_id
     *
     * @mbg.generated
     */
    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.content
     *
     * @return the value of moe_marketing_email_template.content
     *
     * @mbg.generated
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.content
     *
     * @param content the value for moe_marketing_email_template.content
     *
     * @mbg.generated
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_marketing_email_template.client_filter
     *
     * @return the value of moe_marketing_email_template.client_filter
     *
     * @mbg.generated
     */
    public String getClientFilter() {
        return clientFilter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_marketing_email_template.client_filter
     *
     * @param clientFilter the value for moe_marketing_email_template.client_filter
     *
     * @mbg.generated
     */
    public void setClientFilter(String clientFilter) {
        this.clientFilter = clientFilter == null ? null : clientFilter.trim();
    }
}