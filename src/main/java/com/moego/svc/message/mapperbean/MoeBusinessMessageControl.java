package com.moego.svc.message.mapperbean;

public class MoeBusinessMessageControl {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.company_id
     *
     * @mbg.generated
     */
    private Integer companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.cycle_begin_time
     *
     * @mbg.generated
     */
    private Integer cycleBeginTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.cycle_end_time
     *
     * @mbg.generated
     */
    private Integer cycleEndTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.force_block_message
     *
     * @mbg.generated
     */
    private Boolean forceBlockMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.force_allow_message
     *
     * @mbg.generated
     */
    private Boolean forceAllowMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.force_block_email
     *
     * @mbg.generated
     */
    private Boolean forceBlockEmail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.subscription_amount
     *
     * @mbg.generated
     */
    private Integer subscriptionAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.purchase_amount
     *
     * @mbg.generated
     */
    private Integer purchaseAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.purchase_leftover
     *
     * @mbg.generated
     */
    private Integer purchaseLeftover;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.used_2way_message
     *
     * @mbg.generated
     */
    private Integer used2wayMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.free_auto_message
     *
     * @mbg.generated
     */
    private Integer freeAutoMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.used_auto_message
     *
     * @mbg.generated
     */
    private Integer usedAutoMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.used_call
     *
     * @mbg.generated
     */
    private Integer usedCall;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.subscription_date
     *
     * @mbg.generated
     */
    private Integer subscriptionDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.used_message
     *
     * @mbg.generated
     */
    private Integer usedMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.available_emails
     *
     * @mbg.generated
     */
    private Integer availableEmails;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.used_emails
     *
     * @mbg.generated
     */
    private Integer usedEmails;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_control.locked_emails
     *
     * @mbg.generated
     */
    private Integer lockedEmails;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.id
     *
     * @return the value of moe_business_message_control.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.id
     *
     * @param id the value for moe_business_message_control.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.company_id
     *
     * @return the value of moe_business_message_control.company_id
     *
     * @mbg.generated
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.company_id
     *
     * @param companyId the value for moe_business_message_control.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.cycle_begin_time
     *
     * @return the value of moe_business_message_control.cycle_begin_time
     *
     * @mbg.generated
     */
    public Integer getCycleBeginTime() {
        return cycleBeginTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.cycle_begin_time
     *
     * @param cycleBeginTime the value for moe_business_message_control.cycle_begin_time
     *
     * @mbg.generated
     */
    public void setCycleBeginTime(Integer cycleBeginTime) {
        this.cycleBeginTime = cycleBeginTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.cycle_end_time
     *
     * @return the value of moe_business_message_control.cycle_end_time
     *
     * @mbg.generated
     */
    public Integer getCycleEndTime() {
        return cycleEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.cycle_end_time
     *
     * @param cycleEndTime the value for moe_business_message_control.cycle_end_time
     *
     * @mbg.generated
     */
    public void setCycleEndTime(Integer cycleEndTime) {
        this.cycleEndTime = cycleEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.force_block_message
     *
     * @return the value of moe_business_message_control.force_block_message
     *
     * @mbg.generated
     */
    public Boolean getForceBlockMessage() {
        return forceBlockMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.force_block_message
     *
     * @param forceBlockMessage the value for moe_business_message_control.force_block_message
     *
     * @mbg.generated
     */
    public void setForceBlockMessage(Boolean forceBlockMessage) {
        this.forceBlockMessage = forceBlockMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.force_allow_message
     *
     * @return the value of moe_business_message_control.force_allow_message
     *
     * @mbg.generated
     */
    public Boolean getForceAllowMessage() {
        return forceAllowMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.force_allow_message
     *
     * @param forceAllowMessage the value for moe_business_message_control.force_allow_message
     *
     * @mbg.generated
     */
    public void setForceAllowMessage(Boolean forceAllowMessage) {
        this.forceAllowMessage = forceAllowMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.force_block_email
     *
     * @return the value of moe_business_message_control.force_block_email
     *
     * @mbg.generated
     */
    public Boolean getForceBlockEmail() {
        return forceBlockEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.force_block_email
     *
     * @param forceBlockEmail the value for moe_business_message_control.force_block_email
     *
     * @mbg.generated
     */
    public void setForceBlockEmail(Boolean forceBlockEmail) {
        this.forceBlockEmail = forceBlockEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.subscription_amount
     *
     * @return the value of moe_business_message_control.subscription_amount
     *
     * @mbg.generated
     */
    public Integer getSubscriptionAmount() {
        return subscriptionAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.subscription_amount
     *
     * @param subscriptionAmount the value for moe_business_message_control.subscription_amount
     *
     * @mbg.generated
     */
    public void setSubscriptionAmount(Integer subscriptionAmount) {
        this.subscriptionAmount = subscriptionAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.purchase_amount
     *
     * @return the value of moe_business_message_control.purchase_amount
     *
     * @mbg.generated
     */
    public Integer getPurchaseAmount() {
        return purchaseAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.purchase_amount
     *
     * @param purchaseAmount the value for moe_business_message_control.purchase_amount
     *
     * @mbg.generated
     */
    public void setPurchaseAmount(Integer purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.purchase_leftover
     *
     * @return the value of moe_business_message_control.purchase_leftover
     *
     * @mbg.generated
     */
    public Integer getPurchaseLeftover() {
        return purchaseLeftover;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.purchase_leftover
     *
     * @param purchaseLeftover the value for moe_business_message_control.purchase_leftover
     *
     * @mbg.generated
     */
    public void setPurchaseLeftover(Integer purchaseLeftover) {
        this.purchaseLeftover = purchaseLeftover;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.used_2way_message
     *
     * @return the value of moe_business_message_control.used_2way_message
     *
     * @mbg.generated
     */
    public Integer getUsed2wayMessage() {
        return used2wayMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.used_2way_message
     *
     * @param used2wayMessage the value for moe_business_message_control.used_2way_message
     *
     * @mbg.generated
     */
    public void setUsed2wayMessage(Integer used2wayMessage) {
        this.used2wayMessage = used2wayMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.free_auto_message
     *
     * @return the value of moe_business_message_control.free_auto_message
     *
     * @mbg.generated
     */
    public Integer getFreeAutoMessage() {
        return freeAutoMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.free_auto_message
     *
     * @param freeAutoMessage the value for moe_business_message_control.free_auto_message
     *
     * @mbg.generated
     */
    public void setFreeAutoMessage(Integer freeAutoMessage) {
        this.freeAutoMessage = freeAutoMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.used_auto_message
     *
     * @return the value of moe_business_message_control.used_auto_message
     *
     * @mbg.generated
     */
    public Integer getUsedAutoMessage() {
        return usedAutoMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.used_auto_message
     *
     * @param usedAutoMessage the value for moe_business_message_control.used_auto_message
     *
     * @mbg.generated
     */
    public void setUsedAutoMessage(Integer usedAutoMessage) {
        this.usedAutoMessage = usedAutoMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.used_call
     *
     * @return the value of moe_business_message_control.used_call
     *
     * @mbg.generated
     */
    public Integer getUsedCall() {
        return usedCall;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.used_call
     *
     * @param usedCall the value for moe_business_message_control.used_call
     *
     * @mbg.generated
     */
    public void setUsedCall(Integer usedCall) {
        this.usedCall = usedCall;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.subscription_date
     *
     * @return the value of moe_business_message_control.subscription_date
     *
     * @mbg.generated
     */
    public Integer getSubscriptionDate() {
        return subscriptionDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.subscription_date
     *
     * @param subscriptionDate the value for moe_business_message_control.subscription_date
     *
     * @mbg.generated
     */
    public void setSubscriptionDate(Integer subscriptionDate) {
        this.subscriptionDate = subscriptionDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.used_message
     *
     * @return the value of moe_business_message_control.used_message
     *
     * @mbg.generated
     */
    public Integer getUsedMessage() {
        return usedMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.used_message
     *
     * @param usedMessage the value for moe_business_message_control.used_message
     *
     * @mbg.generated
     */
    public void setUsedMessage(Integer usedMessage) {
        this.usedMessage = usedMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.business_id
     *
     * @return the value of moe_business_message_control.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.business_id
     *
     * @param businessId the value for moe_business_message_control.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.available_emails
     *
     * @return the value of moe_business_message_control.available_emails
     *
     * @mbg.generated
     */
    public Integer getAvailableEmails() {
        return availableEmails;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.available_emails
     *
     * @param availableEmails the value for moe_business_message_control.available_emails
     *
     * @mbg.generated
     */
    public void setAvailableEmails(Integer availableEmails) {
        this.availableEmails = availableEmails;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.used_emails
     *
     * @return the value of moe_business_message_control.used_emails
     *
     * @mbg.generated
     */
    public Integer getUsedEmails() {
        return usedEmails;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.used_emails
     *
     * @param usedEmails the value for moe_business_message_control.used_emails
     *
     * @mbg.generated
     */
    public void setUsedEmails(Integer usedEmails) {
        this.usedEmails = usedEmails;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_control.locked_emails
     *
     * @return the value of moe_business_message_control.locked_emails
     *
     * @mbg.generated
     */
    public Integer getLockedEmails() {
        return lockedEmails;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_control.locked_emails
     *
     * @param lockedEmails the value for moe_business_message_control.locked_emails
     *
     * @mbg.generated
     */
    public void setLockedEmails(Integer lockedEmails) {
        this.lockedEmails = lockedEmails;
    }
}
