# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Setup and Installation
```bash
make init              # Initialize git hooks and install development tools
make tools             # Install all required development dependencies
go mod tidy            # Clean up go.mod and go.sum files
```

### Development Workflow
```bash
make lint              # Run linting, formatting, and code quality checks
make build             # Build the payment service binary (outputs 'server')
make test              # Run all tests
make run               # Build and run the service locally with config/config.yaml
```

### Testing
```bash
go test ./...                                    # Run all tests
go test ./internal/logic/payment/...             # Run payment logic tests
go test -coverprofile=coverage.out ./...        # Run tests with coverage
```

### CI/CD Commands (from ci/ci.json)
```bash
golangci-lint run -v --allow-parallel-runners --timeout 5m    # Lint (CI version)
CGO_ENABLED=0 go build -o moego-svc-payment ./internal        # Production build
```

## Service Architecture

### High-Level Structure
This is a payment microservice built in Go that handles payment processing, onboarding, refunds, and webhooks. The service follows a layered architecture with clear separation between transport (gRPC services), business logic, and data access layers.

### Core Components

#### Services Layer (`internal/service/`)
- **PaymentService**: Main payment processing operations
- **OnboardService**: Merchant/business onboarding workflows  
- **RefundService**: Refund processing and management
- **WebhookService**: Webhook handling for payment providers
- **TaskService**: Background task processing
- **MergeClientService**: Legacy payment system integration
- **PaymentOpsService**: Operational payment tasks

#### Business Logic Layer (`internal/logic/`)
- **payment/**: Core payment processing logic with processor abstraction
- **onboard/**: Onboarding workflows with channel-specific implementations
- **processor/**: Payment channel processors (currently Adyen, with Stripe legacy support)
- **refund/**: Refund state management and processing
- **recurring/**: Recurring payment management
- **webhook/**: Webhook processing for different payment channels
- **event/**: Event bus integration and task queuing

#### Repository Layer (`internal/repo/`)
- **payment/**: Payment data access and client interfaces
- **transaction/**: Transaction entity management
- **account/**: Channel account management
- **paymentmethod/**: Payment method persistence
- **adyen/**: Adyen-specific API integrations
- **mergeclient/**: Legacy system integration
- **locker/**: Distributed locking for concurrency control

### Payment Channel Architecture
The service uses a pluggable processor pattern for different payment channels:
- Each channel implements the `Processor` interface
- Factory pattern (`processor.Factory`) routes to appropriate channel processors
- Currently supports Adyen as primary channel, Stripe for legacy payments
- Channel-specific logic is isolated in respective processor packages

### Key Business Concepts
- **Payment Transaction**: Core payment entity with state management
- **Channel Accounts**: Merchant accounts with payment providers
- **Onboarding**: Multi-step merchant verification and setup process
- **Recurring Payments**: Subscription and recurring payment handling
- **Legacy Integration**: Backward compatibility with existing payment system (payment ID < **********)

### Database Configuration
- **moego_payment**: Primary PostgreSQL database for new payments
- **moe_payment**: Legacy MySQL database for backward compatibility
- Uses GORM as ORM with custom serializers from go-lib

### Configuration
- Config loading handled by go-lib with YAML support
- Environment-specific configs in `config/` directory
- Supports Redis for caching and distributed locking
- Growthbook integration for feature flags

### Dependency Management
- Uses Google Wire for dependency injection
- Mock generation with go.uber.org/mock for testing
- Structured logging with go.uber.org/zap

### Testing Strategy
- Unit tests focus on `internal/logic/` business logic
- Coverage gate set at 45% minimum
- Excludes mock, repo, and entity packages from coverage
- Test reports generated in XML format for CI/CD integration

## AddRecurringPaymentMethod Feature

### Status
The AddRecurringPaymentMethod feature has been implemented but is currently commented out pending API definition updates in moego-api-definitions.

### Implementation Details
- **Core Logic**: Zero-amount payment approach to trigger card storage via Adyen webhooks
- **Location**: `internal/logic/payment/payment.go` (commented implementation)
- **Service Layer**: `internal/service/payment_service.go` (commented API)
- **Documentation**: See `docs/add-recurring-payment-method-implementation-plan.md` for detailed implementation plan
- **API Requirements**: See `docs/api-definitions-needed.md` for required protobuf definitions

### Key Features Implemented
- Zero-amount payment validation and fee calculation logic
- Comprehensive error handling with structured logging
- Integration with existing payment flow and webhook system
- Support for Adyen card storage via StorePaymentMethod flag

### Activation Steps
1. Add required API definitions to moego-api-definitions (see `docs/api-definitions-needed.md`)
2. Uncomment implementation in `internal/logic/payment/payment.go`
3. Uncomment service method in `internal/service/payment_service.go`
4. Update CreatePayment validation to allow zero-amount payments for RECURRING_PAYMENT_METHOD type
5. Run tests and verify functionality