package com.moego.server.retail.mapper;

import com.moego.server.retail.mapperbean.Package;
import com.moego.server.retail.mapperbean.PackageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PackageMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    long countByExample(PackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    int deleteByExample(PackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    int insert(Package record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    int insertSelective(Package record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    List<Package> selectByExample(PackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    Package selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") Package record, @Param("example") PackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") Package record, @Param("example") PackageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(Package record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table package
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(Package record);

    /**
     * 这个方法和 {@link PackageService#deprecatedSelectPageList(Integer, String, Boolean, GetListQueryParams)} 同生共死
     */
    @Deprecated(since = "2024/7/24")
    Long getListCount(
            @Param("businessId") Integer businessId,
            @Param("name") String name,
            @Param("onlyAvailable") Boolean onlyAvailable);

    /**
     * 这个方法和 {@link PackageService#deprecatedSelectPageList(Integer, String, Boolean, GetListQueryParams)} 同生共死
     */
    @Deprecated(since = "2024/7/24")
    List<Package> selectList(
            @Param("businessId") Integer businessId,
            @Param("name") String name,
            @Param("onlyAvailable") Boolean onlyAvailable,
            @Param("limitOffset") Integer limitOffset,
            @Param("pageSize") Integer pageSize,
            @Param("sort") String sort,
            @Param("order") String order);

    void addSoldQuantity(
            @Param("packageId") Integer packageId,
            @Param("quantity") Integer quantity,
            @Param("updateTime") Long updateTime);

    List<Package> queryAll(@Param("businessIds") List<Integer> businessIds, @Param("taxIds") List<Integer> taxIds);

    List<Package> listPackages(@Param("companyId") Long companyId, @Param("businessIds") List<Integer> businessIds);
}
