package com.moego.server.retail.service.params;

import com.moego.server.retail.service.params.order.LineDiscountParams;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SetDiscountParams {

    @NotNull
    private Long orderId;

    private List<LineDiscountParams> lineDiscounts;

    private Boolean checkRefund;
}
