package com.moego.server.retail.service;

import static com.moego.common.utils.PageUtil.hasEmptyCollectionFilter;
import static com.moego.common.utils.PageUtil.selectPage;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.BusinessUtil;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessTaxClient;
import com.moego.server.business.dto.MoeBusinessTaxDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.retail.dto.CreateProductResult;
import com.moego.server.retail.dto.ListProductsResult;
import com.moego.server.retail.mapper.ProductMapper;
import com.moego.server.retail.mapper.StockEventMapper;
import com.moego.server.retail.mapperbean.InvoiceItem;
import com.moego.server.retail.mapperbean.Product;
import com.moego.server.retail.mapperbean.StockEvent;
import com.moego.server.retail.mapstruct.ProductConverter;
import com.moego.server.retail.param.CreateProductParams;
import com.moego.server.retail.param.DescribeProductsParams;
import com.moego.server.retail.param.ListProductsParams;
import com.moego.server.retail.service.dto.ProductInfoDto;
import com.moego.server.retail.service.dto.ProductQueryDTO;
import com.moego.server.retail.service.dto.RetailItemPurchaseInfoDTO;
import com.moego.server.retail.service.dto.RetailProductListDto;
import com.moego.server.retail.service.enums.StockEventEnum;
import com.moego.server.retail.service.params.GetListQueryParams;
import com.moego.server.retail.service.params.ProductParams;
import com.moego.server.retail.service.params.ProductQueryParams;
import com.moego.server.retail.service.params.UpdateProductParams;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
public class ProductService {

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private StockEventMapper stockEventMapper;

    @Autowired
    private OrderService orderService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private IBusinessTaxClient iBusinessTaxClient;

    public ProductInfoDto insert(Long companyId, ProductParams productParams) {
        if (productParams.getBusinessId() == null || productParams.getBusinessId() == 0) {
            throw new CommonException(ResponseCodeEnum.BUSINESS_IS_EMPTY);
        }
        // 检查唯一字段是否有效
        checkUniqueParams(productParams, null);
        if ("".equals(productParams.getSku())) {
            productParams.setSku(null);
        }
        if ("".equals(productParams.getBarcode())) {
            productParams.setBarcode(null);
        }

        Product product = new Product();
        BeanUtils.copyProperties(productParams, product);
        product.setCompanyId(companyId);

        if (productParams.getTaxId() != null && productParams.getTaxId() != 0) {
            InfoIdParams infoIdParams = new InfoIdParams();
            infoIdParams.setInfoId(productParams.getTaxId());
            MoeBusinessTaxDto moeBusinessTaxDto = iBusinessTaxClient.getTaxById(infoIdParams);
            if (moeBusinessTaxDto == null
                    || !moeBusinessTaxDto.getCompanyId().equals(companyId)
                    || (moeBusinessTaxDto.getBusinessId() != 0
                            && !moeBusinessTaxDto.getBusinessId().equals(productParams.getBusinessId()))) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid tax id");
            }
            product.setTaxRate(new BigDecimal(moeBusinessTaxDto.getTaxRate()));
        }

        if ((productParams.getStock() == null || productParams.getStock() == 0)
                && productParams.getAddedStock() != null
                && productParams.getAddedStock() != 0) {
            product.setStock(productParams.getAddedStock());
        }

        product.setCreateTime(CommonUtil.get10Timestamp());
        product.setUpdateTime(CommonUtil.get10Timestamp());

        try {
            productMapper.insertSelective(product);
        } catch (DataIntegrityViolationException ex) {
            if (ex.getMessage().contains("Duplicate")) {
                if (ex.getMessage().contains("UIX_product_business_id_name")) {
                    throw new CommonException(ResponseCodeEnum.NAME_IS_EXIST);
                }
                if (ex.getMessage().contains("UIX_product_business_id_sku")) {
                    throw new CommonException(ResponseCodeEnum.SKU_IS_EXIST);
                }
            }
            throw ex;
        }

        if (productParams.getStock() != null && productParams.getStock() != 0) {
            setStockEvent(product.getId(), 0, productParams.getStock());
        } else if (productParams.getAddedStock() != null && productParams.getAddedStock() != 0) {
            addStockEvent(product.getId(), productParams.getAddedStock());
        }

        return selectById(productParams.getBusinessId(), product.getId());
    }

    private void checkUniqueParams(ProductParams params, Integer productId) {
        if (params.getName() != null && params.getName().isEmpty()) {
            throw new CommonException(ResponseCodeEnum.NAME_IS_EMPTY);
        }
        if (productMapper.countNotDeletedProductByBusinessName(params.getBusinessId(), params.getName(), productId)
                > 0) {
            throw new CommonException(ResponseCodeEnum.NAME_IS_EXIST);
        }
        if (StringUtils.hasLength(params.getSku())
                && productMapper.countNotDeletedProductByBusinessSku(params.getBusinessId(), params.getSku(), productId)
                        > 0) {
            throw new CommonException(ResponseCodeEnum.SKU_IS_EXIST);
        }
        if (StringUtils.hasLength(params.getBarcode())
                && productMapper.countNotDeletedProductByBusinessBarcode(
                                params.getBusinessId(), params.getBarcode(), productId)
                        > 0) {
            throw new CommonException(ResponseCodeEnum.BARCODE_IS_EXIST);
        }
    }

    public ProductInfoDto selectById(Integer businessId, Integer productId) {
        ProductInfoDto productInfoDto = productMapper.selectInfoByPrimaryKey(productId);

        if (productInfoDto == null) {
            throw new CommonException(ResponseCodeEnum.PRODUCT_NOT_FOUND);
        }
        // 跨商家检查
        BusinessUtil.checkBusinessId(businessId, productInfoDto.getBusinessId());

        // 更新 taxRate
        if (productInfoDto.getTaxId() != null && productInfoDto.getTaxId() > 0) {
            MoeBusinessTaxDto taxInfo = iBusinessTaxClient.getTaxById(
                    InfoIdParams.builder().infoId(productInfoDto.getTaxId()).build());
            if (taxInfo != null && taxInfo.getTaxRate() != null) {
                productInfoDto.setTaxRate(BigDecimal.valueOf(taxInfo.getTaxRate()));
            }
        }

        List<StockEvent> stockEvents = stockEventMapper.selectListByProductId(productId);
        if (stockEvents != null) {
            productInfoDto.setStockEvents(stockEvents.stream()
                    .map(s -> {
                        ProductInfoDto.StockEventInfo info = new ProductInfoDto.StockEventInfo();
                        BeanUtils.copyProperties(s, info);
                        return info;
                    })
                    .collect(Collectors.toList()));
        }

        return productInfoDto;
    }

    public List<ProductInfoDto> selectByIds(Integer businessId, Set<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        return productMapper.selectByIds(businessId, productIds);
    }

    public int delete(Integer businessId, Integer productId) {
        ProductInfoDto productInfoDto = productMapper.selectInfoByPrimaryKey(productId);

        if (productInfoDto == null) {
            throw new CommonException(ResponseCodeEnum.PRODUCT_NOT_FOUND);
        }
        // 跨商家检查
        BusinessUtil.checkBusinessId(businessId, productInfoDto.getBusinessId());

        Product product = new Product();
        product.setId(productId);
        product.setDeleted(true);
        return productMapper.updateByPrimaryKeySelective(product);
        //        return productMapper.deleteByPrimaryKey(productId);
    }

    public ProductInfoDto update(Long companyId, UpdateProductParams productParams) {
        ProductInfoDto productInfo = productMapper.selectInfoByPrimaryKey(productParams.getProductId());
        // 跨商家检查
        if (productInfo == null || !Objects.equals(productParams.getBusinessId(), productInfo.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR);
        }
        // 检查唯一字段是否有效
        checkUniqueParams(productParams, productParams.getProductId());

        Product product = new Product();
        BeanUtils.copyProperties(productParams, product);
        product.setId(productParams.getProductId());

        if (productParams.getTaxId() != null && productParams.getTaxId() != 0) {
            InfoIdParams infoIdParams = new InfoIdParams();
            infoIdParams.setInfoId(productParams.getTaxId());
            MoeBusinessTaxDto moeBusinessTaxDto = iBusinessTaxClient.getTaxById(infoIdParams);
            if (moeBusinessTaxDto != null && moeBusinessTaxDto.getTaxRate() != null) {
                if (!moeBusinessTaxDto.getCompanyId().equals(companyId)
                        || moeBusinessTaxDto.getBusinessId() != 0
                                && !moeBusinessTaxDto.getBusinessId().equals(productParams.getBusinessId())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid tax id");
                }
                product.setTaxRate(new BigDecimal(moeBusinessTaxDto.getTaxRate()));
            } else {
                product.setTaxId(0);
                product.setTaxRate(BigDecimal.ZERO);
            }
        }

        if (productParams.getStock() != null) {
            setStockEvent(product.getId(), productInfo.getStock(), productParams.getStock());
        } else if (productParams.getAddedStock() != null && productParams.getAddedStock() != 0) {
            StockEvent event = addStockEvent(product.getId(), productParams.getAddedStock());
            product.setStock(event.getCurrentQuantity());
        }

        product.setUpdateTime(CommonUtil.get10Timestamp());

        if (product.hasValue()) {
            try {
                productMapper.updateByPrimaryKeySelectiveWithSpecialPrice(product);
            } catch (DataIntegrityViolationException ex) {
                if (ex.getMessage().contains("Duplicate")) {
                    if (ex.getMessage().contains("UIX_product_business_id_name")) {
                        throw new CommonException(ResponseCodeEnum.NAME_IS_EXIST);
                    }
                    if (ex.getMessage().contains("UIX_product_business_id_sku")) {
                        throw new CommonException(ResponseCodeEnum.SKU_IS_EXIST);
                    }
                }
                throw ex;
            }
        }

        return selectById(productParams.getBusinessId(), productParams.getProductId());
    }

    public RetailProductListDto selectPageList(Integer businessId, String name, GetListQueryParams params) {
        int productCount = productMapper.getListCount(businessId, name);
        List<ProductInfoDto> productList = productMapper.selectList(
                businessId,
                name,
                CommonUtil.getLimitOffset(params.getPageNum(), params.getPageSize()),
                params.getPageSize(),
                params.validSortFor("product"),
                params.validOrder());

        // 更新 taxRate
        updateProductTax(productList);

        RetailProductListDto returnMap = new RetailProductListDto();
        returnMap.setProductList(productList);
        returnMap.setProductCount(productCount);
        return returnMap;
    }

    /**
     * 三种方式查询 product <br/>
     * 1. 按 barcode 查询，返回唯一匹配的一个，用于前端扫条形码查询 <br/>
     * 2. 按 customerId 查询，返回该 customer 最近购买的6个 product，用于前端 customer recent <br/>
     * 3. 按关键词查询，分页返回，支持排序
     */
    public ProductQueryDTO queryProducts(Integer businessId, ProductQueryParams params) {
        Integer resultCount = 0;
        if (Objects.nonNull(params.getBusinessId())) {
            businessId = params.getBusinessId();
        }
        List<ProductInfoDto> resultList = new ArrayList<>();
        if (StringUtils.hasLength(params.getBarcode())) {
            // 有barcode时，全匹配查询，且只返回一个
            ProductInfoDto dto = productMapper.selectByBarcode(businessId, params.getBarcode());
            if (dto != null) {
                resultCount++;
                resultList.add(dto);
            }
        } else if (params.getCustomerId() != null) {
            // customer recent默认查6个
            List<RetailItemPurchaseInfoDTO> items = orderService.getCustomerRecentPurchaseProducts(
                    businessId.longValue(), params.getCustomerId().longValue(), 6);
            if (!CollectionUtils.isEmpty(items)) {
                Map<Integer, RetailItemPurchaseInfoDTO> itemMap = items.stream()
                        .collect(Collectors.toMap(
                                item -> item.getObjectId().intValue(),
                                item -> item,
                                (v1, v2) -> v1.getPurchasedTime() > v2.getPurchasedTime() ? v1 : v2));
                resultList = productMapper.selectByIds(businessId, itemMap.keySet());
                resultList.removeIf(p -> Boolean.TRUE.equals(p.getDeleted())); // 过滤已删除的product
                resultList.forEach(product -> {
                    if (itemMap.containsKey(product.getId())) {
                        product.setLastPurchasedTime(
                                itemMap.get(product.getId()).getPurchasedTime());
                    } else {
                        product.setLastPurchasedTime(0L); // 避免后面排序空指针
                    }
                });
                // 按customer查询的结果数设置为查询的数量
                resultCount = resultList.size();
                // 根据lastPurchasedTime降序排序
                resultList.sort(Comparator.comparing(ProductInfoDto::getLastPurchasedTime, Comparator.reverseOrder()));
            }
        } else {
            // 分页默认值
            if (params.getPageNum() == null) params.setPageNum(1);
            if (params.getPageSize() == null) params.setPageSize(10);
            String sortBy = params.validSortField();
            String orderBy = params.validOrder();

            resultCount = productMapper.getListByKeywordCount(businessId, params.getKeyword(), params.getCategoryId());
            if (Objects.equals(resultCount, 0)) {
                return new ProductQueryDTO().setResultCount(resultCount).setResultList(resultList);
            }
            resultList = productMapper.selectListByKeyword(
                    businessId,
                    params.getKeyword(),
                    params.getCategoryId(),
                    CommonUtil.getLimitOffset(params.getPageNum(), params.getPageSize()),
                    params.getPageSize(),
                    sortBy,
                    orderBy);
        }

        // 查询最新的 taxRate 填充到 resultList
        updateProductTax(resultList);

        return new ProductQueryDTO().setResultCount(resultCount).setResultList(resultList);
    }

    public StockEvent setSold(InvoiceItem invoiceItem) {
        StockEvent existStockEvent = stockEventMapper.selectLatestByProductId(invoiceItem.getProductId());
        int previousQuantity = existStockEvent == null ? 0 : existStockEvent.getCurrentQuantity();

        StockEvent newStockEvent = new StockEvent();
        newStockEvent.setInvoiceItemId(invoiceItem.getId());
        newStockEvent.setProductId(invoiceItem.getProductId());
        newStockEvent.setOperation(StockEventEnum.SOLD.getName());
        newStockEvent.setQuantity(-invoiceItem.getQuantity());
        newStockEvent.setCurrentQuantity(previousQuantity - invoiceItem.getQuantity());

        newStockEvent.setCreateTime(CommonUtil.get10Timestamp());
        stockEventMapper.insertSelective(newStockEvent);

        updateProductStock(newStockEvent.getProductId(), newStockEvent.getCurrentQuantity());

        return newStockEvent;
    }

    /**
     * 更新production stock
     *
     * @param productId
     * @param newStock
     * @return
     */
    private int updateProductStock(Integer productId, Integer newStock) {
        Product product = new Product();
        product.setId(productId);
        product.setStock(newStock);
        product.setUpdateTime(CommonUtil.get10Timestamp());
        return productMapper.updateByPrimaryKeySelective(product);
    }

    private StockEvent addStockEvent(Integer productId, Integer addedStockQuantity) {
        StockEvent newStockEvent = new StockEvent();
        newStockEvent.setProductId(productId);
        newStockEvent.setOperation(StockEventEnum.ADDED.getName());
        newStockEvent.setQuantity(addedStockQuantity);
        newStockEvent.setCurrentQuantity(addedStockQuantity);

        StockEvent existStockEvent = stockEventMapper.selectLatestByProductId(productId);
        if (existStockEvent != null) {
            newStockEvent.setCurrentQuantity(existStockEvent.getCurrentQuantity() + addedStockQuantity);
        }

        newStockEvent.setCreateTime(CommonUtil.get10Timestamp());
        stockEventMapper.insertSelective(newStockEvent);
        return newStockEvent;
    }

    private StockEvent setStockEvent(Integer productId, Integer originStockQuantity, Integer newStockQuantity) {
        StockEvent newStockEvent = new StockEvent();
        newStockEvent.setProductId(productId);
        if (originStockQuantity.compareTo(newStockQuantity) > 0) {
            newStockEvent.setOperation(StockEventEnum.REMOVED.getName());
        } else {
            newStockEvent.setOperation(StockEventEnum.ADDED.getName());
        }
        newStockEvent.setQuantity(newStockQuantity);
        newStockEvent.setCurrentQuantity(newStockQuantity);

        StockEvent existStockEvent = stockEventMapper.selectLatestByProductId(productId);
        if (existStockEvent != null) {
            newStockEvent.setQuantity(Math.abs(newStockQuantity - existStockEvent.getCurrentQuantity()));
            if (newStockEvent.getQuantity() == 0) {
                return existStockEvent;
            }
        }

        newStockEvent.setCreateTime(CommonUtil.get10Timestamp());
        stockEventMapper.insertSelective(newStockEvent);
        return newStockEvent;
    }

    /**
     * 保存product库存更新事件，并更新库存
     *
     * @param productId
     * @param event
     * @param quantity      更新数量，Added/Return-正数，Removed/Sold-负数
     * @param invoiceItemId
     */
    @Transactional
    public void saveStockEvent(Integer productId, StockEventEnum event, Integer quantity, Integer invoiceItemId) {
        Product product = productMapper.selectByPrimaryKey(productId);
        if (product == null) {
            return;
        }

        StockEvent existStockEvent = stockEventMapper.selectLatestByProductId(productId);
        // 如果找不到上一个库存事件，则取 product 的 stock 字段作为更新前库存
        int previousQuantity = existStockEvent == null ? product.getStock() : existStockEvent.getCurrentQuantity();

        StockEvent newStockEvent = new StockEvent();
        newStockEvent.setProductId(productId);
        newStockEvent.setOperation(event.getName());
        newStockEvent.setQuantity(Math.abs(quantity)); // 取绝对值
        newStockEvent.setCurrentQuantity(previousQuantity + quantity); // 事件前的数量加上更新数量，可能变负数
        newStockEvent.setInvoiceItemId(invoiceItemId);
        newStockEvent.setCreateTime(CommonUtil.get10Timestamp());
        stockEventMapper.insertSelective(newStockEvent);

        updateProductStock(newStockEvent.getProductId(), newStockEvent.getCurrentQuantity());
    }

    public boolean checkProductTaxExist(List<Integer> businessIdList, List<Integer> taxIdList) {
        if (CollectionUtils.isEmpty(taxIdList) || CollectionUtils.isEmpty(businessIdList)) {
            return false;
        }
        // product 白名单 & tax 检查
        Map<Integer, Boolean> isBusinessRetailEnableMap = iBusinessBusinessClient.isRetailEnable(businessIdList);
        Set<Integer> isBusinessTaxInUse = productMapper.queryAll(businessIdList, taxIdList).stream()
                .map(Product::getBusinessId)
                .collect(Collectors.toSet());

        for (Integer businessId : businessIdList) {
            if (isBusinessRetailEnableMap.getOrDefault(businessId, false) && isBusinessTaxInUse.contains(businessId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 更新 product 列表的 taxRate
     *
     * @param productList product list
     */
    private void updateProductTax(List<ProductInfoDto> productList) {
        Map<Integer, Double> taxMap = getTaxMapForProduct(productList);
        if (!CollectionUtils.isEmpty(taxMap)) {
            productList.forEach(result -> {
                if (taxMap.containsKey(result.getTaxId())) {
                    result.setTaxRate(BigDecimal.valueOf(taxMap.get(result.getTaxId())));
                }
            });
        }
    }

    /**
     * 查询 product 列表的 tax
     *
     * @param products product 列表
     * @return taxId 对应的 taxRate
     */
    public Map<Integer, Double> getTaxMapForProduct(List<ProductInfoDto> products) {
        List<Integer> taxIds = products.stream()
                .map(ProductInfoDto::getTaxId)
                .filter(taxId -> taxId != null && taxId > 0)
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(taxIds)) {
            return iBusinessTaxClient.getTaxListByIds(taxIds).stream()
                    .collect(Collectors.toMap(MoeBusinessTaxDto::getId, MoeBusinessTaxDto::getTaxRate));
        }
        return Collections.emptyMap();
    }

    public Pair<List<Product>, Pagination> describeProducts(DescribeProductsParams params, Pagination pagination) {
        if (hasEmptyCollectionFilter(params.ids())) {
            return Pair.of(Collections.emptyList(), new Pagination(pagination.pageNum(), pagination.pageSize(), 0));
        }
        return selectPage(pagination, () -> productMapper.describeProducts(params));
    }

    public ListProductsResult listProducts(ListProductsParams params) {
        List<Product> products = productMapper.listProducts(params.companyId(), params.businessIds());
        var productInfos = products.stream()
                .map(ProductConverter.INSTANCE::toProductInfoDTO)
                .toList();
        return new ListProductsResult(productInfos);
    }

    public CreateProductResult createProduct(CreateProductParams params) {
        ProductInfoDto productInfo = insert(params.getCompanyId(), ProductConverter.INSTANCE.toProductParams(params));
        return new CreateProductResult(ProductConverter.INSTANCE.toProductInfoDTO(productInfo));
    }
}
