package com.moego.server.retail.server;

import com.moego.server.retail.api.IPackageServiceBase;
import com.moego.server.retail.dto.CreatePackageResult;
import com.moego.server.retail.dto.ListPackagesResult;
import com.moego.server.retail.mapstruct.PackageConverter;
import com.moego.server.retail.param.CreatePackageParams;
import com.moego.server.retail.param.GetPackageParams;
import com.moego.server.retail.param.ListPackagesByIdsParams;
import com.moego.server.retail.param.ListPackagesParams;
import com.moego.server.retail.result.GetPackageResult;
import com.moego.server.retail.service.PackageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class PackageServer extends IPackageServiceBase {

    @Autowired
    private PackageService packageService;

    @Override
    public GetPackageResult getPackage(GetPackageParams params) {
        var dto = packageService.getById(params.packageId());
        return new GetPackageResult(dto);
    }

    @Override
    public ListPackagesResult listPackages(@RequestBody ListPackagesParams params) {
        return packageService.listPackages(params);
    }

    @Override
    public ListPackagesResult listPackagesByIds(@RequestBody ListPackagesByIdsParams params) {
        return new ListPackagesResult(packageService.listPackagesByIds(params.packageIds()));
    }

    @Override
    public CreatePackageResult createPackage(@RequestBody CreatePackageParams params) {
        var pkg = packageService.createPackage(
                params.getCompanyId(), params.getBusinessId(), PackageConverter.INSTANCE.toPackageParams(params));
        return new CreatePackageResult(pkg);
    }
}
