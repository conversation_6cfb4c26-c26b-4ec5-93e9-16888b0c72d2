package com.moego.server.retail.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class ProductQueryDTO {

    @Schema(description = "总结果数")
    private Integer resultCount;

    @Schema(description = "分页结果列表")
    private List<ProductInfoDto> resultList;
}
