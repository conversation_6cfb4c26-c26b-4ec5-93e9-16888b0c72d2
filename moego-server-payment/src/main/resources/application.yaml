server:
  port: 9204

spring:
  application:
    name: moego-server-payment
  profiles:
    active: local
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-west-2}
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:mysql://${secret.datasource.mysql.url}:${secret.datasource.mysql.port}/moe_payment?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.mysql.moego_server_payment.username}
    password: ${secret.datasource.mysql.moego_server_payment.password}
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
  freemarker:
    prefer-file-system-access: false
  data:
    redis:
      host: ${secret.redis.host}
      password: ${secret.redis.password}
      port: ${secret.redis.port}
      timeout: 60000
      key:
        delimiter: ":"
        prefix: apiv2
      ssl:
        enabled: ${secret.redis.tls}
  activemq:
    broker-url: ${secret.mq.activemq.url}
    password: ${secret.mq.activemq.password}
    user: ${secret.mq.activemq.user}
    enabled: true
    destinationPrefix: refund

mybatis:
  mapper-locations: classpath*:/com/moego/server/payment/mapperxml/*.xml
  type-aliases-package: com.moego.server.payment.mapperbean

pagehelper:
  helperDialect: mysql
  page-size-zero: true
  params: count=countSql
  reasonable: true
  supportMethodsArguments: true

security:
  aes:
    key: 'bPeShVmYq3t6w9z$C&F%C*F-JaNdRnZr'

square:
  access:
    token: ${secret.square.access_token}
  application:
    id: ${secret.square.application.id}
    secret: ${secret.square.application.secret}
  auth:
    baseUrl: https://connect.squareupsandbox.com/oauth2/authorize
  env: sandbox
  webhook:
    secret: ${secret.square.webhook.secret}
    url: https://api.t2.moego.pet/payment/square/webhook

stripe:
  endpoint:
    connect:
      secret: ${secret.stripe.endpoint.connect_secret}
    secret: ${secret.stripe.endpoint.secret}
  key: ${secret.stripe.secret_key}

notification:
  slackWorkflowsBaseUrl: https://hooks.slack.com/workflows
  slackServicesBaseUrl: https://hooks.slack.com/services
  slackTriggersBaseUrl: https://hooks.slack.com/triggers
  slackNotification:
    firstUpgradeUrl: "/T011CF3CMJN/B01QR6ULAUA/lY3vUu7otJbZNpE1OvS46SsO"
  bookingFee:
    slackBonusClaimUrl: "/T011CF3CMJN/A05E5P8AZEJ/466932083109940924/Nd4Ij4JAdl53EF4vg72Ib81M"
  reconcile:
    url: "/T011CF3CMJN/A04QM1QH5TP/448822985436978435/bSIS5NdeLPX3SbreYuGKTy7H"
  volCheck:
    url: "/T011CF3CMJN/A052Z62KBM3/456092206462181265/QcZVFa3226xHTjUAJRzEJtB9"
  preAuth:
    url: "/T011CF3CMJN/A05D98V77L3/465919224947148152/0c1namPMDHg9QavNL3LJyMXD"
  paymentBlocked:
    url: "/T011CF3CMJN/A05NN1FB7GT/474884578771763139/pFyS2kETqsrNKx9TDgXk5bZB"
  payoutReview:
    url: "/T011CF3CMJN/*************/7686e1e791b22ade9b5907e5ca814981"
  accountActivated:
    url: "/T011CF3CMJN/*************/126c286c38318c7d9c9b414d5e637b0c"
  antiFraud:
    url: "/T011CF3CMJN/*************/a0a078f420cbb93ac37c8437cb16eff8"
  subscription:
    url: "/T011CF3CMJN/*************/f0817c36d37d93cdfeae313195250214"
  moegoCare:
    alert:
      url: "/T011CF3CMJN/*************/dc108feab22e606d246cb33c70527b93"
    success:
      url: "/T011CF3CMJN/*************/4a37c12fccfb210f6f1abefe60bf65d1"
  moegoSale:
    alert:
      url: "/T011CF3CMJN/*************/8002d432a9a0880935f1900e7e6e2518"
    success:
      url: "/T011CF3CMJN/*************/2939be62e6bcb0769fd02aa883d76c5a"
  split_payment:
    alert:
      url: "/T011CF3CMJN/*************/92282014fd597198af22f90b53915e26"
  downgrade_enterprise:
    success:
      url: "T011CF3CMJN/*************/dcf24427e3bdb5f1f8e53f8182c6dafe"

# allow processing fee pay by client state list
processing_fee:
  available:
    state: Alabama,AL,Alaska,AK,Arizona,AZ,Arkansas,AR,California,CA,Colorado,CO,Delaware,DE,Florida,FL,Georgia,GA,Hawaii,HI,Idaho,ID,Illinois,IL,Indiana,IN,Iowa,IA,Kansas,KS,Kentucky,KY,Louisiana,LA,Maine,ME,Maryland,MD,Michigan,MI,Minnesota,MN,Mississippi,MS,Missouri,MO,Montana,MT,Nebraska,NE,Nevada,NV,New Hampshire,NH,New Jersey,NJ,New Mexico,NM,New York,NY,North Carolina,NC,North Dakota,ND,Ohio,OH,Oklahoma,OK,Oregon,OR,Pennsylvania,PA,Rhode Island,RI,South Carolina,SC,South Dakota,SD,Tennessee,TN,Texas,TX,Utah,UT,Vermont,VT,Virginia,VA,Washington,WA,West Virginia,WV,Wisconsin,WI,Wyoming,WY,District of Columbia,DC

moego:
  server:
    url:
      business: http://moego-service-business:9203
      customer: http://moego-service-customer:9201
      grooming: http://moego-service-grooming:9206
      message: http://moego-service-message:9205
      retail: http://moego-service-retail:9207
  grpc:
    server:
      enabled: false
    client:
      stubs:
        - service: moego.service.sms.**
          authority: moego-svc-sms:9090
        - service: moego.service.account.**
          authority: moego-svc-account:9090
        - service: moego.service.order.**
          authority: moego-svc-order:9090
        - service: moego.service.message.**
          authority: moego-svc-message:9090
        - service: moego.service.agreement.**
          authority: moego-svc-agreement:9090
        - service: moego.service.account.**
          authority: moego-svc-account:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.business_customer.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.permission.**
          authority: moego-svc-permission:9090
        - service: moego.service.capital.**
          authority: moego-svc-capital:9090
        - service: moego.service.split_payment.**
          authority: moego-svc-split-payment:9090
        - service: moego.service.enterprise.**
          authority: moego-svc-enterprise:9090
        - service: moego.service.metadata.**
          authority: moego-svc-metadata:9090
        - service: moego.service.appointment.**
          authority: moego-svc-appointment:9090
        - service: moego.service.online_booking.**
          authority: moego-svc-online-booking:9090
        - service: moego.service.accounting.v1.*
          authority: moego-svc-accounting:9090
        - service: backend.proto.sales.v1.**
          authority: moego-sales:9090
        - service: moego.service.payment.v2.*
          authority: moego-svc-payment:9090

  messaging:
    pulsar:
      service-url: ${secret.mq.pulsar.service_url}
      authentication: ${secret.mq.pulsar.token}
      tenant: test2
  session:
    moego-pay-max-age: 3600
  event-bus:
    brokers:
      # 可以添加多个 broker 集群的配置, 但是至少要有一个
      # 每个 broker 集群需要指定一个唯一的 name, 以及 broker 的地址列表
      - name: default
        # 一个 broker 集群可以有多个地址, 用逗号分隔或者使用数组, 推荐使用数组, 方便更好的阅读
        addresses:
          - ${secret.mq.kafka.broker_url_0}
          - ${secret.mq.kafka.broker_url_1}
          - ${secret.mq.kafka.broker_url_2}
        security:
          enabled: true
          properties:
            security.protocol: SASL_SSL
            sasl.mechanism: AWS_MSK_IAM
            sasl.jaas.config: software.amazon.msk.auth.iam.IAMLoginModule required;
            sasl.client.callback.handler.class: software.amazon.msk.auth.iam.IAMClientCallbackHandler
    producer:
      # 如果 enabled 为 false (默认值), 则不会初始化 producer, 此时如果代码里依赖了 producer, 则会抛出异常
      enabled: true
      # 发送消息成功时是否打印日志, 默认为 false
      log-success: false
      # 发送消息失败时是否打印日志, 默认为 true
      log-failure: true
  feature-flag:
    growth-book:
      api-host: ${secret.growthbook.host}
      client-key: ${secret.growthbook.client_key}
order:
  offset:
    retail: 20000000
    new: *********

monitoring:
  metrics:
    enabled: true
    url:
      prefix: /payment

s3:
  asset:
    private:
      bucket: moego-private-assets-prod
    signature:
      prefix: signature/
  payment:
    public:
      bucket: moegonew
      key:
        prefix: Public/Uploads/
  domain: https://moegonew.s3-us-west-2.amazonaws.com/
  key: ${secret.aws.access_key_id}
  region: ${secret.aws.region}
  secret: ${secret.aws.secret_access_key}

payment:
  fraud:
    warning_threshold_per_week: 2
company:
  ach:
    application_fee_percent: 0.82

springdoc:
  packages-to-scan:
    - com.moego.server.payment.web
