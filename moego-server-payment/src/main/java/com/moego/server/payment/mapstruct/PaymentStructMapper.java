package com.moego.server.payment.mapstruct;

import com.moego.server.payment.dto.PayDetailDTO;
import com.moego.server.payment.dto.PaymentBlockDTO;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.dto.PaymentDetailDTO;
import com.moego.server.payment.mapperbean.MoePayDetail;
import com.moego.server.payment.mapperbean.MoePaymentBlockedDetail;
import com.moego.server.payment.mapperbean.Payment;
import com.moego.server.payment.params.CreatePaymentParams;
import com.moego.server.payment.params.PaymentRecordParam;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PaymentStructMapper {
    PaymentStructMapper INSTANCE = Mappers.getMapper(PaymentStructMapper.class);

    List<PaymentDTO> toDtos(List<Payment> list);

    PaymentDTO toDto(Payment entity);

    PaymentBlockDTO toBlockDto(MoePaymentBlockedDetail detail);

    @Mapping(target = "id", source = "payment.id")
    @Mapping(target = "businessId", source = "payment.businessId")
    @Mapping(target = "stripeIntentId", source = "payment.stripeIntentId")
    @Mapping(target = "amount", source = "payment.amount")
    @Mapping(target = "tips", source = "payment.tips")
    @Mapping(target = "isDeposit", source = "payment.isDeposit")
    @Mapping(target = "createTime", source = "payment.createTime")
    @Mapping(target = "updateTime", source = "payment.updateTime")
    @Mapping(target = "companyId", source = "payment.companyId")
    PaymentDetailDTO toDetail(Payment payment, MoePayDetail detail);

    PayDetailDTO toPayDetail(MoePayDetail detail);

    Payment toEntity(PaymentRecordParam param);

    Payment toEntity(CreatePaymentParams param);
}
