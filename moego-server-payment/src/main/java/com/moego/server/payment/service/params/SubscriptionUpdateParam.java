package com.moego.server.payment.service.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/8/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "new pricing 订阅参数")
public class SubscriptionUpdateParam extends SubscriptionBaseParam {

    @NotNull
    Integer companyId;

    @Schema(description = "根据客服提供给用户的优惠码， 从/payment/subscription/coupon接口查询stripe coupon code id")
    String stripeCouponId;

    // 降级到低版本后门,允许 MIS/开发 调接口降级到低版本
    // 目前用在 BD plan 降到普通 plan
    Boolean allowCrossVersionDowngrade;
}
