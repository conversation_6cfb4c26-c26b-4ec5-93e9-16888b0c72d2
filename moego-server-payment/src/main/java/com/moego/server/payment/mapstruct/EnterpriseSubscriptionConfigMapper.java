package com.moego.server.payment.mapstruct;

import com.moego.server.payment.dto.EnterpriseSubscriptionConfigDTO;
import com.moego.server.payment.mapperbean.MoeEnterpriseSubscriptionConfig;
import com.moego.server.payment.params.CreateEnterpriseSubscriptionConfigParams;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface EnterpriseSubscriptionConfigMapper {
    EnterpriseSubscriptionConfigMapper INSTANCE = Mappers.getMapper(EnterpriseSubscriptionConfigMapper.class);

    EnterpriseSubscriptionConfigDTO toEnterpriseSubscriptionConfigDTO(MoeEnterpriseSubscriptionConfig input);

    MoeEnterpriseSubscriptionConfig toMoeEnterpriseSubscriptionConfig(CreateEnterpriseSubscriptionConfigParams input);
}
