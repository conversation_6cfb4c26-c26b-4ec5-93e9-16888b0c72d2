package com.moego.server.payment.service;

import com.moego.common.response.ResponseResult;
import com.moego.server.payment.dto.TestDTO;
import com.moego.server.payment.params.TestParams;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-06-07 12:48
 */
@Service
public class TestService {

    public ResponseResult<TestDTO> test(TestParams testParams) {
        TestDTO testDto = new TestDTO();
        testDto.setId(1);
        testDto.setName("paymentTest");
        testDto.setTest("test api desc");
        return ResponseResult.success(testDto);
    }
}
