package com.moego.server.payment.web.vo;

import com.moego.server.payment.dto.CanRefundChannel;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class SubmitInvoiceRefundVo {

    @NotNull
    private Integer invoiceId;

    @NotNull
    @Valid
    List<CanRefundChannel> refunds;

    private String refundReason;

    @NotNull
    private BigDecimal refundAmount;
}
