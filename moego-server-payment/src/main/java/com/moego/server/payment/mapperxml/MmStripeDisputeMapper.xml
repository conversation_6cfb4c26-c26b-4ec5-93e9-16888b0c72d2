<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.payment.mapper.MmStripeDisputeMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.payment.mapperbean.MmStripeDispute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="dispute_id" jdbcType="VARCHAR" property="disputeId" />
    <result column="payment_id" jdbcType="INTEGER" property="paymentId" />
    <result column="payment_intent_id" jdbcType="VARCHAR" property="paymentIntentId" />
    <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="customer" jdbcType="VARCHAR" property="customer" />
    <result column="charged_on" jdbcType="BIGINT" property="chargedOn" />
    <result column="disputed_on" jdbcType="BIGINT" property="disputedOn" />
    <result column="responded_on" jdbcType="BIGINT" property="respondedOn" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
    <result column="is_read" jdbcType="BIT" property="isRead" />
    <result column="read_status" jdbcType="BIT" property="readStatus" />
    <result column="fund_withdraw_status" jdbcType="VARCHAR" property="fundWithdrawStatus" />
    <result column="last_handled_time" jdbcType="BIGINT" property="lastHandledTime" />
    <result column="dispute_fee" jdbcType="BIGINT" property="disputeFee" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, dispute_id, payment_id, payment_intent_id, payment_method, amount,
    currency, status, reason, customer, charged_on, disputed_on, responded_on, created_at,
    updated_at, deleted_at, is_read, read_status, fund_withdraw_status, last_handled_time,
    dispute_fee
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from mm_stripe_dispute
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from mm_stripe_dispute
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.payment.mapperbean.MmStripeDispute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mm_stripe_dispute (business_id, dispute_id, payment_id,
      payment_intent_id, payment_method, amount,
      currency, status, reason,
      customer, charged_on, disputed_on,
      responded_on, created_at, updated_at,
      deleted_at, is_read, read_status,
      fund_withdraw_status, last_handled_time, dispute_fee
      )
    values (#{businessId,jdbcType=INTEGER}, #{disputeId,jdbcType=VARCHAR}, #{paymentId,jdbcType=INTEGER},
      #{paymentIntentId,jdbcType=VARCHAR}, #{paymentMethod,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT},
      #{currency,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR},
      #{customer,jdbcType=VARCHAR}, #{chargedOn,jdbcType=BIGINT}, #{disputedOn,jdbcType=BIGINT},
      #{respondedOn,jdbcType=BIGINT}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP},
      #{deletedAt,jdbcType=TIMESTAMP}, #{isRead,jdbcType=BIT}, #{readStatus,jdbcType=BIT},
      #{fundWithdrawStatus,jdbcType=VARCHAR}, #{lastHandledTime,jdbcType=BIGINT}, #{disputeFee,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.payment.mapperbean.MmStripeDispute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mm_stripe_dispute
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="disputeId != null">
        dispute_id,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="paymentIntentId != null">
        payment_intent_id,
      </if>
      <if test="paymentMethod != null">
        payment_method,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="customer != null">
        customer,
      </if>
      <if test="chargedOn != null">
        charged_on,
      </if>
      <if test="disputedOn != null">
        disputed_on,
      </if>
      <if test="respondedOn != null">
        responded_on,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="deletedAt != null">
        deleted_at,
      </if>
      <if test="isRead != null">
        is_read,
      </if>
      <if test="readStatus != null">
        read_status,
      </if>
      <if test="fundWithdrawStatus != null">
        fund_withdraw_status,
      </if>
      <if test="lastHandledTime != null">
        last_handled_time,
      </if>
      <if test="disputeFee != null">
        dispute_fee,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="disputeId != null">
        #{disputeId,jdbcType=VARCHAR},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=INTEGER},
      </if>
      <if test="paymentIntentId != null">
        #{paymentIntentId,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethod != null">
        #{paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="customer != null">
        #{customer,jdbcType=VARCHAR},
      </if>
      <if test="chargedOn != null">
        #{chargedOn,jdbcType=BIGINT},
      </if>
      <if test="disputedOn != null">
        #{disputedOn,jdbcType=BIGINT},
      </if>
      <if test="respondedOn != null">
        #{respondedOn,jdbcType=BIGINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedAt != null">
        #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isRead != null">
        #{isRead,jdbcType=BIT},
      </if>
      <if test="readStatus != null">
        #{readStatus,jdbcType=BIT},
      </if>
      <if test="fundWithdrawStatus != null">
        #{fundWithdrawStatus,jdbcType=VARCHAR},
      </if>
      <if test="lastHandledTime != null">
        #{lastHandledTime,jdbcType=BIGINT},
      </if>
      <if test="disputeFee != null">
        #{disputeFee,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mm_stripe_dispute
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.disputeId != null">
        dispute_id = #{record.disputeId,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentId != null">
        payment_id = #{record.paymentId,jdbcType=INTEGER},
      </if>
      <if test="record.paymentIntentId != null">
        payment_intent_id = #{record.paymentIntentId,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentMethod != null">
        payment_method = #{record.paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.customer != null">
        customer = #{record.customer,jdbcType=VARCHAR},
      </if>
      <if test="record.chargedOn != null">
        charged_on = #{record.chargedOn,jdbcType=BIGINT},
      </if>
      <if test="record.disputedOn != null">
        disputed_on = #{record.disputedOn,jdbcType=BIGINT},
      </if>
      <if test="record.respondedOn != null">
        responded_on = #{record.respondedOn,jdbcType=BIGINT},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deletedAt != null">
        deleted_at = #{record.deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isRead != null">
        is_read = #{record.isRead,jdbcType=BIT},
      </if>
      <if test="record.readStatus != null">
        read_status = #{record.readStatus,jdbcType=BIT},
      </if>
      <if test="record.fundWithdrawStatus != null">
        fund_withdraw_status = #{record.fundWithdrawStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.lastHandledTime != null">
        last_handled_time = #{record.lastHandledTime,jdbcType=BIGINT},
      </if>
      <if test="record.disputeFee != null">
        dispute_fee = #{record.disputeFee,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mm_stripe_dispute
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      dispute_id = #{record.disputeId,jdbcType=VARCHAR},
      payment_id = #{record.paymentId,jdbcType=INTEGER},
      payment_intent_id = #{record.paymentIntentId,jdbcType=VARCHAR},
      payment_method = #{record.paymentMethod,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      currency = #{record.currency,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      customer = #{record.customer,jdbcType=VARCHAR},
      charged_on = #{record.chargedOn,jdbcType=BIGINT},
      disputed_on = #{record.disputedOn,jdbcType=BIGINT},
      responded_on = #{record.respondedOn,jdbcType=BIGINT},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      deleted_at = #{record.deletedAt,jdbcType=TIMESTAMP},
      is_read = #{record.isRead,jdbcType=BIT},
      read_status = #{record.readStatus,jdbcType=BIT},
      fund_withdraw_status = #{record.fundWithdrawStatus,jdbcType=VARCHAR},
      last_handled_time = #{record.lastHandledTime,jdbcType=BIGINT},
      dispute_fee = #{record.disputeFee,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.payment.mapperbean.MmStripeDispute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mm_stripe_dispute
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="disputeId != null">
        dispute_id = #{disputeId,jdbcType=VARCHAR},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=INTEGER},
      </if>
      <if test="paymentIntentId != null">
        payment_intent_id = #{paymentIntentId,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethod != null">
        payment_method = #{paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="customer != null">
        customer = #{customer,jdbcType=VARCHAR},
      </if>
      <if test="chargedOn != null">
        charged_on = #{chargedOn,jdbcType=BIGINT},
      </if>
      <if test="disputedOn != null">
        disputed_on = #{disputedOn,jdbcType=BIGINT},
      </if>
      <if test="respondedOn != null">
        responded_on = #{respondedOn,jdbcType=BIGINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="deletedAt != null">
        deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isRead != null">
        is_read = #{isRead,jdbcType=BIT},
      </if>
      <if test="readStatus != null">
        read_status = #{readStatus,jdbcType=BIT},
      </if>
      <if test="fundWithdrawStatus != null">
        fund_withdraw_status = #{fundWithdrawStatus,jdbcType=VARCHAR},
      </if>
      <if test="lastHandledTime != null">
        last_handled_time = #{lastHandledTime,jdbcType=BIGINT},
      </if>
      <if test="disputeFee != null">
        dispute_fee = #{disputeFee,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.payment.mapperbean.MmStripeDispute">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update mm_stripe_dispute
    set business_id = #{businessId,jdbcType=INTEGER},
      dispute_id = #{disputeId,jdbcType=VARCHAR},
      payment_id = #{paymentId,jdbcType=INTEGER},
      payment_intent_id = #{paymentIntentId,jdbcType=VARCHAR},
      payment_method = #{paymentMethod,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      currency = #{currency,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      customer = #{customer,jdbcType=VARCHAR},
      charged_on = #{chargedOn,jdbcType=BIGINT},
      disputed_on = #{disputedOn,jdbcType=BIGINT},
      responded_on = #{respondedOn,jdbcType=BIGINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      is_read = #{isRead,jdbcType=BIT},
      read_status = #{readStatus,jdbcType=BIT},
      fund_withdraw_status = #{fundWithdrawStatus,jdbcType=VARCHAR},
      last_handled_time = #{lastHandledTime,jdbcType=BIGINT},
      dispute_fee = #{disputeFee,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateReadStatusByBizId">
    update mm_stripe_dispute
    set read_status=true
    where business_id = #{businessId}
  </update>


  <select id="queryStripeDisputeList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_stripe_dispute
    <where>
      <if test="params.businessId != null">
        and business_id = #{params.businessId, jdbcType=INTEGER}
      </if>
      <if test="params.customer != null">
        and customer like CONCAT('%',#{params.customer, jdbcType=VARCHAR},'%')
      </if>
      <if test="params.dateStart != null">
        and DATE(FROM_UNIXTIME(disputed_on)) &gt;= DATE(FROM_UNIXTIME(#{params.dateStart, jdbcType=BIGINT}))
      </if>
      <if test="params.dateEnd != null">
        and DATE(FROM_UNIXTIME(disputed_on)) &lt;= DATE(FROM_UNIXTIME(#{params.dateEnd, jdbcType=BIGINT}))
      </if>
      <if test="params.status != null and params.status.size() != 0">
        and `status` in
        <foreach close=")" collection="params.status" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="params.reason != null">
        and `reason` = #{params.reason, jdbcType=VARCHAR}
      </if>
    </where>
      order by `disputed_on`
      <if test="params.orderByDesc != null and params.orderByDesc == 1">
        desc
      </if>
    limit #{offset, jdbcType=INTEGER}, #{limit, jdbcType=INTEGER}
  </select>

  <select id="countByParams" resultType="java.lang.Integer">
      select count(1) from mm_stripe_dispute
      <where>
          <if test="businessId != null">
              business_id = #{businessId, jdbcType=INTEGER}
          </if>
          <if test="customer != null">
              and customer like CONCAT('%', #{customer, jdbcType=VARCHAR},'%')
          </if>
          <if test="dateStart != null">
              and DATE(FROM_UNIXTIME(disputed_on)) &gt;= DATE(FROM_UNIXTIME(#{dateStart, jdbcType=BIGINT}))
          </if>
          <if test="dateEnd != null">
              and DATE(FROM_UNIXTIME(disputed_on)) &lt;= DATE(FROM_UNIXTIME(#{dateEnd, jdbcType=BIGINT}))
          </if>
        <if test="status != null and status.size() != 0">
          and `status` in
          <foreach close=")" collection="status" item="item" open="(" separator=",">
            #{item}
          </foreach>
        </if>
          <if test="reason != null">
              and `reason` = #{reason, jdbcType=VARCHAR}
          </if>
      </where>
  </select>
  <select id="queryById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_stripe_dispute
    where id = #{id} and deleted_at is null
  </select>
  <select id="queryAllByLimit" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_stripe_dispute
    <where>
      <if test="stripeDispute.id != null">
        and id = #{stripeDispute.id}
      </if>
      <if test="stripeDispute.amount != null">
        and amount = #{stripeDispute.amount}
      </if>
      <if test="stripeDispute.businessId != null">
        and business_id = #{stripeDispute.businessId}
      </if>
      <if test="stripeDispute.disputeId != null">
        and dispute_id = #{stripeDispute.disputeId}
      </if>
      <if test="stripeDispute.paymentId != null">
        and payment_id = #{stripeDispute.paymentId}
      </if>
      <if test="stripeDispute.paymentIntentId != null">
        and payment_intent_id = #{stripeDispute.paymentIntentId}
      </if>
      <if test="stripeDispute.paymentMethod != null">
        and payment_method = #{stripeDispute.paymentMethod}
      </if>
      <if test="stripeDispute.currency != null">
        and currency = #{stripeDispute.currency}
      </if>
      <if test="stripeDispute.status != null">
        and status = #{stripeDispute.status}
      </if>
      <if test="stripeDispute.reason != null">
        and reason = #{stripeDispute.reason}
      </if>
      <if test="stripeDispute.customer != null">
        and customer = #{stripeDispute.customer}
      </if>
      <if test="stripeDispute.chargedOn != null">
        and charged_on = #{stripeDispute.chargedOn}
      </if>
      <if test="stripeDispute.disputedOn != null">
        and disputed_on = #{stripeDispute.disputedOn}
      </if>
      <if test="stripeDispute.respondedOn != null">
        and responded_on = #{stripeDispute.respondedOn}
      </if>
      <if test="stripeDispute.createdAt != null">
        and created_at = #{stripeDispute.createdAt}
      </if>
      <if test="stripeDispute.updatedAt != null">
        and updated_at = #{stripeDispute.updatedAt}
      </if>
      and deleted_at is null
    </where>
    order by id desc
    limit #{pageable.offset}, #{pageable.pageSize}
  </select>

  <select id="count" resultType="java.lang.Long">
    select count(*)
    from mm_stripe_dispute
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="businessId != null">
        and business_id = #{businessId}
      </if>
      <if test="disputeId != null">
        and dispute_id = #{disputeId}
      </if>
      <if test="paymentId != null">
        and payment_id = #{paymentId}
      </if>
      <if test="paymentIntentId != null">
        and payment_intent_id = #{paymentIntentId}
      </if>
      <if test="paymentMethod != null">
        and payment_method = #{paymentMethod}
      </if>
      <if test="amount != null">
        and amount = #{amount}
      </if>
      <if test="currency != null">
        and currency = #{currency}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="reason != null">
        and reason = #{reason}
      </if>
      <if test="customer != null">
        and customer = #{customer}
      </if>
      <if test="chargedOn != null">
        and charged_on = #{chargedOn}
      </if>
      <if test="disputedOn != null">
        and disputed_on = #{disputedOn}
      </if>
      <if test="respondedOn != null">
        and responded_on = #{respondedOn}
      </if>
      <if test="createdAt != null">
        and created_at = #{createdAt}
      </if>
      <if test="updatedAt != null">
        and updated_at = #{updatedAt}
      </if>
      and deleted_at is null
    </where>
  </select>

  <select id="queryByDisputeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_stripe_dispute
    where dispute_id = #{disputeId} and deleted_at is null
  </select>

  <select id="selectByDisputeIdForUpdateNoWait" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_stripe_dispute
    where dispute_id = #{disputeId} and deleted_at is null
    for update nowait
  </select>

  <select id="queryByIntentId" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM mm_stripe_dispute
    WHERE business_id = #{businessId}
    AND payment_intent_id = #{intentId}
  </select>
  <select id="countReadStatusByBizId" resultType="java.lang.Integer">
    select count(*)
    from mm_stripe_dispute
    where business_id = #{businessId} and read_status = false
  </select>
</mapper>
