<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.payment.mapper.MoeCompanyFeatureRelationMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.payment.mapperbean.MoeCompanyFeatureRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="allow_type" jdbcType="TINYINT" property="allowType" />
    <result column="enable" jdbcType="TINYINT" property="enable" />
    <result column="quota" jdbcType="INTEGER" property="quota" />
    <result column="expiration_time" jdbcType="BIGINT" property="expirationTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.payment.mapperbean.MoeCompanyFeatureRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="note" jdbcType="LONGVARCHAR" property="note" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, code, allow_type, enable, quota, expiration_time, create_time, update_time,
    is_deleted
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    note
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_company_feature_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_company_feature_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.payment.mapperbean.MoeCompanyFeatureRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_company_feature_relation (company_id, code, allow_type,
      enable, quota, expiration_time,
      create_time, update_time, is_deleted,
      note)
    values (#{companyId,jdbcType=INTEGER}, #{code,jdbcType=VARCHAR}, #{allowType,jdbcType=TINYINT},
      #{enable,jdbcType=TINYINT}, #{quota,jdbcType=INTEGER}, #{expirationTime,jdbcType=BIGINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT},
      #{note,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.payment.mapperbean.MoeCompanyFeatureRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_company_feature_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="allowType != null">
        allow_type,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="quota != null">
        quota,
      </if>
      <if test="expirationTime != null">
        expiration_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="note != null">
        note,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="allowType != null">
        #{allowType,jdbcType=TINYINT},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=TINYINT},
      </if>
      <if test="quota != null">
        #{quota,jdbcType=INTEGER},
      </if>
      <if test="expirationTime != null">
        #{expirationTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="note != null">
        #{note,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.payment.mapperbean.MoeCompanyFeatureRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_company_feature_relation
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="allowType != null">
        allow_type = #{allowType,jdbcType=TINYINT},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=TINYINT},
      </if>
      <if test="quota != null">
        quota = #{quota,jdbcType=INTEGER},
      </if>
      <if test="expirationTime != null">
        expiration_time = #{expirationTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.payment.mapperbean.MoeCompanyFeatureRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_company_feature_relation
    set company_id = #{companyId,jdbcType=INTEGER},
      code = #{code,jdbcType=VARCHAR},
      allow_type = #{allowType,jdbcType=TINYINT},
      enable = #{enable,jdbcType=TINYINT},
      quota = #{quota,jdbcType=INTEGER},
      expiration_time = #{expirationTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      note = #{note,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.payment.mapperbean.MoeCompanyFeatureRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_company_feature_relation
    set company_id = #{companyId,jdbcType=INTEGER},
      code = #{code,jdbcType=VARCHAR},
      allow_type = #{allowType,jdbcType=TINYINT},
      enable = #{enable,jdbcType=TINYINT},
      quota = #{quota,jdbcType=INTEGER},
      expiration_time = #{expirationTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByCompanyId" resultType="com.moego.server.payment.dto.feature.FeatureRelation">
    select
    id,code,enable,quota,allow_type as allowType
    from moe_company_feature_relation
    where
    company_id = #{companyId}
    and is_deleted = 0
    and ( expiration_time = 0 or expiration_time &gt; unix_timestamp(now()) )
  </select>

  <select id="selectCount" resultType="java.lang.Integer">
    select
        count(1)
    from moe_company_feature_relation as mcfr
    where
        is_deleted = 0
    <if test="enable != null">
      and enable = #{enable,jdbcType=TINYINT}
    </if>
    <if test="code != null">
      and code = #{code, jdbcType=INTEGER}
    </if>
    <if test="companyId != null">
      and company_id = #{companyId, jdbcType=INTEGER}
    </if>
  </select>

  <select id="selectByPage"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from moe_company_feature_relation as mcfr
    where 1 = 1
    <if test="params.deleted != null">
      and is_deleted = #{params.deleted}
    </if>
    <if test="params.enable != null">
      and enable = #{params.enable,jdbcType=TINYINT}
    </if>
    <if test="params.code != null">
      and code = #{params.code, jdbcType=INTEGER}
    </if>
    <if test="params.companyId != null">
      and company_id = #{params.companyId, jdbcType=INTEGER}
    </if>
    order by id desc
    limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
  </select>

  <delete id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    update moe_company_feature_relation
    set is_deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </delete>

</mapper>
