package com.moego.server.payment.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table business_square
 */
public class BusinessSquare {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   商家使用square的默认location， take payment需要
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.default_location_id
     *
     * @mbg.generated
     */
    private String defaultLocationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.access_token
     *
     * @mbg.generated
     */
    private String accessToken;

    /**
     * Database Column Remarks:
     *   access_token的失效时间, e.g. 2006-01-02T15:04:05Z
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.expires_at
     *
     * @mbg.generated
     */
    private String expiresAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.refresh_token
     *
     * @mbg.generated
     */
    private String refreshToken;

    /**
     * Database Column Remarks:
     *   square seller merchant_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.merchant_id
     *
     * @mbg.generated
     */
    private String merchantId;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   1 normal  2 deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column business_square.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.id
     *
     * @return the value of business_square.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.id
     *
     * @param id the value for business_square.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.business_id
     *
     * @return the value of business_square.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.business_id
     *
     * @param businessId the value for business_square.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.default_location_id
     *
     * @return the value of business_square.default_location_id
     *
     * @mbg.generated
     */
    public String getDefaultLocationId() {
        return defaultLocationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.default_location_id
     *
     * @param defaultLocationId the value for business_square.default_location_id
     *
     * @mbg.generated
     */
    public void setDefaultLocationId(String defaultLocationId) {
        this.defaultLocationId = defaultLocationId == null ? null : defaultLocationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.access_token
     *
     * @return the value of business_square.access_token
     *
     * @mbg.generated
     */
    public String getAccessToken() {
        return accessToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.access_token
     *
     * @param accessToken the value for business_square.access_token
     *
     * @mbg.generated
     */
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken == null ? null : accessToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.expires_at
     *
     * @return the value of business_square.expires_at
     *
     * @mbg.generated
     */
    public String getExpiresAt() {
        return expiresAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.expires_at
     *
     * @param expiresAt the value for business_square.expires_at
     *
     * @mbg.generated
     */
    public void setExpiresAt(String expiresAt) {
        this.expiresAt = expiresAt == null ? null : expiresAt.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.refresh_token
     *
     * @return the value of business_square.refresh_token
     *
     * @mbg.generated
     */
    public String getRefreshToken() {
        return refreshToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.refresh_token
     *
     * @param refreshToken the value for business_square.refresh_token
     *
     * @mbg.generated
     */
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken == null ? null : refreshToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.merchant_id
     *
     * @return the value of business_square.merchant_id
     *
     * @mbg.generated
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.merchant_id
     *
     * @param merchantId the value for business_square.merchant_id
     *
     * @mbg.generated
     */
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId == null ? null : merchantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.create_time
     *
     * @return the value of business_square.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.create_time
     *
     * @param createTime the value for business_square.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.update_time
     *
     * @return the value of business_square.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.update_time
     *
     * @param updateTime the value for business_square.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.status
     *
     * @return the value of business_square.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.status
     *
     * @param status the value for business_square.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column business_square.company_id
     *
     * @return the value of business_square.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column business_square.company_id
     *
     * @param companyId the value for business_square.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
