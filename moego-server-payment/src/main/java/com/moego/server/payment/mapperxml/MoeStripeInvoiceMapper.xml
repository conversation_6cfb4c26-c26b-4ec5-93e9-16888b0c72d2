<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.payment.mapper.MoeStripeInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.payment.mapperbean.MoeStripeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_id" jdbcType="VARCHAR" property="invoiceId" />
    <result column="customer_id" jdbcType="VARCHAR" property="customerId" />
    <result column="subscription_id" jdbcType="VARCHAR" property="subscriptionId" />
    <result column="subtotal" jdbcType="DECIMAL" property="subtotal" />
    <result column="total" jdbcType="DECIMAL" property="total" />
    <result column="starting_balance" jdbcType="DECIMAL" property="startingBalance" />
    <result column="ending_balance" jdbcType="DECIMAL" property="endingBalance" />
    <result column="amount_paid" jdbcType="DECIMAL" property="amountPaid" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="paid_at" jdbcType="BIGINT" property="paidAt" />
    <result column="livemode" jdbcType="VARCHAR" property="livemode" />
    <result column="discount_coupon_id" jdbcType="VARCHAR" property="discountCouponId" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="billing_reason" jdbcType="TINYINT" property="billingReason" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, invoice_id, customer_id, subscription_id, subtotal, total, starting_balance, 
    ending_balance, amount_paid, currency, paid_at, livemode, discount_coupon_id, company_id, 
    status, billing_reason
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_stripe_company_subscription_invoice
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_stripe_company_subscription_invoice
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.payment.mapperbean.MoeStripeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_stripe_company_subscription_invoice (invoice_id, customer_id, subscription_id,
      subtotal, total, starting_balance, 
      ending_balance, amount_paid, currency, 
      paid_at, livemode, discount_coupon_id, 
      company_id, status, billing_reason
      )
    values (#{invoiceId,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR}, #{subscriptionId,jdbcType=VARCHAR}, 
      #{subtotal,jdbcType=DECIMAL}, #{total,jdbcType=DECIMAL}, #{startingBalance,jdbcType=DECIMAL}, 
      #{endingBalance,jdbcType=DECIMAL}, #{amountPaid,jdbcType=DECIMAL}, #{currency,jdbcType=VARCHAR}, 
      #{paidAt,jdbcType=BIGINT}, #{livemode,jdbcType=VARCHAR}, #{discountCouponId,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{billingReason,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.payment.mapperbean.MoeStripeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_stripe_company_subscription_invoice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        invoice_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="subscriptionId != null">
        subscription_id,
      </if>
      <if test="subtotal != null">
        subtotal,
      </if>
      <if test="total != null">
        total,
      </if>
      <if test="startingBalance != null">
        starting_balance,
      </if>
      <if test="endingBalance != null">
        ending_balance,
      </if>
      <if test="amountPaid != null">
        amount_paid,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="paidAt != null">
        paid_at,
      </if>
      <if test="livemode != null">
        livemode,
      </if>
      <if test="discountCouponId != null">
        discount_coupon_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="billingReason != null">
        billing_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="subscriptionId != null">
        #{subscriptionId,jdbcType=VARCHAR},
      </if>
      <if test="subtotal != null">
        #{subtotal,jdbcType=DECIMAL},
      </if>
      <if test="total != null">
        #{total,jdbcType=DECIMAL},
      </if>
      <if test="startingBalance != null">
        #{startingBalance,jdbcType=DECIMAL},
      </if>
      <if test="endingBalance != null">
        #{endingBalance,jdbcType=DECIMAL},
      </if>
      <if test="amountPaid != null">
        #{amountPaid,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="paidAt != null">
        #{paidAt,jdbcType=BIGINT},
      </if>
      <if test="livemode != null">
        #{livemode,jdbcType=VARCHAR},
      </if>
      <if test="discountCouponId != null">
        #{discountCouponId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="billingReason != null">
        #{billingReason,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.payment.mapperbean.MoeStripeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_stripe_company_subscription_invoice
    <set>
      <if test="invoiceId != null">
        invoice_id = #{invoiceId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="subscriptionId != null">
        subscription_id = #{subscriptionId,jdbcType=VARCHAR},
      </if>
      <if test="subtotal != null">
        subtotal = #{subtotal,jdbcType=DECIMAL},
      </if>
      <if test="total != null">
        total = #{total,jdbcType=DECIMAL},
      </if>
      <if test="startingBalance != null">
        starting_balance = #{startingBalance,jdbcType=DECIMAL},
      </if>
      <if test="endingBalance != null">
        ending_balance = #{endingBalance,jdbcType=DECIMAL},
      </if>
      <if test="amountPaid != null">
        amount_paid = #{amountPaid,jdbcType=DECIMAL},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="paidAt != null">
        paid_at = #{paidAt,jdbcType=BIGINT},
      </if>
      <if test="livemode != null">
        livemode = #{livemode,jdbcType=VARCHAR},
      </if>
      <if test="discountCouponId != null">
        discount_coupon_id = #{discountCouponId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="billingReason != null">
        billing_reason = #{billingReason,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.payment.mapperbean.MoeStripeInvoice">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_stripe_company_subscription_invoice
    set invoice_id = #{invoiceId,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=VARCHAR},
      subscription_id = #{subscriptionId,jdbcType=VARCHAR},
      subtotal = #{subtotal,jdbcType=DECIMAL},
      total = #{total,jdbcType=DECIMAL},
      starting_balance = #{startingBalance,jdbcType=DECIMAL},
      ending_balance = #{endingBalance,jdbcType=DECIMAL},
      amount_paid = #{amountPaid,jdbcType=DECIMAL},
      currency = #{currency,jdbcType=VARCHAR},
      paid_at = #{paidAt,jdbcType=BIGINT},
      livemode = #{livemode,jdbcType=VARCHAR},
      discount_coupon_id = #{discountCouponId,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      billing_reason = #{billingReason,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <resultMap id="StripeInvoiceCountMap" type="com.moego.server.payment.dto.StripeInvoiceCountDTO">
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="charge_count" jdbcType="INTEGER" property="chargeCount" />
  </resultMap>

  <update id="updateStatusByInvoiceId">
    update moe_stripe_company_subscription_invoice
    set status = #{status,jdbcType=TINYINT}
    where invoice_id = #{invoiceId,jdbcType=VARCHAR}
  </update>

  <select id="getPaidGteCountByCompanyIdList" parameterType="int" resultType="int">
    select company_id from (
      select
        company_id,
        count(*) count
      from
        moe_stripe_company_subscription_invoice
      where
        company_id in
        <foreach close=")" collection="list" item="companyId" open="(" separator=",">
            #{companyId,jdbcType=INTEGER}
        </foreach>
        and status = 1
        and billing_reason in
        <foreach collection="@com.moego.common.enums.InvoiceBillingReasonEnum@getValidSubscriptionReason()" item="billingReason" open="(" close=")" separator=",">
            #{billingReason,jdbcType=TINYINT}
        </foreach>
      group by
        company_id
    ) t where count &gt;= #{invoicePaidCount,jdbcType=INTEGER}
  </select>

  <select id="countPaidInvoiceByCompanyIdList" parameterType="int" resultMap="StripeInvoiceCountMap">
    select
    company_id,
    count(*) charge_count
    from
    moe_stripe_company_subscription_invoice
    where
        company_id in
        <foreach close=")" collection="list" item="companyId" open="(" separator=",">
            #{companyId,jdbcType=INTEGER}
        </foreach>
        and status = 1
        and billing_reason in
        <foreach collection="@com.moego.common.enums.InvoiceBillingReasonEnum@getValidSubscriptionReason()" item="billingReason" open="(" close=")" separator=",">
            #{billingReason,jdbcType=TINYINT}
        </foreach>
    group by
    company_id
  </select>
</mapper>