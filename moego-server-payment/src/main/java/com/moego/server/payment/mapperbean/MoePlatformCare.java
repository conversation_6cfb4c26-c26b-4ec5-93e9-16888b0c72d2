package com.moego.server.payment.mapperbean;

import java.util.Date;

/**
 * Database Table Remarks:
 *   platform care table
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_platform_care
 */
public class MoePlatformCare {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   link code
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.code
     *
     * @mbg.generated
     */
    private String code;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.agreement_id
     *
     * @mbg.generated
     */
    private Long agreementId;

    /**
     * Database Column Remarks:
     *   agreement record uuid
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.agreement_record_uuid
     *
     * @mbg.generated
     */
    private String agreementRecordUuid;

    /**
     * Database Column Remarks:
     *   account
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.account_id
     *
     * @mbg.generated
     */
    private Long accountId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   email associated with the account
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.email
     *
     * @mbg.generated
     */
    private String email;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.discount_code
     *
     * @mbg.generated
     */
    private String discountCode;

    /**
     * Database Column Remarks:
     *   status, 0: not opened link, 1: open link but not signed, 2: signed but not ordered, 3: Order paid
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.order_shipping_status
     *
     * @mbg.generated
     */
    private String orderShippingStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.signed_time
     *
     * @mbg.generated
     */
    private Date signedTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   0 normal, 1 deleted
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.is_deleted
     *
     * @mbg.generated
     */
    private Byte isDeleted;

    /**
     * Database Column Remarks:
     *   是否显示accounting入口 0:不显示 1:显示
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_platform_care.show_accounting
     *
     * @mbg.generated
     */
    private Byte showAccounting;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.id
     *
     * @return the value of moe_platform_care.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.id
     *
     * @param id the value for moe_platform_care.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.code
     *
     * @return the value of moe_platform_care.code
     *
     * @mbg.generated
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.code
     *
     * @param code the value for moe_platform_care.code
     *
     * @mbg.generated
     */
    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.agreement_id
     *
     * @return the value of moe_platform_care.agreement_id
     *
     * @mbg.generated
     */
    public Long getAgreementId() {
        return agreementId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.agreement_id
     *
     * @param agreementId the value for moe_platform_care.agreement_id
     *
     * @mbg.generated
     */
    public void setAgreementId(Long agreementId) {
        this.agreementId = agreementId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.agreement_record_uuid
     *
     * @return the value of moe_platform_care.agreement_record_uuid
     *
     * @mbg.generated
     */
    public String getAgreementRecordUuid() {
        return agreementRecordUuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.agreement_record_uuid
     *
     * @param agreementRecordUuid the value for moe_platform_care.agreement_record_uuid
     *
     * @mbg.generated
     */
    public void setAgreementRecordUuid(String agreementRecordUuid) {
        this.agreementRecordUuid = agreementRecordUuid == null ? null : agreementRecordUuid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.account_id
     *
     * @return the value of moe_platform_care.account_id
     *
     * @mbg.generated
     */
    public Long getAccountId() {
        return accountId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.account_id
     *
     * @param accountId the value for moe_platform_care.account_id
     *
     * @mbg.generated
     */
    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.company_id
     *
     * @return the value of moe_platform_care.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.company_id
     *
     * @param companyId the value for moe_platform_care.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.email
     *
     * @return the value of moe_platform_care.email
     *
     * @mbg.generated
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.email
     *
     * @param email the value for moe_platform_care.email
     *
     * @mbg.generated
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.discount_code
     *
     * @return the value of moe_platform_care.discount_code
     *
     * @mbg.generated
     */
    public String getDiscountCode() {
        return discountCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.discount_code
     *
     * @param discountCode the value for moe_platform_care.discount_code
     *
     * @mbg.generated
     */
    public void setDiscountCode(String discountCode) {
        this.discountCode = discountCode == null ? null : discountCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.status
     *
     * @return the value of moe_platform_care.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.status
     *
     * @param status the value for moe_platform_care.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.order_shipping_status
     *
     * @return the value of moe_platform_care.order_shipping_status
     *
     * @mbg.generated
     */
    public String getOrderShippingStatus() {
        return orderShippingStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.order_shipping_status
     *
     * @param orderShippingStatus the value for moe_platform_care.order_shipping_status
     *
     * @mbg.generated
     */
    public void setOrderShippingStatus(String orderShippingStatus) {
        this.orderShippingStatus = orderShippingStatus == null ? null : orderShippingStatus.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.signed_time
     *
     * @return the value of moe_platform_care.signed_time
     *
     * @mbg.generated
     */
    public Date getSignedTime() {
        return signedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.signed_time
     *
     * @param signedTime the value for moe_platform_care.signed_time
     *
     * @mbg.generated
     */
    public void setSignedTime(Date signedTime) {
        this.signedTime = signedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.create_time
     *
     * @return the value of moe_platform_care.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.create_time
     *
     * @param createTime the value for moe_platform_care.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.update_time
     *
     * @return the value of moe_platform_care.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.update_time
     *
     * @param updateTime the value for moe_platform_care.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.is_deleted
     *
     * @return the value of moe_platform_care.is_deleted
     *
     * @mbg.generated
     */
    public Byte getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.is_deleted
     *
     * @param isDeleted the value for moe_platform_care.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_platform_care.show_accounting
     *
     * @return the value of moe_platform_care.show_accounting
     *
     * @mbg.generated
     */
    public Byte getShowAccounting() {
        return showAccounting;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_platform_care.show_accounting
     *
     * @param showAccounting the value for moe_platform_care.show_accounting
     *
     * @mbg.generated
     */
    public void setShowAccounting(Byte showAccounting) {
        this.showAccounting = showAccounting;
    }
}
