<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.payment.mapper.MoeMessageRecordMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.payment.mapperbean.MoeMessageRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="message_status" jdbcType="INTEGER" property="messageStatus" />
    <result column="destination" jdbcType="VARCHAR" property="destination" />
    <result column="delay_seconds" jdbcType="BIGINT" property="delaySeconds" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.moego.server.payment.mapperbean.MoeMessageRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="body" jdbcType="LONGVARCHAR" property="body" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    message_id, message_status, destination, delay_seconds, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    body
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_message_record
    where message_id = #{messageId,jdbcType=VARCHAR}
  </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_message_record
    where message_id = #{messageId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.moego.server.payment.mapperbean.MoeMessageRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into moe_message_record (message_id, message_status, destination,
      delay_seconds, create_time, update_time,
      body)
    values (#{messageId,jdbcType=VARCHAR}, #{messageStatus,jdbcType=INTEGER}, #{destination,jdbcType=VARCHAR},
      #{delaySeconds,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{body,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.payment.mapperbean.MoeMessageRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into moe_message_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        message_id,
      </if>
      <if test="messageStatus != null">
        message_status,
      </if>
      <if test="destination != null">
        destination,
      </if>
      <if test="delaySeconds != null">
        delay_seconds,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="body != null">
        body,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="messageStatus != null">
        #{messageStatus,jdbcType=INTEGER},
      </if>
      <if test="destination != null">
        #{destination,jdbcType=VARCHAR},
      </if>
      <if test="delaySeconds != null">
        #{delaySeconds,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="body != null">
        #{body,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.payment.mapperbean.MoeMessageRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_message_record
    <set>
      <if test="messageStatus != null">
        message_status = #{messageStatus,jdbcType=INTEGER},
      </if>
      <if test="destination != null">
        destination = #{destination,jdbcType=VARCHAR},
      </if>
      <if test="delaySeconds != null">
        delay_seconds = #{delaySeconds,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="body != null">
        body = #{body,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where message_id = #{messageId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.moego.server.payment.mapperbean.MoeMessageRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_message_record
    set message_status = #{messageStatus,jdbcType=INTEGER},
      destination = #{destination,jdbcType=VARCHAR},
      delay_seconds = #{delaySeconds,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      body = #{body,jdbcType=LONGVARCHAR}
    where message_id = #{messageId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.payment.mapperbean.MoeMessageRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_message_record
    set message_status = #{messageStatus,jdbcType=INTEGER},
      destination = #{destination,jdbcType=VARCHAR},
      delay_seconds = #{delaySeconds,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where message_id = #{messageId,jdbcType=VARCHAR}
  </update>
  <select id="selectByStatus" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from moe_message_record
    where
        create_time >= #{time}
    and message_status = #{status}
  </select>
</mapper>
