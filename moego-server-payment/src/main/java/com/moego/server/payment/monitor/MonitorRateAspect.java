package com.moego.server.payment.monitor;

import java.lang.reflect.Method;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * @create: 2023/2/23 15:34
 * @author: channy.shu
 **/
@Aspect
@Slf4j
@Component
public class MonitorRateAspect {

    @Around("@annotation(com.moego.server.payment.monitor.MonitorRate)")
    public Object rateMethod(ProceedingJoinPoint pjp) throws Throwable {
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        method = pjp.getTarget().getClass().getMethod(method.getName(), method.getParameterTypes());
        MonitorRate mr = method.getAnnotation(MonitorRate.class);
        String metric = mr.metric();
        try {
            Object proceed = pjp.proceed();
            PrometheusMonitor.reportSuccessRate(metric, PrometheusMonitor.SUCCESS, "");
            return proceed;
        } catch (Exception ex) {
            PrometheusMonitor.reportSuccessRate(metric, PrometheusMonitor.FAIL, ex.getMessage());
            throw ex;
        }
    }
}
