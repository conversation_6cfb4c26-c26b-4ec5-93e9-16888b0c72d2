package com.moego.server.payment.service;

import com.moego.api.thirdparty.IPaymentSlackClient;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.account.v1.AccountServiceGrpc;
import com.moego.idl.service.account.v1.GetAccountRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.BusinessIdWithLevelDto;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.payment.constant.TimeConstant;
import com.moego.server.payment.dto.AdminCustomizedPaymentSettingDTO;
import com.moego.server.payment.dto.AdminCustomizedPaymentSettingView;
import com.moego.server.payment.dto.AdminEnterpriseCustomizedPaymentSettingDTO;
import com.moego.server.payment.dto.AdminEnterpriseCustomizedPaymentSettingView;
import com.moego.server.payment.mapper.MoeCustomizedPaymentSettingMapper;
import com.moego.server.payment.mapper.MoeCustomizedRatesVolCheckMapper;
import com.moego.server.payment.mapper.MoeEnterpriseCustomizedPaymentSettingMapper;
import com.moego.server.payment.mapper.PaymentMapper;
import com.moego.server.payment.mapper.po.StripeTransactionSummaryPo;
import com.moego.server.payment.mapperbean.MoeCustomizedPaymentSetting;
import com.moego.server.payment.mapperbean.MoeCustomizedPaymentSettingExample;
import com.moego.server.payment.mapperbean.MoeCustomizedRatesVolCheck;
import com.moego.server.payment.mapperbean.MoeEnterpriseCustomizedPaymentSetting;
import com.moego.server.payment.mapperbean.MoeEnterpriseCustomizedPaymentSettingExample;
import com.moego.server.payment.params.CompanyCustomFeeQueryParams;
import com.moego.server.payment.params.EnterpriseCustomFeeQueryParams;
import com.moego.server.payment.params.VolCheckMessageParam;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/10/31
 */
@Service
@Slf4j
public class CustomizedPaymentSettingService {

    @Autowired
    private MoeCustomizedPaymentSettingMapper moeCustomizedPaymentSettingMapper;

    @Autowired
    private MoeEnterpriseCustomizedPaymentSettingMapper moeEnterpriseCustomizedPaymentSettingMapper;

    @Autowired
    private MoeCustomizedRatesVolCheckMapper moeCustomizedRatesVolCheckMapper;

    @Autowired
    private IPaymentSlackClient paymentSlackClient;

    @Autowired
    private IBusinessBusinessClient businessBusinessClient;

    @Autowired
    private PayAccountService payAccountService;

    @Autowired
    private AccountServiceGrpc.AccountServiceBlockingStub accountServiceBlockingStub;

    @Autowired
    private PaymentMapper paymentMapper;

    public static final Integer DEFAULT_VOL_CHECK_DAYS = 30;

    public static final String VOL_AVG_CHECK_PREFIX = "THREE MONTHS VOL AVG: $";
    public static final String VOL_RECORD_COUNT_LESS_THAN_THREE = "RECENT VOL RECORD NOT ENOUGH: ";

    public static final Integer MIN_VOL_CHECK_MONTH_COUNT = 3;

    @Value("${spring.profiles.active:local}")
    private String env;

    public MoeCustomizedPaymentSetting getByCompanyId(Integer companyId) {
        return moeCustomizedPaymentSettingMapper.selectByCompanyId(companyId);
    }

    public MoeCustomizedPaymentSetting getById(Integer id) {
        return moeCustomizedPaymentSettingMapper.selectByPrimaryKey(id);
    }

    public int insert(MoeCustomizedPaymentSetting customizedPaymentSetting) {
        return moeCustomizedPaymentSettingMapper.insertSelective(customizedPaymentSetting);
    }

    public int update(MoeCustomizedPaymentSetting customizedPaymentSetting) {
        return moeCustomizedPaymentSettingMapper.updateByPrimaryKeySelective(customizedPaymentSetting);
    }

    public int delete(Set<Integer> ids) {
        return !CollectionUtils.isEmpty(ids) ? moeCustomizedPaymentSettingMapper.batchDelete(ids) : 0;
    }

    public int deleteCusPaySettingByCompanyIds(List<Integer> companyIds) {
        MoeCustomizedPaymentSettingExample example = new MoeCustomizedPaymentSettingExample();
        MoeCustomizedPaymentSettingExample.Criteria criteria = example.createCriteria();
        criteria.andCompanyIdIn(companyIds);
        return moeCustomizedPaymentSettingMapper.deleteByExample(example);
    }

    public List<Integer> getAllCustomizedCompanyId() {
        return moeCustomizedPaymentSettingMapper.getAllCustomizedCompanyIds();
    }

    public void dailyVolCheck() {
        // step1:查看是否有增量的定制费率商家
        prepareCompanyData();
        // step2:计算每个商家的stripe最近31天的交易额
        calculateVolSummary();
    }

    private void calculateVolSummary() {
        // example: 计算[4/19-5/19]闭区间的流水,表示 4/19:00:00~5/19:23.59,共31天。-1才能保证最后一天包含在内,不-1则是到5/19:00:00
        String yesterday =
                LocalDate.now().plusDays(-1).format(DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT));
        List<MoeCustomizedRatesVolCheck> all = moeCustomizedRatesVolCheckMapper.selectByEndDate(yesterday);
        if (CollectionUtils.isEmpty(all)) {
            return;
        }
        for (MoeCustomizedRatesVolCheck volCheck : all) {
            checkEachRecord(volCheck);
        }
    }

    private void checkEachRecord(MoeCustomizedRatesVolCheck volCheck) {
        MoeCustomizedRatesVolCheck update = new MoeCustomizedRatesVolCheck();
        update.setId(volCheck.getId());
        update.setCompanyId(volCheck.getCompanyId());
        update.setTaskTriggerStatus(true);
        try {
            MoeCustomizedPaymentSetting setting =
                    moeCustomizedPaymentSettingMapper.selectByCompanyId(volCheck.getCompanyId());
            // 已经不是定制费率商家了,不再检查
            if (setting == null) {
                update.setVolCheckStatus(true);
                update.setRemark("customized setting is null");
                moeCustomizedRatesVolCheckMapper.updateByPrimaryKeySelective(update);
                return;
            }
            long vol = calculateVolSummaryEachCompany(
                    volCheck.getCompanyId(), volCheck.getStartDate(), volCheck.getEndDate());
            update.setVolSummary(vol);
            update.setMinVolLimit(setting.getCustomizedMinVol());
            checkVolPassStatus(update, volCheck.getStartDate());
            int c = moeCustomizedRatesVolCheckMapper.updateByPrimaryKeySelective(update);
            log.info("check vol result update:{},{}", update, c);
            if (!update.getVolCheckStatus()) {
                notifySlack(volCheck.getId(), "");
            }
            genNextCheckRecord(volCheck);
        } catch (DuplicateKeyException sqlE) {
            log.info("DuplicateKeyException check,{}", volCheck, sqlE);
        } catch (Exception e) {
            log.error("calculateVolSummary error,{}", volCheck, e);
            notifySlack(volCheck.getId(), e.getMessage());
        }
    }

    private void checkVolPassStatus(MoeCustomizedRatesVolCheck update, String startDate) {
        if (update.getVolSummary() >= update.getMinVolLimit()) {
            update.setVolCheckStatus(true);
            return;
        }
        // 如果当前月份的交易量小于最小交易量,则需要检查前三个月的平均交易量是否大于最小交易量
        // 计算前两期
        LocalDate parse = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT));
        String prev1 = parse.plusDays(-1).format(DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT));
        String prev2 = parse.plusDays(-1 - DEFAULT_VOL_CHECK_DAYS - 1)
                .format(DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT));
        List<MoeCustomizedRatesVolCheck> recentRecord = moeCustomizedRatesVolCheckMapper.selectByCompanyIdAndEndDates(
                update.getCompanyId(), List.of(prev1, prev2));
        recentRecord.add(update);
        if (recentRecord.size() < MIN_VOL_CHECK_MONTH_COUNT) {
            update.setVolCheckStatus(false);
            update.setRemark(VOL_RECORD_COUNT_LESS_THAN_THREE + prev1 + "," + prev2);
            return;
        }
        long avg = recentRecord.stream()
                        .map(MoeCustomizedRatesVolCheck::getVolSummary)
                        .reduce(Long::sum)
                        .orElse(0L)
                / recentRecord.size();
        update.setVolCheckStatus(avg >= update.getMinVolLimit());
        update.setRemark(VOL_AVG_CHECK_PREFIX + avg);
    }

    private void notifySlack(Integer volCheckId, String exMsg) {
        MoeCustomizedRatesVolCheck volCheck = moeCustomizedRatesVolCheckMapper.selectByPrimaryKey(volCheckId);
        if (volCheck == null) {
            log.error("notify slack error,no vol check record found,volCheckId:{}", volCheckId);
            return;
        }
        try {
            CompanyDto company = businessBusinessClient.getCompanyById(volCheck.getCompanyId());
            int count = moeCustomizedRatesVolCheckMapper.selectFailedTimes(volCheck.getCompanyId());
            StringBuilder bizStr = new StringBuilder(System.lineSeparator()).append("New Vol Check Event:");
            bizStr.append(System.lineSeparator()).append("Env: ").append(env).append(System.lineSeparator());
            if (StringUtils.hasText(exMsg)) {
                bizStr.append("Exception Msg: ").append(exMsg).append(System.lineSeparator());
            }
            if (company != null) {
                bizStr.append("Company Name: ").append(company.getName()).append(System.lineSeparator());

                AccountModel account = accountServiceBlockingStub.getAccount(GetAccountRequest.newBuilder()
                        .setId(company.getAccountId())
                        .build());
                if (account != null) {
                    bizStr.append("Company Email: ").append(account.getEmail()).append(System.lineSeparator());
                }
            }
            bizStr.append("Company Id: ")
                    .append(volCheck.getCompanyId())
                    .append(System.lineSeparator())
                    .append("Vol Summary: $")
                    .append(volCheck.getVolSummary())
                    .append(System.lineSeparator())
                    .append("Vol Min Limit: $")
                    .append(volCheck.getMinVolLimit())
                    .append(System.lineSeparator())
                    .append("Vol Check Status: ")
                    .append(volCheck.getVolCheckStatus())
                    .append(System.lineSeparator())
                    .append("Vol Start Date: ")
                    .append(volCheck.getStartDate())
                    .append(System.lineSeparator())
                    .append("Vol End Date: ")
                    .append(volCheck.getEndDate())
                    .append(System.lineSeparator())
                    .append("Remark: ")
                    .append(volCheck.getRemark())
                    .append(System.lineSeparator())
                    .append("Number of Historical Check Not Pass: ")
                    .append(count)
                    .append(System.lineSeparator());

            paymentSlackClient.sendVolCheckMessage(VolCheckMessageParam.builder()
                    .monthlyCheckingResult(bizStr.toString())
                    .build());
        } catch (Exception e) {
            log.error("notifySlack error,{}", volCheck, e);
        }
    }

    private void genNextCheckRecord(MoeCustomizedRatesVolCheck volCheck) {
        MoeCustomizedRatesVolCheck newCheck = new MoeCustomizedRatesVolCheck();
        newCheck.setCompanyId(volCheck.getCompanyId());
        LocalDate startDate = LocalDate.parse(
                        volCheck.getEndDate(), DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT))
                .plusDays(1);
        newCheck.setStartDate(startDate.format(DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT)));
        newCheck.setEndDate(startDate
                .plusDays(DEFAULT_VOL_CHECK_DAYS)
                .format(DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT)));
        MoeCustomizedRatesVolCheck record = moeCustomizedRatesVolCheckMapper.selectByCompanyIdAndEndDate(
                newCheck.getCompanyId(), newCheck.getEndDate());
        // 防止重复执行插入
        if (record != null) {
            return;
        }
        moeCustomizedRatesVolCheckMapper.insertSelective(newCheck);
    }

    private long calculateVolSummaryEachCompany(Integer companyId, String startDate, String endDate) {
        // 31天闭区间的交易额
        long startTs = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT))
                        .atStartOfDay()
                        .atZone(ZoneId.of(TimeConstant.DEFAULT_TIME_ZONE))
                        .toInstant()
                        .toEpochMilli()
                / 1000;

        long endTs = LocalDate.parse(endDate, DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT))
                        .plusDays(1)
                        .atStartOfDay()
                        .atZone(ZoneId.of(TimeConstant.DEFAULT_TIME_ZONE))
                        .toInstant()
                        .toEpochMilli()
                / 1000;

        Map<Integer, BusinessIdWithLevelDto> infoMap =
                businessBusinessClient.queryBusinessIdByCompanyId(List.of(companyId));
        if (CollectionUtils.isEmpty(infoMap) || !infoMap.containsKey(companyId)) {
            log.error("{} does not have business", companyId);
            return 0;
        }
        List<Integer> businessIds = infoMap.get(companyId).getBusinessIds();
        double amount = 0L;
        for (Integer businessId : businessIds) {
            StripeTransactionSummaryPo summary = paymentMapper.getStripTransactionSummary(businessId, startTs, endTs);
            if (summary != null && summary.getAmount() != null) {
                amount += summary.getAmount();
            }
        }
        // 向上取整为整数美元
        return BigDecimal.valueOf(amount).setScale(0, RoundingMode.HALF_UP).longValue();
    }

    private void prepareCompanyData() {
        List<MoeCustomizedPaymentSetting> cms = moeCustomizedPaymentSettingMapper.getAllCustomizedCompany();
        if (CollectionUtils.isEmpty(cms)) {
            return;
        }
        for (MoeCustomizedPaymentSetting company : cms) {
            MoeCustomizedRatesVolCheck check =
                    moeCustomizedRatesVolCheckMapper.selectRecentRecord(company.getCompanyId());
            if (check == null) {
                check = buildNewVolCheck(company);
                log.info("add new vol check record:{}", check);
                moeCustomizedRatesVolCheckMapper.insertSelective(check);
            } else {
                LocalDate endDate = LocalDate.parse(
                        check.getEndDate(), DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT));
                // 这里说明中断过一端时间，需要重新生成一条记录
                if (company.getCreateTime()
                        .after(Date.from(endDate.plusDays(1)
                                .atStartOfDay()
                                .atZone(ZoneId.systemDefault())
                                .toInstant()))) {
                    check = buildNewVolCheck(company);
                    log.info("rebuild new vol check record:{}", check);
                    moeCustomizedRatesVolCheckMapper.insertSelective(check);
                }
            }
        }
    }

    private MoeCustomizedRatesVolCheck buildNewVolCheck(MoeCustomizedPaymentSetting setting) {
        LocalDate startDate = buildDate(setting.getCreateTime());
        MoeCustomizedRatesVolCheck task = new MoeCustomizedRatesVolCheck();
        task.setCompanyId(setting.getCompanyId());
        task.setStartDate(startDate.format(DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT)));
        task.setEndDate(startDate
                .plusDays(DEFAULT_VOL_CHECK_DAYS)
                .format(DateTimeFormatter.ofPattern(TimeConstant.DEFAULT_DATE_FORMAT)));
        return task;
    }

    // 历史记录转换成当期,不需要计算历史
    private LocalDate buildDate(Date createTime) {
        LocalDate now = LocalDate.now();
        LocalDate startDate =
                createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        while (startDate.plusDays(DEFAULT_VOL_CHECK_DAYS).isBefore(now)) {
            startDate = startDate.plusDays(DEFAULT_VOL_CHECK_DAYS).plusDays(1);
        }
        return startDate;
    }

    public void volCheckById(Integer id) {
        MoeCustomizedRatesVolCheck volCheck = moeCustomizedRatesVolCheckMapper.selectByPrimaryKey(id);
        if (volCheck == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        checkEachRecord(volCheck);
    }

    public AdminCustomizedPaymentSettingDTO queryByPages(CompanyCustomFeeQueryParams request) {
        List<AdminCustomizedPaymentSettingView> views = new ArrayList<>();
        AdminCustomizedPaymentSettingDTO result = AdminCustomizedPaymentSettingDTO.builder()
                .views(views)
                .total(moeCustomizedPaymentSettingMapper
                        .getAllCustomizedCompanyIds()
                        .size())
                .build();
        if (request.getCompanyId() != null) {
            MoeCustomizedPaymentSetting customizedPaymentSetting =
                    moeCustomizedPaymentSettingMapper.selectByCompanyId(request.getCompanyId());
            if (customizedPaymentSetting != null) {
                views.add(buildAdminCustomizedSettingView(customizedPaymentSetting));
            }
        } else {
            Pagination pagination = request.getPagination();
            if (pagination == null) {
                pagination = Pagination.DEFAULT;
            }
            List<MoeCustomizedPaymentSetting> customizedPaymentSettings =
                    moeCustomizedPaymentSettingMapper.selectByPages(
                            CommonUtil.getLimitOffset(pagination.pageNum(), pagination.pageSize()),
                            pagination.pageSize());
            views.addAll(customizedPaymentSettings.stream()
                    .map(CustomizedPaymentSettingService::buildAdminCustomizedSettingView)
                    .toList());
        }

        // query email/company info
        payAccountService.buildAccountAndCompanyInfo(views);
        return result;
    }

    public AdminCustomizedPaymentSettingDTO listCompanyCusPaySettingByEnterpriseId(
            CompanyCustomFeeQueryParams request, List<Integer> filterCompanyIds) {
        // 先查 enterprise 下有哪些 company

        // 构建查询条件
        MoeCustomizedPaymentSettingExample example = new MoeCustomizedPaymentSettingExample();
        MoeCustomizedPaymentSettingExample.Criteria criteria = example.createCriteria();

        if (filterCompanyIds != null && !filterCompanyIds.isEmpty()) {
            criteria.andCompanyIdIn(filterCompanyIds);
        }

        // 计算总数
        long total = moeCustomizedPaymentSettingMapper.countByExample(example);
        List<AdminCustomizedPaymentSettingView> views = new ArrayList<>();
        AdminCustomizedPaymentSettingDTO result = AdminCustomizedPaymentSettingDTO.builder()
                .views(views)
                .total((int) total)
                .build();

        Pagination pagination = request.getPagination();
        if (pagination == null) {
            pagination = Pagination.DEFAULT;
        }
        List<MoeCustomizedPaymentSetting> customizedPaymentSettings =
                moeCustomizedPaymentSettingMapper.listCompanyCusPaySetting(
                        filterCompanyIds,
                        CommonUtil.getLimitOffset(pagination.pageNum(), pagination.pageSize()),
                        pagination.pageSize());
        views.addAll(customizedPaymentSettings.stream()
                .map(CustomizedPaymentSettingService::buildAdminCustomizedSettingView)
                .toList());

        // query email/company info
        payAccountService.buildAccountAndCompanyInfo(views);
        return result;
    }

    public int insertEnterpriseCusPaySetting(MoeEnterpriseCustomizedPaymentSetting enterpriseCustomizedPaymentSetting) {
        return moeEnterpriseCustomizedPaymentSettingMapper.insertSelective(enterpriseCustomizedPaymentSetting);
    }

    public List<MoeEnterpriseCustomizedPaymentSetting> getEnterpriseCusPaySetByIds(List<Long> ids) {
        MoeEnterpriseCustomizedPaymentSettingExample example = new MoeEnterpriseCustomizedPaymentSettingExample();
        MoeEnterpriseCustomizedPaymentSettingExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        return moeEnterpriseCustomizedPaymentSettingMapper.selectByExample(example);
    }

    public List<MoeEnterpriseCustomizedPaymentSetting> getByEnterpriseId(Long enterpriseId) {
        MoeEnterpriseCustomizedPaymentSettingExample example = new MoeEnterpriseCustomizedPaymentSettingExample();
        MoeEnterpriseCustomizedPaymentSettingExample.Criteria criteria = example.createCriteria();

        criteria.andEnterpriseIdEqualTo(enterpriseId);
        criteria.andDeletedAtIsNull();

        return moeEnterpriseCustomizedPaymentSettingMapper.selectByExample(example);
    }

    public int deleteEnterpriseCusPaySetting(Long id) {
        LocalDateTime deletedAt = LocalDateTime.now();
        return moeEnterpriseCustomizedPaymentSettingMapper.deleteEnterpriseCusPaySetting(id, deletedAt);
    }

    public int updateEnterpriseCusPaySetting(MoeEnterpriseCustomizedPaymentSetting enterpriseCusPaySetting) {
        return moeEnterpriseCustomizedPaymentSettingMapper.updateByPrimaryKeySelective(enterpriseCusPaySetting);
    }

    // company cus pay settings
    public void batchUpsertCusPaySettings(List<MoeCustomizedPaymentSetting> settings) {
        moeCustomizedPaymentSettingMapper.batchUpsert(settings);
    }

    public MoeEnterpriseCustomizedPaymentSetting getEnterpriseCusPaySettingById(Long id) {
        return moeEnterpriseCustomizedPaymentSettingMapper.selectByPrimaryKey(id);
    }

    public AdminEnterpriseCustomizedPaymentSettingDTO queryEnterpriseSettingByPages(
            EnterpriseCustomFeeQueryParams request) {
        // 构建查询条件
        MoeEnterpriseCustomizedPaymentSettingExample example = new MoeEnterpriseCustomizedPaymentSettingExample();
        MoeEnterpriseCustomizedPaymentSettingExample.Criteria criteria = example.createCriteria();

        if (request.getEnterpriseId() != null) {
            criteria.andEnterpriseIdEqualTo(request.getEnterpriseId());
        }
        criteria.andDeletedAtIsNull();
        // 计算总数
        long total = moeEnterpriseCustomizedPaymentSettingMapper.countByExample(example);

        // 设置分页
        Pagination pagination = request.getPagination();
        if (pagination == null) {
            pagination = Pagination.DEFAULT;
        }

        // 分页查询
        List<MoeEnterpriseCustomizedPaymentSetting> customizedPaymentSettings =
                moeEnterpriseCustomizedPaymentSettingMapper.selectByPages(
                        request.getEnterpriseId(),
                        CommonUtil.getLimitOffset(pagination.pageNum(), pagination.pageSize()),
                        pagination.pageSize());

        List<AdminEnterpriseCustomizedPaymentSettingView> views = customizedPaymentSettings.stream()
                .map(CustomizedPaymentSettingService::buildAdminEnterpriseCustomizedSettingView)
                .toList();

        // 补充信息
        payAccountService.buildAccountAndEnterpriseInfo(views);

        // 返回结果
        return AdminEnterpriseCustomizedPaymentSettingDTO.builder()
                .views(views)
                .total(total)
                .build();
    }

    private static AdminCustomizedPaymentSettingView buildAdminCustomizedSettingView(
            MoeCustomizedPaymentSetting customizedPaymentSetting) {
        return AdminCustomizedPaymentSettingView.builder()
                .id(customizedPaymentSetting.getId())
                .companyId(customizedPaymentSetting.getCompanyId())
                .customizedMinVol(customizedPaymentSetting.getCustomizedMinVol())
                .readerFeeCents(customizedPaymentSetting.getReaderFeeCents())
                .readerFeeRate(customizedPaymentSetting.getReaderFeeRate())
                .onlineFeeRate(customizedPaymentSetting.getOnlineFeeRate())
                .onlineFeeCents(customizedPaymentSetting.getOnlineFeeCents())
                .createTime(customizedPaymentSetting.getCreateTime())
                .updateTime(customizedPaymentSetting.getUpdateTime())
                .build();
    }

    private static AdminEnterpriseCustomizedPaymentSettingView buildAdminEnterpriseCustomizedSettingView(
            MoeEnterpriseCustomizedPaymentSetting customizedPaymentSetting) {
        return AdminEnterpriseCustomizedPaymentSettingView.builder()
                .id(customizedPaymentSetting.getId())
                .enterpriseId(customizedPaymentSetting.getEnterpriseId())
                .customizedMinVol(customizedPaymentSetting.getCustomizedMinVol())
                .readerFeeCents(customizedPaymentSetting.getReaderFeeCents())
                .readerFeeRate(customizedPaymentSetting.getReaderFeeRate())
                .onlineFeeRate(customizedPaymentSetting.getOnlineFeeRate())
                .onlineFeeCents(customizedPaymentSetting.getOnlineFeeCents())
                .createTime(customizedPaymentSetting.getCreatedAt())
                .updateTime(customizedPaymentSetting.getUpdatedAt())
                .build();
    }
}
