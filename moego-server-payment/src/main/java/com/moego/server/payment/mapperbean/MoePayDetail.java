package com.moego.server.payment.mapperbean;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_pay_detail
 */
public class MoePayDetail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   payment primary id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.payment_id
     *
     * @mbg.generated
     */
    private Integer paymentId;

    /**
     * Database Column Remarks:
     *   order primary id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.order_id
     *
     * @mbg.generated
     */
    private Integer orderId;

    /**
     * Database Column Remarks:
     *   stripe payment intent id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.stripe_intent_id
     *
     * @mbg.generated
     */
    private String stripeIntentId;

    /**
     * Database Column Remarks:
     *   original payment amount
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.amount
     *
     * @mbg.generated
     */
    private BigDecimal amount;

    /**
     * Database Column Remarks:
     *   service price and add on
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.gross_sales
     *
     * @mbg.generated
     */
    private BigDecimal grossSales;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.discount
     *
     * @mbg.generated
     */
    private BigDecimal discount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.tax
     *
     * @mbg.generated
     */
    private BigDecimal tax;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.tips
     *
     * @mbg.generated
     */
    private BigDecimal tips;

    /**
     * Database Column Remarks:
     *   booking fee
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.booking_fee
     *
     * @mbg.generated
     */
    private BigDecimal bookingFee;

    /**
     * Database Column Remarks:
     *   AKA processing fee for B charging C
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.convenience_fee
     *
     * @mbg.generated
     */
    private BigDecimal convenienceFee;

    /**
     * Database Column Remarks:
     *   deposit sync from payment
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.is_deposit
     *
     * @mbg.generated
     */
    private Boolean isDeposit;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pay_detail.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.id
     *
     * @return the value of moe_pay_detail.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.id
     *
     * @param id the value for moe_pay_detail.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.business_id
     *
     * @return the value of moe_pay_detail.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.business_id
     *
     * @param businessId the value for moe_pay_detail.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.payment_id
     *
     * @return the value of moe_pay_detail.payment_id
     *
     * @mbg.generated
     */
    public Integer getPaymentId() {
        return paymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.payment_id
     *
     * @param paymentId the value for moe_pay_detail.payment_id
     *
     * @mbg.generated
     */
    public void setPaymentId(Integer paymentId) {
        this.paymentId = paymentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.order_id
     *
     * @return the value of moe_pay_detail.order_id
     *
     * @mbg.generated
     */
    public Integer getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.order_id
     *
     * @param orderId the value for moe_pay_detail.order_id
     *
     * @mbg.generated
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.stripe_intent_id
     *
     * @return the value of moe_pay_detail.stripe_intent_id
     *
     * @mbg.generated
     */
    public String getStripeIntentId() {
        return stripeIntentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.stripe_intent_id
     *
     * @param stripeIntentId the value for moe_pay_detail.stripe_intent_id
     *
     * @mbg.generated
     */
    public void setStripeIntentId(String stripeIntentId) {
        this.stripeIntentId = stripeIntentId == null ? null : stripeIntentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.amount
     *
     * @return the value of moe_pay_detail.amount
     *
     * @mbg.generated
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.amount
     *
     * @param amount the value for moe_pay_detail.amount
     *
     * @mbg.generated
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.gross_sales
     *
     * @return the value of moe_pay_detail.gross_sales
     *
     * @mbg.generated
     */
    public BigDecimal getGrossSales() {
        return grossSales;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.gross_sales
     *
     * @param grossSales the value for moe_pay_detail.gross_sales
     *
     * @mbg.generated
     */
    public void setGrossSales(BigDecimal grossSales) {
        this.grossSales = grossSales;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.discount
     *
     * @return the value of moe_pay_detail.discount
     *
     * @mbg.generated
     */
    public BigDecimal getDiscount() {
        return discount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.discount
     *
     * @param discount the value for moe_pay_detail.discount
     *
     * @mbg.generated
     */
    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.tax
     *
     * @return the value of moe_pay_detail.tax
     *
     * @mbg.generated
     */
    public BigDecimal getTax() {
        return tax;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.tax
     *
     * @param tax the value for moe_pay_detail.tax
     *
     * @mbg.generated
     */
    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.tips
     *
     * @return the value of moe_pay_detail.tips
     *
     * @mbg.generated
     */
    public BigDecimal getTips() {
        return tips;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.tips
     *
     * @param tips the value for moe_pay_detail.tips
     *
     * @mbg.generated
     */
    public void setTips(BigDecimal tips) {
        this.tips = tips;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.booking_fee
     *
     * @return the value of moe_pay_detail.booking_fee
     *
     * @mbg.generated
     */
    public BigDecimal getBookingFee() {
        return bookingFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.booking_fee
     *
     * @param bookingFee the value for moe_pay_detail.booking_fee
     *
     * @mbg.generated
     */
    public void setBookingFee(BigDecimal bookingFee) {
        this.bookingFee = bookingFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.convenience_fee
     *
     * @return the value of moe_pay_detail.convenience_fee
     *
     * @mbg.generated
     */
    public BigDecimal getConvenienceFee() {
        return convenienceFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.convenience_fee
     *
     * @param convenienceFee the value for moe_pay_detail.convenience_fee
     *
     * @mbg.generated
     */
    public void setConvenienceFee(BigDecimal convenienceFee) {
        this.convenienceFee = convenienceFee;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.is_deposit
     *
     * @return the value of moe_pay_detail.is_deposit
     *
     * @mbg.generated
     */
    public Boolean getIsDeposit() {
        return isDeposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.is_deposit
     *
     * @param isDeposit the value for moe_pay_detail.is_deposit
     *
     * @mbg.generated
     */
    public void setIsDeposit(Boolean isDeposit) {
        this.isDeposit = isDeposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.create_time
     *
     * @return the value of moe_pay_detail.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.create_time
     *
     * @param createTime the value for moe_pay_detail.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.update_time
     *
     * @return the value of moe_pay_detail.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.update_time
     *
     * @param updateTime the value for moe_pay_detail.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pay_detail.company_id
     *
     * @return the value of moe_pay_detail.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pay_detail.company_id
     *
     * @param companyId the value for moe_pay_detail.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
