package com.moego.server.message.server;

import com.moego.server.grooming.dto.AbandonRecordDTO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link SendServer} tester.
 */
@SpringBootTest
@Disabled("For local test only")
class SendServerTest {

    @Autowired
    SendServer sendServer;

    /**
     * {@link SendServer#sendNewAbandonedBookingsNotification(AbandonRecordDTO)}
     */
    @Test
    void testSendNewAbandonedBookingsNotification() {
        AbandonRecordDTO abandonRecord = new AbandonRecordDTO();
        abandonRecord.setBusinessId(100611);
        abandonRecord.setAbandonStep("select_time");
        abandonRecord.setFirstName("Freeman");
        abandonRecord.setLastName("Liu");
        sendServer.sendNewAbandonedBookingsNotification(abandonRecord);
    }

    /**
     * {@link SendServer#listRecentAbandonRecordForGrooming()}
     */
    @Test
    void testListRecentAbandonRecordForGrooming() {
        sendServer.listRecentAbandonRecordForGrooming();
    }
}
