package com.moego.server.message.service.sendmessage.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class MandrillMessageSendServiceImplTest {

    @Test
    void testFormatEmailHtml_NullInput() {
        String result = MandrillMessageSendServiceImpl.renderEmailMessageHtml(null);
        assertEquals("", result);
    }

    @Test
    void testFormatEmailHtml_EmptyInput() {
        String result = MandrillMessageSendServiceImpl.renderEmailMessageHtml("");
        assertEquals("", result);
    }

    @Test
    void testFormatEmailHtml_Workflow() {
        String input = "<p>A</p>\n<p>B</p>\n<p>&nbsp;</p>\n<p>C</p>\n<p>&nbsp;</p>\n<p>&nbsp;</p>\n<p>D</p>";
        String expected = "<p>A</p><p>B</p><br /><p>C</p><br /><br /><p>D</p>";
        String result = MandrillMessageSendServiceImpl.renderEmailMessageHtml(input);
        assertEquals(expected, result);
    }

    @Test
    void testFormatEmailHtml_AutoReminder() {
        String input = "A\nB\n\nC\n\n\nD";
        String expected = "A<br />B<br /><br />C<br /><br /><br />D";
        String result = MandrillMessageSendServiceImpl.renderEmailMessageHtml(input);
        assertEquals(expected, result);
    }
}
