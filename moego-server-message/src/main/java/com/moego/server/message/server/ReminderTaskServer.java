package com.moego.server.message.server;

import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.message.api.IReminderTaskServiceBase;
import com.moego.server.message.service.MoeReminderSettingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class ReminderTaskServer extends IReminderTaskServiceBase {

    private final MoeReminderSettingService reminderSettingService;

    @Override
    public void beginCalendarPushTask() {
        ThreadPool.execute(() -> reminderSettingService.pushCalendarReminderTask());
    }

    @Override
    // 这里需要一次性拿到所有 enabled 的 cof reminder。auto message 一期重构后，不方便改造，暂时与其它 reminder 任务放到一起
    public Integer sendCardLinkReminder() {
        //        log.info("execute in thread {}", Thread.currentThread().getName());
        //        Long startTime = DateUtil.get10Timestamp();
        //        Long endTime = startTime + 10 * 60L;
        //        List<MoeBusinessReminder> reminderConfigs =
        //
        // moeBusinessReminderMapper.selectByReminderType(ReminderTypeEnum.COF_LINK_REMINDER.getReminderType());
        //        AtomicInteger sentCount = new AtomicInteger(0);
        //        reminderConfigs.parallelStream().forEach(reminderConfig -> {
        //            // 扫描过去指定时间范围内所有初始态的link (扫描出来的都是未过期的， 可直接发送提醒)
        //            List<MoeCardLinkMessage> customerTaskList =
        //                    cardLinkMessageMapper.getReminderTaskList(reminderConfig.getBusinessId(), startTime,
        // endTime);
        //            if (!CollectionUtils.isEmpty(customerTaskList)) {
        //                moeBusinessReminderService.sendCofReminderService(reminderConfig, customerTaskList);
        //            }
        //            sentCount.addAndGet(customerTaskList.size());
        //            log.info(
        //                    "executed businessId: {}, current: {}, total sentCount: {}",
        //                    reminderConfig.getBusinessId(),
        //                    customerTaskList.size(),
        //                    sentCount.get());
        //        });
        //        return reminderConfigs.size();
        return 0;
    }
}
