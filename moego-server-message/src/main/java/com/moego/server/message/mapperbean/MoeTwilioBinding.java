package com.moego.server.message.mapperbean;

public class MoeTwilioBinding {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.twilio_sid
     *
     * @mbg.generated
     */
    private String twilioSid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.twilio_token
     *
     * @mbg.generated
     */
    private String twilioToken;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.twilio_number
     *
     * @mbg.generated
     */
    private String twilioNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.country_code
     *
     * @mbg.generated
     */
    private String countryCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.friendly_name
     *
     * @mbg.generated
     */
    private String friendlyName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.create_time
     *
     * @mbg.generated
     */
    private Integer createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_twilio_binding.update_time
     *
     * @mbg.generated
     */
    private Integer updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.id
     *
     * @return the value of moe_twilio_binding.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.id
     *
     * @param id the value for moe_twilio_binding.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.twilio_sid
     *
     * @return the value of moe_twilio_binding.twilio_sid
     *
     * @mbg.generated
     */
    public String getTwilioSid() {
        return twilioSid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.twilio_sid
     *
     * @param twilioSid the value for moe_twilio_binding.twilio_sid
     *
     * @mbg.generated
     */
    public void setTwilioSid(String twilioSid) {
        this.twilioSid = twilioSid == null ? null : twilioSid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.twilio_token
     *
     * @return the value of moe_twilio_binding.twilio_token
     *
     * @mbg.generated
     */
    public String getTwilioToken() {
        return twilioToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.twilio_token
     *
     * @param twilioToken the value for moe_twilio_binding.twilio_token
     *
     * @mbg.generated
     */
    public void setTwilioToken(String twilioToken) {
        this.twilioToken = twilioToken == null ? null : twilioToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.twilio_number
     *
     * @return the value of moe_twilio_binding.twilio_number
     *
     * @mbg.generated
     */
    public String getTwilioNumber() {
        return twilioNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.twilio_number
     *
     * @param twilioNumber the value for moe_twilio_binding.twilio_number
     *
     * @mbg.generated
     */
    public void setTwilioNumber(String twilioNumber) {
        this.twilioNumber = twilioNumber == null ? null : twilioNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.country_code
     *
     * @return the value of moe_twilio_binding.country_code
     *
     * @mbg.generated
     */
    public String getCountryCode() {
        return countryCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.country_code
     *
     * @param countryCode the value for moe_twilio_binding.country_code
     *
     * @mbg.generated
     */
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode == null ? null : countryCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.friendly_name
     *
     * @return the value of moe_twilio_binding.friendly_name
     *
     * @mbg.generated
     */
    public String getFriendlyName() {
        return friendlyName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.friendly_name
     *
     * @param friendlyName the value for moe_twilio_binding.friendly_name
     *
     * @mbg.generated
     */
    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName == null ? null : friendlyName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.status
     *
     * @return the value of moe_twilio_binding.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.status
     *
     * @param status the value for moe_twilio_binding.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.create_time
     *
     * @return the value of moe_twilio_binding.create_time
     *
     * @mbg.generated
     */
    public Integer getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.create_time
     *
     * @param createTime the value for moe_twilio_binding.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_twilio_binding.update_time
     *
     * @return the value of moe_twilio_binding.update_time
     *
     * @mbg.generated
     */
    public Integer getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_twilio_binding.update_time
     *
     * @param updateTime the value for moe_twilio_binding.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }
}
