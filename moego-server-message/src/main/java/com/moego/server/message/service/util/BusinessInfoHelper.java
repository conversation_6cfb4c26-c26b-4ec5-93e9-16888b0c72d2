package com.moego.server.message.service.util;

import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.server.business.client.IBusinessBusinessClient;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessInfoHelper {

    private final IBusinessBusinessClient businessBusinessClient;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;

    @Deprecated // use getCompanyIdByBusinessIdV2 instead
    public Long getCompanyIdByBusinessId(Integer businessId) {
        try {
            Long companyId =
                    businessBusinessClient.getCompanyIdByBusinessId(businessId).companyId();
            if (Objects.isNull(companyId)) {
                return 0L;
            }
            return companyId;
        } catch (Exception e) {
            log.error("getCompanyIdByBusinessId with businessId[{}] error", businessId, e);
            return 0L;
        }
    }

    public Long getCompanyIdByBusinessIdV2(Integer businessId) {
        try {
            return businessService
                    .getCompanyId(GetCompanyIdRequest.newBuilder()
                            .setBusinessId(businessId.longValue())
                            .build())
                    .getCompanyId();
        } catch (Exception e) {
            log.error("getCompanyIdByBusinessIdV2 with businessId[{}] error", businessId, e);
            return 0L;
        }
    }
}
