<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.message.mapper.MoeBusinessAutoReplyMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.message.mapperbean.MoeBusinessAutoReply">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="auto_reply_body" jdbcType="VARCHAR" property="autoReplyBody" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="start_time" jdbcType="INTEGER" property="startTime" />
    <result column="end_time" jdbcType="INTEGER" property="endTime" />
    <result column="crate_time" jdbcType="INTEGER" property="crateTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="time_range" jdbcType="VARCHAR" property="timeRange" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, auto_reply_body, status, start_time, end_time, crate_time, update_time, 
    time_range, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_business_auto_reply
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_business_auto_reply
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.message.mapperbean.MoeBusinessAutoReply">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_auto_reply (business_id, auto_reply_body, status, 
      start_time, end_time, crate_time, 
      update_time, time_range, company_id
      )
    values (#{businessId,jdbcType=INTEGER}, #{autoReplyBody,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{startTime,jdbcType=INTEGER}, #{endTime,jdbcType=INTEGER}, #{crateTime,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=INTEGER}, #{timeRange,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.message.mapperbean.MoeBusinessAutoReply">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_business_auto_reply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="autoReplyBody != null">
        auto_reply_body,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="crateTime != null">
        crate_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="timeRange != null">
        time_range,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="autoReplyBody != null">
        #{autoReplyBody,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=INTEGER},
      </if>
      <if test="crateTime != null">
        #{crateTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="timeRange != null">
        #{timeRange,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.message.mapperbean.MoeBusinessAutoReply">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_auto_reply
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="autoReplyBody != null">
        auto_reply_body = #{autoReplyBody,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=INTEGER},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=INTEGER},
      </if>
      <if test="crateTime != null">
        crate_time = #{crateTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
      <if test="timeRange != null">
        time_range = #{timeRange,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.message.mapperbean.MoeBusinessAutoReply">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_business_auto_reply
    set business_id = #{businessId,jdbcType=INTEGER},
      auto_reply_body = #{autoReplyBody,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=INTEGER},
      end_time = #{endTime,jdbcType=INTEGER},
      crate_time = #{crateTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER},
      time_range = #{timeRange,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByBusinessId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_business_auto_reply
        where business_id = #{businessId,jdbcType=INTEGER} limit 1
    </select>
</mapper>