package com.moego.server.message.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.server.message.mapperbean.MoeBusinessReminderSendLog;
import org.apache.ibatis.annotations.Param;

public interface MoeBusinessReminderSendLogMapper extends DynamicDataSource<MoeBusinessReminderSendLogMapper> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_reminder_send_log
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_reminder_send_log
     *
     * @mbg.generated
     */
    int insert(MoeBusinessReminderSendLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_reminder_send_log
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessReminderSendLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_reminder_send_log
     *
     * @mbg.generated
     */
    MoeBusinessReminderSendLog selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_reminder_send_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessReminderSendLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_reminder_send_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessReminderSendLog record);

    MoeBusinessReminderSendLog selectByProp(
            @Param("targetId") Integer targetId,
            @Param("targetType") Integer targetType,
            @Param("sendDate") String sendDate,
            @Param("flag") Integer flag);
}
