package com.moego.server.message.mapperbean;

public class MoeBusinessReminderDismiss {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.reminder_type
     *
     * @mbg.generated
     */
    private Integer reminderType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.dismiss_id
     *
     * @mbg.generated
     */
    private Integer dismissId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.crate_time
     *
     * @mbg.generated
     */
    private Integer crateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.update_time
     *
     * @mbg.generated
     */
    private Integer updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_dismiss.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.id
     *
     * @return the value of moe_business_reminder_dismiss.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.id
     *
     * @param id the value for moe_business_reminder_dismiss.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.business_id
     *
     * @return the value of moe_business_reminder_dismiss.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.business_id
     *
     * @param businessId the value for moe_business_reminder_dismiss.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.staff_id
     *
     * @return the value of moe_business_reminder_dismiss.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.staff_id
     *
     * @param staffId the value for moe_business_reminder_dismiss.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.reminder_type
     *
     * @return the value of moe_business_reminder_dismiss.reminder_type
     *
     * @mbg.generated
     */
    public Integer getReminderType() {
        return reminderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.reminder_type
     *
     * @param reminderType the value for moe_business_reminder_dismiss.reminder_type
     *
     * @mbg.generated
     */
    public void setReminderType(Integer reminderType) {
        this.reminderType = reminderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.dismiss_id
     *
     * @return the value of moe_business_reminder_dismiss.dismiss_id
     *
     * @mbg.generated
     */
    public Integer getDismissId() {
        return dismissId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.dismiss_id
     *
     * @param dismissId the value for moe_business_reminder_dismiss.dismiss_id
     *
     * @mbg.generated
     */
    public void setDismissId(Integer dismissId) {
        this.dismissId = dismissId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.status
     *
     * @return the value of moe_business_reminder_dismiss.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.status
     *
     * @param status the value for moe_business_reminder_dismiss.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.crate_time
     *
     * @return the value of moe_business_reminder_dismiss.crate_time
     *
     * @mbg.generated
     */
    public Integer getCrateTime() {
        return crateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.crate_time
     *
     * @param crateTime the value for moe_business_reminder_dismiss.crate_time
     *
     * @mbg.generated
     */
    public void setCrateTime(Integer crateTime) {
        this.crateTime = crateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.update_time
     *
     * @return the value of moe_business_reminder_dismiss.update_time
     *
     * @mbg.generated
     */
    public Integer getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.update_time
     *
     * @param updateTime the value for moe_business_reminder_dismiss.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_dismiss.company_id
     *
     * @return the value of moe_business_reminder_dismiss.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_dismiss.company_id
     *
     * @param companyId the value for moe_business_reminder_dismiss.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
