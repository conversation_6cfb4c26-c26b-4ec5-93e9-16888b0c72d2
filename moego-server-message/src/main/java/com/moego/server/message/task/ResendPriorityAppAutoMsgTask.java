package com.moego.server.message.task;

import com.moego.common.distributed.LockManager;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.ListLocationsRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.message.enums.MessageDetailEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.mapper.MoeBusinessMessageDetailExtensionMapper;
import com.moego.server.message.mapper.MoeBusinessMessageDetailMapper;
import com.moego.server.message.mapperbean.MoeBusinessMessageDetailExtension;
import com.moego.server.message.mapperbean.MoeBusinessMessageDetailExtensionExample;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.service.placeholder.PlaceholderContext;
import com.moego.server.message.service.placeholder.TextReplacer;
import com.moego.server.message.service.sendmessage.MessageSendRouterService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class ResendPriorityAppAutoMsgTask {
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;
    private final MoeBusinessMessageDetailMapper moeBusinessMessageDetailMapper;
    private final MoeBusinessMessageDetailExtensionMapper moeBusinessMessageDetailExtensionMapper;
    private final MessageSendRouterService messageSendRouterService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;
    private final LockManager lockManager;
    private static final String TASK_CONFIG_KEY = "auto_message_priority_app_task_config";
    private static final String COMPANY_CONFIG_KEY = "auto_message_priority_app_config";

    @Resource
    private TextReplacer textReplacer;

    public Boolean run() {
        var v = metadataServiceBlockingStub
                .extractValues(ExtractValuesRequest.newBuilder()
                        .setKeyName(TASK_CONFIG_KEY)
                        .putOwners(OwnerType.OWNER_TYPE_SYSTEM_VALUE, 1)
                        .build())
                .getValues()
                .getOrDefault(TASK_CONFIG_KEY, "{}");
        var config = JsonUtil.toBean(v, TaskConfig.class);
        log.info("ResendPriorityAppAutoMsgTask run for {}", config);
        for (var companyId : config.companyIds()) {
            var companyConfig = metadataServiceBlockingStub
                    .extractValues(ExtractValuesRequest.newBuilder()
                            .setKeyName(COMPANY_CONFIG_KEY)
                            .putOwners(OwnerType.OWNER_TYPE_COMPANY_VALUE, companyId)
                            .build())
                    .getValues()
                    .getOrDefault(COMPANY_CONFIG_KEY, "{}");
            var cc = JsonUtil.toBean(companyConfig, CompanyConfig.class);
            if (!cc.isOpen()) {
                log.info("ResendPriorityAppAutoMsgTask run for company {} is closed", companyId);
                continue;
            }
            Set<Integer> defaultTargetTypes = Set.of(
                    MessageTargetTypeEnums.AUTO_RECEIPT.getValue(),
                    MessageTargetTypeEnums.AUTO_APPOINTMENT_MOVED_TO_WAIT_LIST.getValue(),
                    MessageTargetTypeEnums.AUTO_APPOINTMENT_CANCELLED.getValue(),
                    MessageTargetTypeEnums.AUTO_APPOINTMENT_RESCHEDULED.getValue(),
                    MessageTargetTypeEnums.AUTO_APPOINTMENT_BOOK.getValue(),
                    MessageTargetTypeEnums.REMINDER_APPOINTMENT_FIRST.getValue(),
                    MessageTargetTypeEnums.REMINDER_APPOINTMENT_SECOND.getValue(),
                    MessageTargetTypeEnums.REMINDER_APPOINTMENT_REMIND.getValue(),
                    MessageTargetTypeEnums.REMINDER_REBOOK.getValue(),
                    MessageTargetTypeEnums.REMINDER_PET_BIRTHDAY.getValue(),
                    MessageTargetTypeEnums.TARGET_TYPE_AUTO_PICKUP.getValue());

            ThreadPool.execute(() -> {
                runFor(
                        companyId,
                        CollectionUtils.isEmpty(config.targetTypes()) ? defaultTargetTypes : config.targetTypes(),
                        config.expirySeconds(),
                        cc.retryAfterSeconds(),
                        cc.template());
            });
        }
        return true;
    }

    record TaskConfig(long expirySeconds, List<Long> companyIds, Set<Integer> targetTypes) {}

    record CompanyConfig(long companyId, long retryAfterSeconds, boolean isOpen, String template) {}

    public void runFor(
            long companyId, Set<Integer> targetTypes, long expirySeconds, long retryAfterSeconds, String template) {
        String lockKey = "resend_priority_app_auto_msg_task_" + companyId;
        if (lockManager.lock(lockKey, lockKey, 5 * 60L)) {
            log.info(
                    "ResendPriorityAppAutoMsgTask run for company {} started, expirySeconds: {}, retryAfterSeconds: {}",
                    companyId,
                    expirySeconds,
                    retryAfterSeconds);
        } else {
            log.info("ResendPriorityAppAutoMsgTask run for company {} failed to get lock", companyId);
            return;
        }
        List<LocationModel> locations = new ArrayList<>();
        try {
            locations = businessServiceBlockingStub
                    .listLocations(ListLocationsRequest.newBuilder()
                            .setFilter(ListLocationsRequest.Filter.newBuilder()
                                    .addAllCompanyIds(List.of(companyId))
                                    .build())
                            .build())
                    .getLocationsList();
            if (locations.isEmpty()) {
                log.error("ResendPriorityAppAutoMsgTask run for company {} locations empty", companyId);
            }
        } catch (Exception e) {
            log.error(
                    "ResendPriorityAppAutoMsgTask run for company {} get locations failed,Exception:{}",
                    companyId,
                    e.toString());
        }

        if (!locations.isEmpty()) {
            List<Integer> resendList = new ArrayList<>();
            while (true) {
                try {
                    // FIXME: shadow 表同步不稳定，如果有性能问题还是要读 shadow 表
                    var resendMsgDetailList = moeBusinessMessageDetailMapper.selectNeedResendCAppMsg(
                            locations.stream()
                                    .map(LocationModel::getId)
                                    .map(Long::intValue)
                                    .toList(),
                            targetTypes,
                            expirySeconds,
                            retryAfterSeconds,
                            Instant.now().getEpochSecond(),
                            1000);
                    if (resendMsgDetailList.isEmpty()) {
                        break;
                    }
                    resendMsgDetailList.forEach(sourceMsg -> {
                        if (!StringUtils.hasText(sourceMsg.getMessageText())) {
                            return;
                        }
                        PlaceholderContext.PlaceholderContextBuilder contextBuilder = PlaceholderContext.builder()
                                .businessId(sourceMsg.getBusinessId().longValue())
                                .customerId(sourceMsg.getCustomerId().longValue())
                                .companyId(companyId);
                        var result = textReplacer.process(template, contextBuilder.build());
                        if (!StringUtils.hasText(result.getText())) {
                            return;
                        }
                        SendMessagesParams sendMessagesParams = new SendMessagesParams();
                        sendMessagesParams.setStaffId(sourceMsg.getStaffId());
                        sendMessagesParams.setMessageBody(result.getText());
                        sendMessagesParams.setBusinessId(sourceMsg.getBusinessId());
                        sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_THREAD.getValue());
                        sendMessagesParams.setTargetId(0);
                        sendMessagesParams.getCustomer().setCustomerId(sourceMsg.getCustomerId());
                        sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
                        sendMessagesParams.setResendSourceMessageId(sourceMsg.getId());
                        try {
                            messageSendRouterService.sendServicesMessageToCustomer(sendMessagesParams);
                            resendList.add(sourceMsg.getId());
                        } catch (Exception e) {
                            log.error(
                                    "ResendPriorityAppAutoMsgTask run for company {} msgId {} failed exception: {}",
                                    companyId,
                                    sourceMsg.getId(),
                                    e.toString());
                            MoeBusinessMessageDetailExtensionExample example =
                                    new MoeBusinessMessageDetailExtensionExample();
                            example.createCriteria()
                                    .andMessageIdEqualTo(sourceMsg.getId().longValue());
                            var extension = moeBusinessMessageDetailExtensionMapper.selectByExample(example);
                            if (extension.isEmpty()
                                    || extension.get(0).getResendMessageId().equals(0L)) {
                                log.error(
                                        "ResendPriorityAppAutoMsgTask run for company {} msgId {} set resendMessageId to -1",
                                        companyId,
                                        sourceMsg.getId());
                                MoeBusinessMessageDetailExtension record = new MoeBusinessMessageDetailExtension();
                                record.setMessageId(sourceMsg.getId().longValue());
                                record.setResendMessageId(-1L);
                                moeBusinessMessageDetailExtensionMapper.upsertByMessageId(record);
                            }
                        }
                    });
                } catch (Exception e) {
                    log.error(
                            "ResendPriorityAppAutoMsgTask run for company {} failed exception: {}",
                            companyId,
                            e.toString());
                    break;
                }
            }
            log.info("ResendPriorityAppAutoMsgTask run for company {} done, resendIds {}", companyId, resendList);
        }
        lockManager.unlock(lockKey, lockKey);
    }
}
