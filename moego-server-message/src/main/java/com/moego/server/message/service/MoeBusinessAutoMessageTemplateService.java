package com.moego.server.message.service;

import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_BOOKED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_CANCELLED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_CANCELLED_BY_CLIENT;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_CONFIRMED_BY_CLIENT;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_MOVED_TO_WAITLIST;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_RESCHEDULED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_READY_FOR_PICK_UP;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID;

import com.moego.idl.models.auto_message.v1.AppointmentAutoMsgConfigModel;
import com.moego.idl.models.auto_message.v1.AutoMessageConfigModel;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigDef;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigDefList;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.message.v1.BatchCreateTemplateItemDef;
import com.moego.idl.models.message.v1.MessageTemplateDef;
import com.moego.idl.models.message.v1.MessageTemplateSimpleView;
import com.moego.idl.models.message.v1.MessageTemplateUseCase;
import com.moego.idl.models.message.v1.MessageTemplateUseCaseEnumList;
import com.moego.idl.models.message.v1.TemplateModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.auto_message.v1.AutoMessageConfigServiceGrpc;
import com.moego.idl.service.auto_message.v1.GetAppointmentAutoMsgConfigByBusinessIdsRequest;
import com.moego.idl.service.auto_message.v1.GetAppointmentAutoMsgConfigListRequest;
import com.moego.idl.service.auto_message.v1.GetAutoMessageConfigByBusinessIdsRequest;
import com.moego.idl.service.auto_message.v1.GetAutoMessageConfigListRequest;
import com.moego.idl.service.auto_message.v1.UpdateAppointmentAutoMsgConfigRequest;
import com.moego.idl.service.auto_message.v1.UpdateAutoMessageConfigRequest;
import com.moego.idl.service.message.v1.BatchCreateTemplatesRequest;
import com.moego.idl.service.message.v1.GetMessageTemplatesRequest;
import com.moego.idl.service.message.v1.MGetTemplatesRequest;
import com.moego.idl.service.message.v1.MessageTemplateServiceGrpc;
import com.moego.idl.service.message.v1.TemplateServiceGrpc;
import com.moego.idl.service.message.v1.UpdateMessageTemplateRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO;
import com.moego.server.message.enums.AutoMessageTemplateEnum;
import com.moego.server.message.mapperbean.MoeBusinessAutoMessageTemplate;
import com.moego.server.message.params.MoeBusinessAutoMessageTemplateParams;
import com.moego.server.message.service.dto.AutoMsgDto;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2024-06-18
 * auto message rebuild 都走新接口，老接口只用于兼容老版 app。
 * · ETA、PAY_ONLINE 两个模板挪到了 saved reply 中, 作为 system template
 * · USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID 支持 sms 与 email 分别配置
 * · 其余预约相关的模版均支持 service type config；支持 sms 与 email 分别配置
 * 新老模版的格式不兼容
 * · 旧版读接口优先返回 sms body。对于针对 service type 配置的场景，优先返回 main service 对应的 template
 * · 旧版写接口会检查兼容性，不兼容的场景会报错
 */
@Service
public class MoeBusinessAutoMessageTemplateService {

    @Autowired
    private AutoMessageConfigServiceGrpc.AutoMessageConfigServiceBlockingStub autoMessageConfigClient;

    @Autowired
    private TemplateServiceGrpc.TemplateServiceBlockingStub templateClient;

    @Autowired
    private MessageTemplateServiceGrpc.MessageTemplateServiceBlockingStub messageTemplateClient;

    @Autowired
    private FeatureFlagApi featureFlagApi;

    public static final int DISABLE_STATUS = 2;

    static final String SEND_ETA_TEMPLATE_NAME = "Send ETA";
    static final String SEND_INVOICE_TEMPLATE_NAME = "Send invoice to pay online";

    // 兼容版 app.
    public List<MoeBusinessAutoMessageTemplate> getNewAutoMsgConfig(Long companyId, Integer businessId) {
        List<MoeBusinessAutoMessageTemplate> templates =
                msgTemplateToAutoMsgTemplate(getMessageTemplateForOldAutoMsgs(businessId));
        templates.addAll(apptAutoMsgConfigToAutoMsgTemplate(
                getAppointmentAutoMsgConfigList(companyId, businessId.longValue(), getUserCaseForAPPTAutoMsg())));
        templates.addAll(autoMessageConfigToAutoMsgTemplate(
                getAutoMessageConfigList(companyId, businessId.longValue(), getUserCaseForAutoMsg())));
        return templates;
    }

    // 兼容老版 app.
    public MoeBusinessAutoMessageTemplate getNewAutoMsgByType(
            Long companyId, Integer businessId, AutoMessageTemplateEnum type) {
        List<MoeBusinessAutoMessageTemplate> templates =
                switch (type) {
                    case ETA, PAY_ONLINE -> msgTemplateToAutoMsgTemplate(getMessageTemplateForOldAutoMsgs(businessId));
                    case RECEIPT -> autoMessageConfigToAutoMsgTemplate(
                            getAutoMessageConfigList(companyId, businessId.longValue(), getUserCaseForAutoMsg()));
                    default -> apptAutoMsgConfigToAutoMsgTemplate(getAppointmentAutoMsgConfigList(
                            companyId, businessId.longValue(), getUserCaseForAPPTAutoMsg()));
                };

        return templates.stream()
                .filter(k -> k.getType().equals(type.getValue()))
                .findFirst()
                .orElse(null);
    }

    // 老版 app 更新接口. 可能通过 type 或 id 更新. 会检查兼容性
    public MoeBusinessAutoMessageTemplate updateNewAutoMsgConfig(
            Long companyId, MoeBusinessAutoMessageTemplateParams params, Long tokenStaffID) {
        if (params.getId() == null && params.getType() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid params. id or type is required");
        }
        MoeBusinessAutoMessageTemplate existAutoMsg = null;
        if (params.getType() != null) {
            AutoMessageTemplateEnum typeEnum = AutoMessageTemplateEnum.fromValue(params.getType());
            if (typeEnum == null) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid params. type is not found");
            }
            existAutoMsg = getNewAutoMsgByType(companyId, params.getBusinessId(), typeEnum);
        } else {
            existAutoMsg = getNewAutoMsgConfig(companyId, params.getBusinessId()).stream()
                    .filter(k -> k.getId().equals(params.getId()))
                    .findFirst()
                    .orElse(null);
        }
        if (existAutoMsg == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid params. id or type is not found");
        }
        AutoMessageTemplateEnum typeEnum = AutoMessageTemplateEnum.fromValue(existAutoMsg.getType());

        String newContent = params.getBody() == null ? existAutoMsg.getBody() : params.getBody();
        Integer newStatus = params.getStatus() == null ? existAutoMsg.getStatus() : params.getStatus();
        switch (typeEnum) {
            case ETA, PAY_ONLINE -> {
                updateSystemTemplate(params.getBusinessId().longValue(), tokenStaffID, newContent, existAutoMsg);
                return getNewAutoMsgByType(companyId, params.getBusinessId(), typeEnum);
            }
            case RECEIPT -> {
                return updatePayAutoMsg(
                        companyId, params.getBusinessId().longValue(), typeEnum, newContent, newStatus, existAutoMsg);
            }
            case APPOINTMENT_BOOK,
                    APPOINTMENT_RESCHEDULED,
                    APPOINTMENT_CANCELLED,
                    READY_PICK_UP,
                    APPOINTMENT_CONFIRMED_BY_CLIENT,
                    APPOINTMENT_CANCELLED_BY_CLIENT -> {
                return updateAppointmentAutoMsg(
                        companyId, params.getBusinessId().longValue(), typeEnum, newContent, newStatus, existAutoMsg);
            }
        }
        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid params. auto message type is not exist");
    }

    void updateSystemTemplate(
            Long businessId, Long tokenStaffID, String newContent, MoeBusinessAutoMessageTemplate existAutoMsg) {
        if (existAutoMsg.getBody().equals(newContent)) {
            return;
        }
        UpdateMessageTemplateRequest request = UpdateMessageTemplateRequest.newBuilder()
                .setId(existAutoMsg.getId())
                .setMessageTemplateDef(MessageTemplateDef.newBuilder()
                        .setTemplateName(getMessageTemplateNameByTemplateEnum(
                                AutoMessageTemplateEnum.fromValue(existAutoMsg.getType())))
                        .setTemplateContent(newContent)
                        .build())
                .setBusinessId(businessId)
                .setStaffId(tokenStaffID)
                .build();
        messageTemplateClient.updateMessageTemplate(request);
    }

    MoeBusinessAutoMessageTemplate updateAppointmentAutoMsg(
            Long companyId,
            Long businessId,
            AutoMessageTemplateEnum typeEnum,
            String newContent,
            Integer newStatus,
            MoeBusinessAutoMessageTemplate existAutoMsg) {
        AppointmentAutoMsgConfigModel autoMsgConfig = getAppointmentAutoMsgConfigList(
                        companyId, businessId, List.of(autoMsgTypeToUseCaseEnum(typeEnum)))
                .get(0);
        if (autoMsgConfig.getTemplatesCount() != 1) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "Unsupported. Please upgrade your app to the latest version and try again.");
        }
        ServiceTypeConfigModel serviceTypeConfig = autoMsgConfig.getTemplates(0);
        Map<Long, TemplateModel> templates = getTemplatesByServiceTypeConfig(List.of(serviceTypeConfig));
        String smsBody = templates.get(serviceTypeConfig.getSmsTemplateId()).getBody();
        String emailBody = templates.get(serviceTypeConfig.getEmailTemplateId()).getBody();
        if (!smsBody.equals(emailBody)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "Unsupported. Please upgrade your app to the latest version and try again.");
        }

        UpdateAppointmentAutoMsgConfigRequest.Builder autoMsgConfigUpdate =
                UpdateAppointmentAutoMsgConfigRequest.newBuilder()
                        .setId(existAutoMsg.getId())
                        .setCompanyId(companyId);
        if (!existAutoMsg.getBody().equals(newContent)) {
            autoMsgConfigUpdate.setTemplates(buildServiceTypeConfigDef(serviceTypeConfig, newContent));
        }
        if (newStatus != null) {
            autoMsgConfigUpdate.setIsEnabled(newStatus == 1);
        }

        AppointmentAutoMsgConfigModel result = autoMessageConfigClient
                .updateAppointmentAutoMsgConfig(autoMsgConfigUpdate.build())
                .getAutoMessage();
        return apptAutoMsgConfigToAutoMsgTemplate(List.of(result)).get(0);
    }

    MoeBusinessAutoMessageTemplate updatePayAutoMsg(
            Long companyId,
            Long businessId,
            AutoMessageTemplateEnum typeEnum,
            String newContent,
            Integer newStatus,
            MoeBusinessAutoMessageTemplate existAutoMsg) {
        AutoMessageConfigModel autoMsgConfig = getAutoMessageConfigList(
                        companyId, businessId, List.of(autoMsgTypeToUseCaseEnum(typeEnum)))
                .get(0);
        Map<Long, TemplateModel> templates = getTemplatesByAutoMsgConfig(List.of(autoMsgConfig));
        String smsBody = templates.get(autoMsgConfig.getSmsTemplateId()).getBody();
        String emailBody = templates.get(autoMsgConfig.getEmailTemplateId()).getBody();
        if (!smsBody.equals(emailBody)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FORBIDDEN, "Unsupported. Please upgrade your app to the latest version and try again.");
        }

        UpdateAutoMessageConfigRequest.Builder autoMsgConfigUpdate = UpdateAutoMessageConfigRequest.newBuilder()
                .setId(existAutoMsg.getId())
                .setCompanyId(companyId);
        if (!existAutoMsg.getBody().equals(newContent)) {
            String emailSubject =
                    templates.get(autoMsgConfig.getEmailTemplateId()).getSubject();
            Pair<TemplateModel, TemplateModel> newTemplates = buildSMSAndEmailTemplate(emailSubject, newContent);
            autoMsgConfigUpdate.setSmsTemplateId(newTemplates.getFirst().getId());
            autoMsgConfigUpdate.setEmailTemplateId(newTemplates.getSecond().getId());
        }
        if (newStatus != null) {
            autoMsgConfigUpdate.setIsEnabled(newStatus == 1);
        }

        AutoMessageConfigModel result = autoMessageConfigClient
                .updateAutoMessageConfig(autoMsgConfigUpdate.build())
                .getAutoMessage();
        return autoMessageConfigToAutoMsgTemplate(List.of(result)).get(0);
    }

    public ServiceTypeConfigModel getMainServiceTypeConfig(List<ServiceTypeConfigModel> serviceTypeConfigs) {
        ServiceItemType mainServiceType = getMainServiceItem(serviceTypeConfigs.stream()
                .map(ServiceTypeConfigModel::getServiceItemTypesList)
                .flatMap(List::stream)
                .toList());
        if (mainServiceType == null) {
            return null;
        }

        return serviceTypeConfigs.stream()
                .filter(k -> k.getServiceItemTypesList().contains(mainServiceType))
                .findFirst()
                .orElse(null);
    }

    // 主 service 选择逻辑：boarding > daycare > grooming; evaluation 目前不与其它 service type 同时出现
    public ServiceItemType getMainServiceItem(List<ServiceItemType> serviceItemTypes) {
        if (CollectionUtils.isEmpty(serviceItemTypes)) {
            return null;
        }
        if (serviceItemTypes.contains(ServiceItemType.BOARDING)) {
            return ServiceItemType.BOARDING;
        }
        if (serviceItemTypes.contains(ServiceItemType.DAYCARE)) {
            return ServiceItemType.DAYCARE;
        }
        if (serviceItemTypes.contains(ServiceItemType.DOG_WALKING)) {
            return ServiceItemType.DOG_WALKING;
        }
        if (serviceItemTypes.contains(ServiceItemType.GROOMING)) {
            return ServiceItemType.GROOMING;
        }
        return ServiceItemType.EVALUATION;
    }

    public AutoMsgDto getAutoMessageInfo(Long companyId, Integer businessId, AutoMessageTemplateEnum type) {
        if (type != AutoMessageTemplateEnum.RECEIPT) {
            return null;
        }
        AutoMessageConfigModel config =
                getAutoMessageConfigList(
                                companyId, businessId.longValue(), List.of(USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID))
                        .stream()
                        .findFirst()
                        .orElse(null);
        if (config == null) {
            return null;
        }
        Map<Long, TemplateModel> templates = getTemplatesByAutoMsgConfig(List.of(config));
        return getAutoMessageInfo(config, templates);
    }

    public AutoMsgDto getAutoMessageInfo(AutoMessageConfigModel config, Map<Long, TemplateModel> templates) {
        TemplateModel email = templates.get(config.getEmailTemplateId());
        TemplateModel sms = templates.get(config.getSmsTemplateId());
        TemplateModel app = templates.get(config.getAppTemplateId());
        return new AutoMsgDto(
                config.getIsEnabled(),
                email == null ? "" : email.getSubject(),
                email == null ? "" : email.getBody(),
                sms == null ? "" : sms.getBody(),
                app == null ? "" : app.getBody());
    }

    public AutoMsgDto getAutoMsgInfoForAppointment(
            AutoMessageTemplateEnum type, GroomingTicketWindowDetailDTO appointmentDto) {
        if (!getUserCaseForAPPTAutoMsg().contains(autoMsgTypeToUseCaseEnum(type))) {
            return null;
        }
        return getAutoMsgInfoForAppointment(autoMsgTypeToUseCaseEnum(type), appointmentDto);
    }

    public AutoMsgDto getAutoMsgInfoForAppointment(
            MessageTemplateUseCase type, GroomingTicketWindowDetailDTO appointmentDto) {
        if (appointmentDto == null) {
            return null;
        }
        AppointmentAutoMsgConfigModel config =
                getAppointmentAutoMsgConfigList(
                                appointmentDto.getCompanyId(),
                                appointmentDto.getBusinessId().longValue(),
                                List.of(type))
                        .stream()
                        .findFirst()
                        .orElse(null);
        if (config == null) {
            return null;
        }
        return getAutoMsgInfoForAppointment(config, appointmentDto);
    }

    public AutoMsgDto getAutoMsgInfoForAppointment(
            AppointmentAutoMsgConfigModel config, GroomingTicketWindowDetailDTO appointmentDto) {
        if (CollectionUtils.isEmpty(config.getTemplatesList())) {
            return null;
        }

        // 获取合适的 service type template
        ServiceTypeConfigModel serviceTypeConfig = getTargetAutoMsgConfig(
                config.getTemplatesList(),
                appointmentDto.getAppointmentDate(),
                appointmentDto.getGroomingPetDetails(),
                appointmentDto.getEvaluationServiceDetails());
        if (serviceTypeConfig == null) {
            return null;
        }
        Map<Long, TemplateModel> templates = getTemplatesByServiceTypeConfig(List.of(serviceTypeConfig));
        TemplateModel email = templates.get(serviceTypeConfig.getEmailTemplateId());
        TemplateModel sms = templates.get(serviceTypeConfig.getSmsTemplateId());
        TemplateModel app = templates.get(serviceTypeConfig.getAppTemplateId());
        return new AutoMsgDto(
                config.getIsEnabled(),
                email == null ? "" : email.getSubject(),
                email == null ? "" : email.getBody(),
                sms == null ? "" : sms.getBody(),
                app == null ? "" : app.getBody());
    }

    // bd 包含多种 care type 的服务，不同 care type 的服务起始时间可能不同。取预约第一天的 service 来选择合适的 service type config
    // evaluation 目前不与其它 care type 同时出现，且一个预约只会有一个 evaluation。无需特殊处理取开始日期的 service
    public ServiceTypeConfigModel getTargetAutoMsgConfig(
            List<ServiceTypeConfigModel> serviceTypeConfigs,
            String startDate,
            List<GroomingPetDetailDTO> petDetails,
            List<EvaluationServiceDetailDTO> evaluationDetails) {
        // 获取预约开始日期的 service 详情
        if (!CollectionUtils.isEmpty(petDetails)) {
            petDetails = petDetails.stream()
                    .filter(k -> {
                        // date point
                        if (StringUtils.hasText(k.getStartDate())) {
                            return k.getStartDate().equals(startDate);
                        }
                        // specific dates
                        if (StringUtils.hasText(k.getSpecificDates())
                                && k.getSpecificDates().length() > 2) {
                            return JsonUtil.toList(k.getSpecificDates(), String.class)
                                    .contains(startDate);
                        }
                        // every day
                        return true;
                    })
                    .toList();
        }
        List<ServiceItemType> serviceItemTypes = collectAndSortServiceTypesByPriority(petDetails, evaluationDetails);

        // 收集 care type 对应的 msg config
        Map<ServiceItemType, ServiceTypeConfigModel> serviceTypeConfigMap = new HashMap<>();
        for (ServiceTypeConfigModel serviceTypeConfig : serviceTypeConfigs) {
            for (ServiceItemType serviceItemType : serviceTypeConfig.getServiceItemTypesList()) {
                serviceTypeConfigMap.put(serviceItemType, serviceTypeConfig);
            }
        }

        // 按 care type 优先级获取合适的 msg config
        for (ServiceItemType serviceItemType : serviceItemTypes) {
            if (serviceTypeConfigMap.containsKey(serviceItemType)) {
                return serviceTypeConfigMap.get(serviceItemType);
            }
        }
        return null;
    }

    // 按优先级返回包含的 service type
    // 优先级：boarding -> daycare -> grooming. evaluation 不与其它 care type 同时出现，不参与比较
    // 如果预约包含指定的 serviceId，则对应 care type 的 reminder 不发.  see https://moego.atlassian.net/browse/MER-3230
    List<ServiceItemType> collectAndSortServiceTypesByPriority(
            List<GroomingPetDetailDTO> petDetails, List<EvaluationServiceDetailDTO> evaluationDetails) {
        if (!CollectionUtils.isEmpty(evaluationDetails)) {
            return Collections.singletonList(ServiceItemType.EVALUATION);
        }

        if (CollectionUtils.isEmpty(petDetails)) {
            return Collections.emptyList();
        }

        // 收集所有服务类型和需要关闭提醒的服务类型
        Map<ServiceItemType, Boolean> serviceTypeStatus = petDetails.stream()
                .filter(e -> Objects.nonNull(e.getServiceItemType()))
                .map(e -> {
                    var type = ServiceItemType.forNumber(e.getServiceItemType());
                    return Map.entry(
                            type != null ? type : ServiceItemType.UNRECOGNIZED,
                            isSendServiceItemReminder(e.getServiceId()));
                })
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey, Collectors.reducing(false, Map.Entry::getValue, Boolean::logicalOr)));

        // 按优先级顺序定义服务类型
        return Stream.of(
                        ServiceItemType.BOARDING,
                        ServiceItemType.DAYCARE,
                        ServiceItemType.GROOMING,
                        ServiceItemType.DOG_WALKING)
                .filter(type -> serviceTypeStatus.containsKey(type) && !serviceTypeStatus.get(type))
                .toList();
    }

    private boolean isSendServiceItemReminder(Integer serviceId) {
        return featureFlagApi.isOn(
                FeatureFlags.CLOSE_AUTO_REMINDER_SERVICE,
                FeatureFlagContext.builder()
                        .attributes(Collections.singletonMap("service", String.valueOf(serviceId)))
                        .build());
    }

    MessageTemplateUseCase autoMsgTypeToUseCaseEnum(AutoMessageTemplateEnum type) {
        return switch (type) {
            case APPOINTMENT_BOOK -> USE_CASE_APPOINTMENT_BOOKED;
            case APPOINTMENT_RESCHEDULED -> USE_CASE_APPOINTMENT_RESCHEDULED;
            case APPOINTMENT_CANCELLED -> USE_CASE_APPOINTMENT_CANCELLED;
            case APPOINTMENT_MOVED_TO_WAIT_LIST -> USE_CASE_APPOINTMENT_MOVED_TO_WAITLIST;
            case READY_PICK_UP -> USE_CASE_READY_FOR_PICK_UP;
            case APPOINTMENT_CONFIRMED_BY_CLIENT -> USE_CASE_APPOINTMENT_CONFIRMED_BY_CLIENT;
            case APPOINTMENT_CANCELLED_BY_CLIENT -> USE_CASE_APPOINTMENT_CANCELLED_BY_CLIENT;
            case RECEIPT -> USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID;
            default -> throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "invalid auto message template type");
        };
    }

    AutoMessageTemplateEnum useCaseEnumToAutoMsgType(MessageTemplateUseCase useCaseEnum) {
        return switch (useCaseEnum) {
            case USE_CASE_APPOINTMENT_BOOKED -> AutoMessageTemplateEnum.APPOINTMENT_BOOK;
            case USE_CASE_APPOINTMENT_RESCHEDULED -> AutoMessageTemplateEnum.APPOINTMENT_RESCHEDULED;
            case USE_CASE_APPOINTMENT_CANCELLED -> AutoMessageTemplateEnum.APPOINTMENT_CANCELLED;
            case USE_CASE_APPOINTMENT_MOVED_TO_WAITLIST -> AutoMessageTemplateEnum.APPOINTMENT_MOVED_TO_WAIT_LIST;
            case USE_CASE_READY_FOR_PICK_UP -> AutoMessageTemplateEnum.READY_PICK_UP;
            case USE_CASE_APPOINTMENT_CONFIRMED_BY_CLIENT -> AutoMessageTemplateEnum.APPOINTMENT_CONFIRMED_BY_CLIENT;
            case USE_CASE_APPOINTMENT_CANCELLED_BY_CLIENT -> AutoMessageTemplateEnum.APPOINTMENT_CANCELLED_BY_CLIENT;
            case USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID -> AutoMessageTemplateEnum.RECEIPT;
            default -> throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "invalid use case");
        };
    }

    AutoMessageTemplateEnum messageTemplateNameToTemplateEnum(String name) {
        return switch (name) {
            case SEND_ETA_TEMPLATE_NAME -> AutoMessageTemplateEnum.ETA;
            case SEND_INVOICE_TEMPLATE_NAME -> AutoMessageTemplateEnum.PAY_ONLINE;
            default -> throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "invalid message template name");
        };
    }

    String getMessageTemplateNameByTemplateEnum(AutoMessageTemplateEnum type) {
        return switch (type) {
            case ETA -> SEND_ETA_TEMPLATE_NAME;
            case PAY_ONLINE -> SEND_INVOICE_TEMPLATE_NAME;
            default -> throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "invalid auto message template type");
        };
    }

    public List<MessageTemplateUseCase> getUserCaseForAPPTAutoMsg() {
        return List.of(
                USE_CASE_APPOINTMENT_BOOKED,
                USE_CASE_APPOINTMENT_RESCHEDULED,
                USE_CASE_APPOINTMENT_CANCELLED,
                USE_CASE_APPOINTMENT_MOVED_TO_WAITLIST,
                USE_CASE_APPOINTMENT_CONFIRMED_BY_CLIENT,
                USE_CASE_APPOINTMENT_CANCELLED_BY_CLIENT,
                USE_CASE_READY_FOR_PICK_UP);
    }

    public List<MessageTemplateUseCase> getUserCaseForAutoMsg() {
        return List.of(USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID);
    }

    List<MoeBusinessAutoMessageTemplate> apptAutoMsgConfigToAutoMsgTemplate(
            List<AppointmentAutoMsgConfigModel> configList) {
        Map<Long, TemplateModel> templates = getTemplatesByAppointmentConfig(configList);

        List<MoeBusinessAutoMessageTemplate> result = new ArrayList<>();
        for (AppointmentAutoMsgConfigModel config : configList) {
            ServiceTypeConfigModel template = getMainServiceTypeConfig(config.getTemplatesList());
            // 产品限制，正常不会出现 一个 template 都没有的情况
            if (template == null) {
                throw ExceptionUtil.bizException(
                        Code.CODE_FORBIDDEN,
                        "Unsupported. Please upgrade your app to the latest version and try again.");
            }

            String smsBody = templates.get(template.getSmsTemplateId()).getBody();
            MoeBusinessAutoMessageTemplate messageTemplate = new MoeBusinessAutoMessageTemplate();
            messageTemplate.setId((int) config.getId());
            messageTemplate.setBusinessId((int) config.getBusinessId());
            messageTemplate.setCompanyId(config.getCompanyId());
            messageTemplate.setType(
                    useCaseEnumToAutoMsgType(config.getUseCase()).getValue());
            messageTemplate.setBody(smsBody);
            messageTemplate.setStatus(config.getIsEnabled() ? 1 : 2);
            result.add(messageTemplate);
        }
        return result;
    }

    List<MoeBusinessAutoMessageTemplate> autoMessageConfigToAutoMsgTemplate(List<AutoMessageConfigModel> configList) {
        Map<Long, TemplateModel> templates = getTemplatesByAutoMsgConfig(configList);

        List<MoeBusinessAutoMessageTemplate> result = new ArrayList<>();
        for (AutoMessageConfigModel config : configList) {
            String smsBody = templates.get(config.getSmsTemplateId()).getBody();
            MoeBusinessAutoMessageTemplate messageTemplate = new MoeBusinessAutoMessageTemplate();
            messageTemplate.setId((int) config.getId());
            messageTemplate.setBusinessId((int) config.getBusinessId());
            messageTemplate.setCompanyId(config.getCompanyId());
            messageTemplate.setType(
                    useCaseEnumToAutoMsgType(config.getUseCase()).getValue());
            messageTemplate.setBody(smsBody);
            messageTemplate.setStatus(config.getIsEnabled() ? 1 : 2);
            result.add(messageTemplate);
        }
        return result;
    }

    public Map<Long, TemplateModel> getTemplatesByAppointmentConfig(
            List<AppointmentAutoMsgConfigModel> autoMsgConfigs) {
        return getTemplates(getTemplateIDsByAppointmentConfig(autoMsgConfigs));
    }

    public Map<Long, TemplateModel> getTemplatesByServiceTypeConfig(List<ServiceTypeConfigModel> serviceTypeConfigs) {
        return getTemplates(getTemplateIDsByServiceTypeConfig(serviceTypeConfigs));
    }

    public Map<Long, TemplateModel> getTemplatesByAutoMsgConfig(List<AutoMessageConfigModel> configs) {
        return getTemplates(getTemplateIDsByAutoMsgConfig(configs));
    }

    // 兼容老接口。将 saved reply 中两条系统模板转换为 auto message 模板
    List<MoeBusinessAutoMessageTemplate> msgTemplateToAutoMsgTemplate(List<MessageTemplateSimpleView> views) {
        List<MoeBusinessAutoMessageTemplate> result = new ArrayList<>();
        for (MessageTemplateSimpleView view : views) {
            MoeBusinessAutoMessageTemplate messageTemplate = new MoeBusinessAutoMessageTemplate();
            messageTemplate.setId((int) view.getId());
            messageTemplate.setBusinessId((int) view.getBusinessId());
            messageTemplate.setCompanyId(view.getCompanyId());
            messageTemplate.setType(
                    messageTemplateNameToTemplateEnum(view.getTemplateName()).getValue());
            messageTemplate.setBody(view.getTemplateContent());
            messageTemplate.setStatus(1);
            result.add(messageTemplate);
        }
        return result;
    }

    // auto message 重构后， ETA、Pay Online 两个模板挪到了 saved reply 中，不再使用 auto message config
    List<MessageTemplateSimpleView> getMessageTemplateForOldAutoMsgs(Integer businessId) {
        List<MessageTemplateSimpleView> result = messageTemplateClient
                .getMessageTemplates(GetMessageTemplatesRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setWithSystem(true)
                        .build())
                .getMessageTemplateSimpleViewsList();
        Set<String> namesForOldAutoMsg = Set.of(SEND_ETA_TEMPLATE_NAME, SEND_INVOICE_TEMPLATE_NAME);
        return result.stream()
                .filter(k -> k.getIsSystem() && namesForOldAutoMsg.contains(k.getTemplateName()))
                .toList();
    }

    // 老接口中， reminder、appointment auto message  sms、email 共用一份模板
    Pair<TemplateModel, TemplateModel> buildSMSAndEmailTemplate(String emailSubject, String body) {
        List<BatchCreateTemplateItemDef> templatesToCreate = new ArrayList<>();
        templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                .setSeqNum(0)
                .setBody(body)
                .build());
        templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                .setSeqNum(1)
                .setSubject(emailSubject)
                .setBody(body)
                .build());
        Map<Long, TemplateModel> templatesCreated = templateClient
                .batchCreateTemplates(BatchCreateTemplatesRequest.newBuilder()
                        .addAllTemplates(templatesToCreate)
                        .build())
                .getTemplatesMap();
        return Pair.of(templatesCreated.get(0L), templatesCreated.get(1L));
    }

    public ServiceTypeConfigDefList buildServiceTypeConfigDef(ServiceTypeConfigModel existConfig, String newBody) {
        Map<Long, TemplateModel> templates = getTemplatesByServiceTypeConfig(List.of(existConfig));
        String emailSubject = templates.get(existConfig.getEmailTemplateId()).getSubject();

        Pair<TemplateModel, TemplateModel> newTemplates = buildSMSAndEmailTemplate(emailSubject, newBody);
        return ServiceTypeConfigDefList.newBuilder()
                .addAllValues(List.of(ServiceTypeConfigDef.newBuilder()
                        .addAllServiceItemTypes(existConfig.getServiceItemTypesList())
                        .setEmailTemplateId(newTemplates.getSecond().getId())
                        .setSmsTemplateId(newTemplates.getFirst().getId())
                        .build()))
                .build();
    }

    public List<AppointmentAutoMsgConfigModel> getAppointmentAutoMsgConfigList(
            Long companyId, Long businessId, List<MessageTemplateUseCase> enumList) {
        if (CollectionUtils.isEmpty(enumList)) {
            return new ArrayList<>();
        }
        var request = GetAppointmentAutoMsgConfigListRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
                        .addAllValues(enumList)
                        .build())
                .build();
        return autoMessageConfigClient.getAppointmentAutoMsgConfigList(request).getAutoMessagesList();
    }

    List<AutoMessageConfigModel> getAutoMessageConfigList(
            Long companyId, Long businessId, List<MessageTemplateUseCase> enumList) {
        if (CollectionUtils.isEmpty(enumList)) {
            return new ArrayList<>();
        }
        var request = GetAutoMessageConfigListRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
                        .addAllValues(enumList)
                        .build())
                .build();
        return autoMessageConfigClient.getAutoMessageConfigList(request).getAutoMessagesList();
    }

    public Map<Long, List<AutoMessageConfigModel>> batchGetAutoMessageByBids(
            List<Long> businessIds, List<MessageTemplateUseCase> enumList) {
        if (CollectionUtils.isEmpty(businessIds) || CollectionUtils.isEmpty(enumList)) {
            return new HashMap<>();
        }
        var request = GetAutoMessageConfigByBusinessIdsRequest.newBuilder()
                .addAllBusinessIds(businessIds)
                .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
                        .addAllValues(enumList)
                        .build())
                .build();

        return autoMessageConfigClient.getAutoMessageConfigByBusinessIds(request).getAutoMessagesList().stream()
                .collect(Collectors.groupingBy(AutoMessageConfigModel::getBusinessId));
    }

    public Map<Long, List<AppointmentAutoMsgConfigModel>> batchGetAppointmentAutoMsgByBids(
            List<Long> businessIds, List<MessageTemplateUseCase> enumList) {
        if (CollectionUtils.isEmpty(businessIds) || CollectionUtils.isEmpty(enumList)) {
            return new HashMap<>();
        }
        var request = GetAppointmentAutoMsgConfigByBusinessIdsRequest.newBuilder()
                .addAllBusinessIds(businessIds)
                .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
                        .addAllValues(enumList)
                        .build())
                .build();
        return autoMessageConfigClient.getAppointmentAutoMsgConfigByBusinessIds(request).getAutoMessagesList().stream()
                .collect(Collectors.groupingBy(AppointmentAutoMsgConfigModel::getBusinessId));
    }

    public List<Long> getTemplateIDsByAppointmentConfig(List<AppointmentAutoMsgConfigModel> autoMsgConfigs) {
        if (CollectionUtils.isEmpty(autoMsgConfigs)) {
            return new ArrayList<>();
        }
        List<Long> templateIds = new ArrayList<>();
        for (AppointmentAutoMsgConfigModel config : autoMsgConfigs) {
            templateIds.addAll(getTemplateIDsByServiceTypeConfig(config.getTemplatesList()));
        }
        return templateIds;
    }

    public List<Long> getTemplateIDsByServiceTypeConfig(List<ServiceTypeConfigModel> serviceTypeConfigs) {
        if (CollectionUtils.isEmpty(serviceTypeConfigs)) {
            return new ArrayList<>();
        }
        List<Long> templateIds = new ArrayList<>();
        for (ServiceTypeConfigModel template : serviceTypeConfigs) {
            templateIds.add(template.getSmsTemplateId());
            templateIds.add(template.getEmailTemplateId());
            templateIds.add(template.getAppTemplateId());
        }
        return templateIds;
    }

    public List<Long> getTemplateIDsByAutoMsgConfig(List<AutoMessageConfigModel> configs) {
        if (CollectionUtils.isEmpty(configs)) {
            return new ArrayList<>();
        }
        List<Long> templateIds = new ArrayList<>();
        for (AutoMessageConfigModel config : configs) {
            templateIds.add(config.getSmsTemplateId());
            templateIds.add(config.getEmailTemplateId());
            templateIds.add(config.getAppTemplateId());
        }
        return templateIds;
    }

    public Map<Long, TemplateModel> getTemplates(List<Long> ids) {
        ids = Optional.ofNullable(ids).orElse(List.of()).stream()
                .filter(k -> k > 0)
                .toList();
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        int batchSize = 300;
        Map<Long, TemplateModel> result = new HashMap<>();
        for (int i = 0; i < ids.size(); i += batchSize) {
            List<Long> subList = ids.subList(i, Math.min(i + batchSize, ids.size()));
            var request = MGetTemplatesRequest.newBuilder().addAllIds(subList).build();
            var resp = templateClient.mGetTemplates(request);
            result.putAll(resp.getTemplatesList().stream()
                    .collect(Collectors.toMap(
                            TemplateModel::getId, smsTemplateModel -> smsTemplateModel, (k1, k2) -> k1)));
        }
        return result;
    }
}
