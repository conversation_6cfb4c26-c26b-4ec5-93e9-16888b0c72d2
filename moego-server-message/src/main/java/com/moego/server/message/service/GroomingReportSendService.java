package com.moego.server.message.service;

import static com.moego.common.enums.groomingreport.GroomingReportConst.DEFAULT_CAT_AVATAR;
import static com.moego.common.enums.groomingreport.GroomingReportConst.DEFAULT_DOG_AVATAR;
import static com.moego.lib.featureflag.features.FeatureFlags.ALLOW_GROOMING_REPORT_MULTI_PHOTO;

import com.microtripit.mandrillapp.lutung.view.MandrillMessage;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReport;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportCardSummaryInfo;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportContent;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportQuestion;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportRecommendation;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportSendRecordSync;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportTemplate;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportThemeConfig;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportUniqueKey;
import com.moego.backend.proto.fulfillment.v1.GetFulfillmentReportRequest;
import com.moego.backend.proto.fulfillment.v1.GetFulfillmentReportSummaryInfoRequest;
import com.moego.backend.proto.fulfillment.v1.SendMethod;
import com.moego.backend.proto.fulfillment.v1.SendRecordIdentifier;
import com.moego.backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordRequest;
import com.moego.backend.proto.fulfillment.v1.SyncFulfillmentReportSendRecordResponse;
import com.moego.backend.proto.fulfillment.v1.SyncOperation;
import com.moego.backend.proto.fulfillment.v1.SyncStatus;
import com.moego.backend.proto.offering.v1.CareCategory;
import com.moego.common.enums.PetTypeEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.groomingreport.GroomingReportCategoryEnum;
import com.moego.common.enums.groomingreport.GroomingReportConst;
import com.moego.common.enums.groomingreport.GroomingReportQuestionTypeEnum;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PageUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerPrimaryDto;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.client.IGroomingGroomingReportClient;
import com.moego.server.grooming.dto.appointment.history.SendNotificationLogDTO;
import com.moego.server.grooming.dto.groomingreport.BodyViewUrl;
import com.moego.server.grooming.dto.groomingreport.GroomingRecommendation;
import com.moego.server.grooming.dto.groomingreport.GroomingReportInfoDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportPreviewDataDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSettingDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportSummaryInfoDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportThemeConfigDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.NotificationTypeEnum;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportSendLogsByPageParams;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportSummaryInfoParams;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.grooming.params.groomingreport.GroomingReportPreviewParams;
import com.moego.server.grooming.params.groomingreport.UpdateGroomingReportStatusParams;
import com.moego.server.message.dto.FulfillmentReportSendResultDTO;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import com.moego.server.message.dto.GroomingReportSendParams;
import com.moego.server.message.dto.GroomingReportSendPreviewParams;
import com.moego.server.message.enums.MessageDetailEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.mapper.MoeGroomingReportSendLogMapper;
import com.moego.server.message.mapperbean.MoeBusinessMessageDetail;
import com.moego.server.message.mapperbean.MoeGroomingReportSendLog;
import com.moego.server.message.mapstruct.ReportSendLogConverter;
import com.moego.server.message.params.MailSendParams;
import com.moego.server.message.params.SendMessageCustomerParams;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.service.dto.model.GroomingReportEmailModel;
import com.moego.server.message.service.sendmessage.MessageSendRouterService;
import com.moego.server.message.service.third.MandrillService;
import com.moego.server.message.service.util.BusinessInfoHelper;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import freemarker.template.Template;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

@Service
@Slf4j
public class GroomingReportSendService {

    @Autowired
    private MoeGroomingReportSendLogMapper moeGroomingReportSendLogMapper;

    @Autowired
    private MessageSendRouterService messageSendRouterService;

    @Autowired
    private MandrillService mandrillService;

    @Autowired
    private ICustomerCustomerClient iCustomerCustomerClient;

    @Autowired
    private IGroomingGroomingReportClient iGroomingReportClient;

    // 模板引擎
    @Autowired
    private FreeMarkerConfigurer configurer;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    @Value("${moego.grooming-report.book-again-url}")
    private String bookAgainUrl;

    @Value("${moego.grooming-report.client-url}")
    private String groomingReportClientUrl;

    public static final String GROOMING_REPORT_SMS_TEMPLATE =
            "{PetName}’s {Title} at {BusinessName} {DirectAccessLink}";

    public static final String GROOMING_REPORT_SMS_RESEND_TEMPLATE =
            "[Updated] {PetName}’s {Title} at {BusinessName} {DirectAccessLink}";

    public static final String GROOMING_REPORT_EMAIL_SUBJECT = "{PetName}’s Grooming Report at {BusinessName}";

    public static final String GROOMING_REPORT_EMAIL_RESEND_SUBJECT = "[Updated] {PetName}’s {Title} at {BusinessName}";

    @Autowired
    private FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentReportService;

    @Autowired
    private IGroomingAppointmentClient iGroomingAppointmentClient;

    @Autowired
    private FeatureFlagApi featureFlagApi;

    @Autowired
    private FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentReportServiceBlockingStub;

    /**
     * 查询预约的 Grooming report 每种 sending method 的最近一条发送记录
     *
     * @param businessId businessId
     * @param groomingId 预约id
     * @return 发送记录
     */
    public List<GroomingReportSendLogDTO> getGroomingLastReportSendLogs(Integer businessId, Integer groomingId) {
        return moeGroomingReportSendLogMapper.selectLastSendRecordsByGroomingId(businessId, groomingId).stream()
                .map(log -> {
                    GroomingReportSendLogDTO dto = new GroomingReportSendLogDTO();
                    BeanUtils.copyProperties(log, dto);
                    return dto;
                })
                .toList();
    }

    public Map<Integer, List<GroomingReportSendLogDTO>> getGroomingLastReportSendLogsMap(GroomingIdListParams params) {
        if (CollectionUtils.isEmpty(params.groomingIdList())) {
            return Map.of();
        }
        List<MoeGroomingReportSendLog> sendLogList =
                moeGroomingReportSendLogMapper.selectLastSendRecordsByGroomingIdList(
                        params.businessId(), params.groomingIdList());
        return sendLogList.stream()
                .map(log -> {
                    GroomingReportSendLogDTO dto = new GroomingReportSendLogDTO();
                    BeanUtils.copyProperties(log, dto);
                    return dto;
                })
                .collect(Collectors.groupingBy(GroomingReportSendLogDTO::getGroomingId));
    }

    /**
     * 前端获取 GR SMS/Email 发送内容
     *
     * @param businessId businessId
     * @param reportId reportId
     * @param sendingMethod sendingMethod: 1-sms, 2-email
     * @return 发送内容
     */
    public String getGroomingReportSendContent(Integer businessId, Integer reportId, Byte sendingMethod) {
        List<GroomingReportSummaryInfoDTO> reportInfos = iGroomingReportClient.getGroomingReportSummaryInfoList(
                new GetGroomingReportSummaryInfoParams(businessId, List.of(reportId)));
        if (CollectionUtils.isEmpty(reportInfos)) {
            throw ExceptionUtil.bizException(Code.CODE_GROOMING_REPORT_NOT_AVAILABLE);
        }

        GroomingReportSummaryInfoDTO reportInfo = reportInfos.get(0);
        return switch (sendingMethod) {
            case GroomingReportConst.SEND_BY_EMAIL -> buildEmailSubject(null, reportInfo);
            case GroomingReportConst.SEND_BY_SMS -> buildSmsSendContent(reportInfo);
            default -> null;
        };
    }

    /**
     * 发送 Grooming Report preview email
     *
     * @param businessId
     * @param params
     */
    public void sendGroomingReportPreviewEmail(Integer businessId, GroomingReportSendPreviewParams params) {
        GroomingReportPreviewDataDTO previewDataDTO = iGroomingReportClient.getGroomingReportPreviewData(
                businessId,
                params.getPreviewParams() != null ? params.getPreviewParams() : new GroomingReportPreviewParams());
        GroomingReportSummaryInfoDTO reportSummaryInfo = previewDataDTO.getReportSummary();

        CustomerPrimaryDto customerInfo = new CustomerPrimaryDto();
        customerInfo.setEmail(params.getRecipientEmail());
        customerInfo.setFirstName("Demo");
        customerInfo.setLastName("Customer");

        sendGroomingReportByEmail(businessId, params.getEmailSubject(), customerInfo, reportSummaryInfo);
    }

    /**
     * 批量发送 Grooming report
     *
     * @param businessId businessId
     * @param staffId    发送人 staffId
     * @param params     发送参数
     */
    @Deprecated
    public void sendGroomingReports(Integer businessId, Integer staffId, GroomingReportSendParams params) {
        // 查询 report summary info
        List<GroomingReportSummaryInfoDTO> reportSummaryInfoList =
                iGroomingReportClient.getGroomingReportSummaryInfoList(
                        new GetGroomingReportSummaryInfoParams(businessId, params.getReportIdList()));
        if (CollectionUtils.isEmpty(reportSummaryInfoList)) {
            return;
        }
        // 前端指定发送方式，如无指定则查 setting 的发送方式
        List<Byte> sendingMethodList;
        if (CollectionUtils.isEmpty(params.getSendingMethodList())) {
            GroomingReportSettingDTO settingDTO = iGroomingReportClient.getGroomingReportSetting(businessId);
            sendingMethodList = settingDTO.getSendingMethodList();
            if (CollectionUtils.isEmpty(sendingMethodList)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "no sending method selected");
            }
        } else {
            sendingMethodList = params.getSendingMethodList();
        }

        List<MoeGroomingReportSendLog> sendLogs = reportSummaryInfoList.stream()
                .map(report -> sendOneGroomingReport(businessId, staffId, report, sendingMethodList))
                .flatMap(Collection::stream)
                .toList();

        List<Integer> sentReportIds = sendLogs.stream()
                .map(MoeGroomingReportSendLog::getReportId)
                .distinct()
                .toList();
        // 存 send log
        if (!CollectionUtils.isEmpty(sendLogs)) {
            // 设置成相同的发送时间
            Date now = new Date();
            Long companyId = businessInfoHelper.getCompanyIdByBusinessId(businessId);
            sendLogs.forEach(log -> {
                log.setSentTime(now);
                log.setCompanyId(companyId);
            });
            moeGroomingReportSendLogMapper.batchInsert(sendLogs);
            // 数据双写 -> fulfillment_report_send_record
            CompletableFuture.runAsync(
                    () -> {
                        try {
                            sendLogs.forEach(sendLog -> {
                                SyncFulfillmentReportSendRecordResponse response =
                                        upsertFulfillmentReportSendRecord(sendLog);
                                log.info(
                                        "Double write: Batch insert fulfillment report send record, status: {}, recordId: {}, reportId: {}, errorMessage: {}",
                                        response.getStatus(),
                                        response.getRecordId(),
                                        response.getReportId(),
                                        response.getErrorMessage());
                            });
                        } catch (Exception e) {
                            log.error(
                                    "Double write: Failed to batch insert fulfillment report send record for businessId: {}, logIds: {}",
                                    businessId,
                                    sendLogs.stream()
                                            .map(MoeGroomingReportSendLog::getId)
                                            .toList(),
                                    e);
                        }
                    },
                    ThreadPool.getSubmitExecutor());
        }
        // 更新 GR 状态为 Sent
        if (!CollectionUtils.isEmpty(sentReportIds)) {
            iGroomingReportClient.updateGroomingReportSentStatus(
                    new UpdateGroomingReportStatusParams(businessId, staffId, sentReportIds));
        }

        int failedCount = 0;
        for (MoeGroomingReportSendLog sendLog : sendLogs) {
            // 如有失败的发送记录，抛出异常
            if (sendLog.getStatus() != 0) {
                failedCount++;
            }
        }
        if (failedCount > 0) {
            String errorMessage = failedCount == 1 ? "Report send failed" : failedCount + " reports send failed";
            if (reportSummaryInfoList.stream()
                    .anyMatch(report ->
                            Objects.equals(report.getReportInfo().getStatus(), GroomingReportStatusEnum.sent.name()))) {
                errorMessage = errorMessage.replace("send", "resend");
            }
            throw ExceptionUtil.bizException(Code.CODE_GROOMING_REPORT_SEND_FAILED, errorMessage);
        }
    }

    /**
     * 发送单个 Grooming report
     *
     * @param businessId         businessId
     * @param staffId            发送人 staffId
     * @param reportSummaryInfo  report summary info
     * @param sendingMethodList  发送方式列表，可以多种方式发送
     * @return 发送记录
     */
    @Deprecated
    private List<MoeGroomingReportSendLog> sendOneGroomingReport(
            Integer businessId,
            Integer staffId,
            GroomingReportSummaryInfoDTO reportSummaryInfo,
            List<Byte> sendingMethodList) {
        GroomingReportInfoDTO reportInfo = reportSummaryInfo.getReportInfo();
        if (reportInfo == null || (Objects.equals(reportInfo.getStatus(), GroomingReportStatusEnum.created.name()))) {
            log.warn("grooming report not draft or submitted: " + JsonUtil.toJson(reportSummaryInfo));
            return List.of();
        }

        return sendingMethodList.stream()
                .map(sendingMethod -> switch (sendingMethod) {
                    case GroomingReportConst.SEND_BY_SMS -> sendGroomingReportBySms(
                            businessId, staffId, reportSummaryInfo);
                    case GroomingReportConst.SEND_BY_EMAIL -> sendGroomingReportByEmail(
                            businessId, staffId, reportSummaryInfo);
                    default -> null;
                })
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 通过 sms 发送
     *
     * @param businessId        businessId
     * @param staffId           发送人 staffId
     * @param reportSummaryInfo report summary info
     */
    private MoeGroomingReportSendLog sendGroomingReportBySms(
            Integer businessId, Integer staffId, GroomingReportSummaryInfoDTO reportSummaryInfo) {
        MoeGroomingReportSendLog sendLog = buildSendLog(businessId, staffId, reportSummaryInfo.getReportInfo());
        sendLog.setSendingMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue().byteValue());
        try {
            SendMessagesParams sendMessagesParams = new SendMessagesParams();
            sendMessagesParams.setBusinessId(businessId);
            sendMessagesParams.setStaffId(staffId);
            SendMessageCustomerParams customerParams = new SendMessageCustomerParams();
            customerParams.setCustomerId(reportSummaryInfo.getReportInfo().getCustomerId());
            sendMessagesParams.setCustomer(customerParams);
            sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
            sendMessagesParams.setTargetType(MessageTargetTypeEnums.GROOMING_REPORT.getValue());
            sendMessagesParams.setTargetId(reportSummaryInfo.getReportInfo().getGroomingId());

            String messageBody = buildSmsSendContent(reportSummaryInfo);
            sendMessagesParams.setMessageBody(messageBody);
            // 复用 auto message 发送代码
            MoeBusinessMessageDetail messageDetail =
                    messageSendRouterService.sendServicesMessageToCustomer(sendMessagesParams);

            sendLog.setMsgId(messageDetail.getId());

            ActivityLogRecorder.record(
                    businessId,
                    staffId,
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    reportSummaryInfo.getGroomingInfo().getAppointmentId(),
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_MSG, true, "", NotificationTypeEnum.GROOMING_REPORT));
        } catch (CommonException exception) {
            // 捕获异常后只记录到 sendLog, 在外层统一处理
            String failedReason = messageSendRouterService.processErrorMsg(exception.getCode());
            sendLog.setStatus((byte) 1);
            sendLog.setErrorCode(exception.getCode());
            sendLog.setErrorMsg(failedReason);
            ActivityLogRecorder.record(
                    businessId,
                    staffId,
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    reportSummaryInfo.getGroomingInfo().getAppointmentId(),
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_MSG,
                            false,
                            failedReason,
                            NotificationTypeEnum.GROOMING_REPORT));
        }
        return sendLog;
    }

    @Deprecated
    private MoeGroomingReportSendLog sendGroomingReportByEmail(
            Integer businessId, Integer staffId, GroomingReportSummaryInfoDTO reportSummaryInfo) {
        MoeGroomingReportSendLog sendLog = buildSendLog(businessId, staffId, reportSummaryInfo.getReportInfo());
        sendLog.setSendingMethod(
                MessageDetailEnum.MESSAGE_METHOD_EMAIL.getValue().byteValue());
        try {
            // 这里不挪到外层原因：循环次数不会很多，并且发送 msg 内部也有查询，挪到外面就重复了，后续再优化
            CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
            customerInfoIdParams.setBusinessId(businessId);
            customerInfoIdParams.setCustomerId(reportSummaryInfo.getReportInfo().getCustomerId());
            CustomerPrimaryDto customerDetailWithPrimary =
                    iCustomerCustomerClient.getCustomerDetailWithPrimary(customerInfoIdParams);

            if (!StringUtils.hasText(customerDetailWithPrimary.getEmail())) {
                throw new CommonException(ResponseCodeEnum.MESSAGE_SEND_EMAIL_IS_NULL);
            }

            sendGroomingReportByEmail(businessId, null, customerDetailWithPrimary, reportSummaryInfo);
            ActivityLogRecorder.record(
                    businessId,
                    staffId,
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    reportSummaryInfo.getGroomingInfo().getAppointmentId(),
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_EMAIL,
                            true,
                            "",
                            NotificationTypeEnum.GROOMING_REPORT));
        } catch (CommonException exception) {
            String failedReason = messageSendRouterService.processErrorMsg(exception.getCode());
            sendLog.setStatus((byte) 1);
            sendLog.setErrorCode(exception.getCode());
            sendLog.setErrorMsg(failedReason);
            ActivityLogRecorder.record(
                    businessId,
                    staffId,
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    reportSummaryInfo.getGroomingInfo().getAppointmentId(),
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_APP,
                            false,
                            failedReason,
                            NotificationTypeEnum.GROOMING_REPORT));
        }
        return sendLog;
    }

    public FulfillmentReportSendResultDTO sendFulfillmentGroomingReportByEmail(
            Long reportId,
            Integer businessId,
            Long companyId,
            Long staffId,
            List<String> recipientEmailList,
            String subject) {

        FulfillmentReportCardSummaryInfo reportSummaryInfo = null;
        try {
            reportSummaryInfo = fulfillmentReportServiceBlockingStub
                    .getFulfillmentReportSummaryInfo(GetFulfillmentReportSummaryInfoRequest.newBuilder()
                            .setFulfillmentReportId(reportId)
                            .setBusinessId(businessId.longValue())
                            .setCompanyId(companyId)
                            .build())
                    .getSummaryInfo();
            CustomerPrimaryDto customerDetailWithPrimary = new CustomerPrimaryDto();
            boolean isPreview = isPreviewReport(reportSummaryInfo.getFulfillmentReport());
            if (isPreview) {
                customerDetailWithPrimary.setFirstName("Demo");
                customerDetailWithPrimary.setLastName("Customer");
                if (recipientEmailList.isEmpty()) {
                    throw new CommonException(ResponseCodeEnum.MESSAGE_SEND_EMAIL_IS_NULL);
                }
            } else {
                CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
                customerInfoIdParams.setBusinessId(businessId);
                customerInfoIdParams.setCustomerId(
                        Math.toIntExact(reportSummaryInfo.getFulfillmentReport().getCustomerId()));
                customerDetailWithPrimary = iCustomerCustomerClient.getCustomerDetailWithPrimary(customerInfoIdParams);
                if (!StringUtils.hasText(customerDetailWithPrimary.getEmail()) && recipientEmailList.isEmpty()) {
                    throw new CommonException(ResponseCodeEnum.MESSAGE_SEND_EMAIL_IS_NULL);
                }
            }

            sendFulfillmentGroomingReportByEmail(
                    businessId, subject, customerDetailWithPrimary, reportSummaryInfo, recipientEmailList);
            // preview report 不记录 activity log
            if (!isPreview) {
                ActivityLogRecorder.record(
                        businessId,
                        staffId,
                        AppointmentAction.SEND_NOTIFICATION,
                        ResourceType.APPOINTMENT,
                        reportSummaryInfo.getAppointmentInfo().getAppointmentId(),
                        new SendNotificationLogDTO(
                                MessageMethodTypeEnum.MESSAGE_METHOD_EMAIL,
                                true,
                                "",
                                NotificationTypeEnum.GROOMING_REPORT));
            }
            return new FulfillmentReportSendResultDTO(
                    reportSummaryInfo.getFulfillmentReport().getId(), true, "");
        } catch (CommonException exception) {
            if (reportSummaryInfo == null) {
                return new FulfillmentReportSendResultDTO(reportId, false, "Report not found.");
            }
            String failedReason = processSendReportErrorMsg(exception.getCode());
            // preview report 不记录 activity log
            if (!isPreviewReport(reportSummaryInfo.getFulfillmentReport())) {
                ActivityLogRecorder.record(
                        businessId,
                        staffId,
                        AppointmentAction.SEND_NOTIFICATION,
                        ResourceType.APPOINTMENT,
                        reportSummaryInfo.getAppointmentInfo().getAppointmentId(),
                        new SendNotificationLogDTO(
                                MessageMethodTypeEnum.MESSAGE_METHOD_APP,
                                false,
                                failedReason,
                                NotificationTypeEnum.GROOMING_REPORT));
            }
            return new FulfillmentReportSendResultDTO(
                    reportSummaryInfo.getFulfillmentReport().getId(), false, failedReason);
        }
    }

    // 判断 report 是否为 template report（preview）
    private boolean isPreviewReport(FulfillmentReport report) {
        if (report == null) {
            return false;
        }
        // preview report 的 appointmentId、petId 为 0
        return !CommonUtil.isNormal(report.getAppointmentId()) || !CommonUtil.isNormal(report.getPetId());
    }

    private String processSendReportErrorMsg(Integer errorCode) {
        String sendFailedReason = messageSendRouterService.processErrorMsg(errorCode);
        if (!Strings.isEmpty(sendFailedReason)) {
            return sendFailedReason;
        }
        return "Failed to send report card.";
    }

    /**
     * 通过 email 发送
     *
     * @param businessId        businessId
     * @param emailSubject      email subject
     * @param customerInfo      顾客信息
     * @param reportSummaryInfo report summary info
     */
    @Deprecated
    private void sendGroomingReportByEmail(
            Integer businessId,
            String emailSubject,
            CustomerPrimaryDto customerInfo,
            GroomingReportSummaryInfoDTO reportSummaryInfo) {
        Template template;
        String html;
        boolean isMultiPhoto = false;
        Long companyId = businessInfoHelper.getCompanyIdByBusinessIdV2(businessId);
        if (featureFlagApi.isOn(
                ALLOW_GROOMING_REPORT_MULTI_PHOTO,
                FeatureFlagContext.builder().company(companyId).build())) {
            isMultiPhoto = true;
        }
        try {
            template = configurer.getConfiguration().getTemplate("groomingReport.ftl");
            GroomingReportEmailModel model = buildGroomingReportModel(reportSummaryInfo);
            //            log.info("send email model: {}", JsonUtil.toJson(model));
            model.setIsMultiPhoto(isMultiPhoto);
            html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        } catch (Exception e) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, e.getMessage());
        }

        // 发送邮件
        MailSendParams mailSendParams = new MailSendParams();
        mailSendParams.setSubject(buildEmailSubject(emailSubject, reportSummaryInfo));
        String businessName = reportSummaryInfo.getBusinessInfo().getBusinessName();
        mailSendParams.setFrom_name(businessName);
        mailSendParams.setHtml(html);

        // 设置发送人
        List<MandrillMessage.Recipient> to = new ArrayList<>();
        MandrillMessage.Recipient recipient = new MandrillMessage.Recipient();

        log.info("receiver email {}", customerInfo.getEmail());
        recipient.setEmail(customerInfo.getEmail());
        recipient.setName(customerInfo.getFirstName() + " " + customerInfo.getLastName());
        recipient.setType(MandrillMessage.Recipient.Type.TO);

        to.add(recipient);
        mailSendParams.setTo(to);
        mailSendParams.setBusinessId(businessId);

        mandrillService.sendEmail(mailSendParams);
    }

    private void sendFulfillmentGroomingReportByEmail(
            Integer businessId,
            String emailSubject,
            CustomerPrimaryDto customerInfo,
            FulfillmentReportCardSummaryInfo reportSummaryInfo,
            List<String> recipientEmailList) {
        Template template;
        String html;
        boolean isMultiPhoto = false;
        Long companyId = businessInfoHelper.getCompanyIdByBusinessIdV2(businessId);
        if (featureFlagApi.isOn(
                ALLOW_GROOMING_REPORT_MULTI_PHOTO,
                FeatureFlagContext.builder().company(companyId).build())) {
            isMultiPhoto = true;
        }
        try {
            template = configurer.getConfiguration().getTemplate("groomingReport.ftl");
            GroomingReportSummaryInfoDTO groomingSummaryInfo = convertToGroomingReportSummaryInfoDTO(reportSummaryInfo);
            GroomingReportEmailModel model = buildGroomingReportModel(groomingSummaryInfo);
            //            log.info("send email model: {}", JsonUtil.toJson(model));
            model.setIsMultiPhoto(isMultiPhoto);
            html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
        } catch (Exception e) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, e.getMessage());
        }

        // 发送邮件
        MailSendParams mailSendParams = new MailSendParams();
        if (StringUtils.hasText(emailSubject)) {
            mailSendParams.setSubject(emailSubject);
        } else {
            mailSendParams.setSubject(buildGroomingEmailSubject(emailSubject, reportSummaryInfo));
        }
        String businessName = reportSummaryInfo.getBusinessInfo().getBusinessName();
        mailSendParams.setFrom_name(businessName);
        mailSendParams.setHtml(html);

        // 设置发送人
        List<MandrillMessage.Recipient> to = new ArrayList<>();
        MandrillMessage.Recipient recipient = new MandrillMessage.Recipient();

        recipient.setName(customerInfo.getFirstName() + " " + customerInfo.getLastName());
        recipient.setType(MandrillMessage.Recipient.Type.TO);

        to.add(recipient);
        mailSendParams.setTo(to);
        mailSendParams.setBusinessId(businessId);

        if (!ObjectUtils.isEmpty(recipientEmailList)) {
            recipientEmailList.forEach(email -> {
                log.info("receiver email {}", email);
                recipient.setEmail(email);
                mandrillService.sendEmail(mailSendParams);
            });
        } else {
            log.info("receiver email {}", customerInfo.getEmail());
            recipient.setEmail(customerInfo.getEmail());
            mandrillService.sendEmail(mailSendParams);
        }
    }

    public void updateGroomingReportSendLogStatus(MoeBusinessMessageDetail messageDetail) {
        MoeGroomingReportSendLog sendLog = moeGroomingReportSendLogMapper.selectByBusinessIdAndMsgId(
                messageDetail.getBusinessId(), messageDetail.getId());
        if (sendLog == null) {
            return;
        }

        MoeGroomingReportSendLog update = new MoeGroomingReportSendLog();
        update.setId(sendLog.getId());
        update.setStatus((byte) 1);
        update.setErrorCode(messageDetail.getErrorCode());
        update.setErrorMsg(messageSendRouterService.processErrorMsg(messageDetail.getErrorCode()));

        moeGroomingReportSendLogMapper.updateByPrimaryKeySelective(update);
        // 数据双写 -> fulfillment_report_send_record
        CompletableFuture.runAsync(
                () -> {
                    try {
                        MoeGroomingReportSendLog reportSendLog =
                                moeGroomingReportSendLogMapper.selectByBusinessIdAndMsgId(
                                        messageDetail.getBusinessId(), messageDetail.getId());
                        SyncFulfillmentReportSendRecordResponse response =
                                upsertFulfillmentReportSendRecord(reportSendLog);
                        log.info(
                                "Double write: Update fulfillment report send record, status: {}, recordId: {}, reportId: {}, errorMessage: {}",
                                response.getStatus(),
                                response.getRecordId(),
                                response.getReportId(),
                                response.getErrorMessage());
                    } catch (Exception e) {
                        log.error(
                                "Double write: Failed to update fulfillment report send record for businessId: {}",
                                messageDetail.getBusinessId(),
                                e);
                    }
                },
                ThreadPool.getSubmitExecutor());
    }

    /**
     * 构建 SMS 发送内容
     *
     * @param reportSummaryInfo report summary info
     * @return sms send content
     */
    private String buildSmsSendContent(GroomingReportSummaryInfoDTO reportSummaryInfo) {
        GroomingReportSummaryInfoDTO.BusinessInfo businessInfo = reportSummaryInfo.getBusinessInfo();
        GroomingReportSummaryInfoDTO.PetInfo petInfo = reportSummaryInfo.getPetInfo();
        GroomingReportSummaryInfoDTO.GroomingInfo groomingInfo = reportSummaryInfo.getGroomingInfo();

        String directLink = String.format(
                groomingReportClientUrl, reportSummaryInfo.getReportInfo().getUuid());
        boolean isResend =
                Objects.equals(reportSummaryInfo.getReportInfo().getStatus(), GroomingReportStatusEnum.sent.name());
        String title = reportSummaryInfo.getReportInfo().getTemplate().getTitle();
        if (!StringUtils.hasText(title)) {
            title = GroomingReportConst.DEFAULT_TITLE;
        }
        String sendContent = isResend ? GROOMING_REPORT_SMS_RESEND_TEMPLATE : GROOMING_REPORT_SMS_TEMPLATE;
        sendContent = sendContent.replace("{BusinessName}", businessInfo.getBusinessName());
        sendContent = sendContent.replace("{PetName}", petInfo.getPetName());
        sendContent = sendContent.replace("{Title}", title);
        sendContent = sendContent.replace(
                "{MainStaff}",
                groomingInfo.getPetServiceDetails().stream()
                        .flatMap(detail -> detail.getServiceDetails().stream())
                        .map(GroomingReportSummaryInfoDTO.ServiceDetailInfo::getStaffFirstName)
                        .collect(Collectors.joining(", ")));
        sendContent = sendContent.replace("{DirectAccessLink}", directLink);
        return sendContent;
    }

    /**
     * 组装 email template 所需字段
     *
     * @param reportSummaryInfo report summary info
     * @return grooming report email model
     */
    private GroomingReportEmailModel buildGroomingReportModel(GroomingReportSummaryInfoDTO reportSummaryInfo) {
        GroomingReportSummaryInfoDTO.BusinessInfo businessInfo = reportSummaryInfo.getBusinessInfo();
        GroomingReportSummaryInfoDTO.GroomingInfo groomingInfo = reportSummaryInfo.getGroomingInfo();
        GroomingReportSummaryInfoDTO.GroomingInfo nextGroomingInfo = reportSummaryInfo.getNextGroomingInfo();
        GroomingReportSummaryInfoDTO.PetInfo petInfo = reportSummaryInfo.getPetInfo();
        GroomingReportInfoDTO reportInfo = reportSummaryInfo.getReportInfo();

        GroomingReportEmailModel model = new GroomingReportEmailModel();
        BeanUtils.copyProperties(reportSummaryInfo, model);

        // theme config
        if (reportSummaryInfo.getThemeConfig() != null
                && StringUtils.hasText(reportSummaryInfo.getThemeConfig().getColor())
                && StringUtils.hasText(reportSummaryInfo.getThemeConfig().getLightColor())) {
            reportInfo
                    .getTemplate()
                    .setThemeColor(reportSummaryInfo.getThemeConfig().getColor());
            reportInfo
                    .getTemplate()
                    .setLightThemeColor(reportSummaryInfo.getThemeConfig().getLightColor());
        }

        // curGroomingInfo
        GroomingReportEmailModel.GroomingInfoModel curGroomingInfoModel =
                new GroomingReportEmailModel.GroomingInfoModel();
        curGroomingInfoModel.setPetServiceDetails(groomingInfo.getPetServiceDetails());
        curGroomingInfoModel.setAppointmentDateTimeText(
                buildAppointmentDateTimeText(groomingInfo, businessInfo, false));
        model.setCurGroomingInfo(curGroomingInfoModel);

        if (petInfo != null && !StringUtils.hasText(petInfo.getAvatarPath())) {
            petInfo.setAvatarPath(
                    !PetTypeEnum.DOG.getType().equals(petInfo.getPetTypeId())
                            ? DEFAULT_CAT_AVATAR
                            : DEFAULT_DOG_AVATAR);
        }

        List<GroomingReportEmailModel.QuestionModel> customizedFeedbacks = new ArrayList<>();

        // report info content 需要额外处理，单独设置 key-value
        if (reportInfo.getContent() != null && reportInfo.getContent().getFeedbacks() != null) {
            for (GroomingReportInfoDTO.GroomingReportQuestion question :
                    reportInfo.getContent().getFeedbacks()) {
                switch (question.getKey()) {
                    case GroomingReportConst.QUESTION_KEY_OVERALL_FEEDBACK,
                            GroomingReportConst.QUESTION_KEY_HOW_DI_IT_GO -> model.setFeedback(
                            convertQuestionModel(question));
                    case GroomingReportConst.QUESTION_KEY_COMMENT,
                            GroomingReportConst.QUESTION_KEY_ADDITIONAL_NOTE -> model.setComment(
                            convertQuestionModel(question));
                    case GroomingReportConst.QUESTION_KEY_MOOD -> model.setMood(convertQuestionModel(question));
                    default -> {}
                }

                if (question.getCategory().equals(GroomingReportCategoryEnum.CATEGORY_CUSTOMIZED_FEEDBACK.getType())) {
                    GroomingReportEmailModel.QuestionModel questionModel = convertQuestionModel(question);
                    // answer 为空的 question 不展示
                    if (!questionModel.getAnswer().isEmpty()) {
                        customizedFeedbacks.add(questionModel);
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(customizedFeedbacks)) {
            model.setCustomizedFeedbacks(customizedFeedbacks);
        }

        boolean hasShownPetConditions = false;
        if (reportInfo.getContent() != null && reportInfo.getContent().getPetConditions() != null) {
            List<GroomingReportEmailModel.QuestionModel> questionsExceptBodyView = new ArrayList<>();
            GroomingReportEmailModel.QuestionModel bodyViewQuestion = null;
            // 是否有需要展示的 pet condition
            for (GroomingReportInfoDTO.GroomingReportQuestion question :
                    reportInfo.getContent().getPetConditions()) {
                // show = false 不展示
                if (Boolean.FALSE.equals(question.getShow())) {
                    continue;
                }
                GroomingReportEmailModel.QuestionModel questionModel = convertQuestionModel(question);
                if (!GroomingReportQuestionTypeEnum.isBodyViewQuestion(question.getType())) {
                    questionsExceptBodyView.add(questionModel);
                } else {
                    bodyViewQuestion = questionModel;
                }
                if (Boolean.TRUE.equals(question.getShow())) {
                    hasShownPetConditions = true;
                }
            }
            model.setPetConditions(questionsExceptBodyView);
            if (bodyViewQuestion != null) {
                model.setBodyView(bodyViewQuestion);
            }
        }
        model.setHasShownPetConditions(hasShownPetConditions);

        boolean showDateOnly = Objects.equals(
                reportInfo.getTemplate().getNextAppointmentDateFormatType(),
                GroomingReportConst.APPOINTMENT_SHOW_ONLY_DATE);

        GroomingReportEmailModel.RecommendationModel recommendationModel =
                new GroomingReportEmailModel.RecommendationModel();
        if (reportInfo.getContent() != null && reportInfo.getContent().getRecommendation() != null) {
            GroomingRecommendation recommendation = reportInfo.getContent().getRecommendation();
            recommendationModel.setNextAppointmentDate(
                    buildAppointmentDateTimeText(nextGroomingInfo, businessInfo, showDateOnly));
            recommendationModel.setFrequencyText(recommendation.getFrequencyText());

            // next appointment date 过期，发送时隐藏 next appointment date，book again button
            String nextAppointmentDate = recommendation.getNextAppointmentDate();
            if (isBeforeToday(nextAppointmentDate)) {
                reportInfo.getTemplate().setShowNextAppointment(false);
            }

            if (Boolean.TRUE.equals(businessInfo.getBookOnlineEnable())) {
                recommendationModel.setBookAgainURL(
                        String.format(bookAgainUrl, businessInfo.getBookOnlineName(), reportInfo.getId()));
                model.setIsOBEnable(true);
            } else {
                recommendationModel.setBookAgainURL("");
                model.setIsOBEnable(false);
            }
        } else {
            // 默认值
            recommendationModel.setFrequencyText(null);
            recommendationModel.setNextAppointmentDate(null);
            recommendationModel.setBookAgainURL(null);
            model.setIsOBEnable(false);
        }
        model.setRecommendation(recommendationModel);
        return model;
    }

    private GroomingReportEmailModel.QuestionModel convertQuestionModel(
            GroomingReportInfoDTO.GroomingReportQuestion question) {
        GroomingReportEmailModel.QuestionModel questionModel = new GroomingReportEmailModel.QuestionModel();
        BeanUtils.copyProperties(question, questionModel);
        if (StringUtils.hasText(question.getText())) {
            questionModel.setTextList(List.of(question.getText().split("\n")));
        } else {
            questionModel.setTextList(List.of(""));
        }

        List<String> allChoices = new ArrayList<>();
        if (question.getChoices() != null) {
            allChoices.addAll(question.getChoices());
        }
        if (question.getCustomOptions() != null) {
            allChoices.addAll(question.getCustomOptions());
        }
        questionModel.setAllChoices(allChoices.stream().distinct().toList());

        questionModel.setQuestion(question.getTitle());

        // 新增 answer 字段处理
        String answer = "";
        switch (GroomingReportQuestionTypeEnum.valueOf(question.getType())) {
            case single_choice, multi_choice, tag_choice: {
                if (!CollectionUtils.isEmpty(question.getChoices())) {
                    answer = String.join("\n", question.getChoices());
                }
                break;
            }
            case text_input, short_text_input: {
                answer = question.getText() != null ? question.getText() : "";
                break;
            }
            default:
                answer = "";
        }
        questionModel.setAnswer(answer);

        return questionModel;
    }

    /**
     * 构建 email subject
     *
     * @param subject           前端指定 email subject
     * @param reportSummaryInfo report summary info
     * @return email subject
     */
    private String buildEmailSubject(String subject, GroomingReportSummaryInfoDTO reportSummaryInfo) {
        boolean isResend =
                Objects.equals(reportSummaryInfo.getReportInfo().getStatus(), GroomingReportStatusEnum.sent.name());
        if (!StringUtils.hasText(subject)) {
            subject = isResend ? GROOMING_REPORT_EMAIL_RESEND_SUBJECT : GROOMING_REPORT_EMAIL_SUBJECT;
        }
        String businessName = reportSummaryInfo.getBusinessInfo().getBusinessName();
        String petName = reportSummaryInfo.getPetInfo().getPetName();
        String title = reportSummaryInfo.getReportInfo().getTemplate().getTitle();
        if (!StringUtils.hasText(title)) {
            title = GroomingReportConst.DEFAULT_TITLE;
        }
        subject = subject.replace("{BusinessName}", businessName);
        subject = subject.replace("{PetName}", petName);
        subject = subject.replace("{Title}", title);
        subject = subject.replace(
                "{MainStaff}",
                reportSummaryInfo.getGroomingInfo().getPetServiceDetails().stream()
                        .flatMap(petServiceDetail -> petServiceDetail.getServiceDetails().stream())
                        .map(GroomingReportSummaryInfoDTO.ServiceDetailInfo::getStaffFirstName)
                        .collect(Collectors.joining(", ")));
        return subject;
    }

    private String buildGroomingEmailSubject(String subject, FulfillmentReportCardSummaryInfo reportSummaryInfo) {
        boolean isResend = GroomingReportStatusEnum.sent
                .name()
                .equalsIgnoreCase(
                        reportSummaryInfo.getFulfillmentReport().getStatus().toString());
        if (!StringUtils.hasText(subject)) {
            subject = isResend ? GROOMING_REPORT_EMAIL_RESEND_SUBJECT : GROOMING_REPORT_EMAIL_SUBJECT;
        }
        String businessName = reportSummaryInfo.getBusinessInfo().getBusinessName();
        String petName = reportSummaryInfo.getPetInfo().getPetName();
        String title = reportSummaryInfo.getFulfillmentReport().getTemplate().getTitle();
        if (!StringUtils.hasText(title)) {
            title = GroomingReportConst.DEFAULT_TITLE;
        }
        subject = subject.replace("{BusinessName}", businessName);
        subject = subject.replace("{PetName}", petName);
        subject = subject.replace("{Title}", title);
        subject = subject.replace(
                "{MainStaff}",
                reportSummaryInfo.getAppointmentInfo().getPetServiceList().stream()
                        .flatMap(petService -> petService.getPetDetailsList().stream())
                        .map(petServiceDetail -> petServiceDetail.getStaffInfo().getStaffFirstName())
                        .collect(Collectors.joining(", ")));
        return subject;
    }

    /**
     * 构建 send log
     *
     * @param businessId businessId
     * @param staffId    staffId
     * @param reportInfo reportInfo
     * @return MoeGroomingReportSendLog
     */
    private MoeGroomingReportSendLog buildSendLog(
            Integer businessId, Integer staffId, GroomingReportInfoDTO reportInfo) {
        MoeGroomingReportSendLog sendLog = new MoeGroomingReportSendLog();
        sendLog.setBusinessId(businessId);
        sendLog.setReportId(reportInfo.getId());
        sendLog.setGroomingId(reportInfo.getGroomingId());
        sendLog.setPetId(reportInfo.getPetId());
        sendLog.setSendingType(GroomingReportConst.SENDING_MANUALLY);
        sendLog.setSentBy(staffId);
        sendLog.setStatus((byte) 0);
        sendLog.setMsgId(0);
        return sendLog;
    }

    /**
     * 判断日期是否在今天之前，因为是 yyyy-MM-dd，所以和今天的0点对比
     *
     * @param dateString date string
     * @return true if before today
     */
    private boolean isBeforeToday(String dateString) {
        Date date = DateUtil.parseDate(dateString, DateUtil.STANDARD_DATE);
        if (date != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date today = calendar.getTime();
            return date.before(today);
        }
        return false;
    }

    /**
     * 将FulfillmentReportCardSummaryInfo转换为GroomingReportSummaryInfoDTO
     *
     * @param fulfillmentSummaryInfo FulfillmentReportCardSummaryInfo
     * @return GroomingReportSummaryInfoDTO
     */
    private GroomingReportSummaryInfoDTO convertToGroomingReportSummaryInfoDTO(
            FulfillmentReportCardSummaryInfo fulfillmentSummaryInfo) {
        if (fulfillmentSummaryInfo == null) {
            return null;
        }

        GroomingReportSummaryInfoDTO dto = new GroomingReportSummaryInfoDTO();

        // 转换 BusinessInfo
        if (fulfillmentSummaryInfo.hasBusinessInfo()) {
            dto.setBusinessInfo(convertBusinessInfo(fulfillmentSummaryInfo.getBusinessInfo()));
        }

        // 转换 PetInfo
        if (fulfillmentSummaryInfo.hasPetInfo()) {
            dto.setPetInfo(convertPetInfo(fulfillmentSummaryInfo.getPetInfo()));
        }

        // 转换 AppointmentInfo 为 GroomingInfo
        if (fulfillmentSummaryInfo.hasAppointmentInfo()) {
            dto.setGroomingInfo(convertAppointmentInfoToGroomingInfo(fulfillmentSummaryInfo.getAppointmentInfo()));
        }

        // 转换 NextAppointmentInfo 为 NextGroomingInfo
        if (fulfillmentSummaryInfo.hasNextAppointmentInfo()) {
            dto.setNextGroomingInfo(
                    convertAppointmentInfoToGroomingInfo(fulfillmentSummaryInfo.getNextAppointmentInfo()));
        }

        // 转换 FulfillmentReport 为 ReportInfo
        if (fulfillmentSummaryInfo.hasFulfillmentReport()) {
            dto.setReportInfo(convertFulfillmentReportToReportInfo(fulfillmentSummaryInfo.getFulfillmentReport()));
        }

        // 转换 ReviewBoosterConfig
        if (fulfillmentSummaryInfo.hasReviewBoosterConfig()) {
            dto.setReviewBoosterConfig(convertReviewBoosterConfig(fulfillmentSummaryInfo.getReviewBoosterConfig()));
        }

        // 转换 ReviewBoosterRecord
        if (fulfillmentSummaryInfo.hasReviewBoosterRecord()) {
            dto.setReviewBoosterRecord(convertReviewBoosterRecord(fulfillmentSummaryInfo.getReviewBoosterRecord()));
        }

        // 转换 ThemeConfig
        if (fulfillmentSummaryInfo.hasThemeConfig()) {
            dto.setThemeConfig(convertThemeConfig(fulfillmentSummaryInfo.getThemeConfig()));
        }

        // 转换 PresetTags
        if (fulfillmentSummaryInfo.getPresetTagsCount() > 0) {
            dto.setPresetTags(new ArrayList<>(fulfillmentSummaryInfo.getPresetTagsList()));
        }

        return dto;
    }

    /**
     * 转换BusinessInfo
     */
    private GroomingReportSummaryInfoDTO.BusinessInfo convertBusinessInfo(
            FulfillmentReportCardSummaryInfo.BusinessInfo businessInfo) {
        if (businessInfo == null) {
            return null;
        }

        GroomingReportSummaryInfoDTO.BusinessInfo dto = new GroomingReportSummaryInfoDTO.BusinessInfo();
        dto.setBusinessName(businessInfo.getBusinessName());
        dto.setAvatarPath(businessInfo.getAvatarPath());
        dto.setPhoneNumber(businessInfo.getPhoneNumber());
        dto.setBusinessMode((byte) businessInfo.getBusinessMode());
        dto.setAddress1(businessInfo.getAddress1());
        dto.setAddress2(businessInfo.getAddress2());
        dto.setAddressCity(businessInfo.getAddressCity());
        dto.setAddressState(businessInfo.getAddressState());
        dto.setAddressZipcode(businessInfo.getAddressZipcode());
        dto.setAddressCountry(businessInfo.getAddressCountry());

        // 处理坐标信息 - 注意字段名是addressLat和addressLng
        if (businessInfo.hasCoordinate()) {
            dto.setAddressLat(String.valueOf(businessInfo.getCoordinate().getLatitude()));
            dto.setAddressLng(String.valueOf(businessInfo.getCoordinate().getLongitude()));
        }

        dto.setDateFormat(businessInfo.getDateFormat());
        dto.setTimeFormatType((byte) businessInfo.getTimeFormatType());
        dto.setBookOnlineName(businessInfo.getBookOnlineName());
        dto.setBookOnlineEnable(businessInfo.getBookOnlineEnable());

        return dto;
    }

    /**
     * 转换PetInfo
     */
    private GroomingReportSummaryInfoDTO.PetInfo convertPetInfo(FulfillmentReportCardSummaryInfo.PetInfo petInfo) {
        if (petInfo == null) {
            return null;
        }

        GroomingReportSummaryInfoDTO.PetInfo dto = new GroomingReportSummaryInfoDTO.PetInfo();
        dto.setPetId((int) petInfo.getPetId());
        dto.setPetTypeId(petInfo.getPetType().getNumber());
        dto.setPetName(petInfo.getPetName());
        dto.setAvatarPath(petInfo.getAvatarPath());
        dto.setPetBreed(petInfo.getPetBreed());

        dto.setGender(petInfo.getGender().getNumber());
        dto.setGenderText(petInfo.getGender().name());

        // 转换宠物类型枚举
        if (petInfo.getPetType().getNumber() != 0) {
            dto.setPetTypeId(petInfo.getPetType().getNumber());
        }

        dto.setWeight(petInfo.getWeight());
        dto.setWeightWithUnit(petInfo.getWeightWithUnit());

        return dto;
    }

    /**
     * 转换AppointmentInfo为GroomingInfo
     */
    private GroomingReportSummaryInfoDTO.GroomingInfo convertAppointmentInfoToGroomingInfo(
            FulfillmentReportCardSummaryInfo.AppointmentInfo appointmentInfo) {
        if (appointmentInfo == null) {
            return null;
        }

        GroomingReportSummaryInfoDTO.GroomingInfo dto = new GroomingReportSummaryInfoDTO.GroomingInfo();
        dto.setAppointmentId((int) appointmentInfo.getAppointmentId());

        // 转换状态
        if (appointmentInfo.getState().getNumber() != 0) {
            dto.setStatus((byte) appointmentInfo.getState().getNumber());
        }

        dto.setAppointmentDate(appointmentInfo.getAppointmentDate());
        dto.setAppointmentDateTimeText(appointmentInfo.getAppointmentDateTimeText());
        dto.setAppointmentStartTime(appointmentInfo.getAppointmentStartTime());
        dto.setAppointmentEndTime(appointmentInfo.getAppointmentEndTime());
        dto.setArrivalBeforeStartTime(appointmentInfo.getArrivalWindowBefore());
        dto.setArrivalAfterStartTime(appointmentInfo.getArrivalWindowAfter());

        // 转换PetService列表为PetServiceDetailInfo列表
        if (appointmentInfo.getPetServiceCount() > 0) {
            List<GroomingReportSummaryInfoDTO.PetServiceDetailInfo> petServiceDetails = new ArrayList<>();
            for (FulfillmentReportCardSummaryInfo.PetService petService : appointmentInfo.getPetServiceList()) {
                GroomingReportSummaryInfoDTO.PetServiceDetailInfo petServiceDetail =
                        new GroomingReportSummaryInfoDTO.PetServiceDetailInfo();

                // 设置宠物信息
                if (petService.hasPetInfo()) {
                    FulfillmentReportCardSummaryInfo.PetInfo petInfo = petService.getPetInfo();
                    petServiceDetail.setPetId((int) petInfo.getPetId());
                    petServiceDetail.setPetName(petInfo.getPetName());
                    if (petInfo.getPetType().getNumber() != 0) {
                        petServiceDetail.setPetTypeId(petInfo.getPetType().getNumber());
                    }
                    petServiceDetail.setPetAvatarPath(petInfo.getAvatarPath());
                }

                // 转换服务详情列表
                if (petService.getPetDetailsCount() > 0) {
                    List<GroomingReportSummaryInfoDTO.ServiceDetailInfo> serviceDetails = new ArrayList<>();
                    for (FulfillmentReportCardSummaryInfo.PetDetailInfo petDetail : petService.getPetDetailsList()) {
                        GroomingReportSummaryInfoDTO.ServiceDetailInfo serviceDetail =
                                new GroomingReportSummaryInfoDTO.ServiceDetailInfo();

                        serviceDetail.setServiceId((int) petDetail.getServiceId());
                        serviceDetail.setServiceName(petDetail.getServiceName());
                        serviceDetail.setServiceType(petDetail.getServiceType());
                        serviceDetail.setStartTime(petDetail.getStartTime());
                        serviceDetail.setServiceDuration(petDetail.getServiceDuration());
                        serviceDetail.setPetId((int) petDetail.getPetId());

                        // 设置员工信息
                        if (petDetail.hasStaffInfo()) {
                            FulfillmentReportCardSummaryInfo.StaffInfo staffInfo = petDetail.getStaffInfo();
                            serviceDetail.setStaffId((int) staffInfo.getStaffId());
                            serviceDetail.setStaffFirstName(staffInfo.getStaffFirstName());
                            serviceDetail.setStaffLastName(staffInfo.getStaffLastName());
                            serviceDetail.setStaffAvatarPath(staffInfo.getStaffAvatarPath());
                        }

                        serviceDetails.add(serviceDetail);
                    }
                    petServiceDetail.setServiceDetails(serviceDetails);
                }

                petServiceDetails.add(petServiceDetail);
            }
            dto.setPetServiceDetails(petServiceDetails);
        }

        return dto;
    }

    /**
     * 转换FulfillmentReport为ReportInfo
     */
    private GroomingReportInfoDTO convertFulfillmentReportToReportInfo(FulfillmentReport fulfillmentReport) {
        if (fulfillmentReport == null) {
            return null;
        }

        GroomingReportInfoDTO dto = new GroomingReportInfoDTO();

        // 基本字段转换
        if (fulfillmentReport.hasId()) {
            dto.setId((int) fulfillmentReport.getId());
        }
        dto.setCompanyId(fulfillmentReport.getCompanyId());
        dto.setBusinessId(Math.toIntExact(fulfillmentReport.getBusinessId()));
        dto.setGroomingId((int) fulfillmentReport.getAppointmentId());
        dto.setPetId((int) fulfillmentReport.getPetId());
        dto.setPetTypeId(Math.toIntExact(fulfillmentReport.getPetTypeId()));
        dto.setTemplatePublishTime(fulfillmentReport.getTemplateVersion().getSeconds());
        dto.setCustomerId((int) fulfillmentReport.getCustomerId());
        dto.setStatus(fulfillmentReport.getStatus().name().toLowerCase());
        dto.setUuid(fulfillmentReport.getUuid());
        dto.setLinkOpenedCount(fulfillmentReport.getLinkOpenedCount());
        dto.setThemeCode(fulfillmentReport.getThemeCode());

        // 转换时间字段
        if (fulfillmentReport.hasCreateTime()) {
            dto.setCreateTime(fulfillmentReport.getCreateTime().getSeconds());
        }
        if (fulfillmentReport.hasUpdateTime()) {
            dto.setUpdateTime(fulfillmentReport.getUpdateTime().getSeconds());
        }

        // 转换Template
        if (fulfillmentReport.hasTemplate()) {
            dto.setTemplate(convertFulfillmentReportTemplate(fulfillmentReport.getTemplate()));
        }

        // 转换Content
        if (fulfillmentReport.hasContent()) {
            dto.setContent(convertFulfillmentReportContent(fulfillmentReport.getContent()));
        }

        return dto;
    }

    /**
     * 转换FulfillmentReportTemplate
     */
    private GroomingReportInfoDTO.GroomingReportTemplate convertFulfillmentReportTemplate(
            FulfillmentReportTemplate template) {
        if (template == null) {
            return null;
        }

        GroomingReportInfoDTO.GroomingReportTemplate dto = new GroomingReportInfoDTO.GroomingReportTemplate();
        dto.setTitle(template.getTitle());
        dto.setThemeColor(template.getThemeColor());
        dto.setLightThemeColor(template.getLightThemeColor());
        dto.setThemeCode(template.getThemeCode());
        dto.setThankYouMessage(template.getThankYouMessage());
        dto.setShowShowcase(template.getShowShowcase());
        dto.setShowOverallFeedback(template.getShowOverallFeedback());
        dto.setShowPetCondition(template.getShowPetCondition());
        dto.setShowServiceStaffName(template.getShowStaff());
        dto.setShowNextAppointment(template.getShowNextAppointment());

        // 转换枚举类型
        if (template.getNextAppointmentDateFormatType().getNumber() != 0) {
            dto.setNextAppointmentDateFormatType(
                    (byte) template.getNextAppointmentDateFormatType().getNumber());
        }

        dto.setShowReviewBooster(template.getShowReviewBooster());
        dto.setShowYelpReview(template.getShowYelpReview());
        dto.setYelpReviewLink(template.getYelpReviewLink());
        dto.setShowGoogleReview(template.getShowGoogleReview());
        dto.setGoogleReviewLink(template.getGoogleReviewLink());
        dto.setShowFacebookReview(template.getShowFacebookReview());
        dto.setFacebookReviewLink(template.getFacebookReviewLink());

        return dto;
    }

    /**
     * 转换FulfillmentReportContent
     */
    private GroomingReportInfoDTO.GroomingReportContent convertFulfillmentReportContent(
            FulfillmentReportContent content) {
        if (content == null) {
            return null;
        }

        GroomingReportInfoDTO.GroomingReportContent dto = new GroomingReportInfoDTO.GroomingReportContent();

        // 转换照片和视频列表
        dto.setShowcase(new ArrayList<>(content.getPhotosList()));

        // 转换反馈问题列表
        if (content.getFeedbacksCount() > 0) {
            List<GroomingReportInfoDTO.GroomingReportQuestion> feedbacks = new ArrayList<>();
            for (FulfillmentReportQuestion question : content.getFeedbacksList()) {
                feedbacks.add(convertFulfillmentReportQuestion(question));
            }
            dto.setFeedbacks(feedbacks);
        }

        // 转换宠物状况问题列表
        if (content.getPetConditionsCount() > 0) {
            List<GroomingReportInfoDTO.GroomingReportQuestion> petConditions = new ArrayList<>();
            for (FulfillmentReportQuestion question : content.getPetConditionsList()) {
                petConditions.add(convertFulfillmentReportQuestion(question));
            }
            dto.setPetConditions(petConditions);
        }

        // 转换推荐信息
        if (content.hasRecommendation()) {
            dto.setRecommendation(convertFulfillmentReportRecommendation(content.getRecommendation()));
        }

        return dto;
    }

    /**
     * 转换FulfillmentReportQuestion
     */
    private GroomingReportInfoDTO.GroomingReportQuestion convertFulfillmentReportQuestion(
            FulfillmentReportQuestion question) {
        if (question == null) {
            return null;
        }

        GroomingReportInfoDTO.GroomingReportQuestion dto = new GroomingReportInfoDTO.GroomingReportQuestion();
        dto.setId((int) question.getId());
        dto.setKey(question.getKey());
        dto.setTitle(question.getTitle());
        dto.setText(question.getInputText());
        dto.setType(question.getType().name().toLowerCase());
        dto.setCategory((byte) question.getCategory().getNumber());
        dto.setShow(question.getIsShow());
        dto.setRequired(question.getRequired());

        // 转换选项列表
        dto.setChoices(new ArrayList<>(question.getChoicesList()));
        dto.setCustomOptions(new ArrayList<>(question.getCustomOptionsList()));
        dto.setPlaceholder(question.getPlaceholder());
        dto.setUrls(
                new BodyViewUrl(question.getUrls().getLeft(), question.getUrls().getRight()));

        return dto;
    }

    /**
     * 转换FulfillmentReportRecommendation
     */
    private GroomingRecommendation convertFulfillmentReportRecommendation(
            FulfillmentReportRecommendation recommendation) {
        if (recommendation == null) {
            return null;
        }

        GroomingRecommendation dto = new GroomingRecommendation();
        dto.setFrequencyDay(recommendation.getFrequencyDay());
        dto.setFrequencyType((byte) recommendation.getFrequencyType().getNumber());
        dto.setFrequencyText(recommendation.getFrequencyText());

        return dto;
    }

    /**
     * 转换ReviewBoosterConfig
     */
    private GroomingReportSummaryInfoDTO.ReviewBoosterConfig convertReviewBoosterConfig(
            FulfillmentReportCardSummaryInfo.ReviewBoosterConfig config) {
        if (config == null) {
            return null;
        }

        GroomingReportSummaryInfoDTO.ReviewBoosterConfig dto = new GroomingReportSummaryInfoDTO.ReviewBoosterConfig();
        dto.setPositiveScore(config.getPositiveScore());
        dto.setPositiveYelp(config.getPositiveYelp());
        dto.setPositiveFacebook(config.getPositiveFacebook());
        dto.setPositiveGoogle(config.getPositiveGoogle());

        return dto;
    }

    /**
     * 转换ReviewBoosterRecord
     */
    private GroomingReportSummaryInfoDTO.ReviewBoosterRecord convertReviewBoosterRecord(
            FulfillmentReportCardSummaryInfo.ReviewBoosterRecord record) {
        if (record == null) {
            return null;
        }

        GroomingReportSummaryInfoDTO.ReviewBoosterRecord dto = new GroomingReportSummaryInfoDTO.ReviewBoosterRecord();
        dto.setPositiveScore(record.getPositiveScore());
        dto.setReviewContent(record.getReviewContent());
        dto.setReviewTime(record.getReviewTime());

        return dto;
    }

    /**
     * 转换ThemeConfig
     */
    private GroomingReportThemeConfigDTO convertThemeConfig(FulfillmentReportThemeConfig themeConfig) {
        if (themeConfig == null) {
            return null;
        }

        GroomingReportThemeConfigDTO dto = new GroomingReportThemeConfigDTO();
        dto.setName(themeConfig.getName());
        dto.setCode(themeConfig.getCode());
        dto.setColor(themeConfig.getColor());
        dto.setLightColor(themeConfig.getLightColor());
        dto.setImgUrl(themeConfig.getImgUrl());
        dto.setIcon(themeConfig.getIcon());
        dto.setEmailBottomImgUrl(themeConfig.getEmailBottomImgUrl());
        dto.setRecommend(themeConfig.getRecommend());
        dto.setStatus((byte) themeConfig.getStatus().getNumber());
        dto.setTag((byte) themeConfig.getTag().getNumber());

        return dto;
    }

    private static String buildAppointmentDateTimeText(
            GroomingReportSummaryInfoDTO.GroomingInfo groomingInfo,
            GroomingReportSummaryInfoDTO.BusinessInfo businessInfo,
            boolean showDateOnly) {
        if (groomingInfo == null || businessInfo == null) {
            return null;
        }
        String appointmentDateText =
                DateUtil.dateToBusinessFormat(groomingInfo.getAppointmentDate(), businessInfo.getDateFormat());
        String appointmentDateTimeText;
        if (showDateOnly) {
            // 只展示 date
            appointmentDateTimeText = appointmentDateText;
        } else {
            // Arrival window
            if (Objects.nonNull(groomingInfo.getArrivalBeforeStartTime())
                    && Objects.nonNull(groomingInfo.getArrivalAfterStartTime())
                    && !groomingInfo.getArrivalBeforeStartTime().equals(groomingInfo.getArrivalAfterStartTime())) {
                appointmentDateTimeText = appointmentDateText + ", arrive between: "
                        + DateUtil.formatArrivalWindowTime(
                                groomingInfo.getArrivalBeforeStartTime(),
                                groomingInfo.getArrivalAfterStartTime(),
                                businessInfo.getTimeFormatType());
            } else {
                // date + time
                appointmentDateTimeText = DateUtil.getApptDateAndTimeStr(
                        groomingInfo.getAppointmentDate(),
                        groomingInfo.getAppointmentStartTime(),
                        businessInfo.getDateFormat(),
                        businessInfo.getTimeFormatType());
            }
        }
        return appointmentDateTimeText;
    }

    public List<GroomingReportSendLogDTO> getGroomingReportSendLogsByPage(
            GetGroomingReportSendLogsByPageParams params) {
        var result = PageUtil.selectPage(
                params.getPagination(), () -> moeGroomingReportSendLogMapper.listSendLogs(params.getBusinessId()));

        return result.getFirst().stream()
                .map(log -> {
                    GroomingReportSendLogDTO dto = new GroomingReportSendLogDTO();
                    BeanUtils.copyProperties(log, dto);
                    return dto;
                })
                .toList();
    }

    public Integer countGroomingReportSendLogs(Integer businessId) {
        return moeGroomingReportSendLogMapper.countGroomingReportSendLogs(businessId);
    }

    /**
     * 数据双写：同步 fulfillment_report_send_record
     */
    private SyncFulfillmentReportSendRecordResponse upsertFulfillmentReportSendRecord(
            MoeGroomingReportSendLog updateLog) {
        log.info(
                "Double write: FulfillmentReportSendRecord: companyId: {}, businessId: {}, appointmentId: {}, petId: {}",
                updateLog.getCompanyId(),
                updateLog.getBusinessId(),
                updateLog.getGroomingId(),
                updateLog.getPetId());

        // 查询 fulfillment report
        FulfillmentReport fulfillmentReport = fulfillmentReportService
                .getFulfillmentReport(GetFulfillmentReportRequest.newBuilder()
                        .setCompanyId(updateLog.getCompanyId())
                        .setBusinessId(updateLog.getBusinessId())
                        .setAppointmentId(updateLog.getGroomingId())
                        .setPetId(updateLog.getPetId())
                        .setCareType(CareCategory.GROOMING)
                        .setServiceDate("")
                        .build())
                .getFulfillmentReport();
        if (fulfillmentReport.getId() <= 0) {
            return SyncFulfillmentReportSendRecordResponse.newBuilder()
                    .setStatus(SyncStatus.FAILED)
                    .setErrorMessage("Fulfillment report not found")
                    .build();
        }
        // 构建 fulfillment report send record
        FulfillmentReportSendRecordSync sendLog =
                ReportSendLogConverter.INSTANCE.toFulfillmentReportSendRecord(updateLog, fulfillmentReport);

        return fulfillmentReportService.syncFulfillmentReportSendRecord(
                SyncFulfillmentReportSendRecordRequest.newBuilder()
                        .setOperation(SyncOperation.UPSERT)
                        .setBusinessIdentifier(SendRecordIdentifier.newBuilder()
                                .setReportUniqueKey(FulfillmentReportUniqueKey.newBuilder()
                                        .setBusinessId(updateLog.getBusinessId())
                                        .setAppointmentId(updateLog.getGroomingId())
                                        .setPetId(updateLog.getPetId())
                                        .setCareType(CareCategory.GROOMING)
                                        .setServiceDate("")
                                        .build())
                                .setSendMethod(SendMethod.forNumber(updateLog.getSendingMethod())))
                        .setSendRecord(sendLog)
                        .build());
    }
}
