package com.moego.server.message.mapperbean;

public class MoeMessageRecipient {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.email_id
     *
     * @mbg.generated
     */
    private Long emailId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.mandrill_message_id
     *
     * @mbg.generated
     */
    private String mandrillMessageId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.customer_id
     *
     * @mbg.generated
     */
    private Long customerId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.recipient_name
     *
     * @mbg.generated
     */
    private String recipientName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.recipient_email
     *
     * @mbg.generated
     */
    private String recipientEmail;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.send_status
     *
     * @mbg.generated
     */
    private Integer sendStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.reject_reason
     *
     * @mbg.generated
     */
    private String rejectReason;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.delivered
     *
     * @mbg.generated
     */
    private Boolean delivered;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.opened
     *
     * @mbg.generated
     */
    private Boolean opened;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.clicked
     *
     * @mbg.generated
     */
    private Boolean clicked;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.replied
     *
     * @mbg.generated
     */
    private Boolean replied;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.is_reply_read
     *
     * @mbg.generated
     */
    private Boolean isReplyRead;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.open_time
     *
     * @mbg.generated
     */
    private Long openTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.click_time
     *
     * @mbg.generated
     */
    private Long clickTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.reply_time
     *
     * @mbg.generated
     */
    private Long replyTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_message_recipient.replied_content
     *
     * @mbg.generated
     */
    private String repliedContent;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.id
     *
     * @return the value of moe_message_recipient.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.id
     *
     * @param id the value for moe_message_recipient.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.email_id
     *
     * @return the value of moe_message_recipient.email_id
     *
     * @mbg.generated
     */
    public Long getEmailId() {
        return emailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.email_id
     *
     * @param emailId the value for moe_message_recipient.email_id
     *
     * @mbg.generated
     */
    public void setEmailId(Long emailId) {
        this.emailId = emailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.business_id
     *
     * @return the value of moe_message_recipient.business_id
     *
     * @mbg.generated
     */
    public Long getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.business_id
     *
     * @param businessId the value for moe_message_recipient.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.mandrill_message_id
     *
     * @return the value of moe_message_recipient.mandrill_message_id
     *
     * @mbg.generated
     */
    public String getMandrillMessageId() {
        return mandrillMessageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.mandrill_message_id
     *
     * @param mandrillMessageId the value for moe_message_recipient.mandrill_message_id
     *
     * @mbg.generated
     */
    public void setMandrillMessageId(String mandrillMessageId) {
        this.mandrillMessageId = mandrillMessageId == null ? null : mandrillMessageId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.customer_id
     *
     * @return the value of moe_message_recipient.customer_id
     *
     * @mbg.generated
     */
    public Long getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.customer_id
     *
     * @param customerId the value for moe_message_recipient.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.recipient_name
     *
     * @return the value of moe_message_recipient.recipient_name
     *
     * @mbg.generated
     */
    public String getRecipientName() {
        return recipientName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.recipient_name
     *
     * @param recipientName the value for moe_message_recipient.recipient_name
     *
     * @mbg.generated
     */
    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName == null ? null : recipientName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.recipient_email
     *
     * @return the value of moe_message_recipient.recipient_email
     *
     * @mbg.generated
     */
    public String getRecipientEmail() {
        return recipientEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.recipient_email
     *
     * @param recipientEmail the value for moe_message_recipient.recipient_email
     *
     * @mbg.generated
     */
    public void setRecipientEmail(String recipientEmail) {
        this.recipientEmail = recipientEmail == null ? null : recipientEmail.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.send_status
     *
     * @return the value of moe_message_recipient.send_status
     *
     * @mbg.generated
     */
    public Integer getSendStatus() {
        return sendStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.send_status
     *
     * @param sendStatus the value for moe_message_recipient.send_status
     *
     * @mbg.generated
     */
    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.reject_reason
     *
     * @return the value of moe_message_recipient.reject_reason
     *
     * @mbg.generated
     */
    public String getRejectReason() {
        return rejectReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.reject_reason
     *
     * @param rejectReason the value for moe_message_recipient.reject_reason
     *
     * @mbg.generated
     */
    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason == null ? null : rejectReason.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.delivered
     *
     * @return the value of moe_message_recipient.delivered
     *
     * @mbg.generated
     */
    public Boolean getDelivered() {
        return delivered;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.delivered
     *
     * @param delivered the value for moe_message_recipient.delivered
     *
     * @mbg.generated
     */
    public void setDelivered(Boolean delivered) {
        this.delivered = delivered;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.opened
     *
     * @return the value of moe_message_recipient.opened
     *
     * @mbg.generated
     */
    public Boolean getOpened() {
        return opened;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.opened
     *
     * @param opened the value for moe_message_recipient.opened
     *
     * @mbg.generated
     */
    public void setOpened(Boolean opened) {
        this.opened = opened;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.clicked
     *
     * @return the value of moe_message_recipient.clicked
     *
     * @mbg.generated
     */
    public Boolean getClicked() {
        return clicked;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.clicked
     *
     * @param clicked the value for moe_message_recipient.clicked
     *
     * @mbg.generated
     */
    public void setClicked(Boolean clicked) {
        this.clicked = clicked;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.replied
     *
     * @return the value of moe_message_recipient.replied
     *
     * @mbg.generated
     */
    public Boolean getReplied() {
        return replied;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.replied
     *
     * @param replied the value for moe_message_recipient.replied
     *
     * @mbg.generated
     */
    public void setReplied(Boolean replied) {
        this.replied = replied;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.is_reply_read
     *
     * @return the value of moe_message_recipient.is_reply_read
     *
     * @mbg.generated
     */
    public Boolean getIsReplyRead() {
        return isReplyRead;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.is_reply_read
     *
     * @param isReplyRead the value for moe_message_recipient.is_reply_read
     *
     * @mbg.generated
     */
    public void setIsReplyRead(Boolean isReplyRead) {
        this.isReplyRead = isReplyRead;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.open_time
     *
     * @return the value of moe_message_recipient.open_time
     *
     * @mbg.generated
     */
    public Long getOpenTime() {
        return openTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.open_time
     *
     * @param openTime the value for moe_message_recipient.open_time
     *
     * @mbg.generated
     */
    public void setOpenTime(Long openTime) {
        this.openTime = openTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.click_time
     *
     * @return the value of moe_message_recipient.click_time
     *
     * @mbg.generated
     */
    public Long getClickTime() {
        return clickTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.click_time
     *
     * @param clickTime the value for moe_message_recipient.click_time
     *
     * @mbg.generated
     */
    public void setClickTime(Long clickTime) {
        this.clickTime = clickTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.reply_time
     *
     * @return the value of moe_message_recipient.reply_time
     *
     * @mbg.generated
     */
    public Long getReplyTime() {
        return replyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.reply_time
     *
     * @param replyTime the value for moe_message_recipient.reply_time
     *
     * @mbg.generated
     */
    public void setReplyTime(Long replyTime) {
        this.replyTime = replyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.company_id
     *
     * @return the value of moe_message_recipient.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.company_id
     *
     * @param companyId the value for moe_message_recipient.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_message_recipient.replied_content
     *
     * @return the value of moe_message_recipient.replied_content
     *
     * @mbg.generated
     */
    public String getRepliedContent() {
        return repliedContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_message_recipient.replied_content
     *
     * @param repliedContent the value for moe_message_recipient.replied_content
     *
     * @mbg.generated
     */
    public void setRepliedContent(String repliedContent) {
        this.repliedContent = repliedContent == null ? null : repliedContent.trim();
    }
}
