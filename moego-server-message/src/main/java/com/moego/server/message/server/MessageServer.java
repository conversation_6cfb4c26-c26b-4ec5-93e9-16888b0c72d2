package com.moego.server.message.server;

import com.moego.common.dto.PageDTO;
import com.moego.common.enums.MessageConst;
import com.moego.server.message.api.IMessageServiceBase;
import com.moego.server.message.dto.ApptReminderDetailDTO;
import com.moego.server.message.dto.ArrivalWindowSettingDto;
import com.moego.server.message.dto.BirthdayReminderDetailDTO;
import com.moego.server.message.dto.BusinessTwilioNumberDTO;
import com.moego.server.message.dto.MessageDetailDTO;
import com.moego.server.message.dto.MessageForPricingDataDto;
import com.moego.server.message.dto.RebookReminderDetailDTO;
import com.moego.server.message.dto.RepeatExpitySettingDto;
import com.moego.server.message.mapperbean.MoeBusinessMessageDetail;
import com.moego.server.message.mapstruct.MessageDetailMapper;
import com.moego.server.message.params.AutoMessageParams;
import com.moego.server.message.params.CardLinkParam;
import com.moego.server.message.params.ReminderDetailParams;
import com.moego.server.message.service.BusinessMessageControl;
import com.moego.server.message.service.CompanyMessageService;
import com.moego.server.message.service.LinkMessageService;
import com.moego.server.message.service.MoeBusinessMessageDetailService;
import com.moego.server.message.service.MoeBusinessReminderService;
import com.moego.server.message.service.MoeBusinessTwilioBindingService;
import com.moego.server.message.service.MoeReminderSettingService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class MessageServer extends IMessageServiceBase {

    private final MoeBusinessMessageDetailService moeBusinessMessageDetailService;
    private final CompanyMessageService companyMessageService;
    private final MoeBusinessReminderService moeBusinessReminderService;
    private final MoeBusinessTwilioBindingService twilioBindingService;
    private final MoeReminderSettingService moeReminderSettingService;
    private final LinkMessageService linkMessageService;
    private final BusinessMessageControl messageControl;

    @Override
    @Deprecated
    public Map<Integer, Integer> queryAllCompanySubscriptionAmount(List<Integer> companyIds) {
        return companyMessageService.queryAllCompanySubscriptionAmount(companyIds);
    }

    @Override
    public MessageForPricingDataDto queryMessageForPricingDataDto(List<Integer> companyIds) {
        return companyMessageService.queryMessageForPricingDataDto(companyIds);
    }

    @Override
    public BusinessTwilioNumberDTO getBusinessTwilioNumber(Integer businessId) {
        return twilioBindingService.getBusinessTwilioNumber(businessId);
    }

    @Override
    public List<BusinessTwilioNumberDTO> listBusinessTwilioNumber(List<Integer> businessIdList) {
        return twilioBindingService.listBusinessTwilioNumber(businessIdList);
    }

    @Override
    public MessageDetailDTO getMessageById(Integer messageId) {
        MoeBusinessMessageDetail messageDetail = moeBusinessMessageDetailService.selectMessageById(messageId);
        return MessageDetailMapper.INSTANCE.entityToDTO(messageDetail);
    }

    @Override
    public ArrivalWindowSettingDto getArrivalWindow(Integer businessId) {
        return moeBusinessReminderService.getArrivalWindow(businessId);
    }

    @Override
    public List<ArrivalWindowSettingDto> listArrivalWindow(List<Integer> businessIdList) {
        return moeBusinessReminderService.listArrivalWindow(businessIdList);
    }

    @Override
    public List<MessageDetailDTO> getAutoMessageSentRecords(AutoMessageParams params) {
        return moeBusinessMessageDetailService.getAutoMessageSentRecords(params);
    }

    @Override
    public RepeatExpitySettingDto getRepeatExpirySetting(Integer businessId) {
        return moeReminderSettingService.queryBusinessExpirySetting(businessId);
    }

    @Override
    public MessageDetailDTO queryDetailByMessageSid(Long companyId, String messageSid) {
        return moeBusinessMessageDetailService.queryDetailByMessageSid(companyId, messageSid);
    }

    @Override
    public Integer createOrUpdateCardLink(@RequestBody CardLinkParam cardLinkParam) {
        return linkMessageService.createOrUpdateCardLink(cardLinkParam);
    }

    @Override
    public Integer markAsSubmitted(@RequestParam("customerId") Integer customerId) {
        return linkMessageService.markStatus(customerId, MessageConst.LINK_SUBMITTED);
    }

    @Override
    public PageDTO<ApptReminderDetailDTO> detailAppointmentReal(ReminderDetailParams reminderDetailParams) {
        return moeBusinessReminderService.detailAppointmentRealForApiV3(reminderDetailParams);
    }

    @Override
    public PageDTO<RebookReminderDetailDTO> rebookReminderDetail(ReminderDetailParams reminderDetailParams) {
        return moeBusinessReminderService.rebookReminderDetailForApiV3(reminderDetailParams);
    }

    @Override
    public PageDTO<BirthdayReminderDetailDTO> detailPetBirthday(ReminderDetailParams reminderDetailParams) {
        return moeBusinessReminderService.birthdayReminderDetailForApiV3(reminderDetailParams);
    }

    @Override
    public Boolean verifyMessageQuota(Long companyId, Integer useQuota) {
        int remainQuota = messageControl.verifyMessageQuota(companyId, useQuota);
        return remainQuota > 0;
    }
}
