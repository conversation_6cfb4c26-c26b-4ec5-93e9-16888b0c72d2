package com.moego.server.message.service.messagebodyparse.impl;

import com.moego.common.utils.DateUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.service.messagebodyparse.MessageBodyParseService;
import com.moego.server.message.service.util.AppointmentServiceDetailHelper;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2020-08-10 23:11
 */
@Service
@Slf4j
public class MessageBodyParseAppointmentService implements MessageBodyParseService {
    private static final String HIT_CHAR_ONE = "{Appointment date}";
    private static final String HIT_CHAR_TWO = "{Appointment time}";
    private static final String HIT_CHAR_THREE = "{Day of week}";
    private static final String HIT_CHAR_FOUR = "{appointmentStaffName}";
    private static final String HIT_CHAR_FOUR_OLD = "{staffName}";
    private static final String HIT_CHAR_FIVE = "{pet&Service}";
    private static final String HIT_CHAR_SIX = "{oldAppointmentTime}";
    private static final String HIT_CHAR_ONE_NEW = "{Date}";
    private static final String HIT_CHAR_TWO_NEW = "{Time}";
    private static final String HIT_CHAR_APPOINTMENT_TIME = "{appointmentTime}";
    private static final String HIT_CHAR_THREE_NEW = "{dayOfWeek}";
    private static final String HIT_CHAR_APPOINTMENT_DATE = "{appointmentDate}";
    private static final String HIT_CHAR_APPOINTMENT_NIGHTS = "{appointmentNights}";
    private static final String HIT_CHAR_APPOINTMENT_END_DATE = "{appointmentEndDate}";
    private static final String HIT_CHAR_ESTIMATE_TOTAL = "{estimateTotal}";
    private static final String[] WEEK_ARR = new String[] {
        "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday",
    };

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private AppointmentServiceDetailHelper appointmentServiceDetailHelper;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Override
    public boolean hit(String body) {
        return StringUtils.containsAny(
                body,
                HIT_CHAR_ONE,
                HIT_CHAR_TWO,
                HIT_CHAR_THREE,
                HIT_CHAR_FOUR,
                HIT_CHAR_FIVE,
                HIT_CHAR_SIX,
                HIT_CHAR_APPOINTMENT_NIGHTS,
                HIT_CHAR_APPOINTMENT_DATE,
                HIT_CHAR_ESTIMATE_TOTAL,
                HIT_CHAR_APPOINTMENT_END_DATE,
                HIT_CHAR_ONE_NEW,
                HIT_CHAR_TWO_NEW,
                HIT_CHAR_APPOINTMENT_TIME,
                HIT_CHAR_THREE_NEW,
                HIT_CHAR_FOUR_OLD);
    }

    @Override
    public String parse(String body, SendMessagesParams sendMessagesParams) {
        GroomingTicketWindowDetailDTO groomingTicketWindowDetailDTO = (GroomingTicketWindowDetailDTO)
                sendMessagesParams.getExtParams().get(SendMessagesParams.EXT_PARAM_NAME_APPOINTMENT);
        String parseBody = body;
        if (groomingTicketWindowDetailDTO == null) {
            return body;
        }
        if (StringUtils.containsAny(body, HIT_CHAR_ONE, HIT_CHAR_ONE_NEW, HIT_CHAR_APPOINTMENT_DATE)) {
            String date = DateUtil.dateToBusinessFormat(
                    groomingTicketWindowDetailDTO.getAppointmentDate(), sendMessagesParams.getDateFormat());

            parseBody = parseBody
                    .replace(HIT_CHAR_ONE, date)
                    .replace(HIT_CHAR_ONE_NEW, date)
                    .replace(HIT_CHAR_APPOINTMENT_DATE, date);
        }
        if (StringUtils.containsAny(parseBody, HIT_CHAR_TWO, HIT_CHAR_TWO_NEW, HIT_CHAR_APPOINTMENT_TIME)) {
            String appointmentTime;

            if (StringUtils.isEmpty(groomingTicketWindowDetailDTO.getArrivalWindowTimeString())) {
                Integer startTime = groomingTicketWindowDetailDTO.getAppointmentStartTime();
                int type = sendMessagesParams.getTimeFormatType();

                appointmentTime = DateUtil.minuteToBusinessTime(startTime, type);
            } else {
                // arrival window time str
                appointmentTime = groomingTicketWindowDetailDTO.getArrivalWindowTimeString();
            }
            parseBody = parseBody
                    .replace(HIT_CHAR_TWO, appointmentTime)
                    .replace(HIT_CHAR_TWO_NEW, appointmentTime)
                    .replace(HIT_CHAR_APPOINTMENT_TIME, appointmentTime);
        }
        if (body.contains(HIT_CHAR_THREE) || body.contains(HIT_CHAR_THREE_NEW)) {
            Date date = DateUtil.parseDate(groomingTicketWindowDetailDTO.getAppointmentDate(), DateUtil.STANDARD_DATE);
            int dayOfWeekIndex = DateUtil.dateToWeek(date);
            String dayOfWeek = WEEK_ARR[dayOfWeekIndex];
            parseBody = parseBody.replace(HIT_CHAR_THREE, dayOfWeek);
            parseBody = parseBody.replace(HIT_CHAR_THREE_NEW, dayOfWeek);
        }

        // For appointment end date.
        if (body.contains(HIT_CHAR_APPOINTMENT_END_DATE)) {
            String appointmentEndDate = DateUtil.dateToBusinessFormat(
                    groomingTicketWindowDetailDTO.getAppointmentEndDate(), sendMessagesParams.getDateFormat());
            parseBody = parseBody.replace(HIT_CHAR_APPOINTMENT_END_DATE, appointmentEndDate);
        }

        // For appointment nights.
        if (body.contains(HIT_CHAR_APPOINTMENT_NIGHTS)) {
            Date appointmentDate =
                    DateUtil.parseDate(groomingTicketWindowDetailDTO.getAppointmentDate(), DateUtil.STANDARD_DATE);
            Date appointmentEndDate =
                    DateUtil.parseDate(groomingTicketWindowDetailDTO.getAppointmentEndDate(), DateUtil.STANDARD_DATE);

            int appointmentNight = DateUtil.daysBetween(appointmentDate, appointmentEndDate);
            parseBody = parseBody.replace(HIT_CHAR_APPOINTMENT_NIGHTS, Integer.toString(appointmentNight));
        }

        // For estimate total.
        if (body.contains(HIT_CHAR_ESTIMATE_TOTAL)) {
            InfoIdParams infoIdParams = new InfoIdParams();
            infoIdParams.setInfoId(groomingTicketWindowDetailDTO.getBusinessId());
            MoeBusinessDto moeBusinessDto = iBusinessBusinessClient.getBusinessInfo(infoIdParams);

            // Keep the same format with FE:
            DecimalFormatSymbols dfs = new DecimalFormatSymbols();
            dfs.setCurrencySymbol(moeBusinessDto.getCurrencySymbol());
            dfs.setGroupingSeparator(',');
            dfs.setDecimalSeparator('.');
            NumberFormat numberFormat = NumberFormat.getCurrencyInstance();
            ((DecimalFormat) numberFormat).setDecimalFormatSymbols(dfs);
            numberFormat.setMaximumFractionDigits(2);

            String estimateTotal = numberFormat.format(groomingTicketWindowDetailDTO.getEstimatedTotalPrice());
            parseBody = parseBody.replace(HIT_CHAR_ESTIMATE_TOTAL, estimateTotal);
        }

        /*
        处理 appointmentStaffName 和 pet&Service 描述
         */
        List<GroomingPetDetailDTO> groomingPetDetails = groomingTicketWindowDetailDTO.getGroomingPetDetails();
        List<EvaluationServiceDetailDTO> evaluationDetails =
                groomingTicketWindowDetailDTO.getEvaluationServiceDetails();
        if (!CollectionUtils.isEmpty(groomingPetDetails) || !CollectionUtils.isEmpty(evaluationDetails)) {
            /*
            获取staffName和petName；
             */
            List<Integer> staffIds = new ArrayList<>();
            groomingPetDetails.forEach(p -> staffIds.add(p.getStaffId()));

            if (body.contains(HIT_CHAR_FOUR) || body.contains(HIT_CHAR_FOUR_OLD)) {
                StaffIdListParams staffIdListParams = new StaffIdListParams();
                staffIdListParams.setBusinessId(sendMessagesParams.getBusinessId());
                staffIdListParams.setStaffIdList(staffIds);
                List<MoeStaffDto> staffList = iBusinessStaffClient.getStaffList(staffIdListParams);
                Set<String> staffNameSet = new HashSet<>(); // appointmentStaffName, 去重展示
                for (MoeStaffDto staffDto : staffList) {
                    // {appointmentStaffName} 修改为只展示first name, MOE-4185
                    staffNameSet.add(staffDto.getFirstName());
                }
                // 处理staffName描述
                StringBuilder appStaffNameSb = new StringBuilder();
                staffNameSet.forEach(s -> appStaffNameSb.append(s).append(", "));
                String staffName =
                        appStaffNameSb.isEmpty() ? "" : appStaffNameSb.substring(0, appStaffNameSb.length() - 2);
                parseBody = parseBody.replace(HIT_CHAR_FOUR, staffName);
                parseBody = parseBody.replace(HIT_CHAR_FOUR_OLD, staffName);
            }
            if (body.contains(HIT_CHAR_FIVE)) {
                Map<Integer, CustomerPetDetailDTO> petMap = appointmentServiceDetailHelper.getCustomerPetMapByIdList(
                        appointmentServiceDetailHelper.getPetIds(groomingPetDetails, evaluationDetails));
                parseBody = parseBody.replace(
                        HIT_CHAR_FIVE,
                        appointmentServiceDetailHelper.buildPetAndServiceStr(
                                groomingPetDetails, evaluationDetails, petMap));
            }
        } else {
            log.error("groomingPetDetails 为空，无法获取pet&service 和 appointStaffName 信息。");
        }

        if (body.contains(HIT_CHAR_SIX)) {
            String oldAppointmentDate = groomingTicketWindowDetailDTO.getOldAppointmentDate();
            Integer oldAppointmentStartTime = groomingTicketWindowDetailDTO.getOldAppointmentStartTime();

            if (StringUtils.isNotBlank(oldAppointmentDate)) {
                Date date = Objects.requireNonNull(DateUtil.parseDate(oldAppointmentDate, "yyyy-MM-dd"));
                date = new Date(date.getTime() + oldAppointmentStartTime * 60 * 1000L);
                String dateStr = DateUtil.getDateByBusiness(date, sendMessagesParams.getDateFormat());
                // fixed https://moego.atlassian.net/browse/ERP-882
                // oldAppointmentTime should display time, not only date
                dateStr += " "
                        + DateUtil.minuteToBusinessTime(
                                oldAppointmentStartTime, sendMessagesParams.getTimeFormatType());
                parseBody = parseBody.replace(HIT_CHAR_SIX, dateStr);
            } else {
                parseBody = parseBody.replace(HIT_CHAR_SIX, "");
                log.error("oldAppointmentDate is null id is" + groomingTicketWindowDetailDTO.getId());
                log.error("预约查询有误 appointment is {}", groomingTicketWindowDetailDTO.toString());
            }
        }
        return parseBody;
    }
}
