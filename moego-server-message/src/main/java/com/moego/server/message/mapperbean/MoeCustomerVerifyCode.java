package com.moego.server.message.mapperbean;

public class MoeCustomerVerifyCode {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_verify_code.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_verify_code.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_verify_code.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_verify_code.phone_number
     *
     * @mbg.generated
     */
    private String phoneNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_verify_code.verify_code
     *
     * @mbg.generated
     */
    private String verifyCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_verify_code.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_verify_code.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_verify_code.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_verify_code.id
     *
     * @return the value of moe_customer_verify_code.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_verify_code.id
     *
     * @param id the value for moe_customer_verify_code.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_verify_code.business_id
     *
     * @return the value of moe_customer_verify_code.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_verify_code.business_id
     *
     * @param businessId the value for moe_customer_verify_code.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_verify_code.customer_id
     *
     * @return the value of moe_customer_verify_code.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_verify_code.customer_id
     *
     * @param customerId the value for moe_customer_verify_code.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_verify_code.phone_number
     *
     * @return the value of moe_customer_verify_code.phone_number
     *
     * @mbg.generated
     */
    public String getPhoneNumber() {
        return phoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_verify_code.phone_number
     *
     * @param phoneNumber the value for moe_customer_verify_code.phone_number
     *
     * @mbg.generated
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber == null ? null : phoneNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_verify_code.verify_code
     *
     * @return the value of moe_customer_verify_code.verify_code
     *
     * @mbg.generated
     */
    public String getVerifyCode() {
        return verifyCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_verify_code.verify_code
     *
     * @param verifyCode the value for moe_customer_verify_code.verify_code
     *
     * @mbg.generated
     */
    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode == null ? null : verifyCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_verify_code.status
     *
     * @return the value of moe_customer_verify_code.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_verify_code.status
     *
     * @param status the value for moe_customer_verify_code.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_verify_code.create_time
     *
     * @return the value of moe_customer_verify_code.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_verify_code.create_time
     *
     * @param createTime the value for moe_customer_verify_code.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_verify_code.update_time
     *
     * @return the value of moe_customer_verify_code.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_verify_code.update_time
     *
     * @param updateTime the value for moe_customer_verify_code.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
