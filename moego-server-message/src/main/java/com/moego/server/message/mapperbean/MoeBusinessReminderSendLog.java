package com.moego.server.message.mapperbean;

import java.util.Date;

public class MoeBusinessReminderSendLog {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.target_id
     *
     * @mbg.generated
     */
    private Integer targetId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.target_type
     *
     * @mbg.generated
     */
    private Integer targetType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.send_date
     *
     * @mbg.generated
     */
    private String sendDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.send_type
     *
     * @mbg.generated
     */
    private Integer sendType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.flag
     *
     * @mbg.generated
     */
    private Integer flag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.message_detail_id
     *
     * @mbg.generated
     */
    private Integer messageDetailId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.auto_send_type
     *
     * @mbg.generated
     */
    private Byte autoSendType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_reminder_send_log.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.id
     *
     * @return the value of moe_business_reminder_send_log.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.id
     *
     * @param id the value for moe_business_reminder_send_log.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.target_id
     *
     * @return the value of moe_business_reminder_send_log.target_id
     *
     * @mbg.generated
     */
    public Integer getTargetId() {
        return targetId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.target_id
     *
     * @param targetId the value for moe_business_reminder_send_log.target_id
     *
     * @mbg.generated
     */
    public void setTargetId(Integer targetId) {
        this.targetId = targetId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.target_type
     *
     * @return the value of moe_business_reminder_send_log.target_type
     *
     * @mbg.generated
     */
    public Integer getTargetType() {
        return targetType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.target_type
     *
     * @param targetType the value for moe_business_reminder_send_log.target_type
     *
     * @mbg.generated
     */
    public void setTargetType(Integer targetType) {
        this.targetType = targetType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.send_date
     *
     * @return the value of moe_business_reminder_send_log.send_date
     *
     * @mbg.generated
     */
    public String getSendDate() {
        return sendDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.send_date
     *
     * @param sendDate the value for moe_business_reminder_send_log.send_date
     *
     * @mbg.generated
     */
    public void setSendDate(String sendDate) {
        this.sendDate = sendDate == null ? null : sendDate.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.send_type
     *
     * @return the value of moe_business_reminder_send_log.send_type
     *
     * @mbg.generated
     */
    public Integer getSendType() {
        return sendType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.send_type
     *
     * @param sendType the value for moe_business_reminder_send_log.send_type
     *
     * @mbg.generated
     */
    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.flag
     *
     * @return the value of moe_business_reminder_send_log.flag
     *
     * @mbg.generated
     */
    public Integer getFlag() {
        return flag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.flag
     *
     * @param flag the value for moe_business_reminder_send_log.flag
     *
     * @mbg.generated
     */
    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.message_detail_id
     *
     * @return the value of moe_business_reminder_send_log.message_detail_id
     *
     * @mbg.generated
     */
    public Integer getMessageDetailId() {
        return messageDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.message_detail_id
     *
     * @param messageDetailId the value for moe_business_reminder_send_log.message_detail_id
     *
     * @mbg.generated
     */
    public void setMessageDetailId(Integer messageDetailId) {
        this.messageDetailId = messageDetailId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.auto_send_type
     *
     * @return the value of moe_business_reminder_send_log.auto_send_type
     *
     * @mbg.generated
     */
    public Byte getAutoSendType() {
        return autoSendType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.auto_send_type
     *
     * @param autoSendType the value for moe_business_reminder_send_log.auto_send_type
     *
     * @mbg.generated
     */
    public void setAutoSendType(Byte autoSendType) {
        this.autoSendType = autoSendType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_reminder_send_log.create_time
     *
     * @return the value of moe_business_reminder_send_log.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_reminder_send_log.create_time
     *
     * @param createTime the value for moe_business_reminder_send_log.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
