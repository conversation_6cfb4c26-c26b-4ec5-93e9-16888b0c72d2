<#assign lightThemeColor = lightThemeColor!'#FEF0E8' />
<#assign themeColor = themeColor!'#F96B18' />
<#assign thankYouMessage = thankYouMessage!'Thank you for your trust and support' />
<#assign businessAvatarPath = businessInfo.avatarPath />
<#assign businessName = businessInfo.businessName />
<#assign petName = petInfo.petName />
<table align="center" width="100%" data-id="__react-email-container" role="presentation" cellspacing="0" cellpadding="0" border="0" style="max-width: 600px; display: table; border-collapse: separate; box-sizing: border-box; min-width: 600px; width: 600px; padding: 40px 28px; background-color: ${lightThemeColor};">
  <tbody>
  <tr style="width: 100%;">
    <td>
      <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="margin-top: 0px; margin-bottom: 24px; text-align: center;">
        <tbody>
        <tr>
          <td>
            <img data-id="react-email-img" src="${businessAvatarPath}" width="80" height="80" style="display: block; outline: none; border: none; text-decoration: none; margin: 0px auto; border-radius: 50%; object-fit: cover;">
            <h1 data-id="react-email-heading" style="margin-top: 16px; margin-bottom: 0px; font-weight: 700; font-size: 34px; line-height: 42px; text-align: center; font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;;">${petName}'s ${reportTitle}</h1>
          </td>
        </tr>
        </tbody>
      </table>
      <#if showcase?? && ((showcase.images?? && showcase.images?has_content) || (showcase.videoLink?? && showcase.videoLink?has_content))>
        <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="display: table; border-collapse: separate; box-sizing: border-box; padding: 24px; background-color: rgb(255, 255, 255); margin-top: 12px; border-radius: 20px;">
          <tbody>
          <tr>
            <td>
              <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0">
                <tbody style="width: 100%;">
                <tr style="width: 100%;">
                  <td data-id="__react-email-column" style="width: 100%;">
                    <p data-id="react-email-text" style="font-size: 18px; line-height: 18px; margin: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;; font-weight: 700;">Highlights</p>
                    <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                      <tbody>
                      <tr>
                        <td>
                          <#if showcase.images?? && showcase.images?size == 1>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 16px;">
                              <tbody style="width: 100%;">
                              <tr style="width: 100%;">
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[0]}" width="496" height="496" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                              </tr>
                              </tbody>
                            </table>
                          </#if>
                          <#if showcase.images?? && showcase.images?size == 2>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 16px;">
                              <tbody style="width: 100%;">
                              <tr style="width: 100%;">
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[0]}" width="240" height="240" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                                <td data-id="__react-email-column" style="width: 16px;"></td>
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[1]}" width="240" height="240" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                              </tr>
                              </tbody>
                            </table>
                          </#if>
                          <#if showcase.images?? && showcase.images?size == 3>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 16px;">
                              <tbody style="width: 100%;">
                              <tr style="width: 100%;">
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[0]}" width="154.5" height="154.5" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                                <td data-id="__react-email-column" style="width: 16px;"></td>
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[1]}" width="154.5" height="154.5" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                                <td data-id="__react-email-column" style="width: 16px;"></td>
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[2]}" width="154.5" height="154.5" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                              </tr>
                              </tbody>
                            </table>
                          </#if>
                          <#if showcase.images?? && showcase.images?size == 4>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 16px;">
                              <tbody style="width: 100%;">
                              <tr style="width: 100%;">
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[0]}" width="240" height="240" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                                <td data-id="__react-email-column" style="width: 16px;"></td>
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[1]}" width="240" height="240" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                              </tr>
                              </tbody>
                            </table>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 16px;">
                              <tbody style="width: 100%;">
                              <tr style="width: 100%;">
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[2]}" width="240" height="240" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                                <td data-id="__react-email-column" style="width: 16px;"></td>
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[3]}" width="240" height="240" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                              </tr>
                              </tbody>
                            </table>
                          </#if>
                          <#if showcase.images?? && showcase.images?size == 5>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 16px;">
                              <tbody style="width: 100%;">
                              <tr style="width: 100%;">
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[0]}" width="240" height="240" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                                <td data-id="__react-email-column" style="width: 16px;"></td>
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[1]}" width="240" height="240" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                              </tr>
                              </tbody>
                            </table>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 16px;">
                              <tbody style="width: 100%;">
                              <tr style="width: 100%;">
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[2]}" width="154.5" height="154.5" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                                <td data-id="__react-email-column" style="width: 16px;"></td>
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[3]}" width="154.5" height="154.5" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                                <td data-id="__react-email-column" style="width: 16px;"></td>
                                <td data-id="__react-email-column"><img data-id="react-email-img" src="${showcase.images[4]}" width="154.5" height="154.5" style="display: block; outline: none; border: none; text-decoration: none; border-radius: 20px; object-fit: cover;"></td>
                              </tr>
                              </tbody>
                            </table>
                          </#if>
                          <#if showcase.videoLink?? && showcase.videoLink?has_content>
                            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 16px;">
                              <tbody style="width: 100%;">
                              <tr style="width: 100%;">
                                <td data-id="__react-email-column" style="width: 100%;">
                                  <a href="${showcase.videoLink}" data-id="react-email-button" target="_blank" style="background-color: ${themeColor}; color: rgb(255, 255, 255); padding: 9px 16px; border-radius: 56px; text-decoration: none; display: inline-block; text-align: center; line-height: 100%; font-weight: 500; font-size: 16px; font-family: &quot;Proxima Nova&quot;, Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;; box-sizing: content-box; height: 22px; max-width: 100%;">
                                              <span>
                                                <!--[if mso]><i style="letter-spacing: 16px;mso-font-width:-100%;mso-text-raise:13.5" hidden>&nbsp;</i><![endif]-->
                                              </span>
                                    <span style="max-width: 100%; display: inline-block; line-height: 120%;">
                                                <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="line-height: 22px;">
                                                  <tbody style="width: 100%;">
                                                    <tr style="width: 100%;">
                                                      <td data-id="__react-email-column"><img data-id="react-email-img" alt="video icon" src="https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1748318284d581af22396f4542987d768a8d99b3bd.png?name=video.png" style="display: block; outline: none; border: none; text-decoration: none; width: 20px; height: 20px; vertical-align: middle;"></td>
                                                      <td data-id="__react-email-column" style="width: 4px;"></td>
                                                      <td data-id="__react-email-column" style="color: rgb(255, 255, 255);">View video</td>
                                                    </tr>
                                                  </tbody>
                                                </table>
                                              </span>
                                    <span>
                                                <!--[if mso]><i style="letter-spacing: 16px;mso-font-width:-100%" hidden>&nbsp;</i><![endif]-->
                                              </span>
                                  </a>
                                </td>
                              </tr>
                              </tbody>
                            </table>
                          </#if>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          </tbody>
        </table>
      </#if>
      <#if overallFeedbacks?? && overallFeedbacks?has_content>
        <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="display: table; border-collapse: separate; box-sizing: border-box; padding: 24px; background-color: rgb(255, 255, 255); margin-top: 12px; border-radius: 20px;">
          <tbody>
          <tr>
            <td>
              <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0">
                <tbody style="width: 100%;">
                <tr style="width: 100%;">
                  <td data-id="__react-email-column" style="width: 100%;">
                    <#list overallFeedbacks as feedback>
                      <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                        <tbody>
                        <tr>
                          <td>
                            <p data-id="react-email-text" style="font-size: 18px; line-height: 18px; margin: 0px 0px 8px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;; font-weight: 700; overflow-wrap: break-word; word-break: break-word; white-space: pre-wrap;">${feedback.question}</p>
                            <p data-id="react-email-text" style="font-size: 14px; line-height: 20px; margin: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;; font-weight: 400; overflow-wrap: break-word; word-break: break-word; white-space: pre-wrap;">${feedback.answer}</p>
                            <#if feedback_has_next>
                              <hr data-id="react-email-hr" style="width: 100%; border-top: 1px solid rgb(234, 234, 234); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-top: 16px; margin-bottom: 16px;">
                            </#if>
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </#list>
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          </tbody>
        </table>
      </#if>
      <#if customizedFeedbacks?? && customizedFeedbacks?has_content>
        <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="display: table; border-collapse: separate; box-sizing: border-box; padding: 24px; background-color: rgb(255, 255, 255); margin-top: 12px; border-radius: 20px;">
          <tbody>
          <tr>
            <td>
              <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0">
                <tbody style="width: 100%;">
                <tr style="width: 100%;">
                  <td data-id="__react-email-column" style="width: 100%;">
                    <#list customizedFeedbacks as feedback>
                      <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation">
                        <tbody>
                        <tr>
                          <td>
                            <p data-id="react-email-text" style="font-size: 18px; line-height: 18px; margin: 0px 0px 8px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;; font-weight: 700; overflow-wrap: break-word; word-break: break-word; white-space: pre-wrap;">${feedback.question}</p>
                            <p data-id="react-email-text" style="font-size: 14px; line-height: 20px; margin: 0px; color: rgb(51, 51, 51); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;; font-weight: 400; overflow-wrap: break-word; word-break: break-word; white-space: pre-wrap;">${feedback.answer}</p>
                            <#if feedback_has_next>
                              <hr data-id="react-email-hr" style="width: 100%; border-top: 1px solid rgb(234, 234, 234); border-right: none; border-bottom: none; border-left: none; border-image: initial; margin-top: 16px; margin-bottom: 16px;">
                            </#if>
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </#list>
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          </tbody>
        </table>
      </#if>
      <table align="center" width="100%" data-id="react-email-section" border="0" cellpadding="0" cellspacing="0" role="presentation" style="display: table; border-collapse: separate; box-sizing: border-box; margin-top: 24px;">
        <tbody>
        <tr>
          <td>
            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin-bottom: 8px;">
              <tbody style="width: 100%;">
              <tr style="width: 100%;">
                <td data-id="__react-email-column" style="text-align: center; width: 100%;">
                  <p data-id="react-email-text" style="font-size: 18px; line-height: 18px; margin: 0px; color: rgb(32, 32, 32); font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;; font-weight: 700; text-align: center;">${thankYouMessage}</p>
                </td>
              </tr>
              </tbody>
            </table>
            <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="vertical-align: middle; text-align: center;">
              <tbody style="width: 100%;">
              <tr style="width: 100%;">
                <td data-id="__react-email-column" style="display: inline-block; text-align: left;">
                  <table align="center" width="100%" data-id="react-email-row" role="presentation" cellspacing="0" cellpadding="0" border="0" style="vertical-align: middle;">
                    <p data-id="react-email-text" style="font-size: 14px; line-height: 24px; margin: 0px; color: rgb(32, 32, 32); font-family: Helvetica, Arial, &quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;; font-weight: 400; text-align: left;">${businessName}</p>
                  </table>
                </td>
              </tr>
              </tbody>
            </table>
          </td>
        </tr>
        </tbody>
      </table>
    </td>
  </tr>
  </tbody>
</table>